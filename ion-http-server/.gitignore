# Binaries for programs and plugins	
*.exe	
*.exe~	
*.dll	
*.so	
*.dylib	

# Test binary, built with `go test -c`	
*.test	

# Output of the go coverage tool, specifically when used with LiteIDE	
*.out	
*.log	
*.pid	
bin	
gorm.db	
bin/*
configs/*.pid
logs/*
uploads
uploads/*
local.toml
# Dependency directories (remove the comment below to include it)	
# vendor/	
.DS_Store

templates/avatar/*
!templates/avatar/default_avatar.png
!templates/avatar/group_chat_avatar.png

# cert file
configs/cert
configs/wechat_cert


dev.toml
remote.toml
.DS_Store
