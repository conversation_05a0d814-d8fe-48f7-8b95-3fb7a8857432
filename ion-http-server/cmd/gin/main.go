package main

import (
	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"github.com/Young666666Jian/ion-http-server/pkg/route"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	log.Init(conf.Log.Level)
	entity.Init(conf.Database.Name, conf.Database.Addrs, conf.Database.UidKey)
	defer entity.Close()

	// for now supportde the 32
	if len(conf.Utils.KeyText) != 32 {
		panic("not support the key len")
	}

	if conf.Global.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	} else if conf.Global.Mode != "debug" {
		panic("Http node should be deployed in either 'release' or 'debug' mode")
	}
	entity.AddVersion(conf.Version)
	v, d := entity.GetLatestVersion()
	log.Infof("fangyu app version: %v, function description: %v", v, d)
	r := gin.Default()
	r.Use(cors.Default())
	route.Register(r)
	if conf.Tls.Tls {
		r.RunTLS(conf.Global.Pprof, conf.Tls.Cert, conf.Tls.Key)
	} else {
		r.Run(conf.Global.Pprof) // listen and serve on 0.0.0.0:8080 (for windows "localhost:8080")
	}
}
