version: "3.7"

services:
  usersystem:
    image: fangyu/usersystem:latest
    build:
      dockerfile: ./docker/usersystem.Dockerfile
      context: .
    container_name: "fangyu-usersystem"
    volumes:
      - "./configs/remote.toml:/configs/gin.toml"
      - "./templates:/templates"
      - "./configs/cert:/configs/cert"
      - "./configs/wehcat_cert:/configs/wehcat_cert"
    ports:
      - 20000:20000
    networks:
      - fangyu_network


networks:
  fangyu_network:
    external: true
    name: fangyu_network
    driver: bridge