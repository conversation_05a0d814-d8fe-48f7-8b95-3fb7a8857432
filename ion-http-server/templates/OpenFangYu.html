<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Open FangYu Meeting</title>
</head>
<style type="text/css">
  * {
    padding: 0;
    margin: 0;
  }

  /*顶部标题栏*/
  .title_box {
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #48d484;
    font-size: 20px;
    display: flex;
  }

  .title_box p {
    flex: 3;
    text-align: center;
    color: #ffffff;
  }

  #weixin-tip {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.8);
    filter: alpha(opacity=80);
    width: 100%;
    height: 100%;
    z-index: 100;
    color: #ffffff;
  }

  #weixin-tip p {
    text-align: center;
    margin-top: 10%;
    padding: 0 5%;
    position: relative;
  }

  #weixin-tip p img {
    width: 100%;
  }

  #weixin-tip .close {
    position: absolute;
    width: 50px;
    top: 0;
    left: 5%;
  }

  .app-box {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100px;
    padding: 20px 0;
    text-align: center;
  }

  .app-box .app-left {
    width: 48%;
    float: left;
  }


  .app-box img {
    width: 80%;
  }

  .desc {
    padding-top: 50px;
    padding-bottom: 20px;
    text-align: center;
  }

  .desc h1 {
    height: 5rem;
    line-height: 5rem;
    font-size: 2.2rem;
    color: #333;
  }

  .desc p {
    height: 3rem;
    line-height: 3rem;
    font-size: 1.5rem;
    color: #8E8F90;
  }
</style>

<body>
  <!--标题-->
  <div class="title_box">
    <p>FangYu Meeting</p>
    </p>
  </div>
  <div class="desc">
    <h1>手机客户端</h1>
    <p>随时随地，视频会议。</p>
  </div>
  <div class="app-box">
    <div class="app-left" id="download_android">
      <!-- <img src="images/android-btn.png" /> -->
      打开（Open） APP
    </div>
  </div>
  <div id="weixin-tip">
    <p>
      <span id="close" class="close">×</span>
      <span>请点击右上角使用浏览器打开</span>
    </p>
  </div>
  <script src="http://code.jquery.com/jquery-1.8.3.min.js"></script>
  <script type="text/javascript">
    //判断是不是微信端
    var is_weixin = (function () {
      return navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1
    })();
    var tip = document.getElementById('weixin-tip');
    var close = document.getElementById('close');
    var tip = document.getElementById('weixin-tip');
    var android = document.getElementById('download_android');
    var winHeight = typeof window.innerHeight != 'undefined' ? window.innerHeight : document.documentElement.clientHeight; //兼容IOS，不需要的可以去掉 
    window.onload = function () {
      if (is_weixin) {
        tip.style.height = winHeight + 'px'; //兼容IOS弹窗整屏
        tip.style.display = 'block';
      } else {
        //判断Android
        if (/(Android)/i.test(navigator.userAgent)) {
          console.log(navigator.userAgent)
          //直接下载
          var android_url = "zyt://app/openwith?id=20211005";//这里填写android版本的下载地址
          window.location.href = android_url;
        } else {
          console.log(navigator.userAgent)
        }
      }
    }
    close.onclick = function () {
      tip.style.display = 'none';
    }
    $("#download_android").click(function () {
      if (is_weixin) {
        tip.style.height = winHeight + 'px'; //兼容IOS弹窗整屏
        tip.style.display = 'block';
      } else {
        tip.style.display = 'none';
        //判断iPhone|iPad|iPod|iOS
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
          alert("苹果系统不支持安卓软件！");
        } else if (/(Android)/i.test(navigator.userAgent)) {
          var android_url = "zyt://app/openwith?id=20211005";//这里填写android版本的下载地址
          if (android_url != "") {
            window.location.href = android_url;
          } else {
            alert("安卓版本正在开发中，敬请期待！");
          }
        }
      }
    });

  </script>
</body>

</html>