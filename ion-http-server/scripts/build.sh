#!/bin/bash
APP_DIR=$(cd `dirname $0`/../; pwd)
OS_TYPE=""
#. $APP_DIR/scripts/common
echo "$APP_DIR"
cd $APP_DIR
EXE=gin


COMMAND=$APP_DIR/bin/$EXE


help()
{
    echo ""
    echo "build script"
    echo "Usage: ./build.sh [-h]"
    echo ""
}

while getopts "o:h" arg
do
    case $arg in
        h)
            help;
            exit 0
            ;;
        o)
            OS_TYPE=$OPTARG
            ;;
        ?)
            echo "No argument needed. Ignore them all!"
            ;;
    esac
done

if [[ "$OS_TYPE" == "Darwin" || "$OS_TYPE" == "mac" || "$OS_TYPE" == "darwin" ]];then
    export CGO_ENABLED=1
    export GOOS=darwin
fi

if [[ "$OS_TYPE" == "Ubuntu" || "$OS_TYPE" =~ "CentOS" || "$OS_TYPE" == "ubuntu" || "$OS_TYPE" =~ "centos" || "$OS_TYPE" =~ "linux" || "$OS_TYPE" =~ "Linux" ]];then
    export GOOS=linux
fi

echo "-------------build ion----------"
echo "go build -o $COMMAND"
cd $APP_DIR/cmd/$EXE
go build -o $COMMAND
