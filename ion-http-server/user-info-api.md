### User Info API



#### 1.获取用户基本信息

```
GET /api/v1/user
```

response body：

```json
{
    "avatarUrl": "http://192.168.1.170:5000/templates/avatar/9655ef0ac1ae43bc28afc7e7d3c736331c4267d053b3a810d596dffe069328d4.jpg",
    "email": "",
    "phone": "13003066903",
    "pmi": "1507111819",
    "uid": "usrxxxxx",
    "username": "userxxxx",
    ~~"uid": "63df27583ed0ed2d48a1c959f6bf92adea6ba32650100b51a2ab4eece6ee0d77",~~
    ~~"username": "log"~~
}
```



#### 2.更改用户名

```
POST /api/v1/user/name
```

Post Body:

```json
{
	"username":"logici"
}
```

response body:

```json
{
    "message": "update user name successfully.",
    "username": "logici"
}
```



#### 3.上传头像

```
POST /api/v1/upload/avatar
```

Post Body:

```
form-data
key:file value:图片数据
```

response body：

```json
{
    "avatarUrl": "http://192.168.1.170:5000/templates/avatar/9655ef0ac1ae43bc28afc7e7d3c736331c4267d053b3a810d596dffe069328d4.jpg",
    "message": "update avatar image successfully."
}
```



