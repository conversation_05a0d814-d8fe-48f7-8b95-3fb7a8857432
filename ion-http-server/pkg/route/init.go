package route

import (
	"github.com/Young666666Jian/ion-http-server/pkg/controller"
	"github.com/Young666666Jian/ion-http-server/pkg/middleware"
	"github.com/gin-gonic/gin"
)

func Register(router *gin.Engine) {
	router.Static("/templates", "./templates")
	v1 := router.Group("/api/v1")
	registerAuth(v1)
}

func registerAuth(routers *gin.RouterGroup) {
	routers.POST("/signin", controller.JwtMiddleware.LoginHandler)
	routers.POST("/signup", controller.SignupEndpoint)
	routers.GET("/version", controller.GetVersion)
	routers.POST("/refresh_token", controller.JwtMiddleware.RefreshHandler)

	routers.POST("/email/code", controller.SendEmail)
	routers.POST("/phone/code", controller.SendPhone)

	routers.POST("/oauth/wechat/login", controller.WechatLogin)
	routers.POST("/oauth/qq/login", controller.QQLogin)

	routers.POST("/phone/signup", controller.SignupWithPhone)
	routers.POST("/email/signup", controller.SignupWithEmail)

	//get avatar
	routers.GET("/avatar/:filename", controller.GetUserAvatar)
	// routers.GET("/topicinfo/:topic", controller.GetUserAvatar)
	// routers.GET("/message/illegal", controller.QueryMessages)
	routers.POST("/order/topup", controller.CreateOrder)
	routers.POST("/order/wechcatpay/notify", controller.WechatPayNotify)
	routers.GET("/user/:id", controller.QueryOrderUser)
	routers.GET("/order/category", controller.QueryOrderCategory)
	routers.GET("/order/info/:id", controller.QueryOrder)

	routers.Use(controller.JwtMiddleware.MiddlewareFunc())
	routers.Use(middleware.UserInfoCheck)
	{
		routers.POST("/signout", controller.JwtMiddleware.LogoutHandler)
		// one device login verification middleware
		routers.Use(middleware.AbnLoginCheck)
		routers.POST("/upload/avatar", controller.UploadAvatarEndpoint)
		routers.GET("/user", controller.GetUserEndpoint)
		routers.POST("/user/profile", controller.ChangeProfile)
		routers.POST("/user/submitMsg", controller.FeedbackMessage)

		routers.POST("/update/password", controller.UpdatePassword)
		routers.POST("/update/email", controller.UpdateEmail)
		routers.POST("/update/phone", controller.UpdatePhone)

		routers.POST("/conference/create", controller.CreateConference)
		routers.GET("/conference/history", controller.InfoConference)
		// routers.POST("/conference/query", controller.QueryHistory)
		routers.POST("/conference/updating", controller.SetConference)
		routers.GET("/storage/token", controller.BucketAccessToken)

	}
}
