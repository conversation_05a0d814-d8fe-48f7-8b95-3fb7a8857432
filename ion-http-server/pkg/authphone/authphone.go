package authphone

import (
	"time"

	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
)

var (
	MAX_COUNT = maxError
	MAX_TIME  = (int64)(expired * 60)
	FREQUENCY = (int64)(frequency * 60)
)

const NUMBER_LENGTH = 6

// Send Phone Status
const (
	SEND_CODE_SUCESSFULLY = 0
	SEND_TOO_MUCH_FAILURE = 1
	SEND_PHONE_FREQUENTLY = 2
	FAILED_TO_SEND        = 3
)

// Check user auth code table
const (
	NO_AUTHCODE                = 0
	TOO_MUCH_FAILURE           = 1
	INVALID_AUTHCODE           = 2
	VERIFY_CODE_SUCCESSFULLY   = 3
	VERIFY_CODE_UNSUCCESSFULLY = 4
)

// getCurrTime get current time
func getCurrTime() time.Time {
	return time.Now()
}

// makeUserRecord make a user auth code record
func makeUserRecord(phone string, authcode string, count int) *entity.AuthPhone {
	au := &entity.AuthPhone{
		Phone:    phone,
		AuthCode: authcode,
		Count:    count,
	}
	return au
}

// CheckPhoneCode check user auth code table
func CheckPhoneCode(phone string, remotecode string) int {
	// encrypt phone
	encryptPhone := util.EncryptString(phone)
	au := entity.QueryPhone(&entity.AuthPhone{Phone: encryptPhone})

	if *au == (entity.AuthPhone{}) {
		log.Debugf("Please request your auth code ")
		return NO_AUTHCODE
	} else {
		checkDay(au)
		count := au.Count
		if count >= MAX_COUNT {
			log.Debugf("it has been failed ten times")
			return TOO_MUCH_FAILURE
		} else {
			cur := getCurrTime()
			start := au.UpdatedAt
			dist := cur.Unix() - start.Unix()
			if dist > MAX_TIME {
				log.Debugf("auth code have been invalid")
				return INVALID_AUTHCODE
			} else {
				authcode := au.AuthCode
				if authcode == remotecode {
					log.Debugf("verify Successfully")
					au.AuthCode = util.RandStringBytesRmndr(NUMBER_LENGTH)
					au.UpdatePcode(*au)
					return VERIFY_CODE_SUCCESSFULLY
				} else {
					log.Debugf("verify Unsuccessfully")
					au.Count++
					au.UpdatePcode(*au)
					return VERIFY_CODE_UNSUCCESSFULLY
				}
			}
		}
	}
}

// SendAuthCode client request auth code
func SendAuthCode(phone string) (int, int) {
	// encrypt phone
	encryptPhone := util.EncryptString(phone)
	au := entity.QueryPhone(&entity.AuthPhone{Phone: encryptPhone})
	var authcode string
	var err error
	if *au == (entity.AuthPhone{}) {
		authcode, err = sendPhone(phone)
		if err != nil {
			log.Infof("SendAuthCode:%v", err)
			return FAILED_TO_SEND, 0
		}
		au = makeUserRecord(encryptPhone, authcode, 0)
		au.SavePcode()
		return SEND_CODE_SUCESSFULLY, 0
	} else {
		checkDay(au)
		count := au.Count
		if count >= MAX_COUNT {
			// it has been failed ten times
			return SEND_TOO_MUCH_FAILURE, 0
		} else {
			cur := getCurrTime()
			start := au.UpdatedAt
			dist := cur.Unix() - start.Unix()
			if dist > 0 && dist < FREQUENCY {
				// frequently request!
				return SEND_PHONE_FREQUENTLY, int(FREQUENCY - dist)
			} else {
				authcode, err = sendPhone(phone)
				if err != nil {
					log.Infof("SendAuthCode:%v", err)
					return FAILED_TO_SEND, 0
				}
				au.AuthCode = authcode
				au.UpdatePcode(*au)
				return SEND_CODE_SUCESSFULLY, 0
			}
		}
	}
}

func checkDay(au *entity.AuthPhone) {
	log.Debugf("check day")
	if au == nil {
		return
	}
	cur := getCurrTime()
	start := au.UpdatedAt
	dist := cur.YearDay() - start.YearDay()
	if dist > 0 {
		log.Debugf("more than one day. updated count")
		au.Count = 0
		au.UpdatePcode(*au)
	}
}
