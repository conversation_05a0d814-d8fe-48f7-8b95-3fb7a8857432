package authphone

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"

	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
)

const TMP = `{"recipient":"%s", "clientId" : "%s", "clientSecret":"%s"}`

var (
	CLIENT_ID     = conf.Phone.ClientId
	CLIENT_SECRET = conf.Phone.ClientSecret
	expired       = conf.Phone.Expired
	frequency     = conf.Phone.Frequence
	maxError      = conf.Phone.MaxError
	// CLIENT_ID     = "2c3a39df7f5a4ac388f16010e1ce1c2d"
	// CLIENT_SECRET = "57cee13ddaa644b89ab7191de9ba6407"
)

type remoteMsg struct {
	Authcode     string `json:"otp"`
	ExpireMin    int    `json:"expireMin"`
	MaxFailCount int    `json:"maxFailCount"`
	Length       int    `json:"length"`
	ServiceType  string `json:"type"`
	ErrMsg       string `json:"error"`
}

func sendPhone(number string) (string, error) {
	var temp = fmt.Sprintf(TMP, number, CLIENT_ID, CLIENT_SECRET)
	req := bytes.NewBuffer([]byte(temp))
	resp, err := http.Post("https://www.onlyid.net/api/open/send-otp", "application/json", req)
	if err != nil {
		log.Infof("request remote short message server : %v", err)
	}
	body, _ := ioutil.ReadAll(resp.Body)
	var m remoteMsg
	json.Unmarshal(body, &m)
	if m.ErrMsg != "" {
		// log.Debugf(m.ErrMsg)
		fmt.Println("sendPhone:", m.ErrMsg)
		return "", errors.New(m.ErrMsg)
	}
	return m.Authcode, nil
}
