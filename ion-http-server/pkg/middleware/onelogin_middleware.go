package middleware

import (
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
)

func AbnLoginCheck(c *gin.Context) {
	claims := jwt.ExtractClaims(c)
	cookie := &proto.Cookie{}
	mapstructure.Decode(claims, cookie)
	if cookie.Uid == "" || cookie.DeviceHash == "" {
		c.AbortWithStatusJSON(400, gin.H{"message": "Invalid cookie"})
		return
	}

	uid, err := entity.DecodeUser(cookie.Uid)
	if err != nil {
		c.AbortWithStatusJSON(400, gin.H{"message": "Internal cookie"})
		return
	}

	device := entity.QueryDevice(&entity.Device{
		Userid: uid,
	})
	if device.Hash == "" || device.Hash != cookie.DeviceHash {
		c.AbortWithStatusJSON(401, gin.H{"message": "Accout has login from another device."})
		return
	}
	// Pass on to the next-in-chain
	c.Set("device", device)
	c.Next()
}
