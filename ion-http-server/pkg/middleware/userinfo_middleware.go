package middleware

import (
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
)

func UserInfoCheck(c *gin.Context) {
	claims := jwt.ExtractClaims(c)
	cookie := &proto.Cookie{}
	mapstructure.Decode(claims, cookie)
	if cookie.Uid == "" {
		c.AbortWithStatusJSON(400, gin.H{"message": "Invalid cookie"})
		return
	}
	uid, err := entity.DecodeUser(cookie.Uid)
	if err != nil {
		c.AbortWithStatusJSON(400, gin.H{"message": "Internal cookie"})
		return
	}

	user := entity.QueryUser(&entity.User{Uid: uid})

	if user.Uid == util.ZeroUid {
		c.AbortWithStatusJSON(404, gin.H{"message": "this user is not exist."})
		return
	}
	// Pass on to the next-in-chain
	c.Set("user", user)
	c.Next()
}
