package middleware

import (
	"time"
	"github.com/Young666666Jian/ion-http-server/pkg/middleware/controller"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
)

var (
	IdentityKey = "id"
)

func GetAuthMeddleware(secretKey string) (*jwt.GinJWTMiddleware, error) {
	return jwt.New(&jwt.GinJWTMiddleware{
		Realm:       "\"ion zone\"",
		Key:         []byte(secretKey),
		Timeout:     time.Hour * 24,
		MaxRefresh:  time.Hour * 24 * 12,
		IdentityKey: IdentityKey,
		PayloadFunc: func(data interface{}) jwt.MapClaims {
			if v, ok := data.(*proto.Cookie); ok {
				return jwt.MapClaims{
					IdentityKey:  v.Uid,
					"uid":        v.Uid,
					"username":   v.<PERSON>,
					"email":      v.<PERSON><PERSON>,
					"phone":      v.<PERSON>,
					"avatarUrl":  v.AvatarUrl,
					"deviceHash": v.<PERSON>,
					"issuedTime": v.IssuedTime,
				}
			}
			return jwt.MapClaims{}
		},
		IdentityHandler: func(c *gin.Context) interface{} {
			claims := jwt.ExtractClaims(c)
			return &proto.Cookie{
				Uid: claims[IdentityKey].(string),
			}
		},
		Authenticator: controller.Authenticator,
		Authorizator: func(data interface{}, c *gin.Context) bool {
			return true
		},
		Unauthorized: func(c *gin.Context, code int, message string) {
			c.JSON(code, gin.H{
				"code":    code,
				"message": message,
			})
		},
		// TokenLookup is a string in the form of "<source>:<name>" that is used
		// to extract token from the request.
		// Optional. Default value "header:Authorization".
		// Possible values:
		// - "header:<name>"
		// - "query:<name>"
		// - "cookie:<name>"
		// - "param:<name>"
		TokenLookup: "header: Authorization, query: token, cookie: jwt",
		// TokenLookup: "query:token",
		// TokenLookup: "cookie:token",

		// TokenHeadName is a string in the header. Default value is "Bearer"
		TokenHeadName: "Bearer",

		// TimeFunc provides the current time. You can override it to use another time value. This is useful for testing or if your server uses a different time zone than your tokens.
		TimeFunc:       time.Now,
		SendCookie:     true,
		CookieHTTPOnly: true,
	})

}
