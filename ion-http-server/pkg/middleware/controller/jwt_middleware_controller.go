package controller

import (
	"errors"

	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

func Authenticator(c *gin.Context) (interface{}, error) {
	loginCmd := &proto.LoginCommand{}
	c.ShouldBindBody<PERSON>ith(loginCmd, binding.JSON)
	// not null
	if (loginCmd.Email == "" && loginCmd.Phone == "") || loginCmd.Password == "" {
		return nil, errors.New("email or phone and password are required.")
	}
	if loginCmd.Device == "" || loginCmd.Platform == "" {
		return nil, errors.New("Miss device info.")
	}
	var user *entity.User
	// logining with email account
	if loginCmd.Email != "" {
		user = entity.QueryUser(&entity.User{Email: util.EncryptString(loginCmd.Email)})
		// check if the email account exist
		if user.Email == "" {
			return nil, errors.New("this email account do not exist.")
		}
	} else {
		// logining with phone account
		user = entity.QueryUser(&entity.User{Phone: util.EncryptString(loginCmd.Phone)})
		// check if the email account exist
		if user.Phone == "" {
			return nil, errors.New("this phone account do not exist.")
		}
		if user.Password == "" {
			return nil, errors.New("this phone account do not have the password.")
		}
	}
	// check password
	pwdHash := user.Password
	salt := user.Salt
	if !util.CheckPasswordHash(loginCmd.Password, salt, pwdHash) {
		return nil, errors.New("wrong password.")
	}
	// send jwt and set cookies
	user.Password = ""
	user.Salt = ""
	clientIpCipher := util.EncryptString(c.ClientIP())
	dev := &entity.Device{
		Deviceid: loginCmd.Device,
		Ip:       clientIpCipher,
		Platform: util.PlatformFromUA(loginCmd.Platform),
		Userid:   user.Uid,
	}
	err := dev.InsertOrUpdate()
	if err != nil {
		return nil, errors.New("fail to update device info.")
	}
	cookie := entity.GenerateCookie(user, dev)
	//insert into device table
	return cookie, nil
}
