package proto

import (
	"encoding/json"
	"errors"
	"time"
)

// TopicCat is an enum of topic categories.
type TopicCat int
type AccessMode uint

const MaxImgSize = 1000 * 1000

// Various access mode constants
const (
	ModeJoin       AccessMode = 1 << iota // user can join, i.e. {sub} (J:1)
	ModeRead                              // user can receive broadcasts ({data}, {info}) (R:2)
	ModeWrite                             // user can Write, i.e. {pub} (W:4)
	ModePres                              // user can receive presence updates (P:8)
	ModeApprove                           // user can approve new members or evict existing members (A:0x10, 16)
	ModeShare                             // user can invite new members (S:0x20, 32)
	ModeDelete                            // user can hard-delete messages (D:0x40, 64)
	ModeOwner                             // user is the owner (O:0x80, 128) - full access
	ModeUnset                             // Non-zero value to indicate unknown or undefined mode (:0x100, 256),
	ModeFreeChat                          // user can be added to chat without friend request accepted
	ModeGlobalRead                        // new user has permission to read messages of group and conference topic before he or she join in
	ModeMeeting                           // new user has permission to create conference
	// to make it different from ModeNone

	ModeNone AccessMode = 0 // No access, requests to gain access are processed normally (N:0)

	// Normal user's access to a topic ("JRWPS", 47, 0x2F)
	ModeCPublic AccessMode = ModeJoin | ModeRead | ModeWrite | ModePres | ModeShare
	// User's subscription to 'me' and 'fnd' ("JPS", 41, 0x29)
	ModeCSelf AccessMode = ModeJoin | ModePres | ModeShare
	// Owner's subscription to a generic topic ("JRWPASDO", 255, 0xFF)
	ModeCFull AccessMode = ModeJoin | ModeRead | ModeWrite | ModePres | ModeApprove | ModeShare | ModeDelete | ModeOwner
	// Default P2P access mode ("JRWPA", 31, 0x1F)
	ModeCP2P AccessMode = ModeJoin | ModeRead | ModeWrite | ModePres | ModeApprove
	// Default Auth access mode for a user ("JRWPAS", 63, 0x3F).
	ModeCAuth AccessMode = ModeCP2P | ModeCPublic
	// Default Auth access mode for a vip user ("JRWPASM").
	ModeCVipAuth AccessMode = ModeCP2P | ModeCPublic | ModeMeeting
	// Read-only access to topic ("JR", 3)
	ModeCReadOnly = ModeJoin | ModeRead
	// Access to 'sys' topic by a root user ("JRWPD", 79, 0x4F)
	ModeCSys = ModeJoin | ModeRead | ModeWrite | ModePres | ModeDelete

	// Admin: user who can modify access mode ("OA", dec: 144, hex: 0x90)
	ModeCAdmin = ModeOwner | ModeApprove
	// Sharer: flags which define user who can be notified of access mode changes ("OAS", dec: 176, hex: 0xB0)
	ModeCSharer = ModeCAdmin | ModeShare

	ModeBlock = ModeJoin | ModeRead | ModePres | ModeApprove

	// Invalid mode to indicate an error
	ModeInvalid AccessMode = 0x100000

	// All possible valid bits (excluding ModeInvalid and ModeUnset) = 0xFF, 255
	ModeBitmask AccessMode = ModeJoin | ModeRead | ModeWrite | ModePres | ModeApprove | ModeShare | ModeDelete | ModeOwner | ModeFreeChat | ModeGlobalRead | ModeMeeting
)

// MarshalText converts AccessMode to ASCII byte slice.
func (m AccessMode) MarshalText() ([]byte, error) {
	if m == ModeNone {
		return []byte{'N'}, nil
	}

	if m == ModeInvalid {
		return nil, errors.New("AccessMode invalid")
	}

	var res = []byte{}
	var modes = []byte{'J', 'R', 'W', 'P', 'A', 'S', 'D', 'O', 'U', 'F', 'G', 'M'}
	for i, chr := range modes {
		if (m & (1 << uint(i))) != 0 {
			res = append(res, chr)
		}
	}
	return res, nil
}

func ParseAcs(b []byte) (AccessMode, error) {
	m0 := ModeUnset

Loop:
	for i := 0; i < len(b); i++ {
		switch b[i] {
		case 'J', 'j':
			m0 |= ModeJoin
		case 'R', 'r':
			m0 |= ModeRead
		case 'W', 'w':
			m0 |= ModeWrite
		case 'A', 'a':
			m0 |= ModeApprove
		case 'S', 's':
			m0 |= ModeShare
		case 'D', 'd':
			m0 |= ModeDelete
		case 'P', 'p':
			m0 |= ModePres
		case 'O', 'o':
			m0 |= ModeOwner
		case 'U', 'u':
			m0 |= ModeUnset
		case 'F', 'f':
			m0 |= ModeFreeChat
		case 'G', 'g':
			m0 |= ModeGlobalRead
		case 'M', 'm':
			m0 |= ModeMeeting
		case 'N', 'n':
			m0 = ModeNone // N means explicitly no access, all bits cleared
			break Loop
		default:
			return ModeUnset, errors.New("AccessMode: invalid character '" + string(b[i]) + "'")
		}
	}

	return m0, nil
}

// UnmarshalText parses access mode string as byte slice.
// Does not change the mode if the string is empty or invalid.
func (m *AccessMode) UnmarshalText(b []byte) error {
	m0, err := ParseAcs(b)
	if err != nil {
		return err
	}

	if m0 != ModeUnset {
		*m = (m0 & ModeBitmask)
	}
	return nil
}

// String returns string representation of AccessMode.
func (m AccessMode) String() string {
	res, err := m.MarshalText()
	if err != nil {
		return ""
	}
	return string(res)
}

// BetterThan checks if grant mode allows more permissions than requested in want mode.
func (grant AccessMode) BetterThan(want AccessMode) bool {
	return ModeBitmask&grant&^want != 0
}

// BetterEqual checks if grant mode allows all permissions requested in want mode.
func (grant AccessMode) BetterEqual(want AccessMode) bool {
	return ModeBitmask&grant&want == want
}

func (m *AccessMode) Update(want AccessMode, upgrade bool) {
	if m.BetterEqual(want) {
		return
	}
	val := *m
	if upgrade {
		*m = val | want
	} else {
		*m = val &^ want
	}
}

// DefaultAccess is a per-topic default access modes
type DefaultAccess struct {
	Auth AccessMode `json:"Auth"`
	Anon AccessMode `json:"Anon"`
}

// Scan is an implementation of Scanner interface so the value can be read from SQL DBs
// It assumes the value is serialized and stored as JSON
func (da *DefaultAccess) Scan(val interface{}) error {
	return json.Unmarshal(val.([]byte), da)
}

// Value implements sql's driver.Valuer interface.
func (da DefaultAccess) Value() ([]byte, error) {
	return json.Marshal(da)
}

const (
	// TopicCatMe is a value denoting 'me' topic.
	TopicCatMe TopicCat = iota
	// TopicCatFnd is a value denoting 'fnd' topic.
	TopicCatFnd
	// TopicCatP2P is a a value denoting 'p2p topic.
	TopicCatP2P
	// TopicCatGrp is a a value denoting group topic.
	TopicCatGrp
	// TopicCatSys is a constant indicating a system topic.
	TopicCatSys
)

const (
	FileTemperal = iota
	FilePermanent
)

// Media handling constants
const (
	// UploadStarted indicates that the upload has started but not finished yet.
	UploadStarted = iota
	// UploadCompleted indicates that the upload has completed successfully.
	UploadCompleted
	// UploadFailed indicates that the upload has failed.
	UploadFailed
)

type OrderStatus int

const (
	OrderPending OrderStatus = iota
	OrderPaid
	OrderExpired
	OrderFailed
)

func (os OrderStatus) MarshalText() string {
	return [...]string{"OrderPending", "OrderPaid", "OrderExpired", "OrderFailed"}[os]
}

type MessageType int

const (
	MessageInvalid MessageType = iota - 1
	MessagePlainText
	MessageFmtText
	MessageLink
	MessageImage
	MessageGenericAttachment
	MessageForm
	MessageVoice
	MessageVideo
	MessageVoiceCall
	MessageVideoCall
)

// FeedackMessage support user reports app's problem and some suggestions.
type FeedackMessage struct {
	Reports  string `json:"reports"`
	Feedback string `json:"feedback"`
}

type UserDevice struct {
	Device   string `json:"device"`
	Platform string `json:"platform"`
}

// UpdateConference update conference status
type UpdateConference struct {
	// type :
	// 1. scheduling :UpdateScheduling update scheduling conference setting
	// 2. creating : UpdatePmiSetting update pmi setting
	// 3. cancel: DelConference delete confernece by conference id or conference number
	Type          string `json:"type"`
	ConferenceId  string `json:"conferenceid"`
	Number        string `json:"number"`
	Password      string `json:"password"`
	OnEarlyJoin   bool   `json:"onEarlyJoin"`
	OnWaitingRoom bool   `json:"onWaitingRoom"`
	Subject       string `json:"subject"`
	StartedAt     int64  `json"startedAt"`
	EndAt         int64  `json:"endAt"`
}

type LoginCommand struct {
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	Username string `json:"username"`
	Password string `json:"password"`
	UserDevice
}

type SignupCommand struct {
	Phone    string `json:"phone"`
	Email    string `json:"email"`
	AuthCode string `json:"authcode"`
	Password string `json:"password"`
	Username string `json:"username"`
	UserDevice
}

type WechatSignup struct {
	Code string `json:"code"`
	UserDevice
}

type QQSignup struct {
	AccessToken string `json:"accessToken"`
	Openid      string `json:"openid"`
	UserDevice
}

type TopupParam struct {
	User     string `json:"user"`
	Duration int    `json:"duration"`
}

type WechatPayNotifyParam struct {
	ID           string                  `json:"id"`
	CreateTime   string                  `json:"create_time"`
	EventType    string                  `json:"event_type"`
	ResourceType string                  `json:"resource_type"`
	Resource     WechatPayNotifyResource `json:"resource"`
	Summary      string                  `json:"summary"`
}

type WechatPayNotifyResource struct {
	Algorithm      string `json:"algorithm"`
	Ciphertext     string `json:"ciphertext"`
	AssociatedData string `json:"associated_data"`
	OriginalType   string `json:"original_type"`
	Nonce          string `json:"nonce"`
}

type WechatPayNotifyInfo struct {
	AppID           string             `json:"appid"`
	MchID           string             `json:"mchid"`
	OutTradeNo      string             `json:"out_trade_no"`
	TransactionID   string             `json:"transaction_id"`
	TradeType       string             `json:"trade_type"`
	TradeState      string             `json:"trade_state"`
	TradeStateDesc  string             `json:"trade_state_desc"`
	BankType        string             `json:"bank_type"`
	Attach          string             `json:"attach"`
	SuccessTime     string             `json:"success_time"`
	Payer           WechatPayer        `json:"payer"`
	Amount          WechatPayAmount    `json:"amount"`
	SceneInfo       WechatPaySceneInfo `json:"scene_info"`
	PromotionDetail interface{}        `json:"promotion_detail"`
}

type WechatPayer struct {
	OpenID string `json:"openid"`
}

type WechatPayAmount struct {
	Total         int    `json:"total"`
	PayerTotal    int    `json:"payer_total"`
	Currency      string `json:"currency"`
	PayerCurrency string `json:"payer_currency"`
}

type WechatPaySceneInfo struct {
	DeviceID string `json:"device_id"`
}

type OrderCreateResponse struct {
	CodeUrl string `json:"code_url"`
	OrderID string `json:"orderId"`
}

type Cookie struct {
	Uid        string
	Username   string
	Email      string
	Phone      string
	AvatarUrl  string
	DeviceHash string
	IssuedTime string
}

type MessageQuery struct {
	User        string
	Topic       string
	Seqid       int
	CreatedAt   time.Time
	ContentType int
	Limit       int
	Offset      int
}

type UserQuery struct {
	Uid   string
	Phone string
	Email string
}

type Message struct {
	User        string
	Topic       string
	Seqid       int
	Content     interface{}
	ContentType int
	Description interface{}
	CreatedAt   time.Time
}

// for indexing conference history record
type IndexHistory struct {
	Index int `form:"index"`
	Limit int `form:"limit"`
}

type CensorResult struct {
	Type           int `json:"type,omitempty"`
	Subtype        int `json:"subtype,omitempty"`
	ConclusionType int `json:"conclusionType,omitempty"`
}

// ObjState represents information on objects state,
// such as an indication that User or Topic is suspended/soft-deleted.
type ObjState int

const (
	// StateOK indicates normal user or topic.
	StateOK ObjState = 0
	// StateSuspended indicates suspended user or topic.
	StateSuspended ObjState = 10
	// StateDeleted indicates soft-deleted user or topic.
	StateDeleted ObjState = 20
	// StateUndefined indicates state which has not been set explicitly.
	StateUndefined ObjState = 30
)

type UserType int

const (
	UserNormal UserType = iota
	UserVip
	UserBlocked
)

func (us UserType) MarshalText() string {
	return [...]string{"normal", "vip", "blocked"}[us]
}

type PaymentMethod int

const (
	WechatPay PaymentMethod = iota
	AliPay
	ApplePay
)

type ServiceType int

const (
	ServiceVip ServiceType = iota
)
