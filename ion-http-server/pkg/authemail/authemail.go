package authemail

import (
	"time"

	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
)

var (
	MAX_COUNT = maxError
	MAX_TIME  = (int64)(expired * 60)
	FREQUENCY = (int64)(frequency * 60)
)

const NUMLEN = 6

// Send Email Status
const (
	SEND_CODE_SUCESSFULLY = 0
	SEND_TOO_MUCH_FAILURE = 1
	SEND_EMAIL_FREQUENTLY = 2
	FAILED_TO_SEND        = 3
)

// Check user auth code table
const (
	NO_AUTHCODE                = 0
	TOO_MUCH_FAILURE           = 1
	INVALID_AUTHCODE           = 2
	VERIFY_CODE_SUCCESSFULLY   = 3
	VERIFY_CODE_UNSUCCESSFULLY = 4
)

// getCurrTime get current time
func getCurrTime() time.Time {
	return time.Now()
}

// makeUserRecord make a user auth code record
func makeUserRecord(email string, authcode string, count int) *entity.AuthEmail {
	au := &entity.AuthEmail{
		Email:    email,
		AuthCode: authcode,
		Count:    count,
	}
	return au
}

// CheckEmailCode check user auth code table
func CheckEmailCode(email string, remotecode string) int {
	// encrypt email
	encryptEmail := util.EncryptString(email)
	au := entity.QueryEmail(&entity.AuthEmail{Email: encryptEmail})

	if *au == (entity.AuthEmail{}) {
		log.Debugf("Please request your auth code ")
		return NO_AUTHCODE
	} else {
		checkDay(au)
		count := au.Count
		if count >= MAX_COUNT {
			log.Debugf("it has been failed five times")
			return TOO_MUCH_FAILURE
		} else {
			cur := getCurrTime()
			start := au.UpdatedAt
			dist := cur.Unix() - start.Unix()
			if dist > MAX_TIME {
				log.Debugf("auth code have been invalid")
				return INVALID_AUTHCODE
			} else {
				authcode := au.AuthCode
				if authcode == remotecode {
					log.Debugf("verify Successfully")
					au.AuthCode = util.RandStringBytesRmndr(NUMLEN)
					au.UpdateEcode(*au)
					return VERIFY_CODE_SUCCESSFULLY
				} else {
					log.Debugf("verify Unsuccessfully")
					au.Count++
					au.UpdateEcode(*au)
					return VERIFY_CODE_UNSUCCESSFULLY
				}
			}
		}
	}
}

// SendAuthCode client request auth code
func SendAuthCode(email string) (int, int) {
	// encrypt email
	encryptEmail := util.EncryptString(email)
	au := entity.QueryEmail(&entity.AuthEmail{Email: encryptEmail})
	authcode := util.RandStringBytesRmndr(NUMLEN)
	if *au == (entity.AuthEmail{}) {
		au = makeUserRecord(encryptEmail, authcode, 0)
		if Eerr := au.SaveEcode(); Eerr != nil {
			log.Infof("save email code : %v", Eerr)
		}
		go SendEmail(email, authcode)
		log.Debugf("emali: %v authcode : %v", email, authcode)
		return SEND_CODE_SUCESSFULLY, 0
	} else {
		checkDay(au)
		count := au.Count
		if count >= MAX_COUNT {
			return SEND_TOO_MUCH_FAILURE, 0
		} else {
			cur := getCurrTime()
			start := au.UpdatedAt
			dist := cur.Unix() - start.Unix()
			if dist > 0 && dist < FREQUENCY {
				return SEND_EMAIL_FREQUENTLY, int(FREQUENCY - dist)
			} else {
				au.AuthCode = authcode
				au.UpdateEcode(*au)
				go SendEmail(email, authcode)
				log.Debugf("emali: %v authcode : %v", email, authcode)
				return SEND_CODE_SUCESSFULLY, 0
			}
		}
	}
}

func checkDay(au *entity.AuthEmail) {
	log.Debugf("check day")
	if au == nil {
		return
	}
	cur := getCurrTime()
	start := au.UpdatedAt
	dist := cur.YearDay() - start.YearDay()
	if dist > 0 {
		log.Debugf("more than one day. updated count")
		au.Count = 0
		au.UpdateEcode(*au)
	}
}
