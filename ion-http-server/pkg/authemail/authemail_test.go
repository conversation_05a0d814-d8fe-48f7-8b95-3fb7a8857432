package authemail

import (
	"fmt"
	"testing"

	"github.com/Young666666Jian/ion-http-server/pkg/entity"
)

func TestAuthEmail(t *testing.T) {
	var (
		name  = "sqlite3"
		addrs = "./gorm.db"
	)
	entity.Init(name, addrs)
	t.Run("test SendAuthCode", func(t *testing.T) {
		email := "<EMAIL>"
		// authcode := "123456"
		num := SendAuthCode(email)
		switch num {
		case SEND_CODE_SUCESSFULLY:
			fmt.Println(num, "test a day First time send authcode")
			fmt.Println(num, "reponse successful msg to client ")
		case SEND_EMAIL_FREQUENTLY:
			fmt.Println(num, "test frequently request")
			fmt.Println(num, "response send msg frequently to client")
		case SEND_TOO_MUCH_FAILURE:
			fmt.Println(num, "send to much time fail code")
		}
	})

	t.Run("test CheckUserTable", func(t *testing.T) {
		email := "<EMAIL>"
		authcode := "667952"
		num := CheckEmailCode(email, authcode)
		switch num {
		case NO_AUTHCODE:
			fmt.Println(num, "test Please request your auth code")
			fmt.Println(num, "response no auth code to client")
		case TOO_MUCH_FAILURE:
			fmt.Println(num, "test it has been failed five times ")
			fmt.Println(num, "reponse fail number exceeded to client")
		case INVALID_AUTHCODE:
			fmt.Println(num, "test auth code have been invalid ")
			fmt.Println(num, "response auth code has been Expired")
		case VERIFY_CODE_SUCCESSFULLY:
			fmt.Println(num, "test verify Successfully ")
			fmt.Println(num, "response auth code has been verify successfully")
		case VERIFY_CODE_UNSUCCESSFULLY:
			fmt.Println(num, "test verify Unsuccessfully ")
			fmt.Println(num, "response auth code has been verify unsuccessfully")
		}
	})
}
