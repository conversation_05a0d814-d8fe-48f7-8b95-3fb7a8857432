package authemail

import (
	"bytes"
	"crypto/tls"
	"text/template"

	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"gopkg.in/gomail.v2"
)

var (
	TestSender = conf.Email.TestSender
	smtpHost   = conf.Email.SmtpHost
	smtpPort   = conf.Email.SmtpPort
	password   = conf.Email.Epassword
	expired    = conf.Email.Expired
	frequency  = conf.Email.Frequence
	maxError   = conf.Email.MaxError
)

const (
	// TestSender = "<EMAIL>"
	// smtpHost   = "smtp.163.com"
	// smtpPort   = 465
	// password   = "TPTSYAUYBQXKJWGE"
	senderName = "方宇智云"
	subject    = "方宇智云|验证码验证信息"
	// expired    = 5 //minute
	tplFile = "templates/authemail.html"
)

func parseTemplate(tplPath, authcode string) string {
	var em = make(map[string]interface{})
	em["Code"] = authcode
	em["Expired"] = expired
	t, err := template.ParseFiles(tplPath)
	if err != nil {
		log.Infof("Parse tamplate:%v", err)
		return ""
	}
	var tpl bytes.Buffer

	if err := t.Execute(&tpl, em); err != nil {
		log.Infof("Execute tamplate:%v", err)
		return ""
	}
	log.Infof(tpl.String())
	return tpl.String()
}

func sendEmail(recevier, authCode, tplPath string) {
	msg := parseTemplate(tplPath, authCode)
	if msg == "" {
		log.Infof("parse template fail")
		return
	}
	m := gomail.NewMessage()
	m.SetHeader("From", m.FormatAddress(TestSender, senderName))
	m.SetHeader("To", recevier)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", msg)

	d := gomail.NewDialer(smtpHost, smtpPort, TestSender, password)
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		log.Infof("sendemail : %v", err)
	}
}

func SendEmail(recevier, authCode string) {
	sendEmail(recevier, authCode, tplFile)
}
