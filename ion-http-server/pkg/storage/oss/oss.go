package oss

import (
	"errors"
	"fmt"
	sysconf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/storage"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/aliyun/aliyun-sts-go-sdk/sts"
	"github.com/mitchellh/mapstructure"
	"io"
	"mime"
)

const handlerName = "oss"

type aliyunconfig struct {
	StsEnable       bool   `json:"sts_enable" mapstructure:"sts_enable"`
	AccessKeyId     string `json:"access_key_id" mapstructure:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key" mapstructure:"secret_access_key"`
	Region          string `json:"region" mapstructure:"region"`
	Endpoint        string `json:"endpoint"  mapstructure:"endpoint"`
	BucketName      string `json:"bucket" mapstructure:"bucket"`
	AccountID       string `json:"account_id" mapstructure:"account_id"`
	Rolename        string `json:"role_name" mapstructure:"role_name"`
}

type Aliyunhandler struct {
	svc       *oss.Client
	stsclient *sts.Client
	conf      *aliyunconfig
	location  string
}

func (ah *Aliyunhandler) Init(conf map[string]interface{}) error {
	var err error
	ossconfig := &aliyunconfig{}
	mapstructure.Decode(conf, ossconfig)
	ah.conf = ossconfig
	if ah.conf.AccessKeyId == "" {
		return errors.New("missing Access Key ID")
	}
	if ah.conf.SecretAccessKey == "" {
		return errors.New("missing Secret Access Key")
	}
	if ah.conf.Region == "" {
		return errors.New("missing Region")
	}
	if ah.conf.Endpoint == "" {
		return errors.New("missing Endpoint")
	}
	if ah.conf.BucketName == "" {
		return errors.New("missing Bucket")
	}

	ossclient, err := oss.New(ah.conf.Endpoint, ah.conf.AccessKeyId, ah.conf.SecretAccessKey)
	if err != nil {
		return err
	}
	ah.svc = ossclient
	roleArn := fmt.Sprintf("acs:ram::%v:role/%v", ah.conf.AccountID, ah.conf.Rolename)
	ah.stsclient = sts.NewClient(ah.conf.AccessKeyId, ah.conf.SecretAccessKey, roleArn, "fangyu-http-server")

	// // Check if bucket already exists.
	// isExist, err := ah.svc.IsBucketExist(ah.conf.BucketName)
	// if err != nil {
	// 	return err
	// }
	// if !isExist {
	// 	err = ah.svc.CreateBucket(ah.conf.BucketName , oss.ACL(oss.ACLPublicRead))
	// 	if err != nil {
	// 		return err
	// 	}
	// 	rule := oss.CORSRule{
	//         AllowedOrigin: []string{"*"},
	//         AllowedMethod: []string{"GET"},
	//         AllowedHeader: []string{},
	//         ExposeHeader:  []string{},
	//         MaxAgeSeconds: 100,
	//     }
	//     return ah.svc.SetBucketCORS(ah.conf.BucketName, []oss.CORSRule{rule})
	// }
	if sysconf.Global.Mode == "release" {
		ah.location = "prod/"
	} else {
		ah.location = "dev/"
	}
	return nil
}

func (ah *Aliyunhandler) Upload(file io.ReadSeeker, fileInfo *entity.Fileupload) (string, error) {
	//s3 obj key is not case sensitive
	fname := entity.GetUidStr()
	fileInfo.ID, _ = entity.DecodeUid2Int64(fname)
	fileInfo.Status = proto.UploadStarted
	ext, _ := mime.ExtensionsByType(fileInfo.Mimetype)
	if len(ext) > 0 {
		fname += ext[0]
	}
	fileInfo.Location = ah.location + fname
	err := fileInfo.SaveFileUpload()
	if err != nil {
		return "", err
	}
	key := fileInfo.Location
	bucket, err := ah.svc.Bucket(ah.conf.BucketName)
	if err != nil {
		fileInfo.UpdateFileUpload(entity.Fileupload{Status: proto.UploadFailed})
		return "", err
	}
	if ah.conf.StsEnable {
		if signedURL, err := bucket.SignURL(key, oss.HTTPPut, 60); err == nil {
			err = bucket.PutObjectWithURL(signedURL, file)
		}

	} else {
		err = bucket.PutObject(key, file)
	}
	if err != nil {
		fileInfo.UpdateFileUpload(entity.Fileupload{Status: proto.UploadFailed})
		return "", err
	}

	url := fmt.Sprintf("https://%v.oss-%v.aliyuncs.com/%v", ah.conf.BucketName, ah.conf.Region, fileInfo.Location)
	fileInfo.UpdateFileUpload(entity.Fileupload{Status: proto.UploadCompleted})
	return url, nil
}

func (ah *Aliyunhandler) Download(filename string) (*entity.Fileupload, storage.ReadSeekCloser, error) {
	return nil, nil, nil
}

func (ah *Aliyunhandler) Delete(locations []string) error {
	bucket, err := ah.svc.Bucket(ah.conf.BucketName)
	if err != nil {
		return err
	}
	_, err = bucket.DeleteObjects(locations, oss.DeleteObjectsQuiet(true))
	return err
}

func (ah *Aliyunhandler) AccessToken() (map[string]interface{}, error) {
	resp, err := ah.stsclient.AssumeRole(3600)
	if err != nil {
		fmt.Print(err.Error())
		return nil, err
	}
	fmt.Printf("Credentials:\n")
	fmt.Printf("    AccessKeyID:%s\n", resp.Credentials.AccessKeyId)
	fmt.Printf("    AccessKeySecret:%s\n", resp.Credentials.AccessKeySecret)
	fmt.Printf("    SecurityToken:%s\n", resp.Credentials.SecurityToken)
	fmt.Printf("    Expiration:%s\n", resp.Credentials.Expiration)

	stsparams := map[string]interface{}{
		"accessKeyID":     resp.Credentials.AccessKeyId,
		"accessKeySecret": resp.Credentials.AccessKeyId,
		"securityToken":   resp.Credentials.SecurityToken,
		"expiration":      resp.Credentials.Expiration,
		"bucket":          ah.conf.BucketName,
		"endpoint":        ah.conf.Endpoint,
	}
	return stsparams, nil
}

func (ah *Aliyunhandler) GetBaseUrl() string {
	return fmt.Sprintf("https://%v.oss-%v.aliyuncs.com", ah.conf.BucketName, ah.conf.Region)
}

func init() {
	storage.RegisterMediaHandler(handlerName, &Aliyunhandler{})
}
