package fs

import (
	"errors"
	"fmt"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/storage"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/mitchellh/mapstructure"
	"io"
	"mime"
	"os"
	"path/filepath"
)

const handlerName = "fs"

type fsConfig struct {
	FileUploadDirectory string `json:"upload_dir" mapstructure:"upload_dir"`
}

type Fshandler struct {
	fileUploadLocation string
}

func (fh *Fshandler) Init(config map[string]interface{}) error {
	fsconfig := &fsConfig{}
	mapstructure.Decode(config, fsconfig)
	if fsconfig.FileUploadDirectory == "" {
		return errors.New("empty file upload directory.")
	}
	fh.fileUploadLocation = fsconfig.FileUploadDirectory
	return os.MkdirAll(fh.fileUploadLocation, 0777)
}

func (fh *Fshandler) Upload(file io.ReadSeeker, fileInfo *entity.Fileupload) (string, error) {
	fid := entity.GetUid()
	filename := util.String32(fid)
	location := filepath.Join(fh.fileUploadLocation, filename)
	fileInfo.Location = location
	fileInfo.ID = entity.DecodeUid(fid)
	fileInfo.Status = proto.UploadStarted
	outfile, err := os.Create(location)
	if err != nil {
		os.Remove(fileInfo.Location)
		fmt.Println("Upload: failed to create file", location, err)
		return "", err
	}
	err = fileInfo.SaveFileUpload()
	if err != nil {
		os.Remove(fileInfo.Location)
		return "", err
	}
	_, err = io.Copy(outfile, file)
	if err != nil {
		os.Remove(fileInfo.Location)
		fileInfo.UpdateFileUpload(entity.Fileupload{Status: proto.UploadFailed})
		fmt.Println("Upload: failed to copy content to file", location, err)
		return "", err
	}
	outfile.Close()
	fileInfo.UpdateFileUpload(entity.Fileupload{Status: proto.UploadCompleted})
	ext, _ := mime.ExtensionsByType(fileInfo.Mimetype)
	if len(ext) > 0 {
		filename += ext[0]
	}
	return filename, nil
}

// Download processes request for file download.
// The returned ReadSeekCloser must be closed after use.
func (fh *Fshandler) Download(filename string) (*entity.Fileupload, storage.ReadSeekCloser, error) {
	fid, _ := storage.GetFileId(filename)
	if fid == 0 {
		return nil, nil, errors.New("invalid file id")
	}

	fileInfo := entity.QueryFileUpload(&entity.Fileupload{ID: fid})
	if fileInfo.ID == 0 {
		return nil, nil, errors.New("file not found in data base")
	}
	fmt.Println("file location =>", fileInfo.Location)
	file, err := os.Open(fileInfo.Location)
	if err != nil {
		if os.IsNotExist(err) {
			// If the file is not found, send 404 instead of the default 500
			err = errors.New("file not found")
		}
		return nil, nil, err
	}
	return fileInfo, file, nil
}

// Delete deletes files from storage by provided slice of locations.
func (fh *Fshandler) Delete(locations []string) error {
	for _, loc := range locations {
		if err, _ := os.Remove(loc).(*os.PathError); err != nil {
			if err != os.ErrNotExist {
				fmt.Println("fs: error deleting file", loc, err)
			}
		}
	}
	return nil
}

func (fh *Fshandler) AccessToken() (map[string]interface{}, error) {
	return nil, nil
}

func (ah *Fshandler) GetBaseUrl() string {
	return ""
}

func init() {
	storage.RegisterMediaHandler(handlerName, &Fshandler{})
}
