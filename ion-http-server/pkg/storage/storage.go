package storage

import (
	"errors"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"io"
	"strings"
)

var fileHandlers map[string]StorageSystem
var mediaHandler StorageSystem

// ReadSeekCloser must be implemented by the media being downloaded.
type ReadSeekCloser interface {
	io.Reader
	io.Seeker
	io.Closer
}

type StorageSystem interface {
	Init(jsconf map[string]interface{}) error
	Upload(file io.ReadSeeker, fileInfo *entity.Fileupload) (string, error)
	Download(filename string) (*entity.Fileupload, ReadSeekCloser, error)
	Delete(locations []string) error
	AccessToken() (map[string]interface{}, error)
	GetBaseUrl() string
}

// RegisterMediaHandler saves reference to a media handler (file upload-download handler).
func RegisterMediaHandler(name string, ss StorageSystem) {
	if fileHandlers == nil {
		fileHandlers = make(map[string]StorageSystem)
	}

	if ss == nil {
		panic("RegisterMediaHandler: handler is nil")
	}
	if _, dup := fileHandlers[name]; dup {
		panic("RegisterMediaHandler: called twice for handler " + name)
	}
	fileHandlers[name] = ss
}

// UseMediaHandler sets specified media handler as default.
func InitMediaHandler(name string, config map[string]interface{}) error {
	mediaHandler = fileHandlers[name]
	if mediaHandler == nil {
		panic("UseMediaHandler: unknown handler '" + name + "'")
	}
	if hdlConf, ok := config[name].(map[string]interface{}); ok {
		return mediaHandler.Init(hdlConf)
	}
	return errors.New("invalid config")
}

func GetFileId(filename string) (int64, error) {
	parts := strings.Split(filename, ".")
	if len(parts) != 2 {
		return 0, errors.New("invalid file name")
	}
	objKey := parts[0]
	if strings.HasPrefix(parts[0], "prod/") {
		objKey = objKey[5:]
	}
	if strings.HasPrefix(parts[0], "dev/") {
		objKey = objKey[4:]
	}
	return entity.DecodeUid2Int64(objKey)
}

// GetMediaHandler returns default media handler.
func GetMediaHandler() StorageSystem {
	return mediaHandler
}

func IsDeletable(url string) bool {
	if url == "" || strings.HasPrefix(url, "/templates") {
		return false
	}
	baseUrl := mediaHandler.GetBaseUrl()
	return !strings.HasPrefix(url, "http") || strings.HasPrefix(url, baseUrl)
}
