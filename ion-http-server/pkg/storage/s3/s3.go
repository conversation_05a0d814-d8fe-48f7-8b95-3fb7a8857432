package s3

import (
	"errors"
	"fmt"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/storage"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/mitchellh/mapstructure"
	"io"
	"mime"
	"net/http"
	"time"
)

const handlerName = "s3"

type awsconfig struct {
	AccessKeyId     string   `json:"access_key_id" mapstructure:"access_key_id"`
	SecretAccessKey string   `json:"secret_access_key" mapstructure:"secret_access_key"`
	Region          string   `json:"region"  mapstructure:"region"`
	BucketName      string   `json:"bucket" mapstructure:"bucket"`
	CorsOrigins     []string `json:"cors_origins" mapstructure:"cors_origins"`
}

type Awshandler struct {
	svc  *s3.S3
	conf *awsconfig
}

// readerCounter is a byte counter for bytes read through the io.Reader
type readerCounter struct {
	io.Reader
	count  int64
	reader io.Reader
}

// Init initializes the media handler.
func (ah *Awshandler) Init(conf map[string]interface{}) error {
	var err error
	s3config := &awsconfig{}
	mapstructure.Decode(conf, s3config)
	ah.conf = s3config
	if ah.conf.AccessKeyId == "" {
		return errors.New("missing Access Key ID")
	}
	if ah.conf.SecretAccessKey == "" {
		return errors.New("missing Secret Access Key")
	}
	if ah.conf.Region == "" {
		return errors.New("missing Region")
	}
	if ah.conf.BucketName == "" {
		return errors.New("missing Bucket")
	}

	var sess *session.Session
	if sess, err = session.NewSession(&aws.Config{
		Region:      aws.String(ah.conf.Region),
		Credentials: credentials.NewStaticCredentials(ah.conf.AccessKeyId, ah.conf.SecretAccessKey, ""),
	}); err != nil {
		return err
	}

	// Create S3 service client
	ah.svc = s3.New(sess)

	// Check if bucket already exists.
	_, err = ah.svc.HeadBucket(&s3.HeadBucketInput{Bucket: aws.String(ah.conf.BucketName)})
	if err == nil {
		// Bucket exists
		return nil
	}

	if aerr, ok := err.(awserr.Error); !ok || aerr.Code() != s3.ErrCodeNoSuchBucket {
		// Hard error.
		fmt.Printf("%v", aerr.Code())
		return err
	}

	// Bucket does not exist. Create one.
	_, err = ah.svc.CreateBucket(&s3.CreateBucketInput{Bucket: aws.String(ah.conf.BucketName)})
	if err != nil {
		// Check if someone has already created a bucket (possible in a cluster).
		if aerr, ok := err.(awserr.Error); ok {
			if aerr.Code() == s3.ErrCodeBucketAlreadyExists ||
				aerr.Code() == s3.ErrCodeBucketAlreadyOwnedByYou ||
				// Someone is already creating this bucket:
				// OperationAborted: A conflicting conditional operation is currently in progress against this resource.
				aerr.Code() == "OperationAborted" {
				// Clear benign error
				err = nil
			}
		}
	} else {
		// This is a new bucket.
		// The following serves two purposes:
		// 1. Setup CORS policy to be able to serve media directly from S3.
		// 2. Verify that the bucket is accessible to the current user.
		origins := ah.conf.CorsOrigins
		if len(origins) == 0 {
			origins = append(origins, "*")
		}
		_, err = ah.svc.PutBucketCors(&s3.PutBucketCorsInput{
			Bucket: aws.String(ah.conf.BucketName),
			CORSConfiguration: &s3.CORSConfiguration{
				CORSRules: []*s3.CORSRule{{
					AllowedMethods: aws.StringSlice([]string{http.MethodGet, http.MethodHead}),
					AllowedOrigins: aws.StringSlice(origins),
					AllowedHeaders: aws.StringSlice([]string{"*"}),
				}},
			},
		})
	}
	return err
}

func (ah *Awshandler) Upload(file io.ReadSeeker, fileInfo *entity.Fileupload) (string, error) {
	//s3 obj key is not case sensitive
	fname := entity.GetUidStr()
	fileInfo.ID, _ = entity.DecodeUid2Int64(fname)
	fileInfo.Status = proto.UploadStarted
	ext, _ := mime.ExtensionsByType(fileInfo.Mimetype)
	if len(ext) > 0 {
		fname += ext[0]
	}
	fileInfo.Location = fname
	err := fileInfo.SaveFileUpload()
	if err != nil {
		return "", err
	}
	key := fname
	uploader := s3manager.NewUploaderWithClient(ah.svc)
	// rc := readerCounter{reader: file}
	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(ah.conf.BucketName),
		Key:    aws.String(key),
		Body:   file,
	})

	if err != nil {
		fileInfo.UpdateFileUpload(entity.Fileupload{Status: proto.UploadFailed})
		return "", err
	}
	req, _ := ah.svc.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(ah.conf.BucketName),
		Key:    aws.String(key),
	})
	//X-Amz-Expires must be less than a week (in seconds); that is, the given X-Amz-Expires must be less than 604800 seconds
	urlStr, err := req.Presign(600000 * time.Second)

	if err != nil {
		fileInfo.UpdateFileUpload(entity.Fileupload{Status: proto.UploadFailed})
		ah.svc.DeleteObject(&s3.DeleteObjectInput{
			Bucket: aws.String(ah.conf.BucketName),
			Key:    aws.String(key),
		})
		fmt.Printf("Failed to sign request", err)
		return "", err
	}
	fileInfo.UpdateFileUpload(entity.Fileupload{Status: proto.UploadCompleted})
	return urlStr, nil
}

func (ah *Awshandler) Download(filename string) (*entity.Fileupload, storage.ReadSeekCloser, error) {
	return nil, nil, nil
}

func (ah *Awshandler) Delete(locations []string) error {
	toDelete := make([]s3manager.BatchDeleteObject, len(locations))
	for i, key := range locations {
		toDelete[i] = s3manager.BatchDeleteObject{
			Object: &s3.DeleteObjectInput{
				Key:    aws.String(key),
				Bucket: aws.String(ah.conf.BucketName),
			}}
	}
	batcher := s3manager.NewBatchDeleteWithClient(ah.svc)
	return batcher.Delete(aws.BackgroundContext(), &s3manager.DeleteObjectsIterator{
		Objects: toDelete,
	})
}

func (ah *Awshandler) AccessToken() (map[string]interface{}, error) {
	return nil, nil
}

func (ah *Awshandler) GetBaseUrl() string {
	return fmt.Sprintf("https://%v.s3.%v.amazonaws.com", ah.conf.BucketName, ah.conf.Region)
}

func init() {
	storage.RegisterMediaHandler(handlerName, &Awshandler{})
}
