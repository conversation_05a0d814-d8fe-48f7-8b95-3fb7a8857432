package util

import (
	"testing"
)

func TestRandCode(t *testing.T) {
	t.Run("test rand code", func(t *testing.T) {
		s1 := RandStringBytesRmndr(numLen)
		// fmt.Println(s1)
		s2 := RandStringBytesRmndr(numLen)
		// fmt.Println(s2)
		if s1 == s2 {
			t.<PERSON><PERSON><PERSON>("it shoud not be same! s1: %v,s2 : %v", s1, s2)
		}
	})
}

func TestGurrentTime(t *testing.T) {
	t.Run("test current time ", func(t *testing.T) {
		ms := GetCurrentMs()
		t.Error("ms:", ms)
	})
}
