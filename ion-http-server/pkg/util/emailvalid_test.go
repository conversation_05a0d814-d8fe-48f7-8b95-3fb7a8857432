package util

import (
	"testing"
)

var (
	samples = []struct {
		mail    string
		format  bool
		account bool //host+user
	}{
		// {mail: "<EMAIL>", format: true, account: true},
		{mail: "<EMAIL>", format: true, account: true},
		{mail: "<EMAIL>", format: true, account: true},
		{mail: "<EMAIL>", format: true, account: true},
		{mail: "<EMAIL>", format: true, account: false},
		{mail: " <EMAIL>", format: false, account: false},
		{mail: "<EMAIL> ", format: false, account: false},
		{mail: "<EMAIL>", format: true, account: false},
		{mail: "<EMAIL>", format: true, account: false},
		{mail: "@gmail.com", format: false, account: false},
		{mail: "test@<EMAIL>", format: false, account: false},
		{mail: "test <EMAIL>", format: false, account: false},
		{mail: " <EMAIL>", format: false, account: false},
		{mail: "test@wrong domain.com", format: false, account: false},
		{mail: "é&ààà@gmail.com", format: false, account: false},
		{mail: "<EMAIL>", format: true, account: false},
		{mail: "<EMAIL>", format: true, account: false},
	}
)

func TestValidateHost(t *testing.T) {
	for _, s := range samples {
		if !s.format {
			continue
		}
		ok := CheckEmailValidity(s.mail)

		if ok != true && s.account == true {
			t.Errorf(`"%s" => expected :[%v]`, s.mail, true)
		}
		if ok == true && s.account == false {
			t.Errorf(`"%s" => expected :[%v]`, s.mail, false)
		}
	}
}

func TestValidateFormat(t *testing.T) {
	for _, s := range samples {
		ok := CheckEmailFormat(s.mail)
		if ok == false && s.format == true {
			t.Errorf(`"%s" => expected :[%v]`, s.mail, true)
		}
		if ok == true && s.format == false {
			t.Errorf(`"%s" => expected :[%v]`, s.mail, false)
		}
	}
}
