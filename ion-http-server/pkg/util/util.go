package util

import (
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base32"
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/badoux/checkmail"
	"golang.org/x/crypto/bcrypt"
)

// Lengths of various Uid representations
const (
	uidBase64Unpadded        = 11
	p2pBase64Unpadded        = 22
	letterBytes              = "1234567890"
	ZeroUid           int64  = 0
	ZeroUint64Uid     uint64 = 0
	numLen                   = 6
)

func structToStr(input interface{}) (string, error) {
	b, err := json.Marshal(input)
	if err != nil {
		fmt.Println(err)
		return "", err
	}
	return string(b), nil
}
func HashPassword(password, salt string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password+salt), 14)
	return string(bytes), err
}

func CheckPasswordHash(password, salt, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password+salt))
	return err == nil
}

// random numbers seed
func init() {
	rand.Seed(time.Now().UnixNano())
}

// RandStringBytesRmndr generate random numbers
func RandStringBytesRmndr(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[rand.Int63()%int64(len(letterBytes))]
	}
	return string(b)
}

// RandStringBytes generate random numbers
func RandStringBytes(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[rand.Intn(len(letterBytes))]
	}
	return string(b)
}

// CheckEmailFormat check email format
func CheckEmailFormat(email string) bool {
	if email == "" {
		return false
	}
	err := checkmail.ValidateFormat(email)
	return err == nil
}

// CheckEmailValidity check email validity
func CheckEmailValidity(email string) bool {
	if email == "" {
		return false
	}
	err := checkmail.ValidateHost(email)
	return err == nil
}

//VerifyMobileFormat mobile phone number format verify
func VerifyMobileFormat(mobileNum string) bool {
	if mobileNum == "" {
		return false
	}
	regular := "^((13[0-9])|(14[0-9])|(15[0-3,5-9])|(17[0-9])|(18[0-9])|(16[2,5-7])|(19[0-9]))\\d{8}$"

	reg := regexp.MustCompile(regular)
	return reg.MatchString(mobileNum)
}

// Get default modeWant for the given topic category
func GetDefaultAccess(cat proto.TopicCat, authUser bool) proto.AccessMode {
	if !authUser {
		return proto.ModeNone
	}

	switch cat {
	case proto.TopicCatP2P:
		return proto.ModeCP2P
	case proto.TopicCatFnd:
		return proto.ModeNone
	case proto.TopicCatGrp:
		return proto.ModeCPublic
	case proto.TopicCatMe:
		return proto.ModeCSelf
	default:
		panic("Unknown topic category")
	}
}

func PrefixId(prefix, uid string) string {
	return fmt.Sprintf("%s%s", prefix, uid)
}

// MarshalText converts Uid to string represented as byte slice.
func UidMarshalText(uid uint64) ([]byte, error) {
	if uid == ZeroUint64Uid {
		return []byte{}, nil
	}
	src := make([]byte, 8)
	dst := make([]byte, base64.URLEncoding.WithPadding(base64.NoPadding).EncodedLen(8))
	binary.LittleEndian.PutUint64(src, uid)
	base64.URLEncoding.WithPadding(base64.NoPadding).Encode(dst, src)
	return dst, nil
}

// UnmarshalText reads Uid from string represented as byte slice.
func UidUnmarshalText(src []byte) (uint64, error) {
	if len(src) != uidBase64Unpadded {
		return ZeroUint64Uid, errors.New("Uid.UnmarshalText: invalid length")
	}
	dec := make([]byte, base64.URLEncoding.WithPadding(base64.NoPadding).DecodedLen(uidBase64Unpadded))
	count, err := base64.URLEncoding.WithPadding(base64.NoPadding).Decode(dec, src)
	if count < 8 {
		if err != nil {
			return ZeroUint64Uid, errors.New("Uid.UnmarshalText: failed to decode " + err.Error())
		}
		return ZeroUint64Uid, errors.New("Uid.UnmarshalText: failed to decode")
	}
	return binary.LittleEndian.Uint64(dec), nil
}

// convert uint64 to base 32
func String32(uid uint64) string {
	dst := make([]byte, 8)
	binary.LittleEndian.PutUint64(dst, uid)
	return strings.ToLower(base32.StdEncoding.WithPadding(base32.NoPadding).EncodeToString(dst))
}

func ParseUid32(s string) (uint64, error) {
	data, err := base32.StdEncoding.WithPadding(base32.NoPadding).DecodeString(strings.ToUpper(s))
	if err != nil {
		return 0, err
	}
	if len(data) < 8 {
		return 0, errors.New("Uid.UnmarshalBinary: invalid length")
	}
	uid := binary.LittleEndian.Uint64(data)
	return uid, nil
}

// get current timestamp (Millisecond)
func GetCurrentMs() int64 {
	return time.Now().UnixNano() / 1e6
}

// TimeNow returns current wall time in UTC rounded to milliseconds.
func TimeNow() time.Time {
	return time.Now().UTC().Round(time.Millisecond)
}

// delete local file
func DeleteFile(path string) {
	// delete file
	err := os.Remove(path)
	if err != nil {
		log.Infof("delete file %v err ==> %v", path, err.Error())
		return
	}
	fmt.Println("==> done deleting file")
}

// Detect platform from the UserAgent string.
func PlatformFromUA(ua string) string {
	ua = strings.ToLower(ua)
	switch {
	case strings.Contains(ua, "reactnative"):
		switch {
		case strings.Contains(ua, "iphone"),
			strings.Contains(ua, "ipad"):
			return "ios"
		case strings.Contains(ua, "android"):
			return "android"
		}
		return ""
	case strings.Contains(ua, "tinodejs"):
		return "web"
	case strings.Contains(ua, "ios"):
		return "ios"
	case strings.Contains(ua, "tindroid"):
		return "android"
	case strings.Contains(ua, "android"):
		return "android"
	case strings.Contains(ua, "tinodios"):
		return "ios"
	}
	return ""
}

func GenerateHash(method, input string) string {
	switch method {
	case "sha1":
		return sha1Func(input)
	case "md5":
		return md5Func(input)
	case "sha256":
		return sha256Func(input)
	default:
		return ""
	}
}

func sha1Func(input string) string {
	h := sha1.New()
	h.Write([]byte(input))
	out := fmt.Sprintf("%x", h.Sum(nil))
	return out
}

func md5Func(input string) string {
	h := md5.New()
	h.Write([]byte(input))
	out := fmt.Sprintf("%x", h.Sum(nil))
	return out
}

func sha256Func(input string) string {
	h := sha256.New()
	h.Write([]byte(input))
	out := fmt.Sprintf("%x", h.Sum(nil))
	return out
}

func StripQueryString(inputUrl string) string {
	u, err := url.Parse(inputUrl)
	if err != nil {
		return ""
	}
	u.RawQuery = ""
	return u.String()
}
