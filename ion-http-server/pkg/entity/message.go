package entity

import (
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/jinzhu/gorm"
	"gorm.io/datatypes"
)

type Message struct {
	gorm.Model
	Delid      int                `gorm:"type:int"`
	Seqid      int                `gorm:"type:int"`
	Topic      string             `gorm:"type:char(50)"`
	From       int64              `gorm:"type:bigint"`
	Head       datatypes.JSON     `gorm:"type:json"`
	Content    datatypes.JSON     `gorm:"type:json"`
	Reference  int                `gorm:"type:int"`
	Refcontent datatypes.JSON     `gorm:"type:json"`
	Type       *proto.MessageType `gorm:"type:int"`
}

func QueryMessages(query *proto.MessageQuery) ([]Message, error) {
	limit := 10
	if query.Limit > 0 {
		limit = query.Limit
	}
	offset := -1
	if query.Offset > 0 {
		offset = query.Offset
	}

	msgs := []Message{}
	msgquery := &Message{}
	if query.Topic != "" {
		msgquery.Topic = query.Topic
	}

	if query.User != "" {
		msgquery.From, _ = DecodeUser(query.User)
	}
	filterType := proto.MessagePlainText
	msgquery.Type = &filterType
	db := tidb.DB.Limit(limit).Offset(offset).Where(msgquery)

	if query.Seqid != 0 {
		//return new reference of db
		db = db.Where("seqid > ?", query.Seqid)
	}
	db.Find(&msgs)
	return msgs, nil
}

func QueryMessagesBySeq(seqs []int) []Message {
	msgs := []Message{}
	if len(seqs) == 0 {
		return nil
	}
	tidb.DB.Where("seqid IN ?", seqs).Find(&msgs)
	return msgs
}
