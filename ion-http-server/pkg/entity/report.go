package entity

import (
	"errors"

	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

type Report struct {
	gorm.Model
	Uid      int64  `gorm:"type:bigint;not null" mapstructure: "uid"`
	Username string `gorm:"type:varchar(255)" mapstructure: "username"`
	Message  string `gorm:"type:text" mapstructure:"text"`
}

func NewReport(report map[string]interface{}) *Report {
	r := &Report{}
	mapstructure.Decode(report, r)
	return r
}

func (r *Report) DeleteReport() {
	if r != nil && r.Uid != 0 {
		tidb.DB.Unscoped().Delete(r)
	}
}

func (r *Report) SaveReport() error {
	if r.Message == "" && r.Uid == 0 {
		return errors.New("miss primary key for creating feedback")
	}
	tidb.DB.Create(r)
	return nil
}

func (r *Report) UpdateReport(newReport Report) {
	tidb.DB.Model(r).Updates(newReport)
}

func (r *Report) UpdateReportWithMap(newReport map[string]interface{}) {
	tidb.DB.Model(r).Updates(newReport)
}

func AddReport(report map[string]interface{}) error {
	r := NewReport(report)
	err := r.SaveReport()
	return err
}

func QueryReport(report *Report) *Report {
	r := &Report{}
	tidb.DB.Where(r).First(report)
	return r
}
