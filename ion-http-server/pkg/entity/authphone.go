package entity

import (
	"errors"

	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

type AuthPhone struct {
	gorm.Model
	Phone    string `gorm:"unique;not null;type:varchar(100)" mapstructure: "phone"`
	AuthCode string `gorm:"unique;not null;type:varchar(100)" mapstructure: "authcode"`
	Count    int    `gorm:"not null" mapstructure: "count"`
}

func NewPhoneAuth(phoneuser map[string]string) *AuthPhone {
	pauth := &AuthPhone{}
	mapstructure.Decode(phoneuser, pauth)
	return pauth
}

func (pauth *AuthPhone) DeletePcode() {
	tidb.DB.Unscoped().Delete(pauth)
	pauth.Phone = ""
	pauth.AuthCode = ""
}

func (pauth *AuthPhone) SavePcode() error {
	if pauth.Phone == "" && pauth.AuthCode == "" {
		return errors.New("do not save null record")
	}
	tidb.DB.Create(pauth)
	return nil
}

func (pauth *AuthPhone) UpdatePcode(newUserInfo AuthPhone) {
	tidb.DB.Model(pauth).Updates(newUserInfo)
}

func PAuthExist(userInfo *AuthPhone) (bool, error) {
	if userInfo.Phone == "" {
		return false, errors.New("miss unique key to retrieve user info.")
	}
	userList := QueryPhoneList(userInfo)
	return len(userList) == 1, nil
}

func QueryPhoneList(userinfo *AuthPhone) []AuthPhone {
	pusers := []AuthPhone{}
	tidb.DB.Where(userinfo).Find(&pusers)
	return pusers
}

func QueryPhone(userInfo *AuthPhone) *AuthPhone {
	pauth := &AuthPhone{}
	tidb.DB.Where(userInfo).First(pauth)
	return pauth
}
