package entity

import (
	"fmt"
	"testing"
)

func TestPhone(t *testing.T) {
	var (
		name  = "sqlite3"
		addrs = "./gorm.db"
	)
	Init(name, addrs)
	t.Run("test phone auth models", func(t *testing.T) {
		de := AuthPhone{
			Phone:    "<EMAIL>",
			AuthCode: "123456",
			Count:    1,
		}
		au := &de
		// Add
		au.SavePcode()
		got := QueryPhone(&AuthPhone{Phone: "<EMAIL>"})
		if got.AuthCode != de.AuthCode {
			t.Errorf("got:%v, want:%v", got.AuthCode, au.AuthCode)
		}

		// Update
		au.AuthCode = "999999"
		au.UpdatePcode(*au)
		fmt.Println(*au)
		got = QueryPhone(&AuthPhone{Phone: "<EMAIL>"})
		if got.AuthCode != au.AuthCode {
			t.Errorf("got:%v, want:%v", got.AuthCode, au.AuthCode)
		}

		// Delete
		au.DeletePcode()
		if au.Phone != "" || au.AuthCode != "" {
			fmt.Println(au)
			t.Errorf("should nil")
		}
	})

}
