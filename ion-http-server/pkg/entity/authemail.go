package entity

import (
	"errors"

	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

type AuthEmail struct {
	gorm.Model
	Email    string `gorm:"unique;not null;type:varchar(100)" mapstructure: "email"`
	AuthCode string `gorm:"unique;not null;type:varchar(100)" mapstructure: "authcode"`
	Count    int    `gorm:"not null" mapstructure: "count"`
}

func NewEmailAuth(emailuser map[string]string) *AuthEmail {
	eauth := &AuthEmail{}
	mapstructure.Decode(emailuser, eauth)
	return eauth
}

func (eauth *AuthEmail) DeleteEcode() {
	tidb.DB.Unscoped().Delete(eauth)
	eauth.Email = ""
	eauth.AuthCode = ""
}

func (eauth *AuthEmail) SaveEcode() error {
	if eauth.Email == "" && eauth.AuthCode == "" {
		return errors.New("do not save null record")
	}
	tidb.DB.Create(eauth)
	return nil
}

func (eauth *AuthEmail) UpdateEcode(newUserInfo AuthEmail) {
	tidb.DB.Model(eauth).Updates(newUserInfo)
}

func EAuthExist(userInfo *AuthEmail) (bool, error) {
	if userInfo.Email == "" {
		return false, errors.New("miss unique email to retrieve authcode info.")
	}
	userList := QueryEmailList(userInfo)
	return len(userList) == 1, nil
}

func QueryEmailList(userinfo *AuthEmail) []AuthEmail {
	eauths := []AuthEmail{}
	tidb.DB.Where(userinfo).Find(&eauths)
	return eauths
}

func QueryEmail(userInfo *AuthEmail) *AuthEmail {
	eauth := &AuthEmail{}
	tidb.DB.Where(userInfo).First(eauth)
	return eauth
}
