package entity

import (
	"errors"

	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

type Version struct {
	gorm.Model
	ProductionVersion string `gorm:"type:char(10);not null" mapstructure: "production_version"`
	Description       string `gorm:"type:varchar(255);" mapstructure: "descrtption"`
	IonVersion        string `gorm:"type:char(10);not null" mapstructure: "ion"`
	TinodeVersion     string `gorm:"type:char(10);not null" mapstructure: "tinode"`
	UserSystemVersion string `gorm:"type:char(10);not null" mapstructure: "usersystem"`
}

func NewVersion(version map[string]interface{}) *Version {
	v := &Version{}
	mapstructure.Decode(version, v)
	return v
}

func (v *Version) SaveVersion() error {
	if v.ProductionVersion == "" {
		return errors.New("version do not null")
	}
	tidb.DB.Create(v)
	return nil
}

func (v *Version) UpdateVersion(newVersion Version) {
	tidb.DB.Model(v).Updates(newVersion)
}

func (v *Version) UpdateVersionWithMap(newVersion map[string]interface{}) {
	tidb.DB.Model(v).Updates(newVersion)
}

func QueryVersion(version *Version) *Version {
	v := &Version{}
	tidb.DB.Where(version).First(v)
	return v
}

func AddVersion(version *conf.DeploymentVersion) error {
	v := &Version{
		ProductionVersion: version.AppVersion,
		IonVersion:        version.IonVersion,
		TinodeVersion:     version.TinodeVersion,
		UserSystemVersion: version.UserSystemVersion,
	}
	result := QueryVersion(v)
	if result.ProductionVersion != "" {
		return nil
	}
	v.Description = version.Description
	err := v.SaveVersion()
	return err
}

func GetLatestVersion() (string, string) {
	v := &Version{}
	tidb.DB.Last(v)
	return v.ProductionVersion, v.Description
}
