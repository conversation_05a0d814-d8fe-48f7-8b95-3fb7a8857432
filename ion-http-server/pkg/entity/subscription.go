package entity

import (
	"errors"
	"log"

	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

// Subscription to a topic
type Subscription struct {
	gorm.Model
	User   User  `gorm:"foreignkey:Userid;association_foreignkey:Uid"`
	Userid int64 `gorm:"type:bigint" mapstructure:"userid"`
	// Topic subscribed to
	Topic string `gorm:"type:varchar(50)" mapstructure:"topic"`
	// ID of the latest Soft-delete operation
	Delid int `gorm:"default:0" mapstructure:"delId"`
	// Last SeqId reported by user as received by at least one of his sessions
	Recvseqid int `gorm:"default:0" mapstructure:"recvSeqId"`
	// Last SeqID reported read by the user
	Readseqid int `gorm:"default:0" mapstructure:"readSeqId"`
	// Access mode requested by this user
	Modewant string `gorm:"type:varchar(8)" mapstructure:"modeWant"`
	// Access mode granted to this user
	Modegiven string `gorm:"type:varchar(8)" mapstructure:"modeGiven"`
	// Sub's private data associated with the subscription to topic
	Private interface{} `gorm:"type:json" mapstructure:"private"`
}

func NewSub(subInfo map[string]interface{}) *Subscription {
	sub := &Subscription{}
	mapstructure.Decode(subInfo, sub)
	return sub
}

func (sub *Subscription) DeleteSub() error {
	if sub.ID == 0 {
		return errors.New("Miss primary key")
	}
	err := tidb.DB.Unscoped().Delete(sub).Error
	return err
}

func (sub *Subscription) SaveSub() error {
	if sub.Userid == 0 && sub.Topic == "" {
		return errors.New("miss primary key for register subscription")
	}
	tidb.DB.Create(sub)
	return nil
}

func QuerySubscription(subInfo *Subscription) *Subscription {
	sub := &Subscription{}
	err := tidb.DB.Where(subInfo).First(sub).Error
	if err != nil {
		log.Printf("%v", err)
	}
	return sub
}

func (sub *Subscription) UpdateSub(newSubInfo Subscription) {
	tidb.DB.Model(sub).Updates(newSubInfo)
}

func UpdateSubRaw(newSubInfo map[string]interface{}) error {
	sql := "UPDATE subscriptions SET id=id "
	var args []interface{}
	if modegiven, ok := newSubInfo["modegiven"]; ok {
		sql += ",modegiven = ? "
		args = append(args, modegiven)
	}
	if modewant, ok := newSubInfo["modewant"]; ok {
		sql += ",modewant = ? "
		args = append(args, modewant)
	}

	if private, ok := newSubInfo["private"]; ok {
		sql += ",private = ? "
		args = append(args, toJSON(private))
	}
	if _, ok := newSubInfo["deleted_at"]; ok {
		sql += ",deleted_at = NULL "
	}

	now := util.TimeNow()
	args = append(args, now)
	sql += ",updated_at = ? "

	sql += "WHERE 1=1 "
	// add filder
	if ID, ok := newSubInfo["id"]; ok {
		sql += "AND id = ? "
		args = append(args, ID)
	}
	if topic, ok := newSubInfo["topic"]; ok {
		sql += "AND topic = ? "
		args = append(args, topic)
	}
	if userid, ok := newSubInfo["userid"]; ok {
		sql += "AND userid = ? "
		args = append(args, userid)
	}
	log.Println(sql)
	log.Printf("%+v\n", args)
	err := tidb.DB.Exec(sql, args...).Error
	return err
}

func (sub *Subscription) UpdateSubWithMap(newSubInfo map[string]interface{}) {
	tidb.DB.Model(sub).Updates(newSubInfo)
}

func AddSub(subInfo map[string]interface{}) error {
	sub := NewSub(subInfo)
	err := sub.SaveSub()
	return err
}

func AddSubs(subs []map[string]interface{}) error {
	for _, v := range subs {
		sub := NewSub(v)
		err := sub.SaveSub()
		if err != nil {
			log.Printf(err.Error())
			return err
		}
	}
	return nil
}

func AddSubsWithTx(subs []map[string]interface{}, tx *gorm.DB) error {
	for _, v := range subs {
		sub := NewSub(v)
		err := sub.SaveSubWithTx(tx)
		if err != nil {
			log.Printf(err.Error())
			return err
		}
	}
	return nil
}

func (sub *Subscription) SaveSubWithTx(tx *gorm.DB) error {
	if sub.Userid == 0 && sub.Topic == "" {
		return errors.New("miss primary key for register subscription")
	}
	return tx.Create(sub).Error
}
