package entity

import (
	"time"

	"github.com/Young666666Jian/ion-http-server/pkg/log"
	_ "github.com/go-sql-driver/mysql"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	// "gorm.io/gorm"
	// _ "github.com/jinzhu/gorm/dialects/sqlite"
)

type Tidb struct {
	Addr   string
	DBName string
	DB     *gorm.DB
}

func NewTidb(dbName, dbUrl string) (*Tidb, error) {
	tidb, err := gorm.Open(dbName, dbUrl)
	if err != nil {
		log.Infof("open database err %v", err)
		return nil, err
	}
	// SetMaxIdleConns sets the maximum number of connections in the idle connection pool.
	tidb.DB().SetMaxIdleConns(10)

	// SetMaxOpenConns sets the maximum number of open connections to the database.
	tidb.DB().SetMaxOpenConns(10)

	// SetConnMaxLifetime sets the maximum amount of time a connection may be reused.
	tidb.DB().SetConnMaxLifetime(time.Minute * 3)
	db := &Tidb{
		Addr:   dbUrl,
		DBName: dbName,
		DB:     tidb,
	}
	return db, nil
}

func (ti *Tidb) InitTable(table interface{}) {
	var err error
	if !ti.DB.HasTable(table) {
		err = ti.DB.CreateTable(table).Error
	}
	if err != nil {
		panic(err.Error())
	}
}

func (ti *Tidb) Close() {
	ti.DB.Close()
}
