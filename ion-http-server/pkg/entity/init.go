package entity

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"strconv"
	"strings"

	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
)

var (
	//nolint:unused
	tidb       *Tidb
	uidgen     *UidGenerator
	domainName = conf.Domain.Name
)

// Init func
func Init(dbName, dbUrl, uidKey string) {

	log.Infof("init database")
	tidb = InitDatabase(dbName, dbUrl)
	if tidb == nil {
		log.Infof("init database fail ")
		return
	}

	decodedKey, err := base64.StdEncoding.DecodeString(uidKey)
	if err != nil {
		panic("Failed to decode key.")
	}
	uidgen, err = NewUidGenerator(1, []byte(decodedKey))
	if err != nil {
		panic("Failed to initialize uid generator.")
	}
	InitTable()
}

func InitDatabase(dbName, dbUrl string) *Tidb {
	tmptidb, err := NewTidb(dbName, dbUrl)

	if err != nil {
		log.Infof("open database err %v", err)
		panic("Failed to open database")
	}
	return tmptidb
}

func Close() {
	if tidb.DB != nil {
		tidb.Close()
	}
}

func InitTable() {
	tidb.InitTable(&User{})
	tidb.InitTable(&AuthEmail{})
	tidb.InitTable(&AuthPhone{})
	tidb.InitTable(&Subscription{})
	tidb.InitTable(&Conference{})
	tidb.InitTable(&Participator{})
	tidb.InitTable(&Usertag{})
	tidb.InitTable(&PmiSetting{})
	tidb.InitTable(&Device{})
	tidb.InitTable(&Report{})
	tidb.InitTable(&Feedback{})
	tidb.InitTable(&Version{})
	tidb.InitTable(&Order{})
}

func DecodeUid(uid uint64) int64 {
	if uid == 0 {
		return 0
	}
	return uidgen.DecodeUid(uid)
}

func EncodeUid(id int64) uint64 {
	if id == 0 {
		return 0
	}
	return uidgen.EncodeInt64(id)
}

//user id in format of int64 string to uint64 uid
func EncodeUidStr2Uid(uidStr string) (uint64, error) {
	n, err := strconv.ParseInt(uidStr, 10, 64)
	if err != nil {
		log.Infof("parse user id to int64 error ====> %v", err.Error())
		return 0, err
	}
	uid := EncodeUid(n)
	if uid == 0 {
		return 0, errors.New("invalid user id")
	}
	return uid, nil
}

func DecodeUid2UidStr(uid uint64) (string, error) {
	if uid == 0 {
		return "", errors.New("Invalid uid")
	}
	uidInt64 := DecodeUid(uid)
	uidStr := strconv.FormatInt(uidInt64, 10)
	return uidStr, nil
}

// decode user xxxxx ==> 1234567
func DecodeUid2Int64(id string) (int64, error) {
	var name string
	if strings.HasPrefix(id, "usr") {
		name = id[len("usr"):]
	} else {
		name = id
	}
	encodedID, err := util.UidUnmarshalText([]byte(name))
	if err != nil {
		return util.ZeroUid, err
	}
	userId := DecodeUid(encodedID)
	return userId, nil
}

func EncodeUidInt642Base64(id int64) (string, error) {
	uint64 := EncodeUid(id)
	uidBase64, err := util.UidMarshalText(uint64)
	if err != nil {
		return "", nil
	}
	return "usr" + string(uidBase64), nil
}

func EncodeUidInt64Str2Base64(id string) (string, error) {
	int64uid, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		log.Infof("parse user id to int64 error ====> %v", err.Error())
		return "", err
	}
	return EncodeUidInt642Base64(int64uid)
}

func GetUid() uint64 {
	return uidgen.Get()
}

func GetUidStr() string {
	return uidgen.GetStr()
}

func GenerateCookie(user *User, device *Device) *proto.Cookie {
	uidBase64, _ := EncodeUser(user.Uid)
	avatarInfo := user.GetPublicField("avatarUrl")
	var avatarUrl string
	if _, ok := avatarInfo.(string); ok {
		avatarUrl = avatarInfo.(string)
	}

	if !strings.HasPrefix(avatarUrl, "http") {
		avatarUrl = domainName + avatarUrl
	}
	var username string
	if username := user.GetPublicField("fn").(string); username == "" {
		username = uidBase64
	}
	cookie := &proto.Cookie{
		Uid:        uidBase64,
		Username:   username,
		Email:      user.Email,
		Phone:      user.Phone,
		AvatarUrl:  avatarUrl,
		DeviceHash: device.Hash,
		IssuedTime: device.Lastseen.String(),
	}
	return cookie
}

// Convert to JSON before storing to JSON field.
func toJSON(src interface{}) []byte {
	if src == nil {
		return nil
	}

	jval, _ := json.Marshal(src)
	return jval
}

// Deserialize JSON data from DB.
func fromJSON(src interface{}) interface{} {
	if src == nil {
		return nil
	}
	if bb, ok := src.([]byte); ok {
		var out interface{}
		json.Unmarshal(bb, &out)
		return out
	}
	return nil
}

// String converts Uid to base64 string.
func UidString(uid uint64) string {
	buf, _ := util.UidMarshalText(uid)
	return string(buf)
}

// decode user usrxxxxx ==> 1234567
func DecodeUser(userid string) (int64, error) {
	if strings.HasPrefix(userid, "usr") {
		username := userid[len("usr"):]
		encodedID, err := util.UidUnmarshalText([]byte(username))
		if err != nil {
			return util.ZeroUid, err
		}
		userId := DecodeUid(encodedID)
		return userId, nil
	}
	return util.ZeroUid, errors.New("user name does not have prefix 'usr'.")
}

// encode 123456 ===> usrxxxxx
func EncodeUser(uid int64) (string, error) {
	uidUin64 := EncodeUid(uid)
	uidBase64, err := util.UidMarshalText(uidUin64)
	if err != nil {
		return "", err
	}
	return "usr" + string(uidBase64), nil
}

// GetPMI get personal meeting id
func GetPMI() string {
	// how to ensure created conference id(PMI) difference
	conferenceID := util.RandStringBytesRmndr(lenPMI)
	for {
		pmi := QueryUser(&User{PMI: conferenceID})
		if pmi.PMI != "" {
			conferenceID = util.RandStringBytesRmndr(lenPMI)
		} else {
			break
		}
	}
	return conferenceID
}
