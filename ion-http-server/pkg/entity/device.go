package entity

import (
	"errors"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
	"hash/fnv"
	"log"
	"strconv"
	"time"
)

type Device struct {
	gorm.Model
	Userid int64  `gorm:"type:bigint;not null;unique" mapstructure: "userid"`
	Hash   string `gorm:"type:varchar(255);not null;unique" mapstructure: "hash"`
	//use os id
	Deviceid string    `gorm:"type:varchar(255);not null;" mapstructure: "deviceid"`
	Platform string    `gorm:"type:varchar(255);default:null" mapstructure: "platform"`
	Ip       string    `gorm:"type:char(50);default:null" mapstructure: "ip"`
	Lastseen time.Time `gorm:"not null" mapstructure: "lastseen"`
	Lang     string    `gorm:"type:varchar(8);default:null" mapstructure: "lang"`
}

func NewDevice(devInfo map[string]interface{}) *Device {
	dev := &Device{}
	mapstructure.Decode(devInfo, dev)
	return dev
}

func (dev *Device) DeleteDevice() error {
	if dev.ID == 0 {
		return errors.New("Miss primary key")
	}

	err := tidb.DB.Unscoped().Delete(dev).Error
	return err
}

func (dev *Device) SaveDevice() error {
	if dev.Hash == "" || dev.Deviceid == "" {
		return errors.New("miss primary key for register subscription")
	}
	err := tidb.DB.Create(dev).Error
	return err
}

func (dev *Device) UpdateDevice(newDevInfo Device) {
	tidb.DB.Model(dev).Updates(newDevInfo)
}

func (dev *Device) UpdateSubWithMap(newDevInfo map[string]interface{}) {
	tidb.DB.Model(dev).Updates(newDevInfo)
}

func AddDevice(devInfo map[string]interface{}) error {
	dev := NewDevice(devInfo)
	err := dev.SaveDevice()
	return err
}

func AddDevices(devs []map[string]interface{}) error {
	for _, v := range devs {
		dev := NewDevice(v)
		err := dev.SaveDevice()
		if err != nil {
			log.Printf(err.Error())
			return err
		}
	}
	return nil
}

func QueryDevice(devInfo *Device) *Device {
	dev := &Device{}
	tidb.DB.Where(devInfo).First(dev)
	return dev
}

func (dev *Device) InsertOrUpdate() error {
	if dev.Deviceid == "" || dev.Userid == util.ZeroUid || dev.Platform == "" {
		return errors.New("miss parameters")
	}
	var err error
	var uidBase64 string
	if dev.Hash == "" {
		uidBase64, err = EncodeUser(dev.Userid)
		dev.Hash = deviceHasher(dev.Deviceid + uidBase64)
	}
	if err != nil {
		return err
	}
	now := util.TimeNow()
	dev.Lastseen = now
	log.Printf("client ip address %v", dev.Ip)
	sql := `INSERT INTO devices (id, created_at, updated_at, userid, hash, deviceid, platform, ip, lastseen, lang) 
				VALUES(null,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE hash=?, deviceid=?, platform=?, ip=?, lastseen=?, lang=?, updated_at=?`
	err = tidb.DB.Exec(sql, now, now, dev.Userid, dev.Hash, dev.Deviceid, dev.Platform, dev.Ip, now, dev.Lang,
		dev.Hash, dev.Deviceid, dev.Platform, dev.Ip, now, dev.Lang, now).Error
	return err
}

func deviceHasher(deviceID string) string {
	// Generate custom key as [64-bit hash of device id] to ensure predictable
	// length of the key
	hasher := fnv.New64()
	hasher.Write([]byte(deviceID))
	return strconv.FormatUint(uint64(hasher.Sum64()), 16)
}
