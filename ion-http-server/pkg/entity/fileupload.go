package entity

import (
	"errors"
	"github.com/mitchellh/mapstructure"
	"time"
)

// file type 0: temperal store file, 1: permanent file
type Fileupload struct {
	ID        int64 `gorm:"type:bigint;primaryKey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	Userid    int64  `gorm:"type:bigint;not null" mapstructure: "userid"`
	Status    int    `gorm:"type:int;not null" mapstructure: "status"`
	Mimetype  string `gorm:"type:varchar(255);not null" mapstructure: "mimetype"`
	Size      int64  `gorm:"type:bigint;not null" mapstructure: "size"`
	Location  string `gorm:"type:varchar(2048);not null" mapstructure: "location"`
	Type      int    `gorm:"type:int;default 0" mapstructure: "type"`
}

func NewFileUpload(file map[string]interface{}) *Fileupload {
	fileupload := &Fileupload{}
	mapstructure.Decode(file, fileupload)
	return fileupload
}

func QueryFileUpload(fileInfo *Fileupload) *Fileupload {
	fileupload := &Fileupload{}
	tidb.DB.Where(fileInfo).First(fileupload)
	return fileupload
}

func AddFileUpload(file map[string]interface{}) (*Fileupload, error) {
	fileupload := NewFileUpload(file)
	err := fileupload.SaveFileUpload()
	if err != nil {
		return nil, err
	}
	return fileupload, nil
}

func (file *Fileupload) SaveFileUpload() error {
	if file.Mimetype == "" || file.Size <= 0 || file.Location == "" {
		return errors.New("Invalid file")
	}
	if file.ID == 0 {
		file.ID = DecodeUid(uidgen.Get())
	}
	err := tidb.DB.Create(file).Error
	return err
}

func (file *Fileupload) UpdateFileUpload(newFileInfo Fileupload) error {
	return tidb.DB.Model(file).Updates(newFileInfo).Error
}
