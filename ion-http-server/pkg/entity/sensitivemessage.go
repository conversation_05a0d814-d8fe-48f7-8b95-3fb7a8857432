package entity

import (
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"log"
)

type Sensitivemessage struct {
	ID          int64              `gorm:"type:bigint;primaryKey"`
	Type        int                `gorm:"type:int;not null" mapstructure: "type"`
	ContentType *proto.MessageType `gorm:"type:int;not null" mapstructure: "contentType"`
	Topic       string             `gorm:"type:char(50);not null" mapstructure: "topic"`
	From        int64              `gorm:"type:bigint;not null" mapstructure: "from"`
	Seqid       int                `gorm:"type:int;not null" mapstructure: "seqid"`
	Description interface{}        `gorm:"type:json;not null" mapstructure: "description"`
}

func QuerySensitiveMessages(query *proto.MessageQuery) ([]proto.Message, error) {
	limit := 10
	if query.Limit > 0 {
		limit = query.Limit
	}
	offset := -1
	if query.Offset > 0 {
		offset = query.Offset
	}

	msgs := []Sensitivemessage{}
	msgquery := &Sensitivemessage{}
	if query.Topic != "" {
		msgquery.Topic = query.Topic
	}

	if query.User != "" {
		msgquery.From, _ = DecodeUser(query.User)
	}
	filterType := proto.MessagePlainText
	msgquery.ContentType = &filterType
	db := tidb.DB.Limit(limit).Offset(offset).Where(msgquery)

	if query.Seqid != 0 {
		//return new reference of db
		db = db.Where("seqid > ?", query.Seqid)
	}
	db.Find(&msgs)

	sensitivemsgs := make([]proto.Message, len(msgs))
	for index, msg := range msgs {
		uidBase64, _ := EncodeUidInt642Base64(msg.From)
		sensitivemsgs[index] = proto.Message{
			User:        uidBase64,
			Description: fromJSON(msg.Description),
			Topic:       msg.Topic,
			Seqid:       msg.Seqid,
		}
	}
	return sensitivemsgs, nil
}

func RawQuerySensitiveMessages(query *proto.MessageQuery) ([]proto.Message, error) {
	sql := "SELECT s.topic, s.`from` as user, s.seqid, m.content, s.`description` FROM tinode.sensitivemessages s LEFT JOIN tinode.messages m ON s.topic=m.topic " +
		"AND s.`from`=m.`from` AND s.seqid = m.seqid where 1=1 "
	var args []interface{}
	if query.Topic != "" {
		sql += "AND m.topic=? "
		args = append(args, query.Topic)
	}

	if query.User != "" {
		uid, _ := DecodeUser(query.User)
		sql += "AND m.`from`=? "
		args = append(args, uid)
	}

	limit := 10
	if query.Limit > 0 {
		limit = query.Limit
	}

	sql += "limit ?"
	args = append(args, limit)

	rows, err := tidb.DB.Raw(sql, args...).Rows()
	if err != nil {
		return nil, err
	}
	var result []proto.Message
	defer rows.Close()
	for rows.Next() {
		msg := proto.Message{}
		err = tidb.DB.ScanRows(rows, &msg)
		if err != nil {
			log.Println(err)
			return nil, err
		}
		msg.User, _ = EncodeUidInt64Str2Base64(msg.User)
		msg.Description = fromJSON(msg.Description)
		msg.Content = fromJSON(msg.Content)
		result = append(result, msg)
	}
	return result, nil
}
