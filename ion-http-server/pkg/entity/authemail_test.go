package entity

import (
	"fmt"
	"testing"
)

var (
	name  = "sqlite3"
	addrs = "./gorm.db"
)

func TestEmail(t *testing.T) {
	Init(name, addrs)
	t.Run("test email auth models", func(t *testing.T) {
		au := &AuthEmail{
			Email:    "<EMAIL>",
			AuthCode: "123456",
			Count:    1,
		}

		au.SaveEcode()
		fmt.Println(*au)
		// Add
		got := QueryEmail(&AuthEmail{Email: "<EMAIL>"})
		if got.AuthCode != au.AuthCode {
			t.Errorf("got:%v, want:%v", got.AuthCode, au.AuthCode)
		}

		// Update
		au.AuthCode = "7890"
		au.UpdateEcode(*au)
		fmt.Println(*au)
		got = QueryEmail(&AuthEmail{Email: "<EMAIL>"})
		if got.AuthCode != au.AuthCode {
			t.Errorf("got:%v, want:%v", got.AuthCode, au.AuthCode)
		}

		// Delete
		au.DeleteEcode()
		if au.Email != "" || au.AuthCode != "" {
			fmt.Println(au)
			t.Errorf("should nil")
		}
	})

}
