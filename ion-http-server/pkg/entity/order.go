package entity

import (
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/mitchellh/mapstructure"
	"time"
)

type Order struct {
	ID            int64 `gorm:"type:bigint;primaryKey"`
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
	TopupAccount  int64                `gorm:"type:bigint;not null" mapstructure:"topupAccount"`
	PaidAt        time.Time            `gorm:"default:null" mapstructure:"paidAt"`
	Status        *proto.OrderStatus   `gorm:"default:0" mapstructure:"status"`
	ServiceType   *proto.ServiceType   `gorm:"not null" mapstructure:"serviceType"`
	Method        *proto.PaymentMethod `gorm:"type:varchar(10);not null" mapstructure:"method"`
	Amount        int                  `gorm:"type:int;default:0" mapstructure:"amount"`
	Duration      int                  `gorm:"type:int;default:0" mapstructure:"duration"`
	CodeUrl       string               `gorm:"type:varchar(100);default:null;unique" mapstructure:"codeUrl"`
	RequestIP     string               `gorm:"type:varchar(50);not null" mapstructure:"requestIp"`
	TransactionID string               `gorm:"type:varchar(100);default: null" mapstructure:"transactionId"`
}

func NewOrder(orderInfo map[string]interface{}) *Order {
	order := &Order{}
	mapstructure.Decode(orderInfo, order)
	return order
}

func (order *Order) CreateOrder() error {
	idUint64 := uidgen.Get()
	order.ID = uidgen.DecodeUid(idUint64)
	if order.RequestIP != "" {
		order.RequestIP = util.EncryptString(order.RequestIP)
	}
	curTime := util.TimeNow()
	order.CreatedAt = curTime
	order.UpdatedAt = curTime
	err := tidb.DB.Create(order).Error
	return err
}

func (order *Order) Update(newOrder Order) error {
	curTime := util.TimeNow()
	newOrder.UpdatedAt = curTime
	return tidb.DB.Model(order).Updates(newOrder).Error
}

func QueryOrder(orderInfo *Order) *Order {
	order := &Order{}
	if orderInfo.RequestIP != "" {
		orderInfo.RequestIP = util.EncryptString(orderInfo.RequestIP)
	}
	tidb.DB.Where(orderInfo).First(order)
	return order
}
