package entity

import (
	"errors"

	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

// Status :
// 1. Meeting : the participator is meeting
// 2. Leaving : the participator has left
// 3. Closed  : the meeting has been closed
// 4. Deleted : the meeting has been deleted

const (
	Meeting = 1
	Leaving = 2
	Deleted = 4
)

type Participator struct {
	gorm.Model
	ConferenceID   string `gorm:"type:varchar(255);not null" mapstructure: "conferenceid"`
	ParticipatorID int64  `gorm:"type:bigint;not null" mapstructure: "partid"`
	JoinAt         int64  `gorm:"not null" mapstructure:"joinat"`
	LeaveAt        int64  `mapstructure:"leaveat"`
	Status         int    `gorm:"not null" mapstructure:"status"`
	Number         string `gorm:"type:varchar(255)" mapstructure:"number"`
	Duration       int64  `mapstructure:"duration"`
}

func NewParticipator(partInfo map[string]interface{}) *Participator {
	part := &Participator{}
	mapstructure.Decode(partInfo, part)
	return part
}

func (part *Participator) DeleteParticipator() {
	if part != nil && part.ConferenceID != "" {
		tidb.DB.Unscoped().Delete(part)
	}
}

func (part *Participator) SaveParticipator() error {
	if part.ConferenceID == "" && part.ParticipatorID == 0 {
		return errors.New("miss importance information")
	}
	tidb.DB.Create(part)
	return nil
}

func (part *Participator) UpdateParticipator(newPartInfo Participator) {
	tidb.DB.Model(part).Updates(newPartInfo)
}

func (part *Participator) UpdatePartWithMap(newPartInfo map[string]interface{}) {
	tidb.DB.Model(part).Updates(newPartInfo)
}

func AddParticipator(partInfo map[string]interface{}) error {
	part := NewParticipator(partInfo)
	err := part.SaveParticipator()
	return err
}

// query participatorID => he has joined all the conferences
// query conferenceID => who join this conference
func QueryParticipatorList(uid int64, cursor, limit int) []Participator {
	parts := []Participator{}
	if limit == 0 {
		tidb.DB.Where("participator_id = ? AND status = ?", uid, Closed).Order("join_at desc").Offset(cursor).Find(&parts)
		return parts
	}
	// tidb.DB.Where("participator_id = ? AND status = ? AND join_at > ?", uid, Closed, cursor).Limit(limit).Find(&parts)
	tidb.DB.Where("participator_id = ? AND status = ?", uid, Closed).Limit(limit).Order("join_at desc").Offset(cursor).Find(&parts)
	return parts
}

// query like this :SELECT * FROM participators WHERE Status <> status AND conferenceid = cid;
func QueryUninclude(status int, cid string) []*Participator {
	parts := []*Participator{}
	tidb.DB.Find(&parts, "status <> ? AND conference_id = ?", status, cid)
	return parts
}

func QueryParticipator(partInfo *Participator) *Participator {
	part := &Participator{}
	tidb.DB.Where(partInfo).First(part)
	return part
}

func QueryPartStatus(pid, cid string, meeting, leaving int) *Participator {
	part := &Participator{}
	tidb.DB.Where("participator_id = ? and confernece_id = ? and status = ? or status = ?", pid, cid, meeting, leaving).First(part)
	return part
}
