package entity

import (
	"errors"
	"log"

	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

type Usertag struct {
	gorm.Model
	User   User   `gorm:"foreignkey:Userid;association_foreignkey:Uid"`
	Userid uint64 `gorm:"type:bigint" mapstructure: "userid"`
	Tag    string `gorm:"type:varchar(100)" mapstructure: "tag"`
}

func NewUsertag(utagInfo map[string]interface{}) *Usertag {
	utag := &Usertag{}
	mapstructure.Decode(utagInfo, utag)
	return utag
}

func (utag *Usertag) DeleteUsertag() {
	tidb.DB.Unscoped().Delete(utag)
}

func (utag *Usertag) SaveUsertag() error {
	if utag.Userid == 0 || utag.Tag == "" {
		return errors.New("miss primary key for register Usertag")
	}
	err := tidb.DB.Create(utag).Error
	if err != nil {
		log.Printf("save user tages %v", err.Error())
	}
	return err
}

func (utag *Usertag) UpdateUsertag(newUtagInfo Usertag) {
	tidb.DB.Model(utag).Updates(newUtagInfo)
}

func (utag *Usertag) UpdateSubWithMap(newUtagInfo map[string]interface{}) {
	tidb.DB.Model(utag).Updates(newUtagInfo)
}

// func SubExist(utagInfo *UserTag) (bool, error) {
// 	if sub.Userid == "" && sub.Topic == "" {
// 		return false, errors.New("Miss unique key to retrieve sub info.")
// 	}
// 	subList := QuerySubList(utagInfo)
// 	return len(utagList) == 1, nil
// }

func AddUsertag(utagInfo map[string]interface{}) error {
	utag := NewUsertag(utagInfo)
	err := utag.SaveUsertag()
	return err
}

func AddUsertagWithTx(utagInfo map[string]interface{}, tx *gorm.DB) error {
	utag := NewUsertag(utagInfo)
	err := utag.SaveUsertagWithTx(tx)
	return err
}

func AddUsertags(utags []map[string]interface{}) error {
	err := tidb.DB.Model(&Usertag{}).Create(utags).Error
	return err
}

func (utag *Usertag) SaveUsertagWithTx(tx *gorm.DB) error {
	if utag.Userid == 0 || utag.Tag == "" {
		return errors.New("miss primary key for register Usertag")
	}
	err := tx.Create(utag).Error
	if err != nil {
		log.Printf("save user tages %v", err.Error())
	}
	return err
}
