package entity

import (
	"encoding/json"
	"errors"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
	"gorm.io/datatypes"
	"log"
	"strings"
	"time"
)

const default_avatar = "/templates/avatar/default_avatar.png"

type User struct {
	gorm.Model
	Uid          int64           `gorm:"type:bigint;unique;not null" mapstructure:"uid"`
	Email        string          `gorm:"type:varchar(100);unique;default:null" mapstructure:"email"`
	Phone        string          `gorm:"type:varchar(100);unique;default:null" mapstructure:"phone"`
	Password     string          `gorm:"type:varchar(255)"mapstructure: "password"`
	Salt         string          `gorm:"type:varchar(255);not null" mapstructure:"salt"`
	PMI          string          `gorm:"type:varchar(255);unique;not null" mapstructure:"pmi"`
	State        *proto.ObjState `gorm:"not null;default:0" mapstructure:"state"`
	Stateat      time.Time       `gorm:"default:null" mapstructure:"stateAt"`
	Lastseen     time.Time       `gorm:"default:null" mapstructure:"lastSeen"`
	Useragent    string          `gorm:"default:''" mapstructure:"useragent"`
	Public       datatypes.JSON  `gorm:"type:json" mapstructure:"public"`
	Access       datatypes.JSON  `gorm:"type:json" mapstructure:"access"`
	Tags         datatypes.JSON  `gorm:"type:json" mapstructure:"tags"`
	VipExpiredAt time.Time       `gorm:"default:null" mapstructure: "vipExpiredAt"`
	Type         *proto.UserType `gorm:"type:int;default:0" mapstructure:"type"`
	Wechat       string          `gorm:"type:varchar(50);unique;default:null" mapstructure: "wechat"`
	Qq           string          `gorm:"type:varchar(50);unique;default:null" mapstructure: "qq"`
}

func NewUser(userInfo map[string]interface{}) *User {
	user := &User{}
	mapstructure.Decode(userInfo, user)
	if _, ok := userInfo["public"]; ok {
		if jsonStr, err := json.Marshal(userInfo["public"]); err == nil {
			user.Public = datatypes.JSON([]byte(jsonStr))
		}
	}
	return user
}

func (user *User) DeleteUser() {
	tidb.DB.Unscoped().Delete(user)
}

func (user *User) SaveUser() error {
	// if user.Email == "" && user.Password == "" && user.Phone == "" {
	// 	return errors.New("miss primary key for register account")
	// }
	err := tidb.DB.Create(user).Error
	if err != nil {
		log.Printf("save user %v", err.Error())
	}
	return err
}

func (user *User) UpdateUser(newUserInfo User) error {
	return tidb.DB.Model(user).Updates(newUserInfo).Error
}

func (user *User) UpdateUserWithMap(newUserInfo map[string]interface{}) {
	tidb.DB.Model(user).Updates(newUserInfo)
}

func (user *User) UpdateUsrPublic(field string, val interface{}) error {
	m := make(map[string]interface{})
	// public might has other initial values
	if user.Public != nil {
		public, _ := user.Public.Value()
		json.Unmarshal(public.([]byte), &m)
	}
	m[field] = val

	jsonStr, err := json.Marshal(m)
	if err != nil {
		return err
	}
	user.UpdateUser(User{Public: datatypes.JSON([]byte(jsonStr))})
	return nil
}

func (user *User) GetPublicField(field string) interface{} {
	if user.Public == nil {
		return nil
	}
	m := make(map[string]interface{})
	public, _ := user.Public.Value()
	json.Unmarshal(public.([]byte), &m)
	if val, ok := m[field]; ok {
		return val
	}
	return nil
}

func (user *User) GetAccess() (*proto.DefaultAccess, error) {
	if user.Access == nil {
		return nil, nil
	}
	acsRaw, _ := user.Access.Value()
	access := &proto.DefaultAccess{}
	if err := access.Scan(acsRaw); err != nil {
		return nil, err
	}
	return access, nil
}

func (user *User) UpdateAccess(want proto.AccessMode, upgrade bool) (*proto.DefaultAccess, error) {
	if user.Access == nil {
		return nil, errors.New("invalid user access")
	}
	access, err := user.GetAccess()
	if err != nil {
		return nil, err
	}
	// upgrade access: if user already have all permissions in 'want', do nothing
	// downgrade access: if user don't have any permissions in 'want', do nothing
	if (access.Auth.BetterEqual(want) && upgrade) ||  ( (want & access.Auth == 0) && !upgrade) {
		return access, nil
	}
	if upgrade {
		access.Auth = access.Auth | want
	} else {
		access.Auth = access.Auth &^ want
	}
	acsByte, err := access.Value()
	if err != nil {
		return nil, err
	}
	if err = user.UpdateUser(User{Public: datatypes.JSON(acsByte)}); err != nil {
		return nil, err
	}
	return access, nil
}

func (user *User) SaveUserWithTx(tx *gorm.DB) error {
	err := tx.Create(user).Error
	if err != nil {
		log.Printf("save user %v", err.Error())
	}
	return err
}

func AddUser(userInfo map[string]interface{}) (*User, error) {
	user := NewUser(userInfo)
	// check regestering user whether had account in before
	if olduser := QueryCancelledUser(user); olduser.Uid != util.ZeroUid {
		now := util.TimeNow()
		update := map[string]interface{}{
			"updated_at": now,
			"stateat":    now,
			"state":      proto.StateOK,
		}
		olduser.UpdateUserWithMap(update)
		return olduser, nil
	}
	if _, ok := userInfo["salt"]; !ok {
		userInfo["salt"] = uuid.New().String()
	}

	// Assign default access values in case the acc creator has not provided them
	authAcsModeInt := util.GetDefaultAccess(proto.TopicCatP2P, true) | util.GetDefaultAccess(proto.TopicCatGrp, true)
	anonAcsModeInt := util.GetDefaultAccess(proto.TopicCatP2P, false) | util.GetDefaultAccess(proto.TopicCatGrp, false)
	// user.Stateat = time.Now().UTC().Round(time.Millisecond)
	authAcsModeByte, _ := authAcsModeInt.MarshalText()
	anonAcsModeByte, _ := anonAcsModeInt.MarshalText()

	access := map[string]interface{}{
		"Auth": string(authAcsModeByte),
		"Anon": string(anonAcsModeByte),
	}
	acsJsonByte, err := json.Marshal(access)
	if err != nil {
		return nil, err
	}
	user.Access = datatypes.JSON(acsJsonByte)
	var tags []string
	if user.Email != "" {
		tags = append(tags, "email:"+user.Email)
	}
	if user.Phone != "" {
		tags = append(tags, "tel:"+user.Phone)
	}
	//add uid
	user.Uid = DecodeUid(uidgen.Get())
	if user.Uid == 0 {
		return nil, errors.New("Invalid uid")
	}

	// add PMI
	user.PMI = GetPMI()

	m := make(map[string]interface{})
	// public might has other initial values
	if user.Public != nil {
		public, _ := user.Public.Value()
		json.Unmarshal(public.([]byte), &m)
	}

	if _, ok := m["avatarUrl"]; !ok {
		m["avatarUrl"] = default_avatar
	}
	// add user name
	if _, ok := m["fn"]; !ok {
		if username, err := EncodeUser(user.Uid); err == nil {
			m["fn"] = username
		} else {
			return nil, errors.New("encode user name fail")
		}
	}

	if jsonStr, err := json.Marshal(m); err == nil {
		user.Public = datatypes.JSON([]byte(jsonStr))
	} else {
		return nil, errors.New("Failed to generate public info.")
	}

	// begin transaction
	tx := tidb.DB.Begin()
	if err := user.SaveUserWithTx(tx); err != nil {
		tx.Rollback()
		return user, err
	}

	cSelfModeByte, _ := proto.ModeCSelf.MarshalText()
	sub1 := map[string]interface{}{
		"userid":    user.Uid,
		"topic":     util.PrefixId("usr", UidString(EncodeUid(user.Uid))),
		"modeWant":  string(cSelfModeByte),
		"modeGiven": string(cSelfModeByte),
		"private":   nil,
	}
	sub2 := map[string]interface{}{
		"userid":    user.Uid,
		"topic":     util.PrefixId("fnd", UidString(EncodeUid(user.Uid))),
		"modeWant":  string(cSelfModeByte),
		"modeGiven": string(cSelfModeByte),
		"private":   nil,
	}

	subs := []map[string]interface{}{sub1, sub2}
	if err := AddSubsWithTx(subs, tx); err != nil {
		tx.Rollback()
		return user, err
	}
	for _, tag := range tags {
		usertag := map[string]interface{}{
			"userid": user.Uid,
			"tag":    tag,
		}
		if err := AddUsertagWithTx(usertag, tx); err != nil {
			tx.Rollback()
			return user, err
		}
	}

	if err := DefaultSettingWithTx(user, tx); err != nil {
		tx.Rollback()
		return user, err
	}
	tx.Commit()
	return user, nil
}

func QueryUser(userInfo *User) *User {
	user := &User{}
	userStatus := proto.StateOK
	userInfo.State = &userStatus
	tidb.DB.Where(userInfo).First(user)
	return user
}

func QueryCancelledUser(userInfo *User) *User {
	query := &User{}
	user := &User{}
	query.Uid = userInfo.Uid
	query.Phone = userInfo.Phone
	query.Email = userInfo.Email
	query.Wechat = userInfo.Wechat
	query.Qq = userInfo.Qq
	stateDeleted := proto.StateDeleted
	query.State = &stateDeleted

	tidb.DB.Where(query).First(user)
	return user
}

func QueryUsrOrCondition(queryParam *proto.UserQuery) *User {
	var args []interface{}
	var sql string
	if queryParam.Uid != "" {
		if uid, err := DecodeUser(queryParam.Uid); err == nil {
			args = append(args, uid)
			sql += "OR uid = ? "
		}
	}

	if queryParam.Email != "" {
		emailCipher := util.EncryptString(queryParam.Email)
		args = append(args, emailCipher)
		sql += "OR email = ? "
	}

	if queryParam.Phone != "" {
		PhoneCipher := util.EncryptString(queryParam.Phone)
		args = append(args, PhoneCipher)
		sql += "OR phone = ? "
	}
	if len(args) == 0 {
		return nil
	}
	sql = strings.Replace(sql, "OR ", "", 1)
	user := &User{}
	err := tidb.DB.Where(sql, args...).Find(user).Error
	if err != nil {
		return nil
	}
	return user
}
