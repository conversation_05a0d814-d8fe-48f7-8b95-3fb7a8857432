package entity

import (
	"fmt"

	"github.com/Young666666Jian/ion-http-server/pkg/util"
)

func EncryptUsers() {
	users := []User{}
	tidb.DB.Find(&users)
	for _, user := range users {
		if !util.CheckEmailFormat(user.Email) && !util.VerifyMobileFormat(user.Phone) {
			continue
		}
		user.Email = util.EncryptString(user.Email)
		user.Phone = util.EncryptString(user.Phone)
		fmt.Println("email:", user.Email, "phone:", user.Phone)

		user.UpdateUser(user)
		fmt.Println(user)
	}
}

func EncryptTags() {
	tags := []Usertag{}
	tidb.DB.Find(&tags)
	for _, tag := range tags {
		// fmt.Println(tag.Tag)
		if tag.Tag[:1] == "t" {
			tel := tag.Tag[4:]
			if !util.VerifyMobileFormat(tel) {
				continue
			}
			encrypt := util.EncryptString(tel)
			fmt.Println("tel:", tel, "encrypt:", encrypt)
			tag.Tag = "tel:" + encrypt
			tag.UpdateUsertag(tag)
		} else if tag.Tag[:1] == "e" {
			email := tag.Tag[6:]
			if !util.CheckEmailFormat(email) {
				continue
			}
			encrypt := util.EncryptString(email)
			fmt.Println("email:", email, "encrypt:", encrypt)
			tag.Tag = "email:" + encrypt
			tag.UpdateUsertag(tag)
		}
	}
}

func EncryptAuthEmail() {
	authEmails := []AuthEmail{}
	tidb.DB.Find(&authEmails)
	for _, email := range authEmails {
		if !util.CheckEmailFormat(email.Email) {
			continue
		}
		encrypt := util.EncryptString(email.Email)
		fmt.Println("email:", email.Email, "encrypt:", encrypt)
		email.Email = encrypt
		email.UpdateEcode(email)
	}
}

func EncryptAuthPhone() {
	authPhones := []AuthPhone{}
	tidb.DB.Find(&authPhones)
	for _, phone := range authPhones {
		if !util.VerifyMobileFormat(phone.Phone) {
			continue
		}
		encrypt := util.EncryptString(phone.Phone)
		fmt.Println("phone:", phone.Phone, "encrypt:", encrypt)
		phone.Phone = encrypt
		phone.UpdatePcode(phone)
	}
}

func GetAllRecords(types int) {
	switch types {
	case 0:
		EncryptUsers()
	case 1:
		EncryptTags()
	case 2:
		EncryptAuthEmail()
	case 3:
		EncryptAuthPhone()
	case 4:
		EncryptUsers()
		EncryptTags()
		EncryptAuthEmail()
		EncryptAuthPhone()
	}

}
