package entity

import (
	"errors"
	"strings"

	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

// Status :
// 1. pending : wait for joining conference
// 2. launching : meeting
// 3. closed : this conference had closed

const (
	Pending   = 1
	Launching = 2
	Closed    = 3
)

var (
	lenOMI = conf.Meeting.LengthOMI
	lenPMI = conf.Meeting.LengthPMI
)

type Conference struct {
	gorm.Model
	ConferenceID   string `gorm:"type:varchar(255);not null" mapstructure: "conferenceid"`
	HostID         int64  `gorm:"type:bigint;not null" mapstructure: "hostid"`
	HostName       string `gorm:"type:varchar(255)" mapstructure: "hostname"`
	Password       string `gorm:"type:varchar(255);" mapstructure: "password"`
	Salt           string `gorm:"type:varchar(255);not null" mapstructure: "salt"`
	Subject        string `gorm:"type:varchar(255);" mapstructure: "subject"`
	StartedAt      int64  `gorm:"not null" mapstructure:"startedat"`
	EndAt          int64  `mapstructure:"endat"`
	Status         int    `gorm:"not null" mapstructure:"status"`
	Number         string `gorm:"type:varchar(255)" mapstructure:"number"`
	OnEarlyJoining bool   `gorm:"type:tinyint(1);not null" mapstructure:"onEarlyJoin"`
	OnWaitingRoom  bool   `gorm:"type:tinyint(1);not null" mapstructure:"onWaitingRoom"`
	Topic          string `gorm:"type:varchar(50);unique;default:null" mapstructure:"topic"`
}

func NewConference(confInfo map[string]interface{}) *Conference {
	conf := &Conference{}
	mapstructure.Decode(confInfo, conf)
	return conf
}

func (conf *Conference) DeleteConference() {
	if conf != nil && conf.ConferenceID != "" {
		tidb.DB.Unscoped().Delete(conf)
	}
}

func (conf *Conference) SaveConference() error {
	if conf.ConferenceID == "" && conf.HostID == 0 {
		return errors.New("miss primary key for creating conference")
	}
	tidb.DB.Create(conf)
	return nil
}

func (conf *Conference) UpdateConference(newConfInfo Conference) {
	tidb.DB.Model(conf).Updates(newConfInfo)
}

func (conf *Conference) UpdateConferenceWithMap(newConfInfo map[string]interface{}) {
	tidb.DB.Model(conf).Updates(newConfInfo)
}

func AddConference(confInfo map[string]interface{}) error {
	conf := NewConference(confInfo)
	err := conf.SaveConference()
	return err
}

func QueryConference(confInfo *Conference) *Conference {
	conf := &Conference{}
	tidb.DB.Where(confInfo).First(conf)
	return conf
}

func genVar(numbers []string) string {
	result := "'" + strings.Join(numbers, "','") + "'"
	return result
}

func QueryInConference(numbers []string) []Conference {
	conf := []Conference{}
	if len(numbers) <= 0 {
		return conf
	}
	tidb.DB.Where("number IN (" + genVar(numbers) + ")").Find(&conf)
	return conf
}
