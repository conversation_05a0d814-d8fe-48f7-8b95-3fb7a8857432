package entity

import (
	"errors"

	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

type Feedback struct {
	gorm.Model
	Uid      int64  `gorm:"type:bigint;not null" mapstructure: "uid"`
	Username string `gorm:"type:varchar(255)" mapstructure: "username"`
	Message  string `gorm:"type:text" mapstructure:"text"`
}

func NewFeedback(feedback map[string]interface{}) *Feedback {
	f := &Feedback{}
	mapstructure.Decode(feedback, f)
	return f
}

func (fb *Feedback) DeleteFeedback() {
	if fb != nil && fb.Uid != 0 {
		tidb.DB.Unscoped().Delete(fb)
	}
}

func (fb *Feedback) SaveFeedback() error {
	if fb.Message == "" && fb.Uid == 0 {
		return errors.New("miss primary key for creating feedback")
	}
	tidb.DB.Create(fb)
	return nil
}

func (fb *Feedback) UpdateFeedback(newFeedback Feedback) {
	tidb.DB.Model(fb).Updates(newFeedback)
}

func (fb *Feedback) UpdateFeedbackWithMap(newFeedback map[string]interface{}) {
	tidb.DB.Model(fb).Updates(newFeedback)
}

func AddFeedback(feedback map[string]interface{}) error {
	fb := NewFeedback(feedback)
	err := fb.SaveFeedback()
	return err
}

func QueryFeedback(feedback *Feedback) *Feedback {
	fb := &Feedback{}
	tidb.DB.Where(fb).First(feedback)
	return fb
}
