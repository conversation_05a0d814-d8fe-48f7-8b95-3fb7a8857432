package censor

import (
	"bufio"
	"encoding/base64"
	"encoding/json"
	"flag"
	jcr "github.com/tinode/jsonco"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"testing"
)

func TestCensor(t *testing.T) {
	path, _ := os.Getwd()
	var configfile = flag.String("config", "/server/tinode.conf", "Path to config file.")
	projectDir := filepath.Dir(filepath.Dir(path))
	*configfile = filepath.Clean(filepath.Join(projectDir, *configfile))
	file, err := os.Open(*configfile)
	if err != nil {
		log.Fatal("Failed to read config file: ", err)
	}

	jr := jcr.New(file)
	var result map[string]json.RawMessage
	err = json.NewDecoder(jr).Decode(&result)
	if err != nil {
		log.Fatal("Failed to read parse file: ", err)
	}
	file.Close()
	if _, ok := result["censor"]; !ok {
		log.Fatal("Miss censor info in config")
	}
	censor, cerr := NewCensor(result["censor"])
	if cerr != nil {
		log.Fatal("Failed to create censor: ", cerr)
	}
	testImgDir := filepath.Clean(filepath.Join(projectDir, "/asset/censor-image"))

	files, ferr := ioutil.ReadDir(testImgDir)
	if ferr != nil {
		log.Fatal(ferr)
	}
	log.Println("Start censor content ......")
	for _, file := range files {
		fname := file.Name()
		log.Println(fname)
		f, _ := os.Open(filepath.Join(testImgDir, fname))
		reader := bufio.NewReader(f)
		content, _ := ioutil.ReadAll(reader)
		// Encode as base64.
		encoded := base64.StdEncoding.EncodeToString(content)
		cres, resErr := censor.ImageCensor(encoded)
		if resErr != nil {
			log.Fatal("Failed to censor content: ", resErr)
		}
		log.Println(cres)
		if cres == nil || cres.Conclusion == "" {
			t.Fatal("test failed: ", fname)
		}
	}

}
