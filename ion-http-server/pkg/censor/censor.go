package censor

import (
	"errors"
	"fmt"
	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/http"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/mitchellh/mapstructure"
	"time"
)

const (
	timeout           = 30
	tokenValidatyTime = 30 * 24 * time.Hour
)

type Censor struct {
	Provider       string
	AccessTokenUrl string
	CensorUrl      string
	ApiKey         string
	Secret         string
	TokenFetchedAt time.Time
	AccessToken    string

	httpClient *http.HttpClient
}
type configType struct {
	Enable         bool   `json:"enable,omitempty"`
	Provider       string `json:"provider,omitempty"`
	AccessTokenUrl string `json:"access_token_url,omitempty"`
	CensorUrl      string `json:"censor_url,omitempty"`
	ApiKey         string `json:"api_key,omitempty"`
	Secret         string `json:"secret_key,omitempty"`
}

type Hit struct {
	Probability float64  `mapstructure: "probability"`
	Database    string   `mapstructure: "database"`
	Words       []string `mapstructure: "words"`
}

type CensorData struct {
	Type           int    `mapstructure: "type"`
	Subtype        int    `mapstructure: "subtype"`
	Conclusion     string `mapstructure: "conclusion"`
	ConclusionType int    `mapstructure: "conclusionType"`
	Msg            string `mapstructure: "msg"`
	Hits           []Hit  `mapstructure: "hits"`
}

type CensorResponse struct {
	ContentType    proto.MessageType
	Logid          float64     `mapstructure: "log_id"`
	Conclusion     string      `mapstructure: "conclusion"`
	ConclusionType int         `mapstructure: "conclusionType"`
	Data           interface{} `mapstructure: "data"`
}

func NewCensor(hc *http.HttpClient) (*Censor, error) {
	if conf.Censor == nil || !conf.Censor.Enable {
		return nil, nil
	}
	c := &Censor{
		Provider:       conf.Censor.Provider,
		AccessTokenUrl: conf.Censor.AccessTokenUrl,
		CensorUrl:      conf.Censor.CensorUrl,
		ApiKey:         conf.Censor.ApiKey,
		Secret:         conf.Censor.Secret,
		httpClient:     hc,
	}
	if err := c.fetchToken(); err != nil {
		return nil, err
	}
	return c, nil
}

func (c *Censor) fetchToken() error {
	url := fmt.Sprintf("%v?grant_type=client_credentials&client_id=%v&client_secret=%v", c.AccessTokenUrl, c.ApiKey, c.Secret)

	res, err := c.httpClient.Post(url, nil, nil)
	if err != nil {
		return err
	}

	if _, ok := res["error"]; ok {
		return errors.New(res["error_description"].(string))
	}

	if token, ok := res["access_token"].(string); ok {
		c.AccessToken = token
		c.TokenFetchedAt = util.TimeNow()
	} else {
		return errors.New("miss access token")
	}

	return nil
}

func (c *Censor) ImageCensor(base64Content string) (*CensorResponse, error) {
	if err := c.isTokenExpired(); err != nil {
		fmt.Println("fetchToken failed")
		return nil, err
	}
	url := fmt.Sprintf("%v/img_censor/v2/user_defined?access_token=%v", c.CensorUrl, c.AccessToken)
	header := map[string]interface{}{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	params := map[string]interface{}{
		"image": base64Content,
	}
	res, err := c.httpClient.Post(url, header, params)
	if err != nil {
		fmt.Println("post failed")
		return nil, err
	}
	if _, hasErrCode := res["error_code"]; hasErrCode {
		fmt.Println(res)
		return nil, errors.New("censor error")
	}
	censorRes := &CensorResponse{}
	if err = mapstructure.Decode(res, censorRes); err != nil {
		return nil, err
	}
	censorRes.ContentType = proto.MessageImage
	return censorRes, nil
}

func (c *Censor) TextCensor(text string) (*CensorResponse, error) {

	if err := c.isTokenExpired(); err != nil {
		return nil, err
	}
	url := fmt.Sprintf("%v/text_censor/v2/user_defined?access_token=%v", c.CensorUrl, c.AccessToken)
	header := map[string]interface{}{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	params := map[string]interface{}{
		"text": text,
	}
	res, err := c.httpClient.Post(url, header, params)
	if err != nil {
		return nil, err
	}
	if _, hasErrCode := res["error_code"]; hasErrCode {
		return nil, errors.New("censor error")
	}
	censorRes := &CensorResponse{}
	if err = mapstructure.Decode(res, censorRes); err != nil {
		return nil, err
	}
	censorRes.ContentType = proto.MessagePlainText
	return censorRes, nil
}

//
func (cr *CensorResponse) GetFilterMsg() ([]proto.CensorResult, []string, bool) {
	if cr == nil || cr.Conclusion == "" {
		return nil, nil, true
	}

	list, ok := cr.Data.([]interface{})
	if !ok {
		fmt.Println("Info: invalid CensorData data in censor response")
		return nil, nil, true
	}

	var words []string
	resultMsg := make([]proto.CensorResult, 0, len(list))
	for _, data := range list {
		cdata := CensorData{}
		if err := mapstructure.Decode(data, &cdata); err != nil {
			return nil, nil, true
		}
		resultMsg = append(resultMsg, proto.CensorResult{
			Type:           cdata.Type,
			Subtype:        cdata.Subtype,
			ConclusionType: cdata.ConclusionType,
		})
		if cr.ContentType == proto.MessagePlainText {
			for _, hit := range cdata.Hits {
				if words == nil {
					words = hit.Words
					continue
				}
				words = append(words, hit.Words...)
			}
		}
	}
	return resultMsg, words, false
}

// func checkCensorRes(res *CensorResponse, mtp proto.MessageType) bool {
// 	if res == nil {
// 		return true
// 	}

// 	if list, ok := res.Data.([]CensorData); ok {
// 		for _, val := range list {
// 			if checkSubtype(val.Type, val.Subtype) {
// 				return true
// 			}
// 		}
// 	}
// 	return false
// }

// func checkSubtype(tp, subtype int, mtp proto.MessageType) bool {

// 	var subproto map[int][]int
// 	var ok bool
// 	if mtp == proto.MessageImage {
// 		subproto, ok = imgCsType2Subtype[tp]
// 	} else {
// 		subproto, ok = textCsType2Subtype[tp]
// 	}
// 	if !ok {
// 		return false
// 	}
// 	for _, subtp := range subproto {
// 		if subtp == subtype {
// 			return true
// 		}
// 	}
// 	return false
// }

func (c *Censor) isTokenExpired() error {
	now := util.TimeNow()
	duration := now.Sub(c.TokenFetchedAt)
	var err error
	if duration > tokenValidatyTime {
		err = c.fetchToken()
	}
	return err
}
