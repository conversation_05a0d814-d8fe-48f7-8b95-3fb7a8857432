package controller

import (
	cs "github.com/Young666666Jian/ion-http-server/pkg/censor"
	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/controller/payment"
	"github.com/Young666666Jian/ion-http-server/pkg/http"
	"github.com/Young666666Jian/ion-http-server/pkg/middleware"
	"github.com/Young666666Jian/ion-http-server/pkg/storage"
	_ "github.com/Young666666Jian/ion-http-server/pkg/storage/fs"
	_ "github.com/Young666666Jian/ion-http-server/pkg/storage/oss"
	_ "github.com/Young666666Jian/ion-http-server/pkg/storage/s3"
	jwt "github.com/appleboy/gin-jwt/v2"
)

const timeout = 30

var (
	WechatAppID     string
	WechatAppSecret string
	QQAppID         string
	QQAppSecret     string
	fileStore       storage.StorageSystem
	censor          *cs.Censor
	JwtMiddleware   *jwt.GinJWTMiddleware
	wechatPay       *payment.WechatPay
)

func init() {
	var err error
	JwtMiddleware, err = middleware.GetAuthMeddleware(conf.Auth.SecretKey)
	if err != nil {
		panic("jwt middleware init err")
	}
	err = storage.InitMediaHandler(conf.MediaFile.UseHandler, conf.MediaFile.Handlers)
	if err != nil {
		panic("storage init err")
	}
	fileStore = storage.GetMediaHandler()

	mode := "debug"
	if conf.Global.Mode == "release" {
		mode = "release"
	}
	oauthConfig := conf.Oauth.Config
	WechatAppID = oauthConfig[mode].WechatAppID
	WechatAppSecret = oauthConfig[mode].WechatAppSecret
	if WechatAppID == "" && WechatAppSecret == "" {
		panic("invalid Wechat OAuth config")
	}
	QQAppID = oauthConfig[mode].QQAppID
	QQAppSecret = oauthConfig[mode].QQAppSecret
	if QQAppID == "" && QQAppSecret == "" {
		panic("invalid QQ OAuth config")
	}
	censor, err = cs.NewCensor(http.NewHttpClient(timeout))
	if err != nil {
		panic("failed to init censor")
	}

	wechatPay, err = payment.NewWechatPay()
	if err != nil {
		panic("failed to init wechat pay")
	}
}
