package payment

import (
	"context"
	"crypto/x509"
	"encoding/json"
	"errors"
	"fmt"
	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/native"
	wechatUtils "github.com/wechatpay-apiv3/wechatpay-go/utils"
	"io/ioutil"
	"log"
)

type WechatPay struct {
	client     *core.Client
	svc        *native.NativeApiService
	Currency   string
	AppID      string
	MerchantID string
	NotifyAPI  string
	ApiV3Key   string
}

func NewWechatPay() (*WechatPay, error) {
	if !conf.Payment.WechatPay.Enable {
		return nil, errors.New("miss wechatpay config")
	}

	wechatpayConfig := conf.Payment.WechatPay
	privateKey, err := wechatUtils.LoadPrivateKeyWithPath(wechatpayConfig.PrivateKeyPath)
	if err != nil {
		log.Printf("load private err:%s", err.Error())
		return nil, err
	}
	// load wechatpay platform cert
	wechatPayCertificate, err := wechatUtils.LoadCertificateWithPath(wechatpayConfig.CertFilePath)
	if err != nil {
		log.Printf("load certificate err:%s", err)
		return nil, err
	}
	// set authorization in header
	opts := []core.ClientOption{
		core.WithMerchantCredential(wechatpayConfig.MerchantID, wechatpayConfig.CertSerialNumber, privateKey), // 设置商户相关配置
		core.WithWechatPayValidator([]*x509.Certificate{wechatPayCertificate}),                                // 设置微信支付平台证书，用于校验回包信息用
	}
	ctx := context.TODO()
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Printf("init client err:%s", err)
		return nil, err
	}
	wechatPay := &WechatPay{
		client:     client,
		svc:        &native.NativeApiService{Client: client},
		Currency:   conf.Payment.Currency,
		MerchantID: wechatpayConfig.MerchantID,
		AppID:      conf.Oauth.Config["release"].WechatAppID,
		NotifyAPI:  fmt.Sprintf("%s/api/v1/order/wechcatpay/notify", conf.Domain.Name),
		ApiV3Key:   wechatpayConfig.ApiV3Key,
	}
	return wechatPay, nil
}

func (wp *WechatPay) CreateOrder(order *entity.Order) (*proto.OrderCreateResponse, error) {

	URL := "https://api.mch.weixin.qq.com/v3/pay/transactions/native"
	// 设置请求信息,此处也可以使用结构体来进行请求
	orderId := util.String32(entity.EncodeUid(order.ID))
	mapInfo := map[string]interface{}{
		"mchid":        wp.MerchantID,
		"out_trade_no": orderId,
		"appid":        wp.AppID,
		"description":  "方宇会议VIP充值",
		"notify_url":   wp.NotifyAPI,
		"amount": map[string]interface{}{
			"total":    order.Amount,
			"currency": wp.Currency,
		},
	}
	ctx := context.TODO()
	// 发起请求
	result, err := wp.client.Post(ctx, URL, mapInfo)
	if err != nil {
		log.Printf("client post err:%s", err)
		return nil, err
	}

	// record request ID
	// 校验回包内容是否有逻辑错误
	err = core.CheckResponse(result.Response)
	if err != nil {
		log.Printf("check response err:%s", err)
		return nil, err
	}

	defer result.Response.Body.Close()
	// 读取回包信息
	body, err := ioutil.ReadAll(result.Response.Body)
	if err != nil {
		log.Printf("read response body err:%s", err)
		return nil, err
	}
	res := &proto.OrderCreateResponse{}
	if err = json.Unmarshal(body, res); err != nil {
		return nil, err
	}
	res.OrderID = orderId
	return res, nil
}

func (wp *WechatPay) CloseOrder(orderId int64) error {
	base32Id := util.String32(entity.EncodeUid(orderId))
	ctx := context.TODO()
	result, err := wp.svc.CloseOrder(ctx,
		native.CloseOrderRequest{
			OutTradeNo: core.String(base32Id),
			Mchid:      core.String(wp.MerchantID),
		},
	)
	if err != nil {
		return err
	}
	if result.Response.StatusCode != 204 {
		return errors.New("failed to close order")
	}
	return nil
}

func (wp *WechatPay) DecryptNotification(associatedData, nonce, ciphertext string) (*proto.WechatPayNotifyInfo, error) {
	plaintxt, err := wechatUtils.DecryptAES256GCM(wp.ApiV3Key, associatedData, nonce, ciphertext)
	if err != nil {
		return nil, err
	}
	notification := &proto.WechatPayNotifyInfo{}
	if err = json.Unmarshal([]byte(plaintxt), notification); err != nil {
		return nil, err
	}
	return notification, nil
}
