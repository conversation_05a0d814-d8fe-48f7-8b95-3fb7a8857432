package controller

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/gin-gonic/gin"
)

var (
	lenOMI = conf.Meeting.LengthOMI
	lenPMI = conf.Meeting.LengthPMI
)

const (
	creating   = "creating"
	scheduling = "scheduling"
)

// /conference/create : starting a meeting & schedule meeting
func CreateConference(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)
	// get params
	var params = make(map[string]interface{})
	var schedule string
	c.BindJSON(&params)
	pmiSetting := entity.QueryPmiSetting(&entity.PmiSetting{PMI: user.PMI})
	if pmiSetting.PMI == "" {
		c.JSON(500, gin.H{
			"message": "Internal error.",
		})
		return
	}
	params["salt"] = pmiSetting.Salt
	if val, ok := params["type"].(string); ok {
		schedule = val
	} else {
		schedule = creating
	}
	// if schedule ?
	if schedule == creating {
		// check if this user has hosted meeting
		owner := entity.QueryConference(&entity.Conference{HostID: user.Uid, Status: entity.Launching})
		if owner.ConferenceID != "" {
			//it indicates that some people are in this conference. status: launching
			c.JSON(200, gin.H{
				"message":      "Some people are in your confenrece.",
				"conferenceid": owner.ConferenceID,
			})
			return
		}
	}

	var username string
	if username = user.GetPublicField("fn").(string); username == "" {
		username, _ = entity.EncodeUser(user.Uid)
	}
	// the onwer does not have any launching conference. status: closed & pending
	// schedule a conference
	if params["subject"] == "" || params["subject"] == nil {
		params["subject"] = username + "的快速会议"
	} else if censor != nil {
		cres, _ := censor.TextCensor(params["subject"].(string))
		if cres != nil && cres.ConclusionType == 2 {
			c.JSON(400, gin.H{
				"illegalProfile": true,
				"message":        "Illegal username or description",
			})
			return
		}

	}
	params["hostname"] = username

	rid, err := createConference(params, pmiSetting)
	if err != nil {
		c.JSON(500, gin.H{
			"message": "Internal err.",
		})
		return
	}
	c.JSON(200, gin.H{
		"message":      "Create conference sucessfully.",
		"conferenceid": rid,
	})
	return
}

func createConference(params map[string]interface{}, pmiSetting *entity.PmiSetting) (string, error) {
	password := params["password"].(string)
	subject := params["subject"]
	startedAt := params["startedAt"]
	endAt := params["endAt"]
	pmi := params["pmi"].(string)
	hostName := params["hostname"]
	var schedule string

	if val, ok := params["type"].(string); ok {
		schedule = val
	} else {
		schedule = creating
	}
	// insert into conference talbe
	// generate conferenceID
	salt := params["salt"].(string)
	var passwordCipher string
	var err error
	if password != "" {
		passwordCipher, err = util.HashPassword(password, salt)
		if err != nil {
			log.Warnf("failed to hash password %v", err)
			return "", err
		}
	}
	// how to ensure created conference id difference
	var conferenceID string
	var onEarlyJoin bool
	var onWaitingRoom bool
	if pmi != "" && schedule != scheduling {
		conferenceID = pmiSetting.PMI
		onEarlyJoin = pmiSetting.OnEarlyJoining
		onWaitingRoom = pmiSetting.OnWaitingRoom
	} else {
		conferenceID = util.RandStringBytesRmndr(lenOMI)
		for {
			conf := entity.QueryConference(&entity.Conference{ConferenceID: conferenceID})
			if conf.ConferenceID != "" {
				conferenceID = util.RandStringBytesRmndr(lenOMI)
			} else {
				break
			}
		}
		if one, ok := params["onEarlyJoin"].(bool); ok {
			onEarlyJoin = one
		}
		if one, ok := params["onWaitingRoom"].(bool); ok {
			onWaitingRoom = one
		}
	}

	// create conference & shedule conference
	var status int
	if schedule != scheduling {
		status = entity.Launching
	} else {
		status = entity.Pending
	}

	number := conferenceID + "-" + strconv.FormatInt(util.GetCurrentMs(), 10)
	number = util.GenerateHash("sha1", number)
	conference := map[string]interface{}{
		"conferenceid":  conferenceID,
		"salt":          salt,
		"password":      passwordCipher,
		"subject":       subject,
		"startedat":     startedAt,
		"endat":         endAt,
		"hostid":        pmiSetting.OwnerID,
		"hostname":      hostName,
		"status":        status,
		"number":        number,
		"onEarlyJoin":   onEarlyJoin,
		"onWaitingRoom": onWaitingRoom,
	}
	if err := entity.AddConference(conference); err != nil {
		return "", err
	}
	return conferenceID, nil
}

// /conference/history
func InfoConference(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)

	// get params
	params := proto.IndexHistory{}
	c.ShouldBind(&params)
	// TO DO : better way to query participators information and conference information
	conferenceList := entity.QueryParticipatorList(user.Uid, params.Index, params.Limit)
	var conferences []map[string]interface{}
	var numbers []string
	var numberMap = make(map[string]int)
	if len(conferenceList) <= 0 {
		c.JSON(200, gin.H{
			"message": conferences,
		})
		return
	}
	for i, conference := range conferenceList {
		temp := map[string]interface{}{
			"createdAt":    0,
			"conferenceid": "",
			"host":         "",
			"subject":      "",
			"joinAt":       conference.JoinAt,
			"leaveAt":      conference.LeaveAt,
			"duration":     conference.Duration,
			"number":       conference.Number,
		}

		numbers = append(numbers, conference.Number)
		numberMap[conference.Number] = i
		conferences = append(conferences, temp)
	}

	confInfos := entity.QueryInConference(numbers)
	for _, info := range confInfos {
		conferences[numberMap[info.Number]]["createdAt"] = info.StartedAt
		conferences[numberMap[info.Number]]["conferenceid"] = info.ConferenceID
		conferences[numberMap[info.Number]]["host"] = info.HostName
		conferences[numberMap[info.Number]]["subject"] = info.Subject
	}
	c.JSON(200, gin.H{
		"message": conferences,
	})
	return
}

// QueryHistory query conference history by conference id or subject or host name
func QueryHistory(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)
	// get params
	params := make(map[string]interface{})
	var query string
	c.ShouldBind(&params)
	if q, ok := params["query"].(string); ok {
		query = q
	}
	// TO DO : better way to query participators information and conference information
	conferenceList := entity.QueryParticipatorList(user.Uid, 0, 0)
	var conferences []map[string]interface{}
	var numbers []string
	var numberMap = make(map[string]int)
	if len(conferenceList) <= 0 {
		c.JSON(200, gin.H{
			"message": conferences,
		})
		return
	}
	for i, conference := range conferenceList {
		temp := map[string]interface{}{
			"joinAt":   conference.JoinAt,
			"leaveAt":  conference.LeaveAt,
			"duration": conference.Duration,
			"number":   conference.Number,
		}

		numbers = append(numbers, conference.Number)
		numberMap[conference.Number] = i
		conferences = append(conferences, temp)
	}

	confInfos := entity.QueryInConference(numbers)
	for _, info := range confInfos {
		if strings.Contains(info.ConferenceID, query) || strings.Contains(info.Subject, query) || strings.Contains(info.HostName, query) {
			conferences[numberMap[info.Number]]["createdAt"] = info.StartedAt
			conferences[numberMap[info.Number]]["conferenceid"] = info.ConferenceID
			conferences[numberMap[info.Number]]["host"] = info.HostName
			conferences[numberMap[info.Number]]["subject"] = info.Subject
			continue
		} else {
			index := numberMap[info.Number]
			conferences = append(conferences[:index], conferences[index+1:]...)
		}
	}
	c.JSON(200, gin.H{
		"message": conferences,
	})
	return
}

// /conference/updating
func SetConference(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)

	// get params
	var params = &proto.UpdateConference{}
	var updateErr error
	c.BindJSON(params)
	setting := params.Type
	if setting == "scheduling" {
		updateErr = updateSetting(params)
	} else if setting == "creating" {
		updateErr = updatePMI(params, user.PMI)
	} else if setting == "cancel" {
		updateErr = cancelConfenrece(params, user)
	}
	if updateErr != nil {
		c.JSON(400, gin.H{
			"message": updateErr.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"message": "update setting successfully",
	})
	return
}

// updateSetting update setting for scheduling conference
func updateSetting(params *proto.UpdateConference) error {
	password := params.Password
	conferenceid := params.ConferenceId
	if len(conferenceid) != 9 || conferenceid == "" {
		return errors.New("invalid conference id.")
	}
	conference := entity.QueryConference(&entity.Conference{ConferenceID: conferenceid})
	if conference.ConferenceID != "" && conference.Status == entity.Pending {
		var passwordCipher string
		var err error
		if password != "" {
			passwordCipher, err = util.HashPassword(password, conference.Salt)
			if err != nil {
				log.Warnf("failed to hash password %v", err)
				return err
			}
		}
		params.Password = passwordCipher
		conference.UpdateConferenceWithMap(updateParams(params))
		fmt.Println("confenrence:", conference)
	}
	return nil
}

// updatePMI update personal meeting setting
func updatePMI(params *proto.UpdateConference, pmi string) error {
	password := params.Password
	pmiSetting := entity.QueryPmiSetting(&entity.PmiSetting{PMI: pmi})
	if pmiSetting.PMI == "" {
		log.Warnf("server internal err")
		return errors.New("server internal err")
	}
	salt := pmiSetting.Salt
	var passwordCipher string
	var err error
	if password != "" {
		passwordCipher, err = util.HashPassword(password, salt)
		if err != nil {
			log.Warnf("failed to hash password %v", err)
			return err
		}
	}
	params.Password = passwordCipher
	pmiSetting.UpdatePmiSettingWithMap(updatePmiPatams(params, pmi))
	return err
}

func cancelConfenrece(params *proto.UpdateConference, user *entity.User) error {
	if params.Number != "" {
		// delete conference history closed
		part := entity.QueryParticipator(&entity.Participator{ParticipatorID: user.Uid, Number: params.Number})
		if part.ConferenceID != "" && part.Status == entity.Closed {
			part.Status = entity.Deleted
			part.UpdateParticipator(*part)
			return nil
		}
	} else if params.ConferenceId != "" {
		// cancel scheduled and no starting conference
		con := entity.QueryConference(&entity.Conference{ConferenceID: params.ConferenceId})
		if con.HostID == user.Uid && con.Status == entity.Pending {
			con.Status = entity.Closed
			con.UpdateConference(*con)
			return nil
		}
	}
	return errors.New("Invalid conference id")
}

// if gorm update filed from 1 to 0 ,it need type 'map' not 'struct'
func updateParams(params *proto.UpdateConference) map[string]interface{} {
	update := make(map[string]interface{})
	update["conference_id"] = params.ConferenceId
	update["password"] = params.Password
	update["on_early_joining"] = params.OnEarlyJoin
	update["on_waiting_room"] = params.OnWaitingRoom
	update["subject"] = params.Subject
	update["started_at"] = params.StartedAt
	update["end_at"] = params.EndAt
	return update
}

func updatePmiPatams(params *proto.UpdateConference, pmi string) map[string]interface{} {
	update := make(map[string]interface{})
	update["PMI"] = pmi
	update["password"] = params.Password
	update["on_early_joining"] = params.OnEarlyJoin
	update["on_waiting_room"] = params.OnWaitingRoom
	return update
}

// NOT WORK
// cancel scheduled conference
func CancelConference(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)
	var params *proto.UpdateConference
	c.BindJSON(params)
	if err := cancelConfenrece(params, user); err != nil {
		c.JSON(400, gin.H{
			"message": err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"message": "cancel scheduled conference successfully",
	})
	return
}
