package controller

import (
	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"gorm.io/datatypes"
	"log"
	"time"
)

var (
	OrderPending = proto.OrderPending
	OrderPaid    = proto.OrderPaid
	OrderExpired = proto.OrderExpired
	OrderFailed  = proto.OrderFailed

	UserNormal  = proto.UserNormal
	UserVip     = proto.UserVip
	UserBlocked = proto.UserBlocked

	ServiceVip = proto.ServiceVip

	WechatPay = proto.WechatPay
	AliPay    = proto.AliPay
	ApplePay  = proto.ApplePay
)

func CreateOrder(c *gin.Context) {
	topupParam := &proto.TopupParam{}
	c.Should<PERSON>ind<PERSON>ody<PERSON>ith(topupParam, binding.JSON)
	if topupParam.User == "" {
		c.JSON(400, gin.H{
			"message": "Miss account to be topped up.",
		})
		return
	}
	if topupParam.Duration <= 0 || topupParam.Duration > 12 {
		c.JSON(400, gin.H{
			"message": "Invalid duration.",
		})
		return
	}

	query := &proto.UserQuery{
		Uid:   topupParam.User,
		Phone: topupParam.User,
		Email: topupParam.User,
	}

	user := entity.QueryUsrOrCondition(query)

	if user == nil {
		c.JSON(404, gin.H{
			"message": "User doesn't exist",
		})
		return
	}

	if *user.Type == UserBlocked {
		c.JSON(403, gin.H{
			"message": "Not Allow",
		})
		return
	}
	clientIp := c.ClientIP()
	amount := topupParam.Duration * conf.Payment.VipMonthFee
	order := entity.QueryOrder(&entity.Order{TopupAccount: user.Uid, RequestIP: clientIp, Status: &OrderPending, Amount: amount})
	if order.ID != 0 {
		// the expired time of wechat url is 2 hour
		codeUrlExpiredAt := order.CreatedAt.Add((2*60 - 10) * time.Minute)
		curTime := util.TimeNow()
		if curTime.Before(codeUrlExpiredAt) && order.CodeUrl != "" {
			orderId := util.String32(entity.EncodeUid(order.ID))
			c.JSON(200, gin.H{"orderId": orderId, "codeUrl": order.CodeUrl})
			return
		} else {
			order.Update(entity.Order{Status: &OrderExpired})
		}
	}

	if topupParam.Duration == 12 {
		amount = conf.Payment.VipYearFee
	}
	newOrder := &entity.Order{
		TopupAccount: user.Uid,
		Method:       &WechatPay,
		Amount:       amount,
		Duration:     topupParam.Duration,
		ServiceType:  &ServiceVip,
		Status:       &OrderPending,
		RequestIP:    clientIp,
	}
	if err := newOrder.CreateOrder(); err != nil {
		c.JSON(500, gin.H{
			"message": "Failed to create order",
		})
		return
	}
	res, err := wechatPay.CreateOrder(newOrder)
	if err != nil {
		c.JSON(500, gin.H{
			"message": "Failed to start wechat pay",
		})
		return
	}
	if err := newOrder.Update(entity.Order{CodeUrl: res.CodeUrl}); err != nil {
		wechatPay.CloseOrder(newOrder.ID)
		c.JSON(500, gin.H{
			"message": "failed to update wechatpay code url",
		})
		return
	}
	c.JSON(200, res)
	return
}

func WechatPayNotify(c *gin.Context) {
	notifyParam := &proto.WechatPayNotifyParam{}
	c.ShouldBindBodyWith(notifyParam, binding.JSON)
	log.Println("Print WechatPay notification trigger")
	log.Printf("%+v\n", notifyParam)
	notification, err := wechatPay.DecryptNotification(notifyParam.Resource.AssociatedData, notifyParam.Resource.Nonce, notifyParam.Resource.Ciphertext)
	if err != nil {
		c.JSON(500, gin.H{"code": "FAIL", "message": "failed to decrypt notification"})
		return
	}
	log.Println("Print WechatPay notification cipher resource")
	log.Printf("%+v\n", notification)

	var uid uint64
	if uid, err = util.ParseUid32(notification.OutTradeNo); err != nil {
		log.Println("Error: failed to parse order id")
		c.JSON(500, gin.H{"return_code": "FAIL", "message": "Internal error"})
		return
	}

	order := entity.QueryOrder(&entity.Order{ID: entity.DecodeUid(uid)})
	if order.ID == 0 {
		log.Println("Error: failed to query order")
		order.Update(entity.Order{Status: &OrderFailed})
		c.JSON(404, gin.H{"code": "FAIL", "message": "Invalid out_trade_no"})
		return
	}
	if notifyParam.EventType != "TRANSACTION.SUCCESS" && *order.Status != OrderPaid {
		order.Update(entity.Order{Status: &OrderFailed})
		log.Println("Error: transactions failed")
		c.JSON(400, gin.H{"code": "FAIL", "message": "Trancation fail"})
		return
	}
	if *order.Status == proto.OrderPaid {
		c.JSON(200, gin.H{"code": "SUCCESS", "message": "Payment success"})
		return
	}

	if order.Amount != notification.Amount.PayerTotal {
		order.Update(entity.Order{Status: &OrderFailed})
		log.Println("Error: invalid payment")
		c.JSON(400, gin.H{"code": "FAIL", "message": "Invalid payment"})
		return
	}

	topupUsr := entity.QueryUser(&entity.User{Uid: order.TopupAccount})
	if topupUsr.ID == 0 {
		order.Update(entity.Order{Status: &OrderFailed})
		log.Println("Error: order topups an invalid user")
		c.JSON(400, gin.H{"code": "FAIL", "message": "Invalid payment"})
		return
	}

	var newExpiredTime time.Time
	curTime := util.TimeNow()
	if !topupUsr.VipExpiredAt.IsZero() && curTime.Before(topupUsr.VipExpiredAt) {
		newExpiredTime = topupUsr.VipExpiredAt.AddDate(0, order.Duration, 0)
	} else {
		newExpiredTime = curTime.AddDate(0, order.Duration, 0)
	}
	access, err := topupUsr.GetAccess()
	if err != nil {
		order.Update(entity.Order{Status: &OrderFailed})
		log.Printf("Error: failed to get user access %v\n", err.Error())
		c.JSON(400, gin.H{"code": "FAIL", "message": "Internal error"})
		return
	}
	access.Auth.Update(proto.ModeMeeting, true)
	acsByte, err := access.Value()
	if err != nil {
		order.Update(entity.Order{Status: &OrderFailed})
		log.Printf("Error: failed to serialize user access %v\n", err.Error())
		c.JSON(400, gin.H{"code": "FAIL", "message": "Internal error"})
		return
	}

	if err = topupUsr.UpdateUser(entity.User{VipExpiredAt: newExpiredTime, Type: &UserVip, Access: datatypes.JSON(acsByte)}); err != nil {
		order.Update(entity.Order{Status: &OrderFailed})
		log.Printf("Error: failed to update user access %v\n", err.Error())
		c.JSON(400, gin.H{"code": "FAIL", "message": "Internal error"})
		return
	}
	order.Update(entity.Order{Status: &OrderPaid, PaidAt: curTime, TransactionID: notification.TransactionID})
	c.JSON(200, gin.H{"code": "SUCCESS", "message": "Success"})
	return
}

func QueryOrder(c *gin.Context) {
	orderId := c.Param("id")
	if orderId == "" {
		c.JSON(400, gin.H{"message": "miss order ID"})
		return
	}
	uid, err := util.ParseUid32(orderId)
	if err != nil {
		log.Println(err.Error())
		c.JSON(400, gin.H{"message": "failed to parse order id"})
		return
	}
	order := entity.QueryOrder(&entity.Order{ID: entity.DecodeUid(uid)})
	if order.ID == 0 {
		c.JSON(404, gin.H{"message": "order doesn't exist"})
		return
	}
	orderStatus := order.Status.MarshalText()
	expiredTime := order.CreatedAt.Add(2 * time.Hour)
	curTime := util.TimeNow()
	if expiredTime.Before(curTime) && *order.Status == proto.OrderPending {
		orderStatus = proto.OrderExpired.MarshalText()
	}
	c.JSON(200, gin.H{"orderId": orderId, "status": orderStatus})
	return
}

func QueryOrderUser(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(400, gin.H{"message": "miss query ID"})
		return
	}
	query := &proto.UserQuery{
		Uid:   id,
		Phone: id,
		Email: id,
	}

	user := entity.QueryUsrOrCondition(query)
	if user == nil || user.ID == 0 {
		c.JSON(404, gin.H{
			"message": "User doesn't exist",
		})
		return
	}
	res := gin.H{
		"type": user.Type.MarshalText(),
		"user": id,
	}

	if !user.VipExpiredAt.IsZero() && user.VipExpiredAt.After(util.TimeNow()) && *user.Type != proto.UserBlocked {
		res["vipExpiredAt"] = user.VipExpiredAt.Unix()
	} else if *user.Type == proto.UserVip {
		res["type"] = proto.UserNormal.MarshalText()
	}
	c.JSON(200, res)
	return
}

func QueryOrderCategory(c *gin.Context) {
	c.JSON(200, gin.H{
		"year":  conf.Payment.VipMonthFee,
		"month": conf.Payment.VipYearFee,
	})
	return
}
