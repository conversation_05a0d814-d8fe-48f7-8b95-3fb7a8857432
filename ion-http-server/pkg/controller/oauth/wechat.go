package oauth

import (
	"errors"
	"fmt"
)

const (
	acsTokenUrl       = "https://api.weixin.qq.com/sns/oauth2/access_token"
	wechatAuthUrl     = "https://api.weixin.qq.com/sns/auth"
	wechatUserInfoUrl = "https://api.weixin.qq.com/sns/userinfo"
)

type Wechat struct {
	AppID     string
	AppSecret string
}

func NewWechatOauth(id, secret string) *Wechat {
	return &Wechat{
		AppID:     id,
		AppSecret: secret,
	}
}
func (w *Wechat) GetAccessToken(code string) (string, string) {
	if code == "" {
		return "", ""
	}
	url := fmt.Sprintf("%v?appid=%v&secret=%v&code=%v&grant_type=authorization_code", acsTokenUrl, w.AppID, w.AppSecret, code)
	reps, err := hc.Get(url, nil, nil)
	if err != nil {
		return "", ""
	}

	_, hasErr := reps["errcode"]
	token, hasToken := reps["access_token"].(string)
	openid, hasOpenid := reps["openid"].(string)
	if !hasToken || !hasOpenid || hasErr {
		return "", ""
	}

	return token, openid
}

func (w *Wechat) GetUserByToken(accessToken, openid string) (map[string]interface{}, error) {
	if accessToken == "" || openid == "" {
		return nil, errors.New("Invalid Parameters")
	}
	url := fmt.Sprintf("%v?access_token=%v&openid=%v", wechatUserInfoUrl, accessToken, openid)
	reps, err := hc.Get(url, nil, nil)
	if err != nil {
		return nil, err
	}

	if _, hasErr := reps["errcode"]; hasErr {
		return nil, errors.New(reps["errmsg"].(string))
	}
	return reps, nil
}
