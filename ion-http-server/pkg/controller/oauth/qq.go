package oauth

import (
	"errors"
	"fmt"
)

const (
	qqAcsTokenUrl    = "https://graph.qq.com/oauth2.0/token?grant_type=authorization_code"
	qqOpenidUrl      = "https://graph.qq.com/oauth2.0/me"
	qqUserInfoUrl    = "https://graph.qq.com/user/get_user_info"
	defQqRedirectUrl = "http://127.0.0.1:5000/api/v1/oauth/qq/login"
)

type QQ struct {
	AppID       string
	AppSecret   string
	RedirectUrl string
}

func NewQqOauth(id, secret, redirectUrl string) *QQ {
	if redirectUrl == "" {
		redirectUrl = defQqRedirectUrl
	}
	return &QQ{
		AppID:       id,
		AppSecret:   secret,
		RedirectUrl: redirectUrl,
	}
}

func (qq *QQ) GetUserInfoByID(token, openid string) (map[string]interface{}, error) {
	if openid == "" || token == "" {
		return nil, errors.New("empty parameters")
	}
	url := fmt.Sprintf("%v?access_token=%v&oauth_consumer_key=%v&openid=%v", qqUserInfoUrl, token, qq.AppID, openid)
	reps, err := hc.Get(url, nil, nil)
	if err != nil {
		return nil, err
	}
	repsCode := fmt.Sprintf("%v", reps["ret"])
	if repsCode != "0" {
		return nil, errors.New("failed to get qq user info.")
	}
	return reps, nil
}

func (qq *QQ) GetUserByToken(token string) (map[string]interface{}, error) {
	if token == "" {
		return nil, errors.New("Invalid token")
	}
	openid := qq.GetOpenID(token)
	if openid == "" {
		return nil, errors.New("Invalid openid")
	}
	return qq.GetUserInfoByID(token, openid)
}

func (qq *QQ) GetAcsTokenUrl(code string) string {
	if code == "" {
		return ""
	}
	url := fmt.Sprintf("%v?client_id=%v&client_id=%v&code=%v&redirect_url=%v", qqAcsTokenUrl, qq.AppID, qq.AppSecret, qq.RedirectUrl)
	return url
}

func (qq *QQ) GetOpenID(accessToken string) string {
	if accessToken == "" {
		return ""
	}
	url := fmt.Sprintf("%v?access_token=%v", qqOpenidUrl, accessToken)
	reps, err := hc.Get(url, nil, nil)
	if err != nil {
		return ""
	}
	_, hasErr := reps["errcode"]
	openid, hasOpenid := reps["openid"]
	if !hasOpenid || hasErr {
		return ""
	}
	return openid.(string)

}
