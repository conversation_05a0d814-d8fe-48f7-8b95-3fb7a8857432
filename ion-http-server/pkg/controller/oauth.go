package controller

import (
	"github.com/Young666666Jian/ion-http-server/pkg/controller/oauth"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"log"
)

func WechatLogin(c *gin.Context) {
	params := &proto.WechatSignup{}
	c.ShouldBindBodyWith(params, binding.JSON)
	code := params.Code
	if code == "" {
		log.Printf("no code")
		c.JSON(400, gin.H{
			"message": "invalid code.",
		})
		return
	}
	if params.Platform == "" || params.Device == "" {
		c.JSON(400, gin.H{
			"message": "invalid params.",
		})
		return
	}
	wechat := oauth.NewWechatOauth(WechatAppID, WechatAppSecret)
	acsToken, openid := wechat.GetAccessToken(code)

	if acsToken == "" || openid == "" {
		c.JSON(400, gin.H{
			"message": "failed to query access_token.",
		})
		return
	}
	user := entity.QueryUser(&entity.User{Wechat: openid})
	if user.Uid == 0 {
		//create user
		wechatUser, err := wechat.GetUserByToken(acsToken, openid)
		if err != nil {
			c.JSON(400, gin.H{
				"message": "failed to query wechat user info.",
			})
			return
		}
		userInfo := map[string]interface{}{
			"username": wechatUser["nickname"],
			"public": map[string]interface{}{
				"avatarUrl": wechatUser["headimgurl"],
			},
			"wechat": openid,
		}
		user, err = entity.AddUser(userInfo)
		if err != nil {
			log.Printf(err.Error())
			c.JSON(400, gin.H{
				"message": "failed to create user.",
			})
			return
		}
	}

	clientIpCipher := util.EncryptString(c.ClientIP())
	dev := &entity.Device{
		Deviceid: params.Device,
		Ip:       clientIpCipher,
		Platform: util.PlatformFromUA(params.Platform),
		Userid:   user.Uid,
	}
	err := dev.InsertOrUpdate()
	if err != nil {
		log.Printf(err.Error())
		c.JSON(400, gin.H{
			"message": "failed to update device info.",
		})
		return
	}
	// send jwt to client
	getTokenAndSetCookies(c, user, dev)
	return
}

func QQLogin(c *gin.Context) {
	params := &proto.QQSignup{}
	c.ShouldBindBodyWith(params, binding.JSON)
	openid := params.Openid
	token := params.AccessToken
	if openid == "" || token == "" {
		c.JSON(400, gin.H{
			"message": "invalid params.",
		})
		return
	}

	if params.Platform == "" || params.Device == "" {
		c.JSON(400, gin.H{
			"message": "invalid params.",
		})
		return
	}

	qq := oauth.NewQqOauth(QQAppID, QQAppSecret, "")
	user := entity.QueryUser(&entity.User{Qq: openid})
	if user.Uid == 0 {
		//create user
		qqUser, err := qq.GetUserInfoByID(token, openid)
		if err != nil {
			c.JSON(400, gin.H{
				"message": "failed to query qq user info.",
			})
			return
		}
		var avatarUrl interface{}
		// not all user has QQ 100x100 avatar, but must have 40x40 avatar
		if val, ok := qqUser["figureurl_qq_2"].(string); ok && val != "" {
			avatarUrl = qqUser["figureurl_qq_2"]
		} else {
			avatarUrl = qqUser["figureurl_qq_1"]
		}
		userInfo := map[string]interface{}{
			"username": qqUser["nickname"],
			"public": map[string]interface{}{
				"avatarUrl": avatarUrl,
			},
			"qq": openid,
		}
		user, err = entity.AddUser(userInfo)
		if err != nil {
			c.JSON(400, gin.H{
				"message": "failed to query access_token.",
			})
			return
		}
	}
	clientIpCipher := util.EncryptString(c.ClientIP())
	dev := &entity.Device{
		Deviceid: params.Device,
		Ip:       clientIpCipher,
		Platform: util.PlatformFromUA(params.Platform),
		Userid:   user.Uid,
	}
	err := dev.InsertOrUpdate()
	if err != nil {
		c.JSON(400, gin.H{
			"message": "failed to update device info.",
		})
		return
	}
	// send jwt to client
	getTokenAndSetCookies(c, user, dev)
	return
}
