package controller

import (
	"net/http"
	"time"

	"github.com/Young666666Jian/ion-http-server/pkg/authemail"
	"github.com/Young666666Jian/ion-http-server/pkg/authphone"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func checkEmail(c *gin.Context, email string) bool {
	// email format
	if ok := util.CheckEmailFormat(email); ok != true {
		c.JSON(400, gin.H{
			"message": "this email format is invalid.",
		})
		return false
	}
	// // email validity check
	// if ok := util.CheckEmailValidity(email); ok != true {
	// 	c.JSON(400, gin.H{
	// 		"message": "this email is invalid.",
	// 	})
	// 	return false
	// }
	return true
}

func checkPhone(c *gin.Context, phone string) bool {
	// check mobile phone number format
	if ok := util.VerifyMobileFormat(phone); ok != true {
		c.JSON(400, gin.H{
			"message": "this mobile phone number format is invalid.",
		})
		return false
	}
	return true
}

func sendEmailCode(c *gin.Context, email string) bool {
	status, dist := authemail.SendAuthCode(email)
	switch status {
	case authemail.SEND_CODE_SUCESSFULLY:
		c.JSON(200, gin.H{
			"message": "send auth code with email sucessfully.",
		})
		return true
	case authemail.SEND_EMAIL_FREQUENTLY:
		c.JSON(400, gin.H{
			"message":  "request auth code too frequently.",
			"distance": dist,
		})
		return false
	case authemail.SEND_TOO_MUCH_FAILURE:
		c.JSON(400, gin.H{
			"message": "it has been failed five times.",
		})
		return false
	}
	return false
}

func sendPhoneCode(c *gin.Context, phone string) bool {
	status, dist := authphone.SendAuthCode(phone)
	switch status {
	case authphone.SEND_CODE_SUCESSFULLY:
		c.JSON(200, gin.H{
			"message": "send auth code with phone sucessfully.",
		})
		return true
	case authphone.SEND_PHONE_FREQUENTLY:
		c.JSON(400, gin.H{
			"message":  "request auth code too frequently.",
			"distance": dist,
		})
		return false
	case authphone.SEND_TOO_MUCH_FAILURE:
		c.JSON(400, gin.H{
			"message": "it has been failed ten times.",
		})
		return false
	case authphone.FAILED_TO_SEND:
		c.JSON(400, gin.H{
			"message": "failed to send auth code.",
		})
		return false
	}
	return false
}

func checkEmailCode(c *gin.Context, email, authcode string) bool {
	// check the authcode status
	status := authemail.CheckEmailCode(email, authcode)
	switch status {
	case authemail.NO_AUTHCODE:
		c.JSON(400, gin.H{
			"message": "No auth code! Please request your auth code.",
		})
		return false
	case authemail.TOO_MUCH_FAILURE:
		c.JSON(400, gin.H{
			"message": "it has been failed five times.",
		})
		return false
	case authemail.INVALID_AUTHCODE:
		c.JSON(400, gin.H{
			"message": "auth code have been expired.",
		})
		return false
	case authemail.VERIFY_CODE_SUCCESSFULLY:
		return true
	case authemail.VERIFY_CODE_UNSUCCESSFULLY:
		c.JSON(400, gin.H{
			"message": "verify Unsuccessfully.",
		})
		return false
	}
	return false
}

func checkPhoneCode(c *gin.Context, phone, authcode string) bool {
	// check the status of auth-code
	status := authphone.CheckPhoneCode(phone, authcode)
	switch status {
	case authphone.NO_AUTHCODE:
		c.JSON(400, gin.H{
			"message": "No auth code! Please request your auth code.",
		})
		return false
	case authphone.TOO_MUCH_FAILURE:
		c.JSON(400, gin.H{
			"message": "it has been failed too much times.",
		})
		return false
	case authphone.INVALID_AUTHCODE:
		c.JSON(400, gin.H{
			"message": "auth code have been expired.",
		})
		return false
	case authphone.VERIFY_CODE_SUCCESSFULLY:
		return true
	case authphone.VERIFY_CODE_UNSUCCESSFULLY:
		c.JSON(400, gin.H{
			"message": "verify Unsuccessfully.",
		})
		return false
	}
	return false
}

func register(c *gin.Context, signupCmd *proto.SignupCommand) (bool, *entity.User) {
	// register
	salt := uuid.New().String()

	passwordCipher, err := util.HashPassword(signupCmd.Password, salt)

	if err != nil {
		log.Warnf("failed to hash password %v", err)
		c.JSON(400, gin.H{
			"message": "Failed to hash password.",
		})
		return false, nil
	}

	params := map[string]interface{}{
		"email":    signupCmd.Email,
		"password": passwordCipher,
		"salt":     salt,
	}

	user, addErr := entity.AddUser(params)
	if addErr != nil {
		c.JSON(400, gin.H{
			"message": "failed to register user info.",
		})
		return false, nil
	}
	return true, user
}

func registerNoPassword(c *gin.Context, signupCmd *proto.SignupCommand) (bool, *entity.User) {
	salt := uuid.New().String()
	params := map[string]interface{}{
		"email":    signupCmd.Email,
		"phone":    signupCmd.Phone,
		"password": "",
		"salt":     salt,
	}
	// log.Infof("%v", params)
	user, addErr := entity.AddUser(params)
	if addErr != nil {
		c.JSON(400, gin.H{
			"message": "failed to register user info.",
		})
		return false, nil
	}
	return true, user
}

func isEmailRegistered(c *gin.Context, email string) bool {
	// encryptEmail := util.EncryptString(email)
	user := entity.QueryUser(&entity.User{Email: email})
	// check if the email was registered
	if user.Email != "" {
		c.JSON(400, gin.H{
			// "message": "this email has already registered.",
			"message": "该邮箱已被注册",
		})
		return false
	}
	return true
}

func isPhoneRegistered(c *gin.Context, phone string) bool {
	user := entity.QueryUser(&entity.User{Phone: phone})
	// check if the email was registered
	if user.Phone != "" {
		c.JSON(400, gin.H{
			"message": "this phone number has already registered.",
		})
		return false
	}
	return true
}

func getTokenAndSetCookies(c *gin.Context, user *entity.User, devinfo *entity.Device) {
	user.Salt = ""
	user.Password = ""
	cookie := entity.GenerateCookie(user, devinfo)
	token, expired, err := JwtMiddleware.TokenGenerator(cookie)
	if err != nil {
		c.JSON(400, gin.H{
			"message": "generator token fail.",
		})
		return
	}
	maxage := int(expired.Unix() - time.Now().Unix())
	c.SetCookie(
		JwtMiddleware.CookieName,
		token,
		maxage,
		"/",
		JwtMiddleware.CookieDomain,
		JwtMiddleware.SecureCookie,
		JwtMiddleware.CookieHTTPOnly,
	)
	JwtMiddleware.LoginResponse(c, http.StatusOK, token, expired)
	return
}
