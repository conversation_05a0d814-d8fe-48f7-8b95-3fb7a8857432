package controller

import (
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/log"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// /signup : signup with email
func SignupEndpoint(c *gin.Context) {

	signupCmd := &proto.SignupCommand{}
	c.<PERSON><PERSON><PERSON><PERSON><PERSON>ith(signupCmd, binding.JSON)
	// not null
	if signupCmd.Email == "" || signupCmd.Password == "" || signupCmd.AuthCode == "" {
		c.JSON(400, gin.H{
			"message": "email, authcode and password are required.",
		})
		return
	}
	if signupCmd.Device == "" {
		c.JSON(400, gin.H{
			"message": "miss device info.",
		})
		return
	}

	// check email validity
	if !checkEmail(c, signupCmd.Email) {
		return
	}

	// check if the email was registered
	encryptEmail := util.EncryptString(signupCmd.Email)
	if !isEmailRegistered(c, encryptEmail) {
		return
	}

	// check the authcode status
	if !checkEmailCode(c, signupCmd.Email, signupCmd.AuthCode) {
		return
	}
	// register
	signupCmd.Email = encryptEmail
	if ok, _ := register(c, signupCmd); !ok {
		return
	}
	// auto-login
	JwtMiddleware.LoginHandler(c)
	return
}

// /phone/signin: signup or signin with phone-code
func SignupWithPhone(c *gin.Context) {

	signupCmd := &proto.SignupCommand{}
	c.BindJSON(signupCmd)
	if signupCmd.Phone == "" || signupCmd.AuthCode == "" {
		c.JSON(400, gin.H{
			"message": "phone and authcode are required.",
		})
		return
	}
	if signupCmd.Device == "" {
		c.JSON(400, gin.H{
			"message": "miss device info.",
		})
		return
	}

	// check mobile phone number format
	if !checkPhone(c, signupCmd.Phone) {
		return
	}

	// check the status of auth-code
	if !checkPhoneCode(c, signupCmd.Phone, signupCmd.AuthCode) {
		return
	}

	encryptPhone := util.EncryptString(signupCmd.Phone)
	user := entity.QueryUser(&entity.User{Phone: encryptPhone})
	// check if the phone nunmber has been registered
	if user.Phone == "" {
		var ok bool
		// first time register
		signupCmd.Phone = encryptPhone
		ok, user = registerNoPassword(c, signupCmd)
		if !ok {
			return
		}
	}

	if signupCmd.Password != "" {
		salt := user.Salt
		passwordCipher, err := util.HashPassword(signupCmd.Password, salt)

		if err != nil {
			log.Warnf("failed to hash password %v", err)
			c.JSON(400, gin.H{
				"message": "Failed to hash password.",
			})
			return
		}
		user.Password = passwordCipher
		user.UpdateUser(*user)
	}
	clientIpCipher := util.EncryptString(c.ClientIP())
	dev := &entity.Device{
		Deviceid: signupCmd.Device,
		Ip:       clientIpCipher,
		Platform: util.PlatformFromUA(signupCmd.Platform),
		Userid:   user.Uid,
	}
	err := dev.InsertOrUpdate()
	if err != nil {
		c.JSON(400, gin.H{
			"message": "failed to update device info.",
		})
		return
	}
	// send jwt to client
	getTokenAndSetCookies(c, user, dev)
}

func SignupWithEmail(c *gin.Context) {
	signupCmd := &proto.SignupCommand{}
	c.BindJSON(signupCmd)
	if signupCmd.Email == "" || signupCmd.AuthCode == "" {
		c.JSON(400, gin.H{
			"message": "phone and authcode are required.",
		})
		return
	}
	if signupCmd.Device == "" {
		c.JSON(400, gin.H{
			"message": "miss device info.",
		})
		return
	}

	// check email format
	if !checkEmail(c, signupCmd.Email) {
		return
	}

	// check the status of auth-code
	if !checkEmailCode(c, signupCmd.Email, signupCmd.AuthCode) {
		return
	}

	encryptEmail := util.EncryptString(signupCmd.Email)
	user := entity.QueryUser(&entity.User{Email: encryptEmail})
	// check if the phone nunmber has been registered
	if user.Email == "" {
		var ok bool
		// first time register
		signupCmd.Email = encryptEmail
		ok, user = registerNoPassword(c, signupCmd)
		if !ok {
			return
		}
	}

	if signupCmd.Password != "" {
		salt := user.Salt
		passwordCipher, err := util.HashPassword(signupCmd.Password, salt)

		if err != nil {
			log.Warnf("failed to hash password %v", err)
			c.JSON(400, gin.H{
				"message": "Failed to hash password.",
			})
			return
		}
		user.Password = passwordCipher
		user.UpdateUser(*user)
	}
	clientIpCipher := util.EncryptString(c.ClientIP())
	dev := &entity.Device{
		Deviceid: signupCmd.Device,
		Ip:       clientIpCipher,
		Platform: util.PlatformFromUA(signupCmd.Platform),
		Userid:   user.Uid,
	}
	err := dev.InsertOrUpdate()
	if err != nil {
		c.JSON(400, gin.H{
			"message": "failed to update device info.",
		})
		return
	}
	// send jwt to client
	getTokenAndSetCookies(c, user, dev)
}

// /email/code: send auth code with email and check if the email was registered
// the email account can not be registered on signup
func SendEmail(c *gin.Context) {

	// must finish them and then send auth code
	signupCmd := &proto.SignupCommand{}
	c.BindJSON(signupCmd)
	if signupCmd.Email == "" {
		c.JSON(400, gin.H{
			"message": "email is required.",
		})
		return
	}
	// check email validity
	if !checkEmail(c, signupCmd.Email) {
		return
	}

	// // check if the email was registered
	// if !isEmailRegistered(c, signupCmd) {
	// 	return
	// }
	// return status of sending auth code
	sendEmailCode(c, signupCmd.Email)
}

// WIP
// /email/code-nocheck: send auth code with email and check if the email was registered
// the email can be Not registered on the logining status
func SendEmailOnlogining(c *gin.Context) {
	var params = make(map[string]string)
	c.BindJSON(&params)
	email := params["email"]
	// check email validity
	if !checkEmail(c, email) {
		return
	}

	registered := entity.QueryUser(&entity.User{Email: util.EncryptString(email)})
	// check if the email was registered
	if registered.Email != "" {
		c.JSON(400, gin.H{
			"message": "this email has registered yet.",
		})
		return
	}
	// return status of sending auth code
	sendEmailCode(c, email)
}

// /phone/code: send auth code with phone on signup & signin
func SendPhone(c *gin.Context) {
	// must finish them and then send auth code
	signupCmd := &proto.SignupCommand{}
	c.BindJSON(signupCmd)
	if signupCmd.Phone == "" {
		c.JSON(400, gin.H{
			"message": "phone number is required.",
		})
		return
	}

	// check mobile phone number format
	if !checkPhone(c, signupCmd.Phone) {
		return
	}

	// return status of sending auth code
	sendPhoneCode(c, signupCmd.Phone)
}

// /update/password : update password with authcode
func UpdatePassword(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)

	// check password
	var params = make(map[string]string)
	c.BindJSON(&params)
	pw := params["password"]
	authcode := params["authcode"]
	method := params["method"]

	if pw == "" || authcode == "" || method == "" {
		c.JSON(400, gin.H{
			"message": "Miss params",
		})
		return
	}
	if method == "phone" {
		// check the status of auth-code
		decryptPhone := util.DecryptString(user.Phone)
		if !checkPhoneCode(c, decryptPhone, authcode) {
			return
		}
	} else if method == "email" {
		// check the authcode status
		decryptEmail := util.DecryptString(user.Email)
		if !checkEmailCode(c, decryptEmail, authcode) {
			return
		}
	} else {
		c.JSON(401, gin.H{
			"message": "no this method. Only allow email or phone method",
		})
		return
	}

	salt := user.Salt
	passwordCipher, err := util.HashPassword(pw, salt)

	if err != nil {
		log.Warnf("failed to hash password %v", err)
		c.JSON(400, gin.H{
			"message": "Failed to hash password.",
		})
		return
	}
	user.Password = passwordCipher
	user.UpdateUser(*user)
	c.JSON(200, gin.H{
		"message": "update password successfully.",
	})
	return
}

// /update/email : bind email
func UpdateEmail(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)
	// check if user had bound email
	if user.Email != "" {
		c.JSON(400, gin.H{
			"message": "this user has bound email.",
		})
		return
	}
	var params = make(map[string]string)
	c.BindJSON(&params)
	email := params["email"]
	authcode := params["authcode"]

	if email == "" || authcode == "" {
		c.JSON(400, gin.H{
			"message": "Miss params",
		})
		return
	}
	// check email validity
	if !checkEmail(c, email) {
		return
	}

	// check if the email was registered
	encryptEmail := util.EncryptString(email)
	if !isEmailRegistered(c, encryptEmail) {
		return
	}

	// check the authcode status
	if !checkEmailCode(c, email, authcode) {
		return
	}

	user.Email = encryptEmail
	user.UpdateUser(*user)
	tag := map[string]interface{}{
		"tag":    "email:" + user.Email,
		"userid": user.Uid,
	}
	entity.AddUsertag(tag)
	c.JSON(200, gin.H{
		"message": "bind email successfully.",
	})
	return
}

func UpdatePhone(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)
	// check if user had bound phone number
	if user.Phone != "" {
		c.JSON(400, gin.H{
			"message": "this user has bound phone number.",
		})
		return
	}
	var params = make(map[string]string)
	c.BindJSON(&params)
	phone := params["phone"]
	authcode := params["authcode"]
	if phone == "" || authcode == "" {
		c.JSON(400, gin.H{
			"message": "Miss params",
		})
		return
	}
	// check phone validity
	if !checkPhone(c, phone) {
		return
	}

	// check if the phone was registered
	encryptPhone := util.EncryptString(phone)
	if !isPhoneRegistered(c, encryptPhone) {
		return
	}

	// check the authcode status
	if !checkPhoneCode(c, phone, authcode) {
		return
	}

	user.Phone = encryptPhone
	user.UpdateUser(*user)

	usertag := map[string]interface{}{
		"userid": user.Uid,
		"tag":    "tel:+" + user.Phone,
	}
	entity.AddUsertag(usertag)
	c.JSON(200, gin.H{
		"message": "bind phone number successfully.",
	})
	return
}

// WIP
// /update/password : modified password with old password
func UpdatedPasswordWithOldpassword(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)
	// check if user have the password
	if user.Password == "" {
		c.JSON(400, gin.H{
			"message": "this user has Not got the password.",
		})
		return
	}
	// check input password that the old one and the new one
	// map[oldPossword] map[newPassword]
	var password = make(map[string]string)
	c.BindJSON(&password)
	oldpw := password["oldPassword"]
	newpw := password["newPassword"]
	if oldpw == "" || newpw == "" {
		c.JSON(400, gin.H{
			"message": "the password can not be null.",
		})
		return
	}
	// check if password is ok
	pwdHash := user.Password
	salt := user.Salt
	if !util.CheckPasswordHash(oldpw, salt, pwdHash) {
		c.JSON(400, gin.H{
			"message": "wrong password.",
		})
		return
	}

	// create new password
	NewpasswordCipher, err := util.HashPassword(newpw, salt)
	if err != nil {
		log.Warnf("failed to hash password %v", err)
		c.JSON(400, gin.H{
			"message": "failed to hash password.",
		})
		return
	}
	user.Password = NewpasswordCipher
	user.UpdateUser(*user)
	c.JSON(200, gin.H{
		"message": "updated password successfully.",
	})
	return
}

// WIP
// /cancel/phone : cancel Phone number
func CancelPhone(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)

	if user.Email == "" {
		c.JSON(400, gin.H{
			"message": "the email and phone account can not be canceled at the same time.",
		})
		return
	}
	// check if the phone number exist
	if user.Phone != "" {
		params := map[string]interface{}{
			"Phone": nil,
		}
		user.UpdateUserWithMap(params)
		c.JSON(200, gin.H{
			"message": "cancel phone number successfully.",
		})
		return
	} else {
		c.JSON(400, gin.H{
			"message": "no phone number.",
		})
		return
	}
}

// WIP
// /cancel/email : cancel email
func CancelEmail(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)
	if user.Phone == "" {
		c.JSON(400, gin.H{
			"message": "the email and phone account Can not be canceled at the same time.",
		})
		return
	}
	// check if the email exist
	if user.Email != "" {
		params := map[string]interface{}{
			"Email": nil,
		}
		user.UpdateUserWithMap(params)
		c.JSON(200, gin.H{
			"message": "cancel email successfully.",
		})
		return
	} else {
		c.JSON(400, gin.H{
			"message": "No email.",
		})
		return
	}
}
