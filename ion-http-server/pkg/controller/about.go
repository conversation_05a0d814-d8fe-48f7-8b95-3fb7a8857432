package controller

import (
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/gin-gonic/gin"
)

// /feedback
func FeedbackMessage(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)
	m := proto.FeedackMessage{}
	c.BindJSON(&m)
	var err error
	if m.Feedback != "" {
		err = createFeedback(user, m.Feedback)
	} else if m.Reports != "" {
		err = createReport(user, m.Reports)
	} else {
		c.JSON(400, gin.H{
			"message": "please input messages",
		})
		return

	}
	if err != nil {
		c.JSON(500, gin.H{
			"message": "internal error.",
		})
		return
	}
	c.JSON(200, gin.H{
		"message": "thank you for your contribution.",
	})
}

func createFeedback(user *entity.User, message string) error {
	var username string
	if username = user.GetPublicField("fn").(string); username == "" {
		username, _ = entity.EncodeUser(user.Uid)
	}
	f := &entity.Feedback{
		Uid:      user.Uid,
		Username: username,
		Message:  message,
	}
	return f.SaveFeedback()
}

func createReport(user *entity.User, message string) error {
	var username string
	if username = user.GetPublicField("fn").(string); username == "" {
		username, _ = entity.EncodeUser(user.Uid)
	}
	r := &entity.Report{
		Uid:      user.Uid,
		Username: username,
		Message:  message,
	}
	return r.SaveReport()
}

// /api/v1/version
func GetVersion(c *gin.Context) {
	v, desc := entity.GetLatestVersion()
	if v == "" {
		c.JSON(500, gin.H{
			"message": "internal error.",
		})
		return
	} else {
		c.JSON(200, gin.H{
			"message": desc,
			"version": v,
		})
		return
	}
}
