package controller

import (
	b64 "encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime"
	"path/filepath"
	"strconv"
	"strings"

	conf "github.com/Young666666Jian/ion-http-server/pkg/conf/gin"
	"github.com/Young666666Jian/ion-http-server/pkg/entity"
	"github.com/Young666666Jian/ion-http-server/pkg/proto"
	"github.com/Young666666Jian/ion-http-server/pkg/storage"
	"github.com/Young666666Jian/ion-http-server/pkg/util"
	"github.com/gin-gonic/gin"
	"github.com/h2non/filetype"
	"gorm.io/datatypes"
)

var (
	domainName  = conf.Domain.Name
	templateDir = "/templates/avatar/"
)

// /user/profile
func ChangeProfile(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)

	var params = make(map[string]string)
	c.BindJSON(&params)
	username := params["username"]
	desc := params["description"]

	if censor != nil {
		cres, _ := censor.TextCensor(username + "," + desc)
		if cres != nil && cres.ConclusionType == 2 {
			c.JSON(400, gin.H{
				"illegalProfile": true,
				"message":        "illegal username or description",
			})
			return
		}
	}

	public, err := user.Public.Value()
	m := make(map[string]interface{})
	err = json.Unmarshal(public.([]byte), &m)
	m["description"] = desc
	m["fn"] = username
	publicByte, err := json.Marshal(m)
	if err != nil {
		fmt.Println(err.Error())
		c.JSON(400, gin.H{
			"message": "failed to update avatar info of user",
		})
		return
	}
	user.UpdateUser(entity.User{Public: datatypes.JSON(publicByte)})
	c.JSON(200, gin.H{
		"username":    username,
		"description": desc,
		"message":     "update user profile successfully.",
	})
}

// /user : get user info
func GetUserEndpoint(c *gin.Context) {
	// get user info from middleware
	fmt.Println(c.ClientIP())
	user := c.MustGet("user").(*entity.User)
	uidUin64 := entity.EncodeUid(user.Uid)
	uidBase64, err := util.UidMarshalText(uidUin64)
	if err != nil {
		c.JSON(200, gin.H{
			"message": "encode uid err",
		})
	}
	device := c.MustGet("device").(*entity.Device)
	clientIpCipher := util.EncryptString(c.ClientIP())
	if device.Ip != clientIpCipher {
		device.UpdateDevice(entity.Device{Ip: clientIpCipher})
	}
	var avatarUrl string
	urlObj := user.GetPublicField("avatarUrl")

	if _, ok := urlObj.(string); ok {
		avatarUrl = urlObj.(string)
	}

	if !strings.HasPrefix(avatarUrl, "http") {
		avatarUrl = domainName + avatarUrl
	}
	var description string
	public, err := user.Public.Value()
	m := make(map[string]interface{})
	json.Unmarshal(public.([]byte), &m)
	if desc, ok := m["description"].(string); ok {
		description = desc
	}
	var username string
	if username = user.GetPublicField("fn").(string); username == "" {
		username = "usr" + string(uidBase64)
	}
	access, _ := user.GetAccess()
	res := gin.H{
		"uid":         "usr" + string(uidBase64),
		"username":    username,
		"avatarUrl":   avatarUrl,
		"email":       util.DecryptString(user.Email),
		"phone":       util.DecryptString(user.Phone),
		"pmi":         user.PMI,
		"description": description,
		"access":      access,
		"type":        user.Type.MarshalText(),
	}
	if !user.VipExpiredAt.IsZero() && user.VipExpiredAt.After(util.TimeNow()) && *user.Type != proto.UserBlocked {
		res["vipExpiredAt"] = user.VipExpiredAt.Unix()
	} else if *user.Type == proto.UserVip {
		res["type"] = proto.UserNormal.MarshalText()
	}

	c.JSON(200, res)
	return
}

func UploadAvatarEndpoint(c *gin.Context) {
	// get user info from middleware
	user := c.MustGet("user").(*entity.User)

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(400, gin.H{
			"message": "failed to save avatar",
		})
		return
	}

	buff := make([]byte, header.Size)
	if _, err = file.Read(buff); err != nil {
		c.JSON(400, gin.H{
			"message": "invalid avatar file type",
		})
		return
	}

	if censor != nil {
		imageBase64 := b64.StdEncoding.EncodeToString(buff)
		cres, _ := censor.ImageCensor(imageBase64)
		if cres != nil && cres.ConclusionType == 2 {
			c.JSON(400, gin.H{
				"illegalImage": true,
				"message":      "illegal image",
			})
			return
		}
	}

	kind, _ := filetype.Match(buff)
	if kind == filetype.Unknown || (kind.MIME.Value != "image/png" && kind.MIME.Value != "image/jpeg") {
		c.JSON(400, gin.H{
			"message": "invalid avatar file type",
		})
		return
	}
	fmt.Printf("File type: %s. MIME: %s\n", kind.Extension, kind.MIME.Value)

	if header.Size > proto.MaxImgSize || header.Size <= 0 {
		c.JSON(400, gin.H{
			"message": "image size exceed limit",
		})
		return
	}

	if _, err = file.Seek(0, io.SeekStart); err != nil {
		c.JSON(400, gin.H{
			"message": "failed to file seek",
		})
		return
	}
	fileInfo := &entity.Fileupload{
		Userid:   user.Uid,
		Mimetype: kind.MIME.Value,
		Size:     header.Size,
		Type:     proto.FilePermanent,
	}

	fileurl, err := fileStore.Upload(file, fileInfo)

	if err != nil {
		c.JSON(400, gin.H{
			"message": "failed to upload file.",
		})
		return
	}

	if !strings.HasPrefix(fileurl, "http") {
		fileurl = "/api/v1/avatar/" + fileurl
	}
	oldUrl := user.GetPublicField("avatarUrl")
	err = user.UpdateUsrPublic("avatarUrl", fileurl)
	if err != nil {
		delfiles := []string{fileInfo.Location}
		fileStore.Delete(delfiles)
		c.JSON(400, gin.H{
			"message": "failed to update user avatar url",
		})
		return
	}
	// cannot delete default avatar and ones from second party
	if url, ok := oldUrl.(string); ok && storage.IsDeletable(url) {
		//Or we can mark the previous upload as 'deleted' then make system delete it
		rawUrl := util.StripQueryString(url)
		oldFileName := filepath.Base(rawUrl)
		fid, _ := storage.GetFileId(oldFileName)
		if fid != 0 {
			oldFile := entity.QueryFileUpload(&entity.Fileupload{ID: fid})
			delfiles := []string{oldFile.Location}
			fileStore.Delete(delfiles)
			oldFile.UpdateFileUpload(entity.Fileupload{Type: proto.FileTemperal})
		}
	}

	if !strings.HasPrefix(fileurl, "http") {
		fileurl = domainName + fileurl
	}
	c.JSON(200, gin.H{
		"avatarUrl": fileurl,
	})
	return
}

func GetUserAvatar(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(400, gin.H{"message": "empty file name"})
		return
	}
	extension := filepath.Ext(filename)
	mimeType := mime.TypeByExtension(extension)
	if mimeType != "image/jpeg" && mimeType != "image/png" {
		c.JSON(400, gin.H{"message": "invalid extension"})
		return
	}

	fileinfo, file, err := fileStore.Download(filename)
	if err != nil {
		fmt.Println(err.Error())
	}
	if err != nil || fileinfo == nil || fileinfo.ID == 0 {
		c.JSON(404, gin.H{"message": "not found"})
		return
	}
	defer file.Close()
	contentLength := fileinfo.Size
	contentType := fileinfo.Mimetype

	extraHeaders := map[string]string{
		"Content-Disposition": fmt.Sprintf(`attachment; filename="%v"`, filename),
	}

	c.DataFromReader(200, contentLength, contentType, file, extraHeaders)
}

func QueryMessages(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	user := c.Query("user")
	topic := c.Query("topic")
	seqStr := c.DefaultQuery("seqid", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(400, gin.H{"message": "invalid limit value"})
		return
	}
	seqid, err := strconv.Atoi(seqStr)
	if err != nil {
		c.JSON(400, gin.H{"message": "invalid start index value"})
		return
	}
	if seqid < 0 {
		c.JSON(400, gin.H{"message": "invalid start index value"})
		return
	}
	query := &proto.MessageQuery{}
	if user != "" {
		query.User = user
	}

	if topic != "" {
		query.Topic = topic
	}

	if seqid > 0 {
		query.Seqid = seqid
	}
	query.Limit = limit
	messages, err := entity.RawQuerySensitiveMessages(query)
	if err != nil {
		c.JSON(400, gin.H{"message": "failed to query messages"})
	}
	c.JSON(200, gin.H{"items": messages})
	return
}
