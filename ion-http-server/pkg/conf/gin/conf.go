package conf

import (
	"flag"
	"fmt"
	"os"

	"github.com/spf13/viper"
)

const (
	portRangeLimit = 100
)

var (
	cfg       = config{}
	Global    = &cfg.Global
	Database  = &cfg.Database
	Auth      = &cfg.Auth
	Log       = &cfg.Log
	Email     = &cfg.Email
	Phone     = &cfg.Phone
	Domain    = &cfg.Domain
	Meeting   = &cfg.Conference
	MediaFile = &cfg.MediaConfig
	Oauth     = &cfg.Oauth
	Tls       = &cfg.Tls
	Version   = &cfg.Version
	Utils     = &cfg.Utils
	Censor    = &cfg.Censor
	Payment   = &cfg.Payment
)

func init() {
	if !cfg.parse() {
		showHelp()
		os.Exit(-1)
	}
}

type log struct {
	Level string `mapstructure:"level"`
}

type global struct {
	Pprof string `mapstructure:"pprof"`
	Dc    string `mapstructure:"dc"`
	Mode  string `mapstructure:"mode"`
	// TestIP []string `mapstructure:"testip"`
}

type database struct {
	Name   string `mapstructure:"name"`
	Addrs  string `mapstructure:"addrs"`
	UidKey string `mapstructure:"uidKey"`
}

type auth struct {
	SecretKey string `mapstructure:"secretKey"`
}

type email struct {
	TestSender string `mapstructure:"testSender"`
	SmtpHost   string `mapstructure:"smtpHost"`
	SmtpPort   int    `mapstructure:"smtpPort"`
	Epassword  string `mapstructure:"Epassword"`
	Expired    int    `mapstructure:"expired"`
	Frequence  int    `mapstructure:"frequence"`
	MaxError   int    `mapstructure:"maxError"`
}

type phone struct {
	ClientId     string `mapstructure:"ClientId"`
	ClientSecret string `mapstructure:"ClientSecret"`
	Expired      int    `mapstructure:"expired"`
	Frequence    int    `mapstructure:"frequence"`
	MaxError     int    `mapstructure:"maxError"`
}

type domain struct {
	Name string `mapstructure:"name"`
}

type conference struct {
	LengthPMI int `mapstructure:"LengthPMI"`
	LengthOMI int `mapstructure:"LengthOMI"`
}

type mediaConfig struct {
	// The name of the handler to use for file uploads.
	UseHandler string `mapstructure:"use_handler"`
	// Individual handler config params to pass to handlers unchanged.
	Handlers map[string]interface{} `mapstructure:"handlers"`
}

type Oauthconfig struct {
	WechatAppSecret string `mapstructure:"wechatAppSecret"`
	WechatAppID     string `mapstructure:"wechatAppID"`
	QQAppID         string `mapstructure:"qqAppID"`
	QQAppSecret     string `mapstructure:"qqAppSecret"`
}

type oauth struct {
	Config map[string]Oauthconfig
}
type tls struct {
	Tls  bool   `mapstructure:"tls"`
	Cert string `mapstructure:"cert"`
	Key  string `mapstructure:"key"`
}

type DeploymentVersion struct {
	AppVersion        string `mapstructure:"app_version"`
	Description       string `mapstructure:"description"`
	IonVersion        string `mapstructure:"ion_version"`
	TinodeVersion     string `mapstructure:"tinode_version"`
	UserSystemVersion string `mapstructure:"user_system_version"`
}

type utils struct {
	KeyText string `mapstructure:"keytext"`
}

type censor struct {
	Enable         bool   `mapstructure:"enable"`
	Provider       string `mapstructure:"provider"`
	AccessTokenUrl string `mapstructure:"access_token_url"`
	CensorUrl      string `mapstructure:"censor_url"`
	ApiKey         string `mapstructure:"api_key"`
	Secret         string `mapstructure:"secret_key"`
	AccessToken    string `mapstructure:"access_token"`
}

type payment struct {
	Currency    string    `mapstructure:"currency"`
	VipMonthFee int       `mapstructure:"vip_month_fee"`
	VipYearFee  int       `mapstructure:"vip_year_fee"`
	WechatPay   wechatpay `mapstructure:"wechatpay"`
}

type wechatpay struct {
	Enable           bool   `mapstructure:"enable"`
	MerchantID       string `mapstructure:"merchantID"`
	CertSerialNumber string `mapstructure:"certSerialNumber"`
	PrivateKeyPath   string `mapstructure:"privateKeyPath"`
	CertFilePath     string `mapstructure:"certFilePath"`
	ApiV3Key         string `mapstructure:"apiv3_key"`
}

type config struct {
	Global      global            `mapstructure:"global"`
	Database    database          `mapstructure:"database"`
	Log         log               `mapstructure:"log"`
	Auth        auth              `mapstructure:"auth"`
	Email       email             `mapstructure:"email"`
	Phone       phone             `mapstructure:"phone"`
	Domain      domain            `mapstructure:"domain"`
	Conference  conference        `mapstructure:"conference"`
	MediaConfig mediaConfig       `mapstructure:"media"`
	Oauth       oauth             `mapstructure:"oauth"`
	Tls         tls               `mapstructure:"tls"`
	Version     DeploymentVersion `mapstructure:"version"`
	Utils       utils             `mapstructure:"utils"`
	Censor      censor            `mapstructure:"censor"`
	Payment     payment           `mapstructure:"payment"`
	CfgFile     string
}

func showHelp() {
	fmt.Printf("Usage:%s {params}\n", os.Args[0])
	fmt.Println("      -c {config file}")
	fmt.Println("      -h (show help info)")
}

func (c *config) load() bool {
	_, err := os.Stat(c.CfgFile)
	if err != nil {
		return false
	}

	viper.SetConfigFile(c.CfgFile)
	viper.SetConfigType("toml")

	err = viper.ReadInConfig()
	if err != nil {
		fmt.Printf("config file %s read failed. %v\n", c.CfgFile, err)
		return false
	}
	err = viper.GetViper().UnmarshalExact(c)
	if err != nil {
		fmt.Printf("config file %s loaded failed. %v\n", c.CfgFile, err)
		return false
	}

	fmt.Printf("config %s load ok!\n", c.CfgFile)
	return true
}

func (c *config) parse() bool {
	flag.StringVar(&c.CfgFile, "c", "conf/conf.toml", "config file")
	help := flag.Bool("h", false, "help info")
	flag.Parse()
	if !c.load() {
		return false
	}

	if *help {
		showHelp()
		return false
	}
	return true
}
