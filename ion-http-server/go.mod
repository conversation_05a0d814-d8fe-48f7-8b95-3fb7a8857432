module github.com/Young666666Jian/ion-http-server

go 1.13

require (
	github.com/aliyun/aliyun-oss-go-sdk v2.1.7+incompatible
	github.com/aliyun/aliyun-sts-go-sdk v0.0.0-20171106034748-98d3903a2309
	github.com/appleboy/gin-jwt/v2 v2.6.3
	github.com/aws/aws-sdk-go v1.29.29
	github.com/badoux/checkmail v0.0.0-20200623144435-f9f80cb795fa
	github.com/gin-contrib/cors v1.3.1
	github.com/gin-gonic/gin v1.6.3
	github.com/go-playground/validator/v10 v10.3.0 // indirect
	github.com/go-sql-driver/mysql v1.5.0
	github.com/golang/protobuf v1.3.5 // indirect
	github.com/google/uuid v1.1.1
	github.com/h2non/filetype v1.1.0
	github.com/jinzhu/gorm v1.9.14
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.10 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/mitchellh/mapstructure v1.3.2
	github.com/niemeyer/pretty v0.0.0-20200227124842-a10e7caefd8e // indirect
	github.com/pelletier/go-toml v1.4.0 // indirect
	github.com/rs/zerolog v1.19.0
	github.com/spf13/viper v1.7.0
	github.com/tinode/snowflake v1.0.0
	github.com/wechatpay-apiv3/wechatpay-go v0.2.0
	golang.org/x/crypto v0.0.0-20200622213623-75b288015ac9
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gopkg.in/check.v1 v1.0.0-20200227125254-8fa46927fb4f // indirect
	gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df
	gopkg.in/ini.v1 v1.62.0 // indirect
	gorm.io/datatypes v0.0.0-20200806042100-bc394008dd0d
)
