## Database Model Design



### Overview

i. [User Information Table](#user-information-table)

ii. [Email AuthCode Table](#email-authcode-table)

iii. [Phone AuthCode Table](#phone-authcode-table)



### User Information Table

To Save user information in this table.



#### Description

| Field        | Type         | Null | Key     | Default | Extra          |
| ------------ | ------------ | ---- | ------- | ------- | -------------- |
| `id`         | integer      | NO   | primary | NULL    | auto_increment |
| `created_at` | datetime     | YES  |         | NULL    |                |
| `updated_at` | datetime     | YES  |         | NULL    |                |
| `deleted_at` | datetime     | YES  |         | NULL    |                |
| `uid`        | varchar(255) | NO   |         | NULL    | unique         |
| `username`   | varchar(255) | YES  |         | NULL    |                |
| `email`      | varchar(100) | YES  |         | NULL    | unique         |
| `phone`      | varchar(100) | YES  |         | NULL    | unique         |
| `password`   | varchar(255) | YES  |         | NULL    |                |
| `salt`       | varchar(255) | NO   |         | NULL    |                |
| `permission` | varchar(255) | NO   |         | 1       |                |
| `avatar_url` | varchar(255) | YES  |         | NULL    |                |

- `uid` : user id
- `password` : save something that have encrypted password
- `salt` : enctrypt password with `salt`
- `permission` : permission level of user( contraints user to use specific resource in system)
- `avatar_url` : avatar address on file system



### Email AuthCode Table

To save authcode and check authcode with it.



#### Description

| Field        | Type         | Null | Key     | Default | Extra          |
| ------------ | ------------ | ---- | ------- | ------- | -------------- |
| `id`         | integer      | NO   | primary | NULL    | auto_increment |
| `created_at` | datetime     | YES  |         | NULL    |                |
| `updated_at` | datetime     | YES  |         | NULL    |                |
| `deleted_at` | datetime     | YES  |         | NULL    |                |
| `email`      | varchar(100) | NO   |         | NULL    | unique         |
| `authcode`   | varchar(100) | NO   |         | NULL    |                |
| `count`      | integer      | NO   |         | NULL    |                |

- `updated_at` : finish timer function using `updated_at` and GetCurrentTime() function. 
- `count` : finish counter function that if verify authcode unsuccessfully , `count` will plus one.
- `authcode` : use sending authcode with email API and save this code in `authcode` field. query table with email and Verify code if timer and counter is ok.



### Phone AuthCode Table

To save authcode and check authcode with it.



#### Description

| Field        | Type         | Null | Key     | Default | Extra          |
| ------------ | ------------ | ---- | ------- | ------- | -------------- |
| `id`         | integer      | NO   | primary | NULL    | auto_increment |
| `created_at` | datetime     | YES  |         | NULL    |                |
| `updated_at` | datetime     | YES  |         | NULL    |                |
| `deleted_at` | datetime     | YES  |         | NULL    |                |
| `phone`      | varchar(100) | NO   |         | NULL    | unique         |
| `authcode`   | varchar(100) | NO   |         | NULL    |                |
| `count`      | integer      | NO   |         | NULL    |                |

- `updated_at` : finish timer function using `updated_at` and GetCurrentTime() function. 
- `count` : finish counter function that if verify authcode unsuccessfully , `count` will plus one.
- `authcode` : use sending authcode with phone API and save this code in `authcode` field. query table with email and Verify code if timer and counter is ok.

#### Version table

| Field                 | Type         | Null | Key     | Default | Extra          |
| --------------------- | ------------ | ---- | ------- | ------- | -------------- |
| `id`                  | integer      | NO   | primary | NULL    | auto_increment |
| `created_at`          | datetime     | YES  |         | NULL    |                |
| `updated_at`          | datetime     | YES  |         | NULL    |                |
| `deleted_at`          | datetime     | YES  |         | NULL    |                |
| `production_version`  | char(10)     | YES  |         |         |                |
| `description`         | varchar(255) | YES  |         | NULL    |                |
| `ion_version`         | char(10)     | NO   |         |         |                |
| `tinode_version`      | char(10)     | NO   |         |         |                |
| `user_system_version` | char(10)     | NO   |         |         |                |