events {
    accept_mutex on;
    multi_accept on;
    #use epoll;
    worker_connections  1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format myFormat '$remote_addr–$remote_user [$time_local] $request $status $body_bytes_sent $http_referer $http_user_agent $http_x_forwarded_for';
    sendfile on;
    sendfile_max_chunk 100k;
    keepalive_timeout 65;

    server {
        rewrite_log on;
        keepalive_requests 120;
        listen       20200;
        server_name  127.0.0.1;
        location / {
            # (should be in same network)https://container-name:port/
            proxy_pass https://fangyu-usersystem:20000/;
            proxy_redirect     off;
            proxy_set_header   Host             $host;
            proxy_set_header   X-Real-IP        $remote_addr;
            proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
        }
    }
}