## User-System : Login & authenticate v0.0.1

### Overview:

i. [Send AuthCode With Email](#send-authcode-with-email)

ii. [Send AuthCode With Phone](#send-authcode-with-phone)

iii. [Signup With Email](#signup-with-email)

iv. [Signup With Phone](#signup-with-phone)

v. [Signin With Account](#signin-with-account)

vi. [Singout](#signout)

vii. [Update Password](#update-password)

viii. [Update Email](#update-email)

ix. [Update Phone](#update-phone)

x. [Refresh Token](#refresh-token)

### Send AuthCode With Email

Send authcode to this `email`, and corresponding email box will receive the authcode. ~~But this api only is used when signup and update email. Beacuse it checks whether this `email` had been registered or not.~~

```
POST /api/v1/email/code
```



#### Post Body

| Name    | Required | Type     | Explanation                   |
| ------- | -------- | -------- | ----------------------------- |
| `email` | `True`   | `String` | Send authcode to this `email` |



#### Response

Status: 200 OK

```json
{
    "message": "send auth code with email sucessfully.",
    "distance": 23
}
```

- distance: return the distance of the next time sending authcode.



#### Error Response

| Status Code | Response                                            | Cause                                                        |
| ----------- | --------------------------------------------------- | ------------------------------------------------------------ |
| 400         | `{"message": "email is required."}`                 | `email`  does not supported null value                       |
| 400         | `{"message": "this email format is invalid."}`      | `email` format is invalid                                    |
| 400         | `{"message": "this email is invalid."}`             | domain or user identity of this `email` is invalid           |
| ~~400~~         | ~~`{"message": "this email has already registered."}`~~ | ~~`email` has been already registered~~                          |
| 400         | `{"message": "request auth code too frequently."}`  | Within a  minute, can not resend auth email                  |
| 400         | `{"message": "it has been failed five times."}`     | Failure code auth has already been over five times wiithin a day |



#### Examples & Usage:

```shell
curl -i "http://localhost:5000/api/v1/email/code" -H "Accept: application/json" -H "Content-type: application/json" -X POST -d '{"email":"<EMAIL>"}'
```



### Send AuthCode With Phone

Send authcode to this `phone` number, and mobile wiith this phone number will receive the authcode. This api can be used to signup or signin with phone-authcode.

```
POST /api/v1/phone/code
```



#### Post Body

| Name    | Required | Type     | Explanation                                                  |
| ------- | -------- | -------- | ------------------------------------------------------------ |
| `phone` | `True`   | `String` | To send authcode to this `phone` with SMS(Short Message Service) |

#### Response

Status: 200 OK

```json
{
  "message":"send auth code with phone sucessfully.",
  "distance":23
}
```

- distance: return the distance of the next time sending authcode.



#### Error Response

| Status Code | Response                                                     | Cause                                                     |
| ----------- | ------------------------------------------------------------ | --------------------------------------------------------- |
| 400         | `{"message": "phone number is required."}`                   | `phone` number does not supported null value              |
| 400         | `{"message": "this mobile phone number format is invalid."}` | this `phone`  number format is invalid                    |
| 400         | `{"message": "request auth code too frequently."}`           | Within a minute can not resend auth code                  |
| 400         | `{"message": "it has been failed ten times."}`               | Within a day, code verification failure is over ten times |
| 400         | `{"message": "failed to send auth code."}`                   | third-party service provider return this error            |



#### Examples & Usage:

```shell
curl -i "http://localhost:5000/api/v1/phone/code" -H "Accept: application/json" -H "Content-type: application/json" -X POST -d '{"phone":"phone number"}'
```



### Signup

signup with email and login automatically

```
POST /api/v1/signup
```



#### Post Body

| Name       | Required | Type     | Explanation                          |
| ---------- | -------- | -------- | ------------------------------------ |
| `email`    | `True`   | `String` | To signup with this email as account |
| `password` | `True`   | `String` | To signup with this password         |
| `authcode` | `True`   | `String` | To verify this `email`               |

#### Response

Status: 200 OK

```json
{
  "code":200,
  "expire":"2020-08-01T13:22:41+08:00",
 "token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVcmwiOiIiLCJlbWFpbCI6IjM2MzE1Mzc3OUBxcS5jb20iLCJleHAiOjE1OTYyNTkzNjEsImlkIjoiNjgxNjc3TViNWU4MTRmNWRmMGVmYWM1YTg1MzQyZTM1NzRhOWRhNjY4MTlkODE3YjU3N2Y5ZDJiYWZhMSIsIm9yaWdfaWF0IjoxNTk2MjU1NzYxLCJ1aWQiOiI2ODE2NzcxMGZhNWI1ZTgxNGY1ZGYwZWZhYzVhODUzNDJlMzU3NGE5ZGE2NMTdiNTc3ZjlkMmJhZmExIiwidXNlcm5hbWUiOiIifQ.6MvN2zmGLmJLlRjCE7mU0_X297mwC79EtWtZIZp6hdA"
}
```



#### Error Response

| Status Code | Response                                                     | Cause                                                        |
| ----------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 400         | `{"message": "email, authcode and password are required."}`  | `email`, `authcode`, `password` not supported null           |
| 400         | `{"message": "this email format is invalid."}`               | this `email` format is not supported                         |
| 400         | `{"message": "this email is invalid."}`                      | domain or user identity of this `email` is invalid           |
| 400         | `{"message": "this email has already registered."}`          | `email` has already registered                               |
| 400         | `{"message": "No auth code! Please request your auth code."}` | first time verifying authcode without requesting  authcode will response this error |
| 400         | `{"message": "it has been failed five times."}`              | Within a day, no more than five errors                       |
| 400         | `{"message": "auth code have been expired."}`                | setting five minutes effective time                          |
| 400         | `{"message": "verify Unsuccessfully."}`                      | Verify `authcode` failedly                                   |
| 400         | `{"message": "Failed to hash password."}`                    | make `password` hash error                                   |
| 400         | `{"message": "failed to register user info."}`               | failed to insert user info record into user table            |



#### Examples & Usage:

```shell
curl -i "http://localhost:5000/api/v1/signup" -X POST -d '{"email":"<EMAIL>", "password":"1234", "authcode":"507165"}'
```



### Signup With Phone

Signup & signin with phone-authcode.When user first time uses, and this api will register your account and login.If your phone had registered before, you can login directly.

```
POST /api/v1/phone/signup
```



#### Post Body

| Name       | Required | Type     | Explanation                        |
| ---------- | -------- | -------- | ---------------------------------- |
| `phone`    | `True`   | `String` | phone num used to signin or signup |
| `authcode` | `True`   | `String` | auth code sent to  `phone` num     |



#### Response

Status: 200 OK

```json
{
    "code": 200,
    "expire": "2020-08-01T07:06:03Z",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************.3VRlxDkFQwW8WeFLkItUDkPswPBv9MzN5d63Wl7lqC8"
}
```



#### Error Response

| Status Code | Response                                                     | Cause                                                        |
| ----------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 400         | `{"message": "phone and authcode are required."}`            | `phone` and `authcode` do not supported null value           |
| 400         | `{"message": "this mobile phone number format is invalid."}` | this `phone`  number format is valid                         |
| 400         | `{"message": "No auth code! Please request your auth code."}` | first time verifying authcode without requesting  authcode will response this error |
| 400         | `{"message": "it has been failed too much times."}`          | Within a day, auth code varification failed over 5 times     |
| 400         | `{"message": "auth code have been expired."}`                | Auth code is expired                                         |
| 400         | `{"message": "verify Unsuccessfully."}`                      | Verify `authcode` failedly                                   |
| 400         | `{"message": "failed to register user info."}`               | failed to insert user info record into user table            |



#### Examples & Usage:

```shell
curl -i "http://localhost:5000/api/v1/phone/signup" -X POST -d '{"phone":"phone number", "authcode":"auth-code"}'
```



### Signin With Account

Signin in with email or phone number and password. If it uses phone number as account, passoword need to be added before. 

```
POST /api/v1/signin
```



#### Post Body

| Name       | Required | Type     | Explanation            |
| ---------- | -------- | -------- | ---------------------- |
| `email`    |          | `String` | using emial as account |
| `phone`    |          | `String` | using phone as account |
| `password` | `True`   | `String` | signin with password   |



#### Response

Status : 200 OK

```json
{
    "code": 200,
    "expire": "2020-08-01T07:29:49Z",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVcmwiOiIiLCJlbWFpbCI6IiIsImV4cCI6MTU5NjI2Njk4OSwiaWQiOiI1YzQQiOjE1OTYyNjMzODksInVpZCI6IjVjNDk5NDFkYzlkMDdmMDdjMDZmNWEzMTljNTI3Mzk1N2JjMjQyNjE3OWFiZTA4YWQ3OGQwZDg5N2U2YmFkNGQiLCJ1c2VybmFtZSI6IjEzMDAzMDY2OTAzIn0.2y3OBbgnRh1cdgowEuBuy5e4HovuSoFnihF4ekZOO2Q"
}
```



#### Error Response

| Status Code | Response                                                     | Cause                                                        |
| ----------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 401         | `{"message": "email or phone and password are required."}`   | `email` or `phone` and `authcode`,  not supported null       |
| 401         | `{"message": "this email account do not exist."}`            | this `email`  account do not exist                           |
| 401         | `{"message": "wrong password."}`                             | wrong password for this account                              |
| 401         | `{"message": "this phone account do not exist."}`            | this `phone`  account do not exist                           |
| 401         | `{"message": "this phone account do not have the password."}` | signup with phone do not have password, and need to add password for it |



#### Examples & Usage:

```shell
curl -i "http://localhost:5000/api/v1/signin" -X POST -d '{"phone":"phone-number", "password":"auth-code"}' 
```



### Signout

can be used by clients to remove the jwt cookie 

```
POST /api/v1/signout
```



#### Response 

Status: 200 OK

```json
{
    "code": 200
}
```



### Update password

Update password with authcode.

1.user has not password and adds password with this api.

2.user has password and updates password with this api.

3.user forgets password and modifies password with this api



```
POST /api/v1/update/password
```



#### Post Body

| Name       | Required | Type     | Explanation        |
| ---------- | -------- | -------- | ------------------ |
| `method`   | `True`   | `String` | `phone` or `email` |
| `password` | `True`   | `String` | pasword            |
| `authcode` | `True`   | `String` | verify code        |

#### Response

Status : 200 OK

```json
{
		"message":"update password successfully."
}
```



#### Error Response

| Status Code | Response                                                     | Cause                                                        |
| ----------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 400         | `"message": "this user is not exist."`                       | uid do not exist                                             |
| 400         | `{"message": "No auth code! Please request your auth code."}` | first time verifying authcode without requesting  authcode will response this error |
| 400         | `{"message": "it has been failed too much times."}`          | Within a day, no more than ten errors                        |
| 400         | `{"message": "auth code have been expired."}`                | setting ten minutes effective time                           |
| 400         | `{"message": "verify Unsuccessfully."}`                      | Verify `authcode` failedly                                   |
| 400         | `{"message": "No this method. Only allow email or phone method"}` | `method` only has `email` and `phone`                        |
| 400         | `{"message": "the password can not be null."}`               | `password` can not be null value                             |
| 400         | `"message": "Failed to hash password."`                      | failed to hash password                                      |



### Update Email

Now, Only allow to bind email and can not update email.

```
POST /api/v1/update/email
```



#### Post Body

| Name       | Required | Type     | Explanation   |
| ---------- | -------- | -------- | ------------- |
| `email`    | `True`   | `String` | bind  `email` |
| `authcode` | `True`   | `String` | verify code   |

#### Response

Status : 200 OK

```json
{
		"message":"bind email successfully."
}
```



#### Error Response

| Status Code | Response                                            | Cause                                              |
| ----------- | --------------------------------------------------- | -------------------------------------------------- |
| 400         | `"message": "this user is not exist."`              | uid do not exist                                   |
| 400         | `{"message": "this user has bound email",}`         | this user has bound the email                      |
| 400         | `{"message": "this email format is invalid."}`      | this `email` format is not supported               |
| 400         | `{"message": "this email is invalid."}`             | domain or user identity of this `email` is invalid |
| 400         | `{"message": "this email has already registered."}` | `email` has already registered                     |
|             | check authcode error response...                          |                                                    |



### Update Phone

Now, Only allow to bind phone and can not update phone.

```
POST /api/v1/update/phone
```



#### Post Body

| Name       | Required | Type     | Explanation   |
| ---------- | -------- | -------- | ------------- |
| `phone`    | `True`   | `String` | bind  `phone` |
| `authcode` | `True`   | `String` | verify code   |

#### Response

Status : 200 OK

```json
{
		"message":"bind phone number successfully."
}
```



#### Error Response

| Status Code | Response                                            | Cause                                |
| ----------- | --------------------------------------------------- | ------------------------------------ |
| 400         | `"message": "this user is not exist."`              | uid do not exist                     |
| 400         | `{"message": "this user has bound phone",}`         | this user has bound the phone        |
| 400         | `{"message": "this phone format is invalid."}`      | this `phone` format is not supported |
| 400         | `{"message": "this phone has already registered."}` | `phone` has already registered       |
|             | check authcode error response...                    |                                      |



### Refresh Token

refresh token with access_token from logining. When cookies expired, will response code `401`. Need to request this api with string params `token` , Or puting `token` in the `headers` and response new access_token。

```
POST /api/v1/refresh_token
```



#### Examples

```
http://localhost:5000/api/v1/refresh_token?token=eyJhbGci...
OR
http://localhost:5000/api/v1/refresh_token
PUT `token` in the headers
```



#### Response

```json
{
    "code": 200,
    "expire": "2020-08-09T22:41:29+08:00",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVcmwiOiIiLCJlbWFpbCI6IiIsImV4cCI6MTU5Njk4NDA4OSwiaWQiOiI2YWIxYjNlN2RhODc2ZGQ1OWYyYjA3MDI0YWM1MjhkYjZkMDkxZWMxMWYwZjZiMzRlZmQxYjdjOTdjZjdiODE0Iiwib3JpZ19pYXQiOjE1OTY4OTc2ODksInVpZCI6IjZhYjFiM2U3ZGE4NzZkZDU5ZjJiMDcwMjRhYzUyOGRiNmQwOTFlYzExZjBmNmIzNGVmZDFiN2M5N2NmN2I4MTQiLCJ1c2VybmFtZSI6IjEzMDAzMDY2OTAzIn0.XTJvnx045PTKMGsfOM9QoEUkCXX8ph5eAca3fSMpaIw"
}
```

#### Setting personal meeting 

提供个人会议相关的设置：密码，会议前的一些设置，比如等候室。

#### Version Control

前端提供版本号,后端检查是否有新版本发布,即返回前端当前最近的版本号

```
GET /api/v1/version
```



response
```json
{
    "message": "增加功能",
    "version": "1.0.2"
}
```