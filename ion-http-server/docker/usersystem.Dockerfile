FROM golang:1.14.4-stretch

ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.io

WORKDIR $GOPATH/src/github.com/fangyu/usersystem

COPY go.mod go.sum ./
RUN cd $GOPATH/src/github.com/fangyu/usersystem && go mod download

COPY pkg/ $GOPATH/src/github.com/fangyu/usersystem/pkg
COPY cmd/ $GOPATH/src/github.com/fangyu/usersystem/cmd

WORKDIR $GOPATH/src/github.com/fangyu/usersystem/cmd/gin
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /gin .

FROM alpine:3.12.0

RUN apk --no-cache add ca-certificates

ARG TZ='Asia/Shanghai'
ENV DEFAULT_TZ ${TZ}
RUN apk upgrade --update \
  && apk add -U tzdata \
  && cp /usr/share/zoneinfo/${DEFAULT_TZ} /etc/localtime \
  && apk del tzdata \
  && rm -rf \
  /var/cache/apk/*

COPY --from=0 /gin /usr/local/bin/gin
COPY configs/remote.toml /configs/gin.toml
COPY configs/docker/version.yaml /configs/version.yaml
COPY configs/cert/ /configs/cert
COPY configs/wechat_cert/ /configs/wechat_cert

ENTRYPOINT ["/usr/local/bin/gin"]
CMD ["-c", "/configs/gin.toml"]
