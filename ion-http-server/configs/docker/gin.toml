[global]
pprof = ":5000"

# running mode(debug or release)
mode = "release"

# data center id
dc = "dc1"

[log]
level   = "info"
# level = "debug"

[database]
# ["ip:port", "ip:port"]
uidKey = ""
name  = "mysql"
addrs = "root:password@tcp(host.docker.internal:3306)/encryptDB?parseTime=true&collation=utf8mb4_unicode_ci&loc=Local"


[auth]
secretKey = ""

[email]
testSender = "<EMAIL>"
smtpHost   = "smtp.163.com"
smtpPort   = 465
Epassword  = "TPTSYAUYBQXKJWGE"
expired    = 5
frequence  = 1
maxError   = 5

[phone]
ClientId     = ""
ClientSecret = ""
expired      = 10
frequence    = 1
maxError     = 10

[domain]
name = "http://*************:5000"

[conference]
LengthOMI = 9
LengthPMI = 10

[media]
use_handler = "fs"
[media.handlers.fs]
upload_dir = "uploads"
[media.handlers.s3]
access_key_id = ""
secret_access_key = ""
region = ""
bucket = ""
cors_origins = ["*"]

[oauth]
[oauth.config.release]
wechatAppID = ""
wechatAppSecret = ""
qqAppID = "" 
qqAppSecret = ""

[oauth.config.debug]
wechatAppID = ""
wechatAppSecret = ""
qqAppID = ""
qqAppSecret = ""


[tls]
tls = false
cert = "/configs/cert/*************.crt"
key = "/configs/cert/*************.key" 

[version]
app_version = "1.0.5"
description = "1. 优化会议视频窗口；\n2. 优化app主题界面；\n"
ion_version = "1.1.2"
tinode_version = "1.0.7"
user_system_version = "1.0.7"

[utils]
keytext = "this-is-test-codo-not-for-remote"

[censor]
enable = true
provider = "baidu"
access_token_url = "https://aip.baidubce.com/oauth/2.0/token"
censor_url = "https://aip.baidubce.com/rest/2.0/solution/v1"
api_key = ""
secret_key = ""