# version record

# on the /configs/docker/version.yaml, have better to record deployed online version only

# history version message
# test(T) or deploy(D) time: 2020-12-02 17:38(T) 2020-12-02 17:38(D) 
# version: 1.0.1
# description: 
# ion-version:1.0.4
# user-system-version: 1.0.2
# tinode-version: 1.0.1



# version
# description: 描述
v1_0_1:
  version: 1.0.1
  ion: 1.0.4
  tinode: 1.0.1
  usersystem: 1.0.2
