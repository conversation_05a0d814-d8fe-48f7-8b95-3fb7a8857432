[global]
pprof = ":5000"

# running mode(debug or release)
mode = "debug"

# data center id
dc = "dc1"

[log]
level   = "info"
# level = "debug"

[database]
# ["ip:port", "ip:port"]
uidKey = ""
name  = "mysql"
# addrs = "root:password@tcp(localhost:3306)/encryptDB?parseTime=true&collation=utf8mb4_unicode_ci&loc=Local"
addrs = "tinode:Hello123!@(localhost)/tinode?parseTime=true&collation=utf8mb4_unicode_ci&loc=Local"
# addrs = "root:password123@tcp(localhost:33333)/ion?charset=utf8&parseTime=True&loc=Local"

[auth]
secretKey = ""

[email]
testSender = "<EMAIL>"
smtpHost   = "smtp.163.com"
smtpPort   = 465
Epassword  = ""
expired    = 5
frequence  = 1
maxError   = 5

[phone]
ClientId     = ""
ClientSecret = ""
expired      = 10
frequence    = 1
maxError     = 10

[domain]
name = "http://*************:5000"

[conference]
LengthOMI = 9
LengthPMI = 10

[media]
use_handler = "oss"
[media.handlers.fs]
upload_dir = "uploads"
[media.handlers.s3]
access_key_id = ""
secret_access_key = ""
region = ""
bucket = ""
cors_origins = ["*"]
[media.handlers.oss]
sts_enable = false
access_key_id = ""
secret_access_key = ""
region = ""
endpoint = ""
bucket = ""
account_id = ""
role_name = ""

[oauth]
[oauth.config.release]
wechatAppID = ""
wechatAppSecret = ""
qqAppID = ""
qqAppSecret = ""

[oauth.config.debug]
wechatAppID = ""
wechatAppSecret = ""
qqAppID = ""
qqAppSecret = ""

[tls]
tls = false
cert = ""
key = ""

[version]
app_version = "1.0.2"
description = "新功能"
ion_version = "1.0.4"
tinode_version = "1.0.1"
user_system_version = "1.0.2"

[utils]
keytext = "this-is-test-codo-not-for-remote"

[censor]
enable = true
provider = "baidu"
access_token_url = "https://aip.baidubce.com/oauth/2.0/token"
censor_url = "https://aip.baidubce.com/rest/2.0/solution/v1"
api_key = ""
secret_key = ""

[payment]
currency = "CNY"
vip_month_fee = 1
vip_year_fee = 1
[payment.wechatpay]
enable = true
merchantID = ""
# 商户证书序列号
certSerialNumber = ""
# 商户私钥文件路径
privateKeyPath = "configs/wechat_cert/apiclient_key.pem"
# 平台证书文件路径
certFilePath = "configs/wechat_cert/platform_key.pem"
# API3 key
apiv3_key = ""