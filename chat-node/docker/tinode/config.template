{
	"listen": ":6060",
	"api_path": "/",
	"cache_control": 39600,
	"static_mount": "/",
	"grpc_listen": ":16060",
	"api_key_salt": "$API_KEY_SALT",
	"max_message_size": 4194304,
	"max_subscriber_count": 32,
	"max_tag_count": 16,
	"expvar": "/stats/expvar/",

	"media": {
		"use_handler": "$MEDIA_HANDLER",
		"max_size": 33554432,
		"gc_period": 60,
		"gc_block_size": 100,
		"handlers": {
			"fs": {
				"upload_dir": "uploads"
			},
			"s3":{
				"access_key_id": "$AWS_ACCESS_KEY_ID",
				"secret_access_key": "$AWS_SECRET_ACCESS_KEY",
				"region": "$AWS_REGION",
				"bucket": "$AWS_S3_BUCKET",
				"cors_origin": $AWS_CORS_ORIGINS
			}
		}
	},

	"tls": {
		"enabled": $TLS_ENABLED,
		"http_redirect": ":80",
		"strict_max_age": 604800,
		"autocert": {
			"cache": "/etc/letsencrypt/live/$TLS_DOMAIN_NAME",
			"email": "$TLS_CONTACT_ADDRESS",
			"domains": ["$TLS_DOMAIN_NAME"]
		}
	},

	"auth_config": {
		"logical_names": [],
		"basic": {
			"add_to_tags": true,
			"min_login_length": 3,
			"min_password_length": 6
		},
		"token": {
			"expire_in": 1209600,
			"serial_num": 1,
			"key": "$AUTH_TOKEN_KEY"
		}
	},

	"store_config": {
		"uid_key": "$UID_ENCRYPTION_KEY",
		"use_adapter": "$STORE_USE_ADAPTER",
		"adapters": {
			"mysql": {
				"database": "tinode",
				"dsn": "$MYSQL_DSN?parseTime=true&collation=utf8mb4_unicode_ci"
			},
			"rethinkdb": {
				"database": "tinode",
				"addresses": "rethinkdb"
			},
			"mongodb": {
				"database": "tinode",
				"addresses": "mongodb",
				"replica_set": "rs0"
			}
		}
	},

	"acc_validation": {
		"email": {
			"add_to_tags": true,
			"required": [$EMAIL_VERIFICATION_REQUIRED],
			"config": {
				"host_url": "$SMTP_HOST_URL",
				"smtp_server": "$SMTP_SERVER",
				"smtp_port": "$SMTP_PORT",
				"login": "$SMTP_LOGIN",
				"sender": "$SMTP_SENDER",
				"sender_password": "$SMTP_PASSWORD",
				"languages": ["en", "ru"],
				"validation_templ": "./templ/email-validation-{{.Language}}.templ",
				"reset_secret_templ": "./templ/email-password-reset-{{.Language}}.templ",
				"max_retries": 4,
				"domains": [$SMTP_DOMAINS],
				"debug_response": "$DEBUG_EMAIL_VERIFICATION_CODE"
			}
		},

		"tel": {
			"add_to_tags": true,
			"config": {
				"template": "./templ/sms-validation.templ",
				"max_retries": 4,
				"debug_response": "$DEBUG_TEL_VERIFICATION_CODE"
			}
		}
	},

	"push": [
		{
			"name":"tnpg",
			"config": {
				"enabled": $TNPG_PUSH_ENABLED,
				"token": "$TNPG_AUTH_TOKEN",
				"org": "$TNPG_USER"
			}
		},
		{
			"name":"fcm",
			"config": {
				"enabled": $FCM_PUSH_ENABLED,
				"project_id": "$FCM_PROJECT_ID",
				"credentials_file": "$FCM_CRED_FILE",
				"time_to_live": 3600,
				"android": {
					"enabled": $FCM_INCLUDE_ANDROID_NOTIFICATION,
					"icon": "ic_logo_push",
					"icon_color": "#3949AB",
					"click_action": ".MessageActivity",
					"msg": {
						"title_loc_key": "new_message",
						"title": "",
						"body_loc_key": "",
						"body": ""
					},
					"sub": {
						"title_loc_key": "new_chat",
						"body_loc_key": ""
					}
				}
			}
		}
	],

	"cluster_config": {
		"self": "",
		"nodes": [
			// Name and TCP address of each node.
			{"name": "tinode-0", "addr": "tinode-0:12000"},
			{"name": "tinode-1", "addr": "tinode-1:12001"},
			{"name": "tinode-2", "addr": "tinode-2:12002"}
		],
		"failover": {
			"enabled": true,
			"heartbeat": 100,
			"vote_after": 8,
			"node_fail_after": 16
		}
	},

	"plugins": [
		{
			"enabled": $PLUGIN_PYTHON_CHAT_BOT_ENABLED,
			"name": "python_chat_bot",
			"timeout": 20000,
			"filters": {
				"account": "C"
			},
			"failure_code": 0,
			"failure_text": null,
			"service_addr": "tcp://localhost:40051"
		}
	]

}
