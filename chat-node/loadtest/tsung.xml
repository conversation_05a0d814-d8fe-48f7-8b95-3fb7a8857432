<?xml version="1.0"?>
<!DOCTYPE tsung SYSTEM "/usr/local/share/tsung/tsung-1.0.dtd" []>
<tsung loglevel="notice" version="1.0" dumptraffic="false">
  <clients>
    <client host="localhost" use_controller_vm="true" maxusers="1000" />
  </clients>

  <servers>
    <server host="127.0.0.1" port="6060" type="tcp" />
  </servers>

  <load>
    <arrivalphase phase="1" duration="120" unit="second">
      <users maxnumber="100" arrivalrate="1" unit="second" />
    </arrivalphase>
  </load>

  <sessions>
    <session name="websocket" probability="100" type="ts_websocket">
      <setdynvars sourcetype="random_string" length="4">
        <var name="baseid" />
      </setdynvars>

      <request subst="true">
        <websocket type="connect" path="/v0/channels?apikey=AQEAAAABAAD_rAp4DJh05a1HAwFT3A6K">
          <!--http_header name="X-Tinode-APIKey" value="AQEAAAABAAD_rAp4DJh05a1HAwFT3A6K"/-->
        </websocket>
      </request>

      <request subst="true">
        <websocket type="message">{"hi":{"id":"%%_baseid%%01","ver":"0.15","ua":"Tsung-Loadtest/1.0; tsung/1.7.0"}}</websocket>
      </request>

      <request subst="true">
        <websocket type="message">{"login":{"id":"%%_baseid%%02","scheme":"basic","secret":"%%tinode:rand_user_secret%%"}}</websocket>
      </request>

      <request subst="true">
        <websocket type="message" frame="text">{"sub":{"id":"%%_baseid%%03","topic":"me","get":{"what":"desc"}}}</websocket>
      </request>

      <thinktime value="3" random="true"></thinktime>

      <request subst="true">
        <!--
        Response looks like:

        {"meta":{"id":"106691","topic":"me","ts":"2018-07-25T18:14:24.433Z","sub":[
          {"updated":"2018-07-16T01:29:25.016Z","acs":{"given":47,"want":47,"mode":47},"topic":"grpOZcxbbwE7Hc","touched":"2018-07-16T17:32:12.48Z","seq":3},{"updated":"2018-07-16T01:35:25.016Z","acs":{"given":47,"want":47,"mode":47},"topic":"grpz6jDYiygVkI","touched":"2018-07-16T17:30:32.165Z","seq":22},{"updated":"2018-07-16T02:05:25.019Z","acs":{"given":47,"want":47,"mode":47},"topic":"grp8efPpvZoUQ4","touched":"2018-07-16T17:24:34.946Z","seq":1},{"updated":"2018-07-15T17:23:25.002Z","acs":{"given":31,"want":31,"mode":31},"topic":"usrlF7rSfaEx9c","touched":"2018-07-16T17:28:42.07Z","seq":9},{"updated":"2018-07-15T23:23:25.008Z","acs":{"given":16,"want":31,"mode":16},"topic":"usrddg3AFqzeEQ"},{"updated":"2018-07-15T23:53:25.009Z","acs":{"given":31,"want":31,"mode":31},"topic":"usrR6qq94MxLZ0","touched":"2018-07-16T17:30:03.93Z","seq":11},{"updated":"2018-07-15T23:56:25.01Z","acs":{"given":31,"want":31,"mode":31},"topic":"usriUhihEisbH8","touched":"2018-07-16T17:26:20.401Z","seq":9}]}}
        -->
        <dyn_variable name="subs" jsonpath="meta.sub[*].topic"/>
        <websocket type="message" frame="text">{"get":{"id":"%%_baseid%%04","topic":"me","what":"sub"}}</websocket>
      </request>

      <!-- main loop -->
      <for from="1" to="100" incr="1" var="ctr">
        <!-- Iterate over subscriptions -->
        <foreach name="topicx" in="subs">
          <thinktime value="3" random="true"></thinktime>

          <request subst="true">
            <websocket type="message" frame="text">{"sub":{"id":"%%_baseid%%%%_topicx%%%%_ctr%%","topic":"%%_topicx%%","get":{"what":"desc sub data del"}}}</websocket>
          </request>

          <for from="1" to="50" incr="1" var="counter">
            <thinktime min="2" max="10" random="true"></thinktime>

            <request subst="true">
              <websocket type="message" frame="text">{"pub":{"id":"%%_baseid%%%%_topicx%%%%_ctr%%%%_counter%%","topic":"%%_topicx%%","content":"This is a Tsung test %%_baseid%% %%_counter%%"}}</websocket>
            </request>
          </for>

          <request subst="true">
            <websocket type="message" frame="text">{"leave":{"id":"%%_baseid%%%%_topicx%%%%_ctr%%","topic":"%%_topicx%%"}}</websocket>
          </request>

        </foreach>
      </for>
      <!-- end main loop -->

      <request>
        <websocket type="close"></websocket>
      </request>
    </session>
  </sessions>
</tsung>
