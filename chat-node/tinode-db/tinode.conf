{
	"store_config": {
		"uid_key": "la6YsO+bNX/+XIkOqc5Svw==",
		"use_adapter": "",
		"adapters": {
			"mysql": {
				"database": "tinode",
                //"dsn": "root:@tcp(localhost:3306)/ion?parseTime=true&collation=utf8mb4_unicode_ci",
                "dsn": "tinode:Hello123!@(localhost)/tinode?parseTime=true&collation=utf8mb4_unicode_ci",
			},
			"rethinkdb": {
				"database": "tinode",
				"addresses": "localhost:28015"
			},
			"mongodb": {
				"database": "tinode",
				"addresses": "localhost:27017",
				"replica_set": "rs0",
				//"auth_source": "admin",
				//"username": "tinode",
				//"password": "tinode",
			}
		}
	}
}
