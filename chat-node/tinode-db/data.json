{"users": [{"createdAt": "-140h", "email": "<EMAIL>", "tel": "+17025550001", "passhash": "alice123", "private": {"comment": "some comment 123"}, "public": {"fn": "<PERSON>", "photo": "alice-64.jpg", "type": "jpg"}, "tags": ["<PERSON>"], "state": "ok", "status": {"text": "DND"}, "username": "alice", "addressBook": ["email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "tel:+17025550001", "tel:+17025550002", "tel:+17025550003", "tel:+17025550004", "tel:+17025550005", "tel:+17025550006", "tel:+17025550007", "tel:+17025550008", "tel:+17025550009"]}, {"createdAt": "-139h", "email": "<EMAIL>", "tel": "+17025550002", "passhash": "bob123", "private": {"comment": "no comments :)"}, "public": {"fn": "<PERSON>", "photo": "bob-64.jpg", "type": "jpg"}, "state": "ok", "status": "stuff", "username": "bob", "addressBook": ["email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "tel:+17025550001", "tel:+17025550002", "tel:+17025550003", "tel:+17025550004", "tel:+17025550005", "tel:+17025550006", "tel:+17025550007", "tel:+17025550008", "tel:+17025550009"]}, {"createdAt": "-138h", "email": "<EMAIL>", "tel": "+17025550003", "passhash": "carol123", "private": {"comment": "more stuff"}, "public": {"fn": "<PERSON>", "photo": "carol-64.jpg", "type": "jpg"}, "state": "", "status": "ho ho ho", "username": "carol", "addressBook": ["email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "tel:+17025550001", "tel:+17025550002", "tel:+17025550003", "tel:+17025550004", "tel:+17025550005", "tel:+17025550006", "tel:+17025550007", "tel:+17025550008", "tel:+17025550009"]}, {"createdAt": "-137h", "email": "<EMAIL>", "tel": "+17025550004", "passhash": "dave123", "private": {"comment": "stuff 123"}, "public": {"fn": "<PERSON>", "photo": "dave-64.jpg", "type": "jpg"}, "state": "ok", "status": "hiding!", "username": "dave", "addressBook": ["email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "tel:+17025550001", "tel:+17025550002", "tel:+17025550003", "tel:+17025550004", "tel:+17025550005", "tel:+17025550006", "tel:+17025550007", "tel:+17025550008", "tel:+17025550009"]}, {"createdAt": "-136h", "email": "<EMAIL>", "tel": "+17025550005", "passhash": "eve123", "private": {"comment": "apples?"}, "public": {"fn": "<PERSON>", "photo": "eve-64.jpg", "type": "jpg"}, "state": "ok", "username": "eve", "addressBook": ["email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "tel:+17025550001", "tel:+17025550002", "tel:+17025550003", "tel:+17025550004", "tel:+17025550005", "tel:+17025550006", "tel:+17025550007", "tel:+17025550008", "tel:+17025550009"]}, {"createdAt": "-135h", "email": "<EMAIL>", "tel": "+17025550006", "passhash": "frank123", "private": {"comment": "things, not stuff"}, "public": {"fn": "<PERSON>"}, "state": "ok", "status": "singing!", "username": "frank", "addressBook": ["email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "email:<EMAIL>", "tel:+17025550001", "tel:+17025550002", "tel:+17025550003", "tel:+17025550004", "tel:+17025550005", "tel:+17025550007", "tel:+17025550008", "tel:+17025550009"]}, {"createdAt": "-134h", "email": "<EMAIL>", "tel": "+17025550010", "passhash": "(random)", "private": {"comment": "I'm a chatbot short and stout"}, "public": {"fn": "<PERSON><PERSON> the Chatbot", "photo": "tino-64.jpg", "type": "jpg"}, "state": "", "status": "Chatting nonsensically", "username": "tino", "addressBook": []}, {"createdAt": "-133h", "email": "<EMAIL>", "tel": "+17025550099", "passhash": "xena123", "authLevel": "root", "private": {"comment": "No one knowns that <PERSON><PERSON> exists"}, "public": {"fn": "<PERSON>ena Pacif<PERSON>", "photo": "xena-128.jpg", "type": "jpg"}, "state": "", "status": "Just a simple peaceful agriculture specialist", "username": "xena", "addressBook": []}], "grouptopics": [{"createdAt": "-128h", "name": "*ABC", "owner": "carol", "tags": ["flower", "flowers", "flora"], "public": {"fn": "Let's talk about flowers", "photo": "abc-64.jpg", "type": "jpg"}}, {"createdAt": "-126h", "name": "*ABCDEF", "owner": "alice", "tags": ["travel"], "public": {"fn": "Travel, travel, travel", "photo": "abcdef-64.jpg", "type": "jpg"}}, {"createdAt": "-124h", "name": "*BF", "owner": "frank", "tags": ["sikrit", "secret"], "public": {"fn": "Sikrit group!", "photo": "bf-64.jpg", "type": "jpg"}}, {"createdAt": "-123h", "name": "*X", "owner": "xena", "tags": ["support", "public"], "public": {"fn": "Support", "photo": "support-128.jpg", "type": "jpg"}, "access": {"auth": "JRWP", "anon": "JW"}}], "p2psubs": [{"createdAt": "-120h", "users": [{"name": "alice"}, {"name": "bob", "private": {"comment": "<PERSON>"}}]}, {"createdAt": "-119h", "users": [{"name": "alice"}, {"name": "carol", "private": {"comment": "<PERSON>"}}]}, {"createdAt": "-118h", "users": [{"name": "alice"}, {"name": "dave", "private": {"comment": "Alice not in Wunderland"}}]}, {"createdAt": "-117h", "private": {"comment": "apples to oranges"}, "users": [{"name": "alice", "private": {"comment": "Apples"}}, {"name": "eve", "private": {"comment": "<PERSON> is not what she seems"}}]}, {"createdAt": "-116h", "users": [{"name": "alice", "private": {"comment": "<PERSON> a->f"}}, {"name": "frank", "private": {"comment": "<PERSON> f->a"}}]}, {"createdAt": "-115.5h", "users": [{"name": "alice", "private": {"comment": "Python chatbot, short and stout"}}, {"name": "tino"}]}, {"createdAt": "-114h", "users": [{"name": "bob", "private": {"comment": "I'm banned by <PERSON>!"}, "have": "A"}, {"name": "dave", "private": {"comment": "I banned <PERSON>."}, "want": "N"}]}, {"createdAt": "-113.5h", "users": [{"name": "bob", "private": {"comment": "<PERSON> ne<PERSON>"}}, {"name": "eve"}]}, {"createdAt": "-113.45h", "users": [{"name": "bob", "private": {"comment": "Python chatbot, short and stout"}}, {"name": "tino"}]}, {"createdAt": "-113.3h", "users": [{"name": "carol", "private": {"comment": "Python chatbot, short and stout"}}, {"name": "tino"}]}, {"createdAt": "-112.7h", "private": {"comment": "Python chatbot short and stout"}, "users": [{"name": "dave", "private": {"comment": "Python chatbot, short and stout"}}, {"name": "tino"}]}, {"createdAt": "-112.3h", "users": [{"name": "eve", "private": {"comment": "Python chatbot, short and stout"}}, {"name": "tino"}]}, {"createdAt": "-112.1h", "users": [{"name": "frank", "private": {"comment": "Python chatbot, short and stout"}}, {"name": "tino"}]}], "groupsubs": [{"createdAt": "-112h", "private": {"comment": "My super cool group topic"}, "topic": "*ABC", "user": "alice"}, {"createdAt": "-111.9h", "private": {"comment": "Wow"}, "topic": "*ABC", "user": "bob"}, {"createdAt": "-111.8h", "private": {"comment": "Custom group description by <PERSON>"}, "topic": "*ABCDEF", "user": "bob"}, {"createdAt": "-111.7h", "private": {"comment": "<PERSON><PERSON><PERSON><PERSON>"}, "topic": "*ABCDEF", "user": "carol"}, {"createdAt": "-111.6h", "topic": "*ABCDEF", "user": "dave"}, {"createdAt": "-111.5h", "topic": "*ABCDEF", "user": "eve"}, {"createdAt": "-111.4h", "topic": "*ABCDEF", "user": "frank"}, {"createdAt": "-111.3h", "private": {"comment": "I'm not the owner, <PERSON> is"}, "topic": "*BF", "user": "bob"}], "messages": ["Caution: Do not view laser light with remaining eye.", "Caution: breathing may be hazardous to your health.", "Celebrate Hannibal Day this year. Take an elephant to lunch.", "Celibacy is not hereditary.", "Center 1127 -- It's not just a job, it's an adventure!", "Center meeting at 4pm in 2C-543", "Centran manuals are available in 2B-515.", "<PERSON> don't surf.", "Children are hereditary: if your parents didn't have any, neither will you.", "Clothes make the man. Naked people have little or no influence on society.", "Club sandwiches, not baby seals.", "Cocaine is nature's way of saying you make too much money.", "Cogito Ergo Spud.", "Cogito cogito ergo cogito sum.", "Colorless green ideas sleep furiously.", "Communication is only possible between equals.", "Computers are not intelligent.  They only think they are.", "Consistency is always easier to defend than correctness.", "Constants aren't.  Variables don't.  LISP does.  Functions won't.  Bytes do.", "Contains no kung fu, car chases or decapitations.", "Continental Life.  Why do you ask?", "Convictions cause convicts -- what you believe imprisons you.", "Core Error - Bus Dumped", "Could not open 2147478952 framebuffers.", "Courage is something you can never totally own or totally lose.", "Cowards die many times before their deaths;/The valiant never taste of death but once.", "<PERSON><PERSON><PERSON>, his prices are INSANE!!!", "Creativity is no substitute for knowing what you are doing.", "Creditors have much better memories than debtors.", "Critics are like eunuchs in a harem: they know how it's done, they've seen it done", "every day, but they're unable to do it themselves.  -<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Saves!  ...  in case He's hungry later.", "Dame<PERSON> is like streetcars -- The oceans is full of 'em.  -<PERSON>", "Dames lie about anything - just for practice.  -<PERSON>", "Damn it, i gotta get outta here!", "Dangerous knowledge is a little thing.", "It is certain", "It is decidedly so", "Without a doubt", "Yes definitely", "You may rely on it", "As I see it yes", "Most likely", "Outlook good", "Yes", "No", "No! No, no, no! No!!", "Signs point to yes", "Reply hazy try again", "Ask again later", "Better not tell you now", "Cannot predict now", "Concentrate and ask again", "Don't count on it", "My reply is no", "My sources say no", "Outlook not so good", "Very doubtful", "All your base are belong to us"], "forms": [{"txt": "Post responseYesДа", "fmt": [{"at": 0, "len": 18, "tp": "FM"}, {"at": 0, "len": 13, "tp": "ST"}, {"at": 13, "len": 3, "key": 0}, {"at": 16, "len": 2, "key": 1}], "ent": [{"tp": "BN", "data": {"name": "yes", "act": "pub"}}, {"tp": "BN", "data": {"name": "yes2", "act": "pub"}}]}, {"txt": "Go to URL:Yes OK", "fmt": [{"at": 0, "len": 16, "tp": "FM"}, {"at": 0, "len": 10, "tp": "ST"}, {"at": 10, "len": 6, "tp": "RW"}, {"at": 10, "len": 3, "key": 0}, {"at": 14, "len": 2, "key": 1}], "ent": [{"tp": "BN", "data": {"name": "ok", "act": "url", "ref": "https://github.com/tinode/chat/?key=val"}}, {"tp": "BN", "data": {"name": "ok2", "act": "url", "ref": "https://github.com/tinode/chat"}}]}]}