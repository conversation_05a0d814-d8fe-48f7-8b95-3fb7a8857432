# Contributing

We're happy you want to contribute! You can help us in different ways:

- [Open an issue](https://github.com/tinode/chat/issues) with suggestions for improvements
- Fork this repository and submit a pull request
- Improve the documentation


To submit a pull request, fork the [repository](https://github.com/tinode/chat) and then clone your fork:

    <NAME_EMAIL>:<your-repo-name>/chat.git

Make your suggested changes, `git push` and then [submit a pull request](https://github.com/tinode/chat/compare/). Note that before we can accept your pull requests, you need to sign our [Contributor License Agreement](docs/CLA.md).


## Why is the Contributor License Agreement necessary?

We very much appreciate your wanting to contribute to Tinode Chat, but we need to add you to the contributors list first. Note that the [agreement](docs/CLA.md) is not a transfer of copyright ownership, this simply is a license agreement for contributions. You also do not change your rights to use your own contributions for any other purpose. For some background on why contributor license agreements are necessary, you can read FAQs from many other open source projects:

- Django's [CLA FAQ](https://www.djangoproject.com/foundation/cla/faq/)
- A [chapter](http://producingoss.com/en/copyright-assignment.html) from <PERSON>'s _Producing Open Source Software_ on CLAs
- The [Wikipedia article on CLAs](http://en.wikipedia.org/wiki/Contributor_license_agreement)

This is part of the legal framework of the open-source ecosystem that adds some red tape, but protects both the contributor and the company / foundation behind the project. It also gives us the option to relicense the code with a more permissive license in the future.
