
#!/bin/bash

# https://www.cnblogs.com/mkxfs/p/12795177.html
DATABASE="tinode"
USER="'tinode'@'localhost'"
PWD="Hello123!"

if ! command -v mysql &> /dev/null
then
    echo "mysql could not be found"
    exit
fi

if [[ "$OS_TYPE" =~ "Darwin" ]];then
    brew services start mysql
    mysql.server start
elif [[ "$OSTYPE" == "linux-gnu"* ]];then
	sudo service mysql start
fi


if lsof -i :3306; then
	echo "mysql has already started!"
	exit
fi

CHECK_TIMES=0
LIMIT=20
while true
do
	sleep 5
	echo "checking tidb............"
	if mysql --host '127.0.0.1' --port 4000 -u 'root' -e "SHOW DATABASES;"
	then
		break
	else
		((CHECK_TIMES++))
	fi

	if [ "$CHECK_TIMES" -gt "$LIMIT" ];
	then
		echo "failed to start tidb"
		exit
	fi
done

INIT_SQL="CREATE DATABASE IF NOT EXISTS $DATABASE;\
CREATE USER IF NOT EXISTS $USER IDENTIFIED BY '$PWD';\
GRANT ALL PRIVILEGES ON ion.* TO $USER;"
# echo "$INIT_SQL"
mysql --host '127.0.0.1' --port 4000 -u 'root' -e "$INIT_SQL"

echo "tidb starts successfully!"