APP_DIR=$(cd `dirname $0`/../; pwd)
cd $APP_DIR


EXE=tinode
COMMAND=$APP_DIR/bin/$EXE
DB_INITILIZER=$APP_DIR/bin/init-db
PID_FILE=$APP_DIR/bin/$EXE.pid
LOG_FILE=$APP_DIR/logs/$EXE.log


mkdir $APP_DIR/bin
mkdir $APP_DIR/logs

echo "build server node"
cd $APP_DIR/server
go build -tags mysql -o $COMMAND

echo "build tinode-db database initializer"
cd $APP_DIR/tinode-db
go build -tags mysql -o $DB_INITILIZER



echo "Run DB initializer"
$DB_INITILIZER -config=$APP_DIR/tinode-db/tinode.conf 
# -reset=true


## run command
echo "nohup $COMMAND -c $CONFIG >>$LOG_FILE 2>&1 &" 

nohup $COMMAND -config=$APP_DIR/server/dev.conf -static_data=$APP_DIR/webapp/ >>$LOG_FILE 2>&1 &

pid=$!
echo "$pid" > $PID_FILE
rpid=`ps aux | grep $pid |grep -v "grep" | awk '{print $2}'`
if [[ $pid != $rpid ]];then
	echo "start failly."
    rm  $PID_FILE
	exit 1
fi

