module github.com/tinode/chat

go 1.14

require (
	firebase.google.com/go v3.12.0+incompatible
	github.com/aliyun/aliyun-oss-go-sdk v2.1.8+incompatible
	github.com/aliyun/aliyun-sts-go-sdk v0.0.0-20171106034748-98d3903a2309
	github.com/aws/aws-sdk-go v1.29.29
	github.com/bitly/go-hostpool v0.1.0 // indirect
	github.com/bmizerany/assert v0.0.0-20160611221934-b7ed37b82869 // indirect
	github.com/cloudwebrtc/nats-protoo v0.0.0-20200604135451-87b43396e8de
	github.com/coreos/etcd v3.3.25+incompatible // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/golang/protobuf v1.4.3
	github.com/google/go-cmp v0.5.0
	github.com/gorilla/handlers v1.4.2
	github.com/gorilla/websocket v1.4.2
	github.com/jmoiron/sqlx v1.2.0
	github.com/mitchellh/mapstructure v1.1.2
	github.com/nfnt/resize v0.0.0-20180221191011-83c6a9932646
	github.com/nyaruka/phonenumbers v1.0.56
	github.com/pion/ion v0.4.6
	github.com/prometheus/client_golang v1.5.1
	github.com/prometheus/common v0.9.1
	github.com/satori/go.uuid v1.2.0 // indirect
	github.com/shirou/gopsutil v2.20.9+incompatible
	github.com/tinode/jsonco v1.0.0
	github.com/tinode/snowflake v1.0.0
	go.etcd.io/etcd v3.3.25+incompatible
	go.mongodb.org/mongo-driver v1.3.1
	golang.org/x/crypto v0.0.0-20200622213623-75b288015ac9
	golang.org/x/net v0.0.0-20201021035429-f5854403a974
	golang.org/x/oauth2 v0.0.0-20200107190931-bf48bf16ab8d
	golang.org/x/sys v0.0.0-20201020230747-6e5568b54d1a // indirect
	golang.org/x/text v0.3.3
	google.golang.org/api v0.20.0
	google.golang.org/genproto v0.0.0-20201019141844-1ed22bb0c154 // indirect
	google.golang.org/grpc v1.33.1
	google.golang.org/grpc/cmd/protoc-gen-go-grpc v1.0.0 // indirect
	google.golang.org/protobuf v1.25.0
	gopkg.in/rethinkdb/rethinkdb-go.v5 v5.1.0
)
