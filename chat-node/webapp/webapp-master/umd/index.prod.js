!function(e){var t={};function s(a){if(t[a])return t[a].exports;var n=t[a]={i:a,l:!1,exports:{}};return e[a].call(n.exports,n,n.exports,s),n.l=!0,n.exports}s.m=e,s.c=t,s.d=function(e,t,a){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(s.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)s.d(a,n,function(t){return e[t]}.bind(null,n));return a},s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="/umd/",s(s.s=7)}([function(e,t){e.exports=React},function(e,t){e.exports=ReactIntl},function(e,t){e.exports=Tinode},function(e,t){e.exports=ReactDOM},function(e){e.exports=JSON.parse('{"de":{"validate_credential_action":"bestätigen","title_tag_manager":"Schlagworte (Nutzer entdecken)","label_user_contacts":"Kontakte:","button_add_another":"Hinzufügen","label_message_sound":"Benachrichtigungston:","label_push_notifications":"Benachrichtigungsmeldungen:","label_push_notifications_disabled":"Benachrichtigungsmeldungen (erfordert HTTPS):","label_incognito_mode":"inkognito-Modus:","delete_account":"Konto löschen","delete_account_arning":"Möchten Sie das Konto wirklich löschen? Das kann nicht rückgängig gemacht werden","label_password":"Passwort","password_unchanged_prompt":"unverändert","button_logout":"Abmelden","button_delete_account":"Konto löschen","label_default_access_mode":"Standard Zugriffsmodus:","blocked_contacts_link":"Blockierte Kontakte ({count})","link_contact_us":"Kontakt","link_terms_of_service":"Nutzungsbedingungen","link_privacy_policy":"Datenschutzerklärung","label_sdk":"SDK:","label_server_address":"Server Adresse:","archived_contacts":"Archivierte Kontakte ({count})","contacts_not_found":"Sie haben keine Unterhaltungen\\n\\n¯\\\\_(ツ)_/¯","full_name_prompt":"Vollständiger Name, z.B. Lisa Musterfrau","email_prompt":"E-Mail, z.B. <EMAIL>","button_sign_up":"Anmelden","label_your_name":"Ihr Name","label_user_id":"ID:","button_edit":"Bearbeiten","requested_permissions":"Angefordert","granted_permissions":"Erteilt","menu_item_edit_permissions":"Berechtigungen ändern","label_other_user":"Andere","action_clear_messages":"Nachrichten leeren","clear_messages_warning":"Sind Sie sicher, dass Sie diese Unterhaltung für alle leeren wollen? Das kann nicht rückgängig gemacht werden.","action_delete_messages":"Nachrichten für alle löschen","delete_messages_warning":"Sind Sie sicher, dass Sie diese Unterhaltung für alle löschen wollen? Das kann nicht rückgängig gemacht werden.","action_leave_chat":"Unterhaltung verlassen","leave_chat_warning":"Möchten Sie diese Unterhaltung wirklich verlassen?","action_block_contact":"Kontakt blockieren","block_contact_warning":"Möchten Sie diesen Kontakt wirklich blockieren?","action_report_chat":"Unterhaltung melden","report_chat_warning":"Möchten Sie diese Unterhaltung wirklich blockieren und melden?","title_info":"Info","label_topic_name":"Name","label_private":"Privater Kommentar","private_editing_placeholder":"Nur für Sie sichtbar","label_muting_topic":"Stumm geschaltet:","action_more":"Mehr","label_your_permissions":"Ihre Berechtigungen:","label_permissions":"Berechtigungen:","label_you":"Sie:","label_default_access":"Standard Zugriffsmodus:","label_group_members":"Gruppenmitglieder:","button_add_members":"Mitglieder hinzufügen","group_has_no_members":"Keine Mitglieder","login_prompt":"Anmelden","password_prompt":"Passwort","stay_logged_in":"Angemeldet bleiben","forgot_password_link":"Passwort vergessen?","button_sign_in":"Anmelden","label_client":"Client:","label_server":"Server:","online_now":"jetzt online","last_seen_timestamp":"Zuletzt gesehen","title_not_found":"Nicht gefunden","unnamed_topic":"Unbenannt","messages_not_readable":"Neue Nachrichten können nicht gelesen werden","peers_messaging_disabled":"Gruppennachrichten sind deaktiviert","enable_peers_messaging":"Aktivieren","search_for_contacts":"Nutzen Sie die Suche um Kontakte zu finden","search_no_results":"Die Suche hatte keine Ergebnisse","tabtitle_find_user":"Suchen","tabtitle_new_group":"Neue Gruppe","tabtitle_group_by_id":"nach ID","new_password_placeholder":"Geben Sie ein neues Passwort ein","label_reset_password":"Passwort per E-Mail wiederherstellen:","credential_email_prompt":"E-Mail Adresse für Registrierung","button_reset":"Zurücksetzen","button_send_request":"Anfrage senden","label_server_to_use":"Server verwenden:","label_wire_transport":"Übertragung per Kabel:","button_update":"Aktualisieren","sidepanel_title_login":"Anmelden","sidepanel_title_register":"Konto erstellen","sidepanel_title_settings":"Einstellungen","sidepanel_title_account_settings":"Konto-Einstellungen","sidepanel_title_acc_general":"Allgemein","sidepanel_title_acc_security":"Sicherheit","sidepanel_title_acc_notifications":"Benachrichtigungen","sidepanel_title_acc_support":"Unterstützung","sidepanel_title_newtpk":"Neue Unterhaltung starten","sidepanel_title_cred":"Anmeldeinformationen bestätigen","sidepanel_title_reset":"Passwort zurücksetzen","sidepanel_title_archive":"Archivierte Unterhaltungen","sidepanel_title_blocked":"Blockierte Unterhaltungen","reconnect_countdown":"Getrennt. Wiederverbinden in {seconds}…","reconnect_now":"Jetzt probieren","push_init_failed":"Initialisierung von Push-Benachrichtigungen fehlgeschlagen","invalid_security_token":"Ungültiger Sicherheitsschlüssel","no_connection":"Keine Verbindung","code_doesnot_match":"Code stimmt nicht überein","update_available":"Aktualisierung verfügbar.","reload_update":"Neu laden","phone_dative":"Telefon","email_dative":"E-Mail","enter_confirmation_code_prompt":"Geben Sie den Bestätigungscode ein, der per {method} geschickt wurde:","numeric_confirmation_code_prompt":"Nur zahlen","button_confirm":"Bestätigen","save_attachment":"Speichern","deleted_content":"Inhalt gelöscht","invalid_content":"ungültiger Inhalt","user_not_found":"Nicht gefunden","badge_you":"Sie","badge_owner":"Besitzer","menu_item_info":"Info","menu_item_clear_messages":"Nachrichten leeren","menu_item_clear_messages_for_all":"Für alle leeren","menu_item_delete":"Löschen","menu_item_delete_for_all":"Für alle löschen","menu_item_send_retry":"Wiederholen","menu_item_mute":"Unterhaltung stumm schalten","menu_item_unmute":"Stumm schalten beenden","menu_item_delete_topic":"Entfernen","topic_delete_warning":"Möchten Sie diese Unterhaltung wirklich löschen?","menu_item_unblock":"Blockierung aufheben","menu_item_block":"Blockieren","topic_block_warning":"Möchten Sie diese Unterhaltung wirklich blockieren?","menu_item_member_delete":"Entfernen","menu_item_archive_topic":"Archivieren","action_cancel":"Abbrechen","upload_finishing":"wird abgeschlossen...","no_contacts":"Sie haben keine Kontakte :-(","contacts_not_found_short":"Keine Kontakte für \'\'{query}\'\'","title_group_members":"Gruppenmitglieder","title_all_contacts":"Alle Kontakte","button_ok":"OK","button_cancel":"Abbrechen","more_online_members":"+{overflow} mehr","download_action":"herunterladen","label_file_name":"Dateiname:","label_content_type":"Inhaltsart:","label_size":"Größe:","chat_invitation":"Sie sind eingeladen, an einem neuen Chat teilzunehmen. Was möchten Sie tun?","chat_invitation_accept":"Akzeptieren","chat_invitation_ignore":"Ignorieren","chat_invitation_block":"Blockieren","error_invalid_id":"ungültige ID","group_user_id_prompt":"Gruppe oder Benutzer ID","button_subscribe":"Abbonieren","topic_name_editing_placeholder":"Freiform Name der Gruppe","button_create":"Erstellen","permission_join":"Beitreten ({val})","permission_read":"Lesen ({val})","permission_write":"Schreiben ({val})","permission_pres":"Benachrichtigt werden ({val})","permission_admin":"Bestätigen ({val})","permission_share":"Teilen ({val})","permission_delete":"Entfernen ({val})","permission_owner":"Besitzer ({val})","title_permissions":"Berechtigungen","message_sending":"wird gesendet...","message_sending_failed":"fehlgeschlagen","search_placeholder":"Liste: email:<EMAIL>, tel:***********...","messaging_disabled_prompt":"Nachrichtenübermittlung deaktiviert","new_message_prompt":"Neue Nachricht","image_caption_prompt":"Bildunterschrift","file_attachment_too_large":"Die Dateigröße {size} überschreitet das Limit von {limit}.","cannot_initiate_file_upload":"Datei kann nicht hochgeladen werden.","tags_not_found":"Keine Schlagworte definiert. Erstellen Sie welche.","tags_editor_no_tags":"Schlagworte hinzufügen","title_manage_tags":"Verwalten"},"en":{"validate_credential_action":"confirm","title_tag_manager":"Tags (user discovery)","label_user_contacts":"Contacts:","button_add_another":"Add another","label_message_sound":"Message sound:","label_push_notifications":"Notification alerts:","label_push_notifications_disabled":"Notification alerts (requires HTTPS):","label_incognito_mode":"Incognito mode:","delete_account":"Delete account","delete_account_arning":"Are you sure you want to delete your account? It cannot be undone.","label_password":"Password","password_unchanged_prompt":"Unchanged","button_logout":"Logout","button_delete_account":"Delete account","label_default_access_mode":"Default access mode:","blocked_contacts_link":"Blocked contacts ({count})","link_contact_us":"Contact Us","link_terms_of_service":"Terms of Service","link_privacy_policy":"Privacy Policy","label_sdk":"SDK:","label_server_address":"Server address:","archived_contacts":"Archived contacts ({count})","contacts_not_found":"You have no chats<br />¯∖_(ツ)_/¯","full_name_prompt":"Full name, e.g. John Doe","email_prompt":"Email, e.g. <EMAIL>","button_sign_up":"Sign up","label_your_name":"Your name","label_user_id":"ID:","button_edit":"Edit","requested_permissions":"Requested","granted_permissions":"Granted","menu_item_edit_permissions":"Edit permissions","label_other_user":"Other","action_clear_messages":"Clear Messages","clear_messages_warning":"Are you sure you want to clear all messages? It cannot be undone.","action_delete_messages":"Clear Messages for All","delete_messages_warning":"Are you sure you want to delete all messages for everyone? It cannot be undone.","action_leave_chat":"Leave Conversation","leave_chat_warning":"Are you sure you want to leave this conversation?","action_block_contact":"Block Contact","block_contact_warning":"Are you sure you want to block this contact?","action_report_chat":"Report Conversation","report_chat_warning":"Are you sure you want to block and report this conversation?","title_info":"Info","label_topic_name":"Name","label_private":"Private comment","private_editing_placeholder":"Visible to you only","label_muting_topic":"Muted:","action_more":"More","label_your_permissions":"Your permissions:","label_permissions":"Permissions:","label_you":"You:","label_default_access":"Default access mode:","label_group_members":"Group members:","button_add_members":"Add members","group_has_no_members":"No members","login_prompt":"Login","password_prompt":"Password","stay_logged_in":"Stay logged in","forgot_password_link":"Forgot password?","button_sign_in":"Sign in","label_client":"Client:","label_server":"Server:","online_now":"online now","last_seen_timestamp":"Last seen","title_not_found":"Not found","unnamed_topic":"Unnamed","messages_not_readable":"no access to messages","peers_messaging_disabled":"Peer\'s messaging is disabled.","enable_peers_messaging":"Enable","search_for_contacts":"Use search to find contacts","search_no_results":"Search returned no results","tabtitle_find_user":"find","tabtitle_new_group":"new group","tabtitle_group_by_id":"by id","new_password_placeholder":"Enter new password","label_reset_password":"Send a password reset email:","credential_email_prompt":"Your registration email","button_reset":"Reset","button_send_request":"Send request","label_server_to_use":"Server to use:","label_wire_transport":"Wire transport:","button_update":"Update","sidepanel_title_login":"Sign In","sidepanel_title_register":"Create Account","sidepanel_title_settings":"Settings","sidepanel_title_account_settings":"Account Settings","sidepanel_title_acc_general":"General","sidepanel_title_acc_security":"Security","sidepanel_title_acc_notifications":"Notifications","sidepanel_title_acc_support":"Support","sidepanel_title_newtpk":"Start New Chat","sidepanel_title_cred":"Confirm Credentials","sidepanel_title_reset":"Reset Password","sidepanel_title_archive":"Archived Chats","sidepanel_title_blocked":"Blocked Chats","reconnect_countdown":"Disconnected. Reconnecting in {seconds}…","reconnect_now":"Try now","push_init_failed":"Failed to initialize push notifications","invalid_security_token":"Invalid security token","no_connection":"No connection","code_doesnot_match":"Code does not match","update_available":"Update available.","reload_update":"Reload","phone_dative":"phone","email_dative":"email","enter_confirmation_code_prompt":"Enter confirmation code sent to you by {method}:","numeric_confirmation_code_prompt":"Numbers only","button_confirm":"Confirm","save_attachment":"save","deleted_content":"content deleted","invalid_content":"invalid content","user_not_found":"Not found","badge_you":"you","badge_owner":"owner","menu_item_info":"Info","menu_item_clear_messages":"Clear messages","menu_item_clear_messages_for_all":"Clear for All","menu_item_delete":"Delete","menu_item_delete_for_all":"Delete for All","menu_item_send_retry":"Retry","menu_item_mute":"Mute","menu_item_unmute":"Unmute","menu_item_delete_topic":"Delete","topic_delete_warning":"Are you sure you want to delete this conversation?","menu_item_unblock":"Unblock","menu_item_block":"Block","topic_block_warning":"Are you sure you want to block this conversation?","menu_item_member_delete":"Remove","menu_item_archive_topic":"Archive","action_cancel":"cancel","upload_finishing":"finishing...","no_contacts":"You have no contacts :-(","contacts_not_found_short":"No contacts match \'\'{query}\'\'","title_group_members":"Group Members","title_all_contacts":"All Contacts","button_ok":"OK","button_cancel":"Cancel","more_online_members":"+{overflow} more","download_action":"download","label_file_name":"File name:","label_content_type":"Content type:","label_size":"Size:","chat_invitation":"You are invited to start a new chat. What would you like to do?","chat_invitation_accept":"Accept","chat_invitation_ignore":"Ignore","chat_invitation_block":"Block","error_invalid_id":"Invalid ID","group_user_id_prompt":"Group or User ID","button_subscribe":"Subscribe","topic_name_editing_placeholder":"Freeform name of the group","button_create":"Create","permission_join":"Join ({val})","permission_read":"Read ({val})","permission_write":"Write ({val})","permission_pres":"Get notified ({val})","permission_admin":"Approve ({val})","permission_share":"Share ({val})","permission_delete":"Delete ({val})","permission_owner":"Owner ({val})","title_permissions":"Permissions","message_sending":"sending...","message_sending_failed":"failed","search_placeholder":"List like email:<EMAIL>, tel:***********...","messaging_disabled_prompt":"Messaging disabled","new_message_prompt":"New message","image_caption_prompt":"Image caption","file_attachment_too_large":"The file size {size} exceeds the {limit} limit.","cannot_initiate_file_upload":"Cannot initiate file upload.","tags_not_found":"No tags defined. Add some.","tags_editor_no_tags":"Add some tags","title_manage_tags":"Manage"},"es":{"validate_credential_action":"confirmar","title_tag_manager":"Etiquetas (descubrimiento de usuarios)","label_user_contacts":"Contactos:","button_add_another":"Añadir contacto","label_message_sound":"Sonido de mensaje:","label_push_notifications":"Alertas de notificaciones:","label_push_notifications_disabled":"Alertas de notificaciones (requiere HTTPS):","label_incognito_mode":"Modo incógnito:","delete_account":"Eliminar cuenta","delete_account_arning":"¿Estás seguro de que deseas eliminar permanentemente tu cuenta? Esta acción es irreversible.","label_password":"Contraseña","password_unchanged_prompt":"Sin cambios","button_logout":"Cerrar sesión","button_delete_account":"Eliminar cuenta","label_default_access_mode":"Modo de acceso predeterminado:","blocked_contacts_link":"Contactos bloqueados ({count})","link_contact_us":"Contáctanos","link_terms_of_service":"Términos de uso","link_privacy_policy":"Política de privacidad","label_sdk":"SDK:","label_server_address":"Dirección del servidor:","archived_contacts":"Contactos archivados ({count})","contacts_not_found":"No tienes chats<br />¯∖_(ツ)_/¯","full_name_prompt":"Nombre completo, p.ej. Juan González Hernández","email_prompt":"Correo electrónico, p.ej. <EMAIL>","button_sign_up":"Regístrate","label_your_name":"Tu nombre","label_user_id":"ID:","button_edit":"Editar","requested_permissions":"Solicitados","granted_permissions":"Otorgados","menu_item_edit_permissions":"Editar permisos","label_other_user":"Otros","action_clear_messages":"Borrar mensajes","clear_messages_warning":"¿Estás seguro de que quieres eliminar todos los mensajes? Esta acción es irreversible.","action_delete_messages":"Borrar mensajes para todos","delete_messages_warning":"¿Estás seguro de que quieres eliminar todos los mensajes para todos? Esta acción es irreversible.","action_leave_chat":"Dejar conversación","leave_chat_warning":"¿Estás seguro de que quieres dejar esta conversación?","action_block_contact":"Bloquear contacto","block_contact_warning":"¿Estás seguro de que quieres bloquear a este contacto?","action_report_chat":"Reportar conversación","report_chat_warning":"¿Estás seguro de que quieres bloquear y reportar a esta conversación?","title_info":"Información","label_topic_name":"Nombre del tema","label_private":"Comentario privado","private_editing_placeholder":"Sólo visible para tí","label_muting_topic":"Silenciado:","action_more":"Más","label_your_permissions":"Tus permisoss:","label_permissions":"Permisos:","label_you":"Tú:","label_default_access":"Modo de acceso predeterminado:","label_group_members":"Miembros del grupo:","button_add_members":"Añadir miembros","group_has_no_members":"No hay miembros","login_prompt":"Nombre de usuario","password_prompt":"Contraseña","stay_logged_in":"Permanecer conectado","forgot_password_link":"¿Olvidaste tu contraseña?","button_sign_in":"Entrar","label_client":"Cliente:","label_server":"Servidor:","online_now":"en línea","last_seen_timestamp":"Últ. vez","title_not_found":"No encontrado","unnamed_topic":"Sin nombre","messages_not_readable":"sin acceso a mensajes","peers_messaging_disabled":"La mensajería Peer está deshabilitada.","enable_peers_messaging":"Habilitar","search_for_contacts":"Usa la búsqueda para encontrar contactos","search_no_results":"La búsqueda no arrojó resultados","tabtitle_find_user":"encontrar","tabtitle_new_group":"nuevo grupo","tabtitle_group_by_id":"por ID","new_password_placeholder":"Introduzca una nueva contraseña","label_reset_password":"Enviar un correo electrónico de restablecimiento de contraseña:","credential_email_prompt":"Tu correo electrónico de registro","button_reset":"Restablecer","button_send_request":"Enviar petición","label_server_to_use":"Servidor para usar:","label_wire_transport":"Transporte de alambre:","button_update":"Actualizar","sidepanel_title_login":"Iniciar sesión","sidepanel_title_register":"Crear cuenta","sidepanel_title_settings":"Ajustes","sidepanel_title_account_settings":"Ajustes de la cuenta","sidepanel_title_acc_general":"General","sidepanel_title_acc_security":"Seguridad","sidepanel_title_acc_notifications":"Notificaciones","sidepanel_title_acc_support":"Soporte","sidepanel_title_newtpk":"Iniciar un nuevo chat","sidepanel_title_cred":"Confirmar credenciales","sidepanel_title_reset":"Restablecer contraseña","sidepanel_title_archive":"Chats archivados","sidepanel_title_blocked":"Chats bloqueados","update_available":"Actualización disponible.","reload_update":"Recargar","reconnect_countdown":"Desconectado. Reconectando en {seconds}…","reconnect_now":"Reintentar","push_init_failed":"Error al inicializar las notificaciones push","invalid_security_token":"Token de seguridad inválido","no_connection":"Sin conexión","code_doesnot_match":"El código no coincide","phone_dative":"teléfono","email_dative":"correo electrónico","enter_confirmation_code_prompt":"Intruduzca el código de confirmación enviado a tu {method}:","numeric_confirmation_code_prompt":"Sólo números","button_confirm":"Confirmar","save_attachment":"guardar","deleted_content":"este mensaje fue eliminado","invalid_content":"contenido inválido","user_not_found":"Usuario no encontrado","badge_you":"tú","badge_owner":"propietario","menu_item_info":"Información","menu_item_clear_messages":"Borrar mensajes","menu_item_clear_messages_for_all":"Borrar para todos","menu_item_delete":"Eliminar","menu_item_delete_for_all":"Eliminar para todos","menu_item_send_retry":"Inténtalo de nuevo","menu_item_mute":"Silenciar","menu_item_unmute":"Anular el silencio","menu_item_delete_topic":"Eliminar","topic_delete_warning":"¿Estás seguro de que quieres eliminar esta conversación?","menu_item_unblock":"Desbloquear","menu_item_block":"Bloquear","topic_block_warning":"¿Estás seguro de que quieres bloquear esta conversación","menu_item_member_delete":"Eliminar","menu_item_archive_topic":"Archivar","action_cancel":"cancelar","upload_finishing":"terminando...","no_contacts":"No tienes contactos :-(","contacts_not_found_short":"Ningún contacto coincide con \'\'{query}\'\'","title_group_members":"Miembros del grupo","title_all_contacts":"Todos los contactos","button_ok":"OK","button_cancel":"Cancelar","more_online_members":"+{overflow} más","download_action":"descargar","label_file_name":"Nombre del archivo:","label_content_type":"Tipo de contenido:","label_size":"Tamaño:","chat_invitation":"Estás invitado a participar en un nuevo chat. ¿Qué te gustaría hacer?","chat_invitation_accept":"Aceptar","chat_invitation_ignore":"Ignorar","chat_invitation_block":"Bloquear","error_invalid_id":"ID inválido","group_user_id_prompt":"ID del grupo o usuario","button_subscribe":"Suscribirse","topic_name_editing_placeholder":"Nombre del grupo","button_create":"Crear","permission_join":"Unirse ({val})","permission_read":"Leer ({val})","permission_write":"Escribir ({val})","permission_pres":"Ser notificado ({val})","permission_admin":"Approbar ({val})","permission_share":"Compartir ({val})","permission_delete":"Eliminar ({val})","permission_owner":"Propietario ({val})","title_permissions":"Permisos","message_sending":"enviando...","message_sending_failed":"no se pudo enviar el mensaje","search_placeholder":"Ej. email:<EMAIL>, tel:***********...","messaging_disabled_prompt":"El envío de mensajes está deshabilitado","new_message_prompt":"Nuevo mensaje","image_caption_prompt":"Añade un comentario","file_attachment_too_large":"El tamaño del archivo {size} excede el límite de {limit}.","cannot_initiate_file_upload":"No se pudo iniciar la carga del archivo.","tags_not_found":"No hay etiquetas definidas. Añade unas.","tags_editor_no_tags":"Añadir etiquetas","title_manage_tags":"Gestionar"},"ko":{"validate_credential_action":"확인","title_tag_manager":"태그(사용자검색)","label_user_contacts":"연락처: ","button_add_another":"다른항목추가","label_message_sound":"메시지소리: ","label_push_notifications":"통지경보: ","label_push_notifications_disabled":"통지경보(HTTPS필요): ","label_incognito_mode":"시크릿모드: ","delete_account":"계정삭제","delete_account_arning":"계정을삭제하시겠습니까?실행취소할수없습니다.","label_password":"비밀번호","password_unchanged_prompt":"변경되지않음","button_logout":"로그아웃","button_delete_account":"계정삭제","label_default_access_mode":"기본액세스모드: ","blocked_contacts_link":"차단된연락처({수})","link_contact_us":"문의처","link_terms_of_service":"서비스약관","link_privacy_policy":"개인정보보호정책","label_sdk":"SDK:","label_server_address":"서버주소: ","archived_contacts":"보관된연락처({수})","contacts_not_found":"채팅이없습니다<br/>¯∖_(ツ)_/¯","full_name_prompt":"전체이름(예:홍길동)","email_prompt":"이메일(예:<EMAIL>)","button_sign_up":"가입","label_your_name":"이름","label_user_id":"ID:","button_edit":"편집","requested_permissions":"요청","menu_item_edit_permissions":"편집권한","label_other_user":"기타","action_clear_messages":"메시지지우기","clear_messages_warning":"모든메시지를지우시겠습니까?실행취소할수없습니다.","action_delete_messages":"모든메시지지우기","delete_messages_warning":"모든사람의모든메시지를삭제하시겠습니까?실행취소할수없습니다.","action_leave_chat":"대화나누기","leave_chat_warning":"이대화를나가시겠습니까?","action_block_contact":"연락차단","block_contact_warning":"이연락처를차단하시겠습니까?","action_report_chat":"대화기록","report_chat_warning":"이대화를차단하고기록하시겠습니까?","title_info":"정보","label_topic_name":"이름","label_private":"개인코멘트","private_editing_placeholder":"나만볼수있습니다","label_muting_topic":"음소거: ","action_more":"더보기","label_your_permissions":"권한: ","label_permissions":"권한: ","label_you":"당신: ","label_default_access":"기본액세스모드: ","label_group_members":"그룹회원: ","button_add_members":"회원추가","group_has_no_members":"회원없음","login_prompt":"로그인","password_prompt":"비밀번호","stay_logged_in":"로그인상태유지","forgot_password_link":"비밀번호를잊으셨습니까?","button_sign_in":"로그인","label_client":"클라이언트: ","label_server":"서버: ","online_now":"현재접속중","last_seen_timestamp":"마지막으로본","title_not_found":"찾을수없음","unnamed_topic":"이름없는","peers_messaging_disabled":"동료의메시지가비활성화되었습니다.","enable_peers_messaging":"활성화","search_for_contacts":"검색을사용하여연락처찾기","search_no_results":"검색결과가없습니다","tabtitle_find_user":"찾기","tabtitle_new_group":"새그룹","tabtitle_group_by_id":"id로","new_password_placeholder":"새비밀번호입력","label_reset_password":"비밀번호재설정이메일보내기: ","credential_email_prompt":"등록이메일","button_reset":"재설정","button_send_request":"요청보내기","label_server_to_use":"사용할서버:","label_wire_transport":"와이어수송:","button_update":"업데이트","sidepanel_title_login":"로그인","sidepanel_title_register":"계정만들기","sidepanel_title_settings":"설정","sidepanel_title_account_settings":"계정설정","sidepanel_title_acc_general":"일반","sidepanel_title_acc_security":"보안","sidepanel_title_acc_notifications":"알림","sidepanel_title_acc_support":"지원","sidepanel_title_newtpk":"새채팅시작","sidepanel_title_cred":"자격증명확인","sidepanel_title_reset":"비밀번호재설정","sidepanel_title_archive":"보관된채팅","sidepanel_title_blocked":"차단된채팅","update_available":"업데이트가능.","reload_update":"재로드","reconnect_countdown":"연결이끊어졌습니다.다시연결하는중{초}…","reconnect_now":"지금시도","push_init_failed":"푸시알림을초기화하지못했습니다","invalid_security_token":"유효하지않은보안토큰","no_connection":"연결없음","code_doesnot_match":"코드가일치하지않습니다","phone_dative":"전화","email_dative":"이메일","enter_confirmation_code_prompt":"{방법}으로보낸확인코드를입력하십시오:","numeric_confirmation_code_prompt":"숫자만","button_confirm":"확인","save_attachment":"저장","invalid_content":"잘못된내용","user_not_found":"찾을수없음","badge_you":"당신","badge_owner":"소유자","menu_item_info":"정보","menu_item_clear_messages":"메시지지우기","menu_item_clear_messages_for_all":"모두지우기","menu_item_delete":"삭제","menu_item_delete_for_all":"모두삭제","menu_item_send_retry":"다시시도","menu_item_mute":"음소거","menu_item_unmute":"음소거해제","menu_item_delete_topic":"삭제","topic_delete_warning":"이대화를삭제하시겠습니까?","menu_item_unblock":"차단해제","menu_item_block":"차단","topic_block_warning":"이대화를차단하시겠습니까?","menu_item_member_delete":"제거","menu_item_archive_topic":"보관","action_cancel":"취소","upload_finishing":"마무리...","no_contacts":"연락처가없습니다 (._.)","contacts_not_found_short":"\'{문의}\'와일치하는연락처가없습니다.","title_group_members":"그룹구성원","title_all_contacts":"모든연락처","button_ok":"OK","button_cancel":"취소","more_online_members":"+{넘침}더보기","download_action":"다운로드","label_file_name":"파일이름: ","label_content_type":"컨텐츠유형: ","label_size":"크기: ","chat_invitation":"새로운대화를시작하도록초대되었습니다.무엇을하시겠습니까?","chat_invitation_accept":"수락","chat_invitation_ignore":"무시","chat_invitation_block":"차단","error_invalid_id":"잘못된ID","group_user_id_prompt":"그룹또는사용자ID","button_subscribe":"구독","topic_name_editing_placeholder":"그룹의자유형이름","button_create":"작성","permission_join":"참여({val})","permission_read":"읽기({val})","permission_write":"쓰기({val})","permission_pres":"알림받기({val})","permission_admin":"승인({val})","permission_share":"공유({val})","permission_delete":"삭제({val})","permission_owner":"소유자({val})","title_permissions":"권한","message_sending":"보내기...","message_sending_failed":"실패","search_placeholder":"email:<EMAIL>,tel:***********...와같은목록","messaging_disabled_prompt":"메시지비활성화","new_message_prompt":"새메시지","image_caption_prompt":"이미지설명","file_attachment_too_large":"파일크기{크기}이(가){제한}제한을초과합니다.","cannot_initiate_file_upload":"파일업로드를시작할수없습니다.","tags_not_found":"태그가정의되지않았습니다.일부를추가하십시오.","tags_editor_no_tags":"일부태그추가","title_manage_tags":"관리"},"ru":{"contacts_not_found":"Чатов нет<br />¯∖_(ツ)_/¯","full_name_prompt":"Полное имя, напр. Иван Петров","email_prompt":"Email, напр. <EMAIL>","button_sign_up":"Создать аккаунт","label_your_name":"Ваше имя","label_password":"Пароль","password_unchanged_prompt":"Не изменен","label_user_id":"ID:","label_default_access_mode":"Доступ по умолчанию:","label_message_sound":"Звук нового сообщения:","label_push_notifications":"Уведомления:","label_push_notifications_disabled":"Уведомления (требуют HTTPS):","title_tag_manager":"Теги для поиска","button_logout":"Выйти","login_prompt":"Логин","password_prompt":"Пароль","stay_logged_in":"Запомнить","forgot_password_link":"Напомнить пароль","button_sign_in":"Войти","label_client":"Клиент:","label_server":"Сервер:","online_now":"онлайн","last_seen_timestamp":"Был активен","title_not_found":"Не найден","unnamed_topic":"Без названия","messages_not_readable":"нет доступа к сообщениям","tabtitle_find_user":"найти","tabtitle_new_group":"создать","tabtitle_group_by_id":"по id","label_server_to_use":"Использовать сервер:","label_wire_transport":"Соединение:","button_update":"Применить","sidepanel_title_login":"Авторизация","sidepanel_title_register":"Зарегистрироваться","sidepanel_title_settings":"Настройки","sidepanel_title_account_settings":"Настройки аккаунта","sidepanel_title_newtpk":"Новый чат","sidepanel_title_cred":"Подтвердить","sidepanel_title_reset":"Сменить пароль","tags_not_found":"Тегов нет. Добавьте","tags_editor_no_tags":"Добавьте теги","title_manage_tags":"Редактировать","message_sending":"в пути...","message_sending_failed":"ошибка","search_placeholder":"Список, напр. email:<EMAIL>, tel:+***********...","messaging_disabled_prompt":"Отправка недоступна","new_message_prompt":"Новое сообщение","file_attachment_too_large":"Размер файла {size} превышает {limit} лимит.","cannot_initiate_file_upload":"Ошибка загрузки файла.","search_for_contacts":"Поиск контактов","enter_confirmation_code_prompt":"Код подтверждения, полученный по {method}:","numeric_confirmation_code_prompt":"Только цифры","button_confirm":"Подтвердить","button_ok":"OK","button_cancel":"Отменить","invalid_content":"сообщение не читается","label_file_name":"Имя файла:","label_content_type":"Тип:","label_size":"Размер:","phone_dative":"телефону","email_dative":"емейлу","title_group_members":"Участники","download_action":"скачать","permission_join":"Подписываться ({val})","permission_read":"Читать ({val})","permission_write":"Писать ({val})","permission_pres":"Уведомлять ({val})","permission_admin":"Подтверждать ({val})","permission_share":"Приглашать ({val})","permission_delete":"Удалять ({val})","permission_owner":"Владелец ({val})","title_permissions":"Права доступа","requested_permissions":"Требуются","granted_permissions":"Получены","menu_item_edit_permissions":"Права доступа","label_other_user":"Второй","label_topic_name":"Название","label_private":"Комментарий","private_editing_placeholder":"Виден только вам","label_muting_topic":"Без уведомлений","action_more":"Ещё","label_your_permissions":"Ваши права доступа:","label_permissions":"Права доступа:","label_you":"Вы:","label_default_access":"Права по умолчанию:","label_group_members":"Участники чата:","button_add_members":"Добавить","group_has_no_members":"Нет участников","action_leave_chat":"Уйти из чата","menu_item_info":"Информация","menu_item_clear_messages":"Удалить сообщения","menu_item_clear_messages_for_all":"Удалить для всех","menu_item_delete":"Удалить","menu_item_delete_for_all":"Удалить для всех","menu_item_send_retry":"Отправить заново","menu_item_mute":"Не уведомлять","menu_item_unmute":"Уведомлять","menu_item_delete_topic":"Удалить чат","menu_item_unblock":"Разблокировать","menu_item_block":"Заблокировать","menu_item_member_delete":"Отписать","title_info":"Подробности","new_password_placeholder":"Введите новый пароль","label_reset_password":"Отправить емейл для смены пароля:","credential_email_prompt":"Регистрационный емейл","button_reset":"Изменить","button_send_request":"Отправить","action_cancel":"отменить","upload_finishing":"завершение...","no_contacts":"Ничего нет :-(","contacts_not_found_short":"Нет контактов для запроса \'\'{query}\'\'","title_all_contacts":"Все контакты","error_invalid_id":"Неверный ID","group_user_id_prompt":"ID чата или пользователя","button_subscribe":"Подписаться","topic_name_editing_placeholder":"Название чата","button_create":"Создать","badge_you":"вы","badge_owner":"влад.","update_available":"Есть новая версия приложения.","reload_update":"Обновить","user_not_found":"Не найден","reconnect_countdown":"Нет связи. Подключение через {seconds}…","reconnect_now":"Подключить сейчас.","save_attachment":"сохранить","menu_item_archive_topic":"В архив","archived_contacts":"Чаты в архиве ({count})","sidepanel_title_archive":"Архив чатов","chat_invitation":"Вас пригласили начать новый чат. Как вы хотите поступить?","chat_invitation_accept":"Принять","chat_invitation_ignore":"Игнорировать","chat_invitation_block":"Заблокировать","peers_messaging_disabled":"Чат заблокирован у корреспондента.","enable_peers_messaging":"Разблокировать.","more_online_members":"+еще {overflow}","label_user_contacts":"Конакты:","validate_credential_action":"подтвердить","link_contact_us":"Связаться с нами","link_terms_of_service":"Условия сервиса","link_privacy_policy":"Политика конфиденциальности","action_delete_messages":"Удалить сообщения","action_block_contact":"Заблокировать контакт","action_report_chat":"Сообщить о нарушении","delete_messages_warning":"Вы действительно хотите удалить все сообщения?","leave_chat_warning":"Вы действительно хотите покинуть этот чат?","block_contact_warning":"Вы действительно заблокировать этот контакт?","report_chat_warning":"Вы действительно хотите сообщить о нарушении и заблокировать этот чат?","action_clear_messages":"Удалить сообщения","clear_messages_warning":"Вы действительно хотите удалить все сообщения в чате? Их будет невозможно восстановить.","topic_delete_warning":"Вы действительно хотите удалить этот чат?","topic_block_warning":"Вы действительно хотите заблокировать этот чат?","search_no_results":"Ничего не найдено","deleted_content":"удалено","image_caption_prompt":"Подпись к фото","label_incognito_mode":"Режим инкогнито:","delete_account":"Удалить аккаунт","delete_account_arning":"Вы уверены, что ходите удалить свой аккаунт? Его невозможно будет восстановить.","button_delete_account":"Удалить аккаунт","sidepanel_title_acc_security":"Безопасность","sidepanel_title_acc_notifications":"Уведомления","sidepanel_title_acc_support":"Поддержка","label_sdk":"SDK:","label_server_address":"Адрес сервера:","button_edit":"Редактировать","button_add_another":"Добавить","sidepanel_title_acc_general":"Общие настройки","blocked_contacts_link":"Заблокированные ({count})","sidepanel_title_blocked":"Заблокированные чаты","push_init_failed":"Ошибка инициализации пуш уведомлений","invalid_security_token":"Токен некорректен","no_connection":"Нет связи","code_doesnot_match":"Код не совпадает"},"zh":{"archived_contacts":"已归档联系人 ({count})","contacts_not_found":"你尚无会话<br />¯∖_(ツ)_/¯","full_name_prompt":"全名，例如张伟","email_prompt":"电子邮件，例如 <EMAIL>","button_sign_up":"注册","label_your_name":"你的姓名","label_password":"密码","password_unchanged_prompt":"未改变","label_user_id":"地址：","label_default_access_mode":"蓦然访问模式：","label_message_sound":"消息提示音：","label_push_notifications":"通知提醒：","label_push_notifications_disabled":"通知提醒（需要 HTTPS）：","title_tag_manager":"标签（用户发现）","button_logout":"登出","requested_permissions":"已请求","granted_permissions":"已授予","menu_item_edit_permissions":"编辑权限","label_other_user":"其他","title_info":"信息","label_topic_name":"名称","label_private":"私人评论","private_editing_placeholder":"仅自己可见","label_muting_topic":"已静音：","action_more":"更多","label_your_permissions":"你的权限：","label_permissions":"权限：","label_you":"你：","label_default_access":"默认权限模式：","label_group_members":"群组成员：","button_add_members":"添加成员","group_has_no_members":"无成员","action_leave_chat":"离开","login_prompt":"登录","password_prompt":"密码","stay_logged_in":"保持登录","forgot_password_link":"忘记密码？","button_sign_in":"登录","label_client":"客户端：","label_server":"服务器：","online_now":"在线","last_seen_timestamp":"最后可见","title_not_found":"无法找到","unnamed_topic":"未命名","messages_not_readable":"无消息访问权限","peers_messaging_disabled":"成员间消息已禁用。","enable_peers_messaging":"启用","tabtitle_find_user":"搜索","tabtitle_new_group":"新群组","tabtitle_group_by_id":"通过 id","search_for_contacts":"使用搜索寻找联系人","new_password_placeholder":"输入新密码","label_reset_password":"发送密码重置邮件：","credential_email_prompt":"你的注册邮箱","button_reset":"重置","button_send_request":"发送请求","label_server_to_use":"使用的服务器：","label_wire_transport":"线路传输：","button_update":"更新","sidepanel_title_login":"登录","sidepanel_title_register":"创建账户","sidepanel_title_settings":"设置","sidepanel_title_account_settings":"帐号设定","sidepanel_title_newtpk":"开始新会话","sidepanel_title_cred":"确认凭据","sidepanel_title_reset":"重置密码","sidepanel_title_archive":"已存档会话","update_available":"更新可用。","reload_update":"重新载入","reconnect_countdown":"连接已断开。{seconds} 秒后重新连接…","reconnect_now":"立即尝试","phone_dative":"电话","email_dative":"电子邮件","enter_confirmation_code_prompt":"输入通过{method}发送的验证码：","numeric_confirmation_code_prompt":"仅数字","button_confirm":"确认","save_attachment":"保存","invalid_content":"无效内容","user_not_found":"未找到","badge_you":"你","badge_owner":"所有者","menu_item_info":"信息","menu_item_clear_messages":"清空消息","menu_item_clear_messages_for_all":"全部清除","menu_item_delete":"删除","menu_item_delete_for_all":"全部删除","menu_item_mute":"静音","menu_item_unmute":"取消静音","menu_item_delete_topic":"删除","menu_item_unblock":"取消屏蔽","menu_item_block":"屏蔽","menu_item_member_delete":"移除","menu_item_archive_topic":"归档","action_cancel":"取消","upload_finishing":"正在结束...","no_contacts":"你尚无联系人 (._.)","contacts_not_found_short":"无联系人匹配\'\'{query}\'\'","title_group_members":"群组成员","title_all_contacts":"全部联系人","button_ok":"好","button_cancel":"取消","download_action":"下载","label_file_name":"文件名：","label_content_type":"内容类型：","label_size":"大小：","chat_invitation":"你受邀开始新会话。你想怎么做？","chat_invitation_accept":"接受","chat_invitation_ignore":"忽略","chat_invitation_block":"屏蔽","error_invalid_id":"无效 ID","group_user_id_prompt":"群组或用户 ID","button_subscribe":"订阅","topic_name_editing_placeholder":"群组自由格式名称","button_create":"创建","permission_join":"加入 ({val})","permission_read":"读取 ({val})","permission_write":"写入 ({val})","permission_pres":"获取通知 ({val})","permission_admin":"批准 ({val})","permission_share":"分享 ({val})","permission_delete":"删除 ({val})","permission_owner":"所有者 ({val})","title_permissions":"权限","message_sending":"正在发送...","message_sending_failed":"发送失败","search_placeholder":"列表如 email:<EMAIL>, tel:+***********...","messaging_disabled_prompt":"消息已禁用","new_message_prompt":"新消息","file_attachment_too_large":"文件大小 {size} 超过 {limit} 限制。","cannot_initiate_file_upload":"无法初始化文件上传。","tags_not_found":"尚未定义标签。添加一些。","tags_editor_no_tags":"添加一些标签","title_manage_tags":"管理标签","more_online_members":"还有{overflow}个","label_user_contacts":"往来：","validate_credential_action":"确认","link_contact_us":"联系我们","link_terms_of_service":"条款和条件","link_privacy_policy":"隐私政策","action_delete_messages":"删除所有帖子","action_block_contact":"屏蔽联系人","delete_messages_warning":"您确定要删除所有消息吗？无法撤消。","leave_chat_warning":"您确定要退出此对话吗？","block_contact_warning":"您确定要阻止此联系人吗？","action_report_chat":"检举垃圾邮件","report_chat_warning":"您确定要停止并报告此对话吗？","action_clear_messages":"删除讯息","clear_messages_warning":"您确定要清除所有消息吗？无法撤消。","topic_delete_warning":"您确定要删除此对话吗？","topic_block_warning":"您确定要阻止此对话吗？","search_no_results":"搜索返回任何结果","deleted_content":"内容已删除","image_caption_prompt":"图片标题","menu_item_send_retry":"重试","label_incognito_mode":"无痕模式：","delete_account":"删除帐户","delete_account_arning":"您确定要删除您的帐户吗？无法撤消。","button_delete_account":"删除帐户","sidepanel_title_acc_security":"安全","sidepanel_title_acc_notifications":"通知","sidepanel_title_acc_support":"支持","label_sdk":"开发包：","label_server_address":"服务器地址：","button_edit":"编辑","button_add_another":"加上另一个","sidepanel_title_acc_general":"常用设定","blocked_contacts_link":"封锁的联络人 ({count})","sidepanel_title_blocked":"被阻止的聊天","push_init_failed":"初始化推送通知失败","invalid_security_token":"无效的安全令牌","no_connection":"无连接","code_doesnot_match":"代码不匹配"}}')},function(e,t){e.exports=firebase},function(e,t){e.exports=firebase.messaging},function(e,t,s){"use strict";s.r(t);var a=s(0),n=s.n(a),i=s(3),o=s.n(i),r=s(1),l=s(4),d=s(5),c=(s(6),s(2)),h=s.n(c);class p extends n.a.PureComponent{render(){return this.props.visible?n.a.createElement("div",{className:"alert-container"},n.a.createElement("div",{className:"alert"},n.a.createElement("div",{className:"title"},this.props.title),n.a.createElement("div",{className:"content"},this.props.content),n.a.createElement("div",{className:"dialog-buttons"},this.props.onReject?n.a.createElement("button",{className:"outline",onClick:this.props.onReject},this.props.reject||n.a.createElement(r.FormattedMessage,{id:"button_cancel"})):null,n.a.createElement("button",{className:"blue",onClick:this.props.onConfirm},this.props.confirm||n.a.createElement(r.FormattedMessage,{id:"button_ok"}))))):null}}const m="TinodeWeb/0.16.7",u={hosted:"web.tinode.co",local:"localhost:6060"},g=u.hosted;class _{static parseUrlHash(e){let t=e.split("?",2),s={},a=[];return t[0]&&(a=t[0].substr(1).split("/")),t[1]&&t[1].split("&").forEach((function(e){let t=e.split("=");t[0]&&(s[decodeURIComponent(t[0])]=decodeURIComponent(t[1]))})),{path:a,params:s}}static navigateTo(e){window.location.hash=e}static composeUrlHash(e,t){var s=e.join("/"),a=[];for(var n in t)t.hasOwnProperty(n)&&a.push(n+"="+t[n]);return a.length>0&&(s+="?"+a.join("&")),s}static addUrlParam(e,t,s){var a=this.parseUrlHash(e);return a.params[t]=s,this.composeUrlHash(a.path,a.params)}static removeUrlParam(e,t){var s=this.parseUrlHash(e);return delete s.params[t],this.composeUrlHash(s.path,s.params)}static setUrlSidePanel(e,t){var s=this.parseUrlHash(e);return s.path[0]=t,this.composeUrlHash(s.path,s.params)}static setUrlTopic(e,t){var s=this.parseUrlHash(e);return s.path[1]=t,delete s.params.info,this.composeUrlHash(s.path,s.params)}}const b=Object(r.defineMessages)({info:{id:"menu_item_info",defaultMessage:"Info"},clear_messages:{id:"menu_item_clear_messages",defaultMessage:"Clear messages"},clear_for_all:{id:"menu_item_clear_messages_for_all",defaultMessage:"Clear for All"},delete:{id:"menu_item_delete",defaultMessage:"Delete"},delete_for_all:{id:"menu_item_delete_for_all",defaultMessage:"Delete for All"},send_retry:{id:"menu_item_send_retry",defaultMessage:"Retry"},mute:{id:"menu_item_mute",defaultMessage:"Mute"},unmute:{id:"menu_item_unmute",defaultMessage:"Unmute"},topic_delete:{id:"menu_item_delete_topic",defaultMessage:"Delete"},topic_delete_warning:{id:"topic_delete_warning",defaultMessage:"Are you sure you want to delete this conversation?"},unblock:{id:"menu_item_unblock",defaultMessage:"Unblock"},block:{id:"menu_item_block",defaultMessage:"Block"},topic_block_warning:{id:"topic_block_warning",defaultMessage:"Are you sure you want to block this conversation?"},member_delete:{id:"menu_item_member_delete",defaultMessage:"Remove"},archive:{id:"menu_item_archive_topic",defaultMessage:"Archive"}});class v extends n.a.Component{constructor(e){super(e);const{formatMessage:t}=e.intl;this.handlePageClick=this.handlePageClick.bind(this),this.handleEscapeKey=this.handleEscapeKey.bind(this),this.handleClick=this.handleClick.bind(this),this.MenuItems={topic_info:{id:"topic_info",title:t(b.info),handler:null},messages_clear:{id:"messages_clear",title:t(b.clear_messages),handler:(s,a)=>e.onShowAlert(t({id:"menu_item_clear_messages"}),t({id:"clear_messages_warning"}),()=>{this.deleteMessages(!0,!1,s,a)},null,!0,null)},messages_clear_hard:{id:"messages_clear_hard",title:t(b.clear_for_all),handler:(s,a)=>e.onShowAlert(t({id:"menu_item_clear_messages_for_all"}),t({id:"delete_messages_warning"}),()=>this.deleteMessages(!0,!0,s,a),null,!0,null)},message_delete:{id:"message_delete",title:t(b.delete),handler:(e,t)=>this.deleteMessages(!1,!1,e,t)},message_delete_hard:{id:"message_delete_hard",title:t(b.delete_for_all),handler:(e,t)=>this.deleteMessages(!1,!0,e,t)},menu_item_send_retry:{id:"menu_item_send_retry",title:t(b.send_retry),handler:(e,t)=>this.retryMessage(e,t)},topic_unmute:{id:"topic_unmute",title:t(b.unmute),handler:this.topicPermissionSetter.bind(this,"+P")},topic_mute:{id:"topic_mute",title:t(b.mute),handler:this.topicPermissionSetter.bind(this,"-P")},topic_unblock:{id:"topic_unblock",title:t(b.unblock),handler:this.topicPermissionSetter.bind(this,"+JP")},topic_block:{id:"topic_block",title:t(b.block),handler:(s,a)=>e.onShowAlert(t({id:"menu_item_block"}),t(b.topic_block_warning),()=>this.topicPermissionSetter("-JP",s,a).then(e=>(this.props.onTopicRemoved(s.topicName),e)),null,!0,null)},topic_delete:{id:"topic_delete",title:t(b.topic_delete),handler:(s,a)=>e.onShowAlert(t({id:"menu_item_delete_topic"}),t(b.topic_delete_warning),()=>{const e=this.props.tinode.getTopic(s.topicName);if(e)return e.delTopic(!0).catch(e=>{a&&a(e.message,"err")});console.log("Topic not found: ",s.topicName)},null,!0,null)},topic_archive:{id:"topic_archive",title:t(b.archive),handler:(e,t)=>{const s=this.props.tinode.getTopic(e.topicName);if(s)return s.archive(!0).catch(e=>{t&&t(e.message,"err")});console.log("Topic not found: ",e.topicName)}},permissions:{id:"permissions",title:t({id:"menu_item_edit_permissions"}),handler:null},member_delete:{id:"member_delete",title:t(b.member_delete),handler:(e,t)=>{const s=this.props.tinode.getTopic(e.topicName);if(s&&e.user)return s.delSubscription(e.user).catch(e=>{t&&t(e.message,"err")});console.log("Topic or user not found: '"+e.topicName+"', '"+e.user+"'")}},member_mute:{id:"member_mute",title:t(b.mute),handler:this.topicPermissionSetter.bind(this,"-P")},member_unmute:{id:"member_unmute",title:t(b.unmute),handler:this.topicPermissionSetter.bind(this,"+P")},member_block:{id:"member_block",title:t(b.block),handler:this.topicPermissionSetter.bind(this,"-JP")},member_unblock:{id:"member_unblock",title:t(b.unblock),handler:this.topicPermissionSetter.bind(this,"+JP")}}}componentDidMount(){document.addEventListener("mousedown",this.handlePageClick,!1),document.addEventListener("keyup",this.handleEscapeKey,!1)}componentWillUnmount(){document.removeEventListener("mousedown",this.handlePageClick,!1),document.removeEventListener("keyup",this.handleEscapeKey,!1)}handlePageClick(e){o.a.findDOMNode(this).contains(e.target)||(e.preventDefault(),e.stopPropagation(),this.props.hide())}handleEscapeKey(e){27===e.keyCode&&this.props.hide()}handleClick(e){e.preventDefault(),e.stopPropagation(),this.props.hide();let t=this.props.items[e.currentTarget.dataset.id];"string"==typeof t&&(t=this.MenuItems[t]),t?this.props.onAction(t.id,t.handler(this.props.params,this.props.onError),this.props.params):console.log("Invalid menu item ID",e.currentTarget.dataset.id)}deleteMessages(e,t,s,a){const n=this.props.tinode.getTopic(s.topicName);if(!n)return void console.log("Topic not found: ",s.topicName);if(!e&&n.cancelSend(s.seq))return;return(e?n.delMessagesAll(t):n.delMessagesList([s.seq],t)).catch(e=>{a&&a(e.message,"err")})}retryMessage(e,t){const s=this.props.tinode.getTopic(e.topicName);if(!s||!s.flushMessage(e.seq))return;const a=s.createMessage(e.content,!1);return s.publishDraft(a).catch(e=>{t&&t(e.message,"err")})}topicPermissionSetter(e,t,s){const a=this.props.tinode.getTopic(t.topicName);if(!a)return void console.log("Topic not found",t.topicName);let n=a.updateMode(t.user,e);return s&&(n=n.catch(e=>{s(e.message,"err")})),n}render(){let e=0,t=[];this.props.items.map(s=>{"string"==typeof s&&(s=this.MenuItems[s]),s&&s.title&&t.push("-"==s.title?n.a.createElement("li",{className:"separator",key:e}):n.a.createElement("li",{onClick:this.handleClick,"data-id":e,key:e},s.title)),e++});const s=13*(.7+2.5*t.length),a={left:(this.props.bounds.right-this.props.clickAt.x<156?this.props.clickAt.x-this.props.bounds.left-156:this.props.clickAt.x-this.props.bounds.left)+"px",top:(this.props.bounds.bottom-this.props.clickAt.y<s?this.props.clickAt.y-this.props.bounds.top-s:this.props.clickAt.y-this.props.bounds.top)+"px"};return n.a.createElement("ul",{className:"menu",style:a},t)}}var f=Object(r.injectIntl)(v);function E(e,t){t=t||window.navigator.userLanguage||window.navigator.language;const s=new Date;return e.getFullYear()==s.getFullYear()?e.getMonth()==s.getMonth()&&e.getDate()==s.getDate()?e.toLocaleTimeString(t,{hour12:!1,hour:"2-digit",minute:"2-digit"}):e.toLocaleDateString(t,{hour12:!1,month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):e.toLocaleDateString(t,{hour12:!1,year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}function w(e){if(!e||0==e)return"0 Bytes";const t=["Bytes","KB","MB","GB","TB","PB"],s=Math.min(0|Math.floor(Math.log2(e)/10),t.length-1),a=e/Math.pow(1024,s),n=s>0?a<3?2:a<30?1:0:0;return a.toFixed(n)+" "+t[s]}class C extends n.a.PureComponent{render(){let e;if(!0===this.props.avatar){const t="grp"==h.a.topicType(this.props.topic),s=(t?"light-color":"dark-color")+Math.abs(function(e){let t=0;e=""+e;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t&=t;return t}(this.props.topic))%16;if(this.props.topic&&this.props.title&&this.props.title.trim()){const t=this.props.title.trim().charAt(0),a="lettertile "+s;e=n.a.createElement("div",{className:a},n.a.createElement("div",null,t))}else{const a="material-icons "+s;e=t?n.a.createElement("i",{className:a},"group"):n.a.createElement("i",{className:a},"person")}}else e=this.props.avatar?n.a.createElement("img",{className:"avatar",alt:"avatar",src:this.props.avatar,onError:e=>{e.target.onerror=null,e.target.src="/img/broken_image.png"}}):null;return e}}const S=["image/jpeg","image/gif","image/png","image/svg","image/svg+xml"],M=["jpg","gif","png","svg","svg"];function k(e){return e&&e.type&&e.data?"data:image/"+e.type+";base64,"+e.data:null}function y(e,t,s,a,n){if(t|=0,s|=0,a|=0,(e|=0)<=0||t<=0||s<=0||a<=0)return null;n&&(s=a=Math.min(s,a));let i=Math.min(Math.min(e,s)/e,Math.min(t,a)/t),o={dstWidth:e*i|0,dstHeight:t*i|0};return n?(o.dstWidth=o.dstHeight=Math.min(o.dstWidth,o.dstHeight),o.srcWidth=o.srcHeight=Math.min(e,t),o.xoffset=(e-o.srcWidth)/2|0,o.yoffset=(t-o.srcWidth)/2|0):(o.xoffset=o.yoffset=0,o.srcWidth=e,o.srcHeight=t),o}function N(e,t){var s=S.indexOf(t),a=M[s],n=e.lastIndexOf(".");return n>=0&&(e=e.substring(0,n)),e+"."+a}function T(e,t,s,a,n,i){var o=new Image;o.crossOrigin="Anonymous",o.onerror=function(e){i("Image format unrecognized")},o.onload=function(){var o=y(this.width,this.height,t,s,a);if(o){var r=document.createElement("canvas");r.width=o.dstWidth,r.height=o.dstHeight;var l=r.getContext("2d");l.imageSmoothingEnabled=!0,l.drawImage(this,o.xoffset,o.yoffset,o.srcWidth,o.srcHeight,0,0,o.dstWidth,o.dstHeight);var d=this.width!=o.dstWidth||this.height!=o.dstHeight||S.indexOf(e.type)<0?"image/jpeg":e.type,c=r.toDataURL(d);if(d=D(c.split(",")[0])){var h=.78;if(F(c.length)>195584&&(d="image/jpeg"),"image/jpeg"==d)for(;F(c.length)>195584&&h>.45;)c=r.toDataURL(d,h),h*=.84;F(c.length)>195584?i("The image size "+bytesToHumanSize(F(c.length))+" exceeds the "+bytesToHumanSize(195584)+" limit.","err"):(r=null,n(c.split(",")[1],d,o.dstWidth,o.dstHeight,N(e.name,d)))}else i("Unsupported image format")}else i("Invalid image")},o.src=URL.createObjectURL(e)}function A(e,t,s){var a=new FileReader;a.addEventListener("load",(function(){var n=a.result.split(","),i=D(n[0]);if(i){var o=new Image;o.crossOrigin="Anonymous",o.onload=function(){t(n[1],i,this.width,this.height,N(e.name,i))},o.onerror=function(e){s("Image format not recognized")},o.src=URL.createObjectURL(e)}else s("Failed to process image file")}),!1),a.readAsDataURL(e)}function P(e,t,s){var a=new FileReader;a.addEventListener("load",(function(){t(e.type,a.result.split(",")[1],e.name)})),a.readAsDataURL(e)}function D(e){var t=/^data:(image\/[-+a-z0-9.]+);base64/.exec(e);return t&&t.length>1?t[1]:null}function F(e){return 3*Math.floor(e/4)}class U extends n.a.Component{constructor(e){super(e),this.state={dataUrl:e.avatar},this.handleFileUpload=this.handleFileUpload.bind(this)}componentDidUpdate(e){this.props.avatar!=e.avatar&&this.setState({dataUrl:this.props.avatar})}handleFileUpload(e){T(e.target.files[0],128,128,!0,(e,t)=>{var s=k({data:e,type:t});this.setState({dataUrl:s}),this.props.onImageChanged(s)},e=>{this.props.onError(e,"err")}),e.target.value=""}render(){const e="file-input-avatar-"+(Math.random()+"").substr(2),t="avatar-upload"+(this.props.readOnly?" read-only":"");return n.a.createElement("div",{className:t},this.props.readOnly||!this.state.dataUrl?null:n.a.createElement("a",{href:"#",className:"clear-avatar",onClick:e=>{e.preventDefault(),this.props.onImageChanged(null)}},n.a.createElement("i",{className:"material-icons"},"clear")),this.state.dataUrl?n.a.createElement("img",{src:this.state.dataUrl,className:"preview"}):this.props.readOnly&&this.props.uid?n.a.createElement("div",{className:"avatar-box"},n.a.createElement(C,{avatar:!0,topic:this.props.uid,title:this.props.title})):n.a.createElement("div",{className:"blank"},128,"×",128),this.props.readOnly?null:n.a.createElement("input",{type:"file",id:e,className:"inputfile hidden",accept:"image/*",onChange:this.handleFileUpload}),this.props.readOnly?null:n.a.createElement("label",{htmlFor:e,className:"round"},n.a.createElement("i",{className:"material-icons"},"file_upload")))}}class x extends n.a.PureComponent{constructor(e){super(e),this.handleChange=this.handleChange.bind(this)}handleChange(){this.props.onChange(this.props.name,!this.props.checked)}render(){return this.props.onChange?this.props.checked?n.a.createElement("i",{className:"material-icons blue clickable",onClick:this.handleChange},"check_box"):n.a.createElement("i",{className:"material-icons blue clickable",onClick:this.handleChange},"check_box_outline_blank"):this.props.checked?n.a.createElement("i",{className:"material-icons"},"check_box"):n.a.createElement("i",{className:"material-icons"},"check_box_outline_blank")}}const I={muted:"notifications_off",banned:"block"};class R extends n.a.PureComponent{render(){let e=null;return this.props.badges&&this.props.badges.length>0?(e=[],this.props.badges.map((function(t){if(t.icon)e.push(n.a.createElement("i",{className:"material-icons as-badge",key:t.key||t.icon},I[t.icon]));else{const s="badge"+(t.color?" "+t.color:"");e.push(n.a.createElement("span",{className:s,key:t.key||t.name},t.name))}})),n.a.createElement(n.a.Fragment,null,e)):null}}class L extends n.a.PureComponent{render(){return this.props.count>0?n.a.createElement("span",{className:"unread"},this.props.count>9?"9+":this.props.count):null}}class q extends n.a.Component{constructor(e){super(e),this.handleClick=this.handleClick.bind(this),this.handleContextClick=this.handleContextClick.bind(this)}handleClick(e){e.preventDefault(),e.stopPropagation(),this.props.onSelected&&this.props.onSelected(this.props.item,this.props.index,this.props.now,this.props.acs)}handleContextClick(e){e.preventDefault(),e.stopPropagation(),this.props.showContextMenu({topicName:this.props.item,y:e.pageY,x:e.pageX})}render(){let e=this.props.title;e?e.length>30&&(e=e.substring(0,28)+"..."):e=n.a.createElement("i",null,n.a.createElement(r.FormattedMessage,{id:"unnamed_topic"}));const t=this.props.now?"online":"offline",s=!this.props.avatar||this.props.avatar,a=this.props.badges?this.props.badges.slice():[],i=[];return this.props.acs&&(this.props.showMode&&a.push({name:this.props.acs.getMode(),key:"mode"}),this.props.acs.isMuted()&&i.push({icon:"muted"}),this.props.acs.isJoiner()||i.push({icon:"banned"})),n.a.createElement("li",{className:!this.props.showCheckmark&&this.props.selected?"selected":null,onClick:this.handleClick},n.a.createElement("div",{className:"avatar-box"},n.a.createElement(C,{avatar:s,title:this.props.title,topic:this.props.item}),this.props.showOnline?n.a.createElement("span",{className:t}):this.props.showCheckmark&&this.props.selected?n.a.createElement("i",{className:"checkmark material-icons"},"check_circle"):null),n.a.createElement("div",{className:"text-box"},n.a.createElement("div",null,n.a.createElement("span",{className:"contact-title"},e),n.a.createElement(L,{count:this.props.unread}),n.a.createElement(R,{badges:i})),this.props.comment?n.a.createElement("div",{className:"contact-comment"},this.props.comment):null,n.a.createElement("span",null,n.a.createElement(R,{badges:a}))),this.props.showContextMenu?n.a.createElement("span",{className:"menuTrigger"},n.a.createElement("a",{href:"#",onClick:this.handleContextClick},n.a.createElement("i",{className:"material-icons"},"expand_more"))):null)}}class O extends n.a.PureComponent{constructor(e){super(e),this.handleClick=this.handleClick.bind(this)}handleClick(e){e.preventDefault(),e.stopPropagation(),this.props.onAction(this.props.action)}render(){const{formatMessage:e}=this.props.intl;return n.a.createElement("li",{onClick:this.handleClick,className:"action"},n.a.createElement("div",{className:"action-text"},e(this.props.title,this.props.values)))}}var j=Object(r.injectIntl)(O);const B=Object(r.defineMessages)({badge_you:{id:"badge_you",defaultMessage:"you"},badge_owner:{id:"badge_owner",defaultMessage:"owner"}});class H extends n.a.Component{render(){const{formatMessage:e}=this.props.intl,t=Array.isArray(this.props.topicSelected),s=[];let a=0;return this.props.contacts&&this.props.contacts.length>0&&this.props.contacts.map(i=>{if(i.action)s.push(n.a.createElement(j,{title:i.title,action:i.action,values:i.values,key:i.action,onAction:this.props.onAction}));else{const o=this.props.showMode?i.user:i.topic||i.user;if(this.props.filterFunc&&this.props.filter){let e=[o];if(i.private&&i.private.comment&&e.push((""+i.private.comment).toLowerCase()),i.public&&i.public.fn&&e.push((""+i.public.fn).toLowerCase()),!this.props.filterFunc(this.props.filter,e))return}const r=t?this.props.topicSelected.indexOf(o)>-1:this.props.topicSelected===o,l=[];this.props.showMode&&(o==this.props.myUserId&&l.push({name:e(B.badge_you),color:"green"}),i.acs&&i.acs.isOwner()&&l.push({name:e(B.badge_owner),color:"blue"}));const d=Array.isArray(i.private)?i.private.join(","):i.private?i.private.comment:null;s.push(n.a.createElement(q,{title:i.public?i.public.fn:null,avatar:k(i.public?i.public.photo:null),comment:d,unread:this.props.showUnread?i.unread:0,now:i.online&&this.props.connected,acs:i.acs,showMode:this.props.showMode,badges:l,showCheckmark:t,selected:r,showOnline:this.props.showOnline,onSelected:this.props.onTopicSelected,showContextMenu:this.props.showContextMenu,item:o,index:s.length,key:o})),a++}},this),n.a.createElement("div",{className:this.props.noScroll?null:"scrollable-panel"},0==a?n.a.createElement("div",{className:"center-medium-text",dangerouslySetInnerHTML:{__html:this.props.emptyListMessage}}):null,s.length>0?n.a.createElement("ul",{className:"contact-box"},s):null)}}var z=Object(r.injectIntl)(H);class K extends n.a.PureComponent{render(){return n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onCancel()}},n.a.createElement("i",{className:"material-icons"},"close"))}}class W extends n.a.PureComponent{constructor(e){super(e),this.state={show:!1},this.hide=this.hide.bind(this)}componentDidUpdate(e){e.level!==this.props.level&&this.setState({show:!!this.props.level})}hide(){this.setState({show:!1}),this.props.onClearError&&this.props.onClearError()}render(){const e={err:"error",warn:"warning",info:"info"}[this.props.level]||"",t="info-box "+e;return n.a.createElement("div",{className:t},n.a.createElement("div",{className:"icon"},n.a.createElement("i",{className:"material-icons"},e)),n.a.createElement("span",null,this.props.text,this.props.action?n.a.createElement(n.a.Fragment,null,n.a.createElement("a",{href:"#",style:{whiteSpace:"nowrap"},onClick:e=>{e.preventDefault(),this.props.action()}},this.props.actionText)):null),n.a.createElement("div",{className:"cancel"},n.a.createElement(K,{onCancel:this.hide})))}}class V extends n.a.PureComponent{constructor(e){super(e),this.handleCancel=this.handleCancel.bind(this)}handleCancel(e){e.preventDefault(),this.props.onCancel(this.props.topic,this.props.index)}render(){const e=this.props.title||this.props.topic,t=this.props.invalid?"chip invalid":"chip";return n.a.createElement("div",{className:t},this.props.noAvatar?n.a.createElement("span",{className:"spacer"}):n.a.createElement("div",{className:"avatar-box"},n.a.createElement(C,{avatar:this.props.avatar||!0,topic:this.props.topic,title:this.props.title})),n.a.createElement("span",null,e),this.props.onCancel&&!this.props.required?n.a.createElement("a",{href:"#",onClick:this.handleCancel},"×"):n.a.createElement("span",{className:"spacer"}))}}class G extends n.a.Component{constructor(e){super(e),this.state=G.deriveStateFromProps(e),this.state.input="",this.state.focused=!1,this.handleTextInput=this.handleTextInput.bind(this),this.removeChipAt=this.removeChipAt.bind(this),this.handleChipCancel=this.handleChipCancel.bind(this),this.handleFocusGained=this.handleFocusGained.bind(this),this.handleFocusLost=this.handleFocusLost.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this)}static deriveStateFromProps(e){return{placeholder:e.chips?"":e.prompt,sortedChips:G.sortChips(e.chips,e.staticMembers),chipIndex:G.indexChips(e.chips)}}componentDidUpdate(e,t){e.chips==this.props.chips&&e.staticMembers==this.props.staticMembers&&e.prompt==this.props.prompt||this.setState(G.deriveStateFromProps(this.props)),(!t||this.props.chips.length>t.sortedChips.length)&&this.setState({input:""})}static indexChips(e){const t={};let s=0;return e.map(e=>{t[e.user]=s,s++}),t}static sortChips(e,t){const s=[],a=[];return e.map(e=>{t&&t.includes(e.user)?s.push(e):a.push(e)}),s.concat(a)}handleTextInput(e){this.setState({input:e.target.value}),this.props.filterFunc&&this.props.filterFunc(e.target.value)}removeChipAt(e){const t=this.state.sortedChips[e];this.props.onChipRemoved(t.user,this.state.chipIndex[t.user])}handleChipCancel(e,t){this.removeChipAt(t)}handleFocusGained(){this.setState({focused:!0})}handleFocusLost(){this.setState({focused:!1}),this.props.onFocusLost&&this.props.onFocusLost(this.state.input)}handleKeyDown(e){if("Backspace"===e.key){if(0==this.state.input.length&&this.state.sortedChips.length>0){const e=this.state.sortedChips.length-1;this.state.sortedChips[e].user!==this.props.staticMembers&&this.removeChipAt(e)}}else"Enter"===e.key?this.props.onEnter&&this.props.onEnter(this.state.input):"Escape"===e.key&&this.props.onCancel&&this.props.onCancel()}render(){const e=[];let t=0;const s=this.props.staticMembers||[];this.state.sortedChips.map(a=>{e.push(n.a.createElement(V,{onCancel:this.handleChipCancel,avatar:k(a.public?a.public.photo:null),title:a.public?a.public.fn:void 0,noAvatar:this.props.avatarDisabled,topic:a.user,required:s.includes(a.user),invalid:a.invalid,index:t,key:a.user})),t++});const a="chip-input"+(this.state.focused?" focused":"");return n.a.createElement("div",{className:a},e,n.a.createElement("input",{type:"text",placeholder:this.state.placeholder,onChange:this.handleTextInput,onFocus:this.handleFocusGained,onBlur:this.handleFocusLost,onKeyDown:this.handleKeyDown,value:this.state.input,autoFocus:!0}))}}const J=Object(r.defineMessages)({no_contacts:{id:"no_contacts",defaultMessage:"You have no contacts :-("},contacts_not_found_short:{id:"contacts_not_found_short",defaultMessage:"No contacts match ''{query}''"}});class Q extends n.a.Component{constructor(e){super(e),this.state={members:e.members,index:Q.indexMembers(e.members),staticMembers:Q.staticMembers(e.members,e.keepInitialMembers,e.requiredMember),contactFilter:"",noContactsMessage:e.intl.formatMessage(J.no_contacts),selectedContacts:Q.selectedContacts(e.members)},this.handleContactSelected=this.handleContactSelected.bind(this),this.handleMemberRemoved=this.handleMemberRemoved.bind(this),this.handleContactFilter=this.handleContactFilter.bind(this),this.handleSubmit=this.handleSubmit.bind(this),this.handleCancel=this.handleCancel.bind(this)}static indexMembers(e){let t={};return e.map(e=>{t[e.user]={delta:0,present:!0}}),t}static staticMembers(e,t,s){let a=[];return e.map(e=>{(t||e.user==s)&&a.push(e.user)}),a}static selectedContacts(e){let t=[];return e.map(e=>{t.push(e.user)}),t}handleContactSelected(e,t){let s=this.state.index[e];if(s){if(s.present)return;s.delta+=1,s.present=!0}else s={delta:1,present:!0};let a=this.state.members.slice();a.push(this.props.contacts[t]);const n=Q.selectedContacts(a),i=this.state.index;i[e]=s,this.setState({members:a,index:i,selectedContacts:n})}handleMemberRemoved(e,t){const s=this.state.index[e];if(!s||!s.present)return;s.present=!1,s.delta-=1;let a=this.state.members.slice();a.splice(t,1);const n=Q.selectedContacts(a),i=this.state.index;i[e]=s,this.setState({members:a,index:i,selectedContacts:n})}handleContactFilter(e){const{formatMessage:t}=this.props.intl,s=e?t(J.contacts_not_found_short,{query:e}):t(J.no_contacts);this.setState({contactFilter:e,noContactsMessage:s})}static doContactFiltering(e,t){if(e){for(let s=0;s<t.length;s++)if(t[s].indexOf(e)>=0)return!0;return!1}return!0}handleSubmit(){var e=this,t=[],s=[],a=[];Object.keys(this.state.index).map((function(n){e.state.index[n].present&&t.push(n),e.state.index[n].delta>0?s.push(n):e.state.index[n].delta<0&&a.push(n)})),this.props.onSubmit(t,s,a)}handleCancel(){this.props.onCancel()}render(){const{formatMessage:e}=this.props.intl;return n.a.createElement("div",{id:"group-manager"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"title_group_members",defaultMessage:"Group Members"}))),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement(G,{chips:this.state.members,staticMembers:this.state.staticMembers,prompt:"add members",filterFunc:this.handleContactFilter,onChipRemoved:this.handleMemberRemoved})),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"title_all_contacts",defaultMessage:"All Contacts"}))),n.a.createElement(z,{contacts:this.props.contacts,myUserId:this.props.myUserId,topicSelected:this.state.selectedContacts,filter:this.state.contactFilter,filterFunc:Q.doContactFiltering,emptyListMessage:this.state.noContactsMessage,showOnline:!1,showUnread:!1,onTopicSelected:this.handleContactSelected}),n.a.createElement("div",{id:"group-manager-buttons",className:"panel-form-row"},n.a.createElement("button",{className:"blue",onClick:this.handleSubmit},n.a.createElement(r.FormattedMessage,{id:"button_ok",defaultMessage:"OK"})),n.a.createElement("button",{className:"white",onClick:this.handleCancel},n.a.createElement(r.FormattedMessage,{id:"button_cancel",defaultMessage:"Cancel"}))))}}var Y=Object(r.injectIntl)(Q);class Z extends n.a.PureComponent{constructor(e){super(e),this.state={value:this.props.value,visible:!1},this.handleVisibility=this.handleVisibility.bind(this),this.handeTextChange=this.handeTextChange.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleEditingFinished=this.handleEditingFinished.bind(this)}handeTextChange(e){this.setState({value:e.target.value}),this.props.onChange&&this.props.onChange(e)}handleVisibility(e){e.preventDefault(),this.setState({visible:!this.state.visible})}handleKeyDown(e){27==e.keyCode?(this.setState({value:this.props.value,visible:!1}),this.props.onFinished&&this.props.onFinished()):13==e.keyCode&&this.handleEditingFinished()}handleEditingFinished(e){if(e){let t=e.currentTarget;setTimeout(()=>{t.contains(document.activeElement)||this.props.onFinished&&this.props.onFinished(this.state.value)},0)}else this.props.onFinished&&this.props.onFinished(this.state.value.trim())}render(){return n.a.createElement("div",{tabIndex:"-1",className:"group-focus",onBlur:this.handleEditingFinished},n.a.createElement("input",{className:"with-visibility",type:this.state.visible?"text":"password",value:this.state.value,placeholder:this.props.placeholder,required:this.props.required?"required":"",autoFocus:this.props.autoFocus?"autoFocus":"",autoComplete:this.props.autoComplete,onChange:this.handeTextChange,onKeyDown:this.handleKeyDown}),n.a.createElement("span",{onClick:this.handleVisibility},n.a.createElement("i",{className:"material-icons clickable light-gray"},this.state.visible?"visibility":"visibility_off")))}}class X extends n.a.Component{constructor(e){super(e),this.state={active:e.active,initialValue:e.value||"",value:e.value||""},this.handeTextChange=this.handeTextChange.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleStartEditing=this.handleStartEditing.bind(this),this.handleEditingFinished=this.handleEditingFinished.bind(this),this.handlePasswordFinished=this.handlePasswordFinished.bind(this)}componentDidUpdate(e,t){const s=this.props.value||"";t.initialValue==s||t.active||this.setState({initialValue:s,value:s})}handeTextChange(e){this.setState({value:e.target.value})}handleKeyDown(e){27===e.keyCode?this.setState({value:this.props.value,active:!1}):13===e.keyCode&&this.handleEditingFinished(e)}handleStartEditing(){this.props.readOnly||(o.a.findDOMNode(this).focus(),this.setState({active:!0}))}handleEditingFinished(e){if(this.props.required&&!e.target.checkValidity())return void this.setState({value:this.props.value,active:!1});this.setState({active:!1});let t=this.state.value.trim();(t||this.props.value)&&t!==this.props.value&&this.props.onFinished(t)}handlePasswordFinished(e){this.setState({active:!1}),e&&e!==this.props.value&&this.props.onFinished(e)}render(){if(this.state.active)var e=this.props.type||"text";else{var t="password"==this.props.type?"••••••••":this.state.value,s="in-place-edit"+(this.props.readOnly?" disabled":"");t||(t=this.props.placeholder,s+=" placeholder"),t.length>20&&(t=t.substring(0,19)+"...")}return this.state.active?"password"==e?n.a.createElement(Z,{value:this.state.value,placeholder:this.props.placeholder,required:this.props.required?"required":"",autoComplete:this.props.autoComplete,autoFocus:!0,onFinished:this.handlePasswordFinished}):n.a.createElement("input",{type:e,value:this.state.value,placeholder:this.props.placeholder,required:this.props.required?"required":"",autoComplete:this.props.autoComplete,autoFocus:!0,onChange:this.handeTextChange,onKeyDown:this.handleKeyDown,onBlur:this.handleEditingFinished}):n.a.createElement("span",{className:s,onClick:this.handleStartEditing},n.a.createElement("span",{className:"content"},t))}}class $ extends n.a.PureComponent{constructor(e){super(e),this.state={open:e.open},this.handleToggle=this.handleToggle.bind(this)}handleToggle(){const e=!this.state.open;this.setState({open:e}),this.props.onToggle&&this.props.onToggle(e)}render(){return n.a.createElement("label",{className:"small clean-clickable",onClick:this.handleToggle},this.props.title,"...",this.state.open?n.a.createElement("i",{className:"material-icons"},"expand_more"):n.a.createElement("i",{className:"material-icons"},"chevron_right"))}}const ee=Object(r.defineMessages)({joiner:{id:"permission_join",defaultMessage:"Join ({val})"},reader:{id:"permission_read",defaultMessage:"Read ({val})"},writer:{id:"permission_write",defaultMessage:"Write ({val})"},preser:{id:"permission_pres",defaultMessage:"Get notified ({val})"},approver:{id:"permission_admin",defaultMessage:"Approve ({val})"},sharer:{id:"permission_share",defaultMessage:"Share ({val})"},deleter:{id:"permission_delete",defaultMessage:"Delete ({val})"},owner:{id:"permission_owner",defaultMessage:"Owner ({val})"}});class te extends n.a.Component{constructor(e){super(e),this.state={mode:(e.mode||"").replace("N","")},this.handleChange=this.handleChange.bind(this),this.handleSubmit=this.handleSubmit.bind(this),this.handleCancel=this.handleCancel.bind(this)}handleChange(e){let t=this.state.mode;-1==t.indexOf(e)?t+=e:t=t.replace(e,""),this.setState({mode:t})}handleSubmit(){var e=(this.state.mode||"N").split("").sort().join("");e!==(this.props.mode||"N").split("").sort().join("")?this.props.onSubmit(e):this.props.onCancel()}handleCancel(){this.props.onCancel()}render(){const{formatMessage:e}=this.props.intl,t={J:e(ee.joiner,{val:"J"}),R:e(ee.reader,{val:"R"}),W:e(ee.writer,{val:"W"}),P:e(ee.preser,{val:"P"}),A:e(ee.approver,{val:"A"}),S:e(ee.sharer,{val:"S"}),D:e(ee.deleter,{val:"D"}),O:e(ee.owner,{val:"O"})};let s=this.props.skip||"",a=this.state.mode,i=(this.props.compare||"").replace("N",""),o=[];for(let e=0;e<"JRWPASDO".length;e++){let r="JRWPASDO".charAt(e);s.indexOf(r)>=0&&a.indexOf(r)<0||o.push(n.a.createElement("tr",{key:r},n.a.createElement("td",null,t[r]),n.a.createElement("td",{className:"checkbox"},s.indexOf(r)<0?n.a.createElement(x,{name:r,checked:a.indexOf(r)>=0,onChange:this.handleChange}):n.a.createElement(x,{name:r,checked:a.indexOf(r)>=0})),this.props.compare?n.a.createElement("td",{className:"checkbox"},n.a.createElement(x,{name:r,checked:i.indexOf(r)>=0})):null))}return n.a.createElement("div",{className:"panel-form-column"},this.props.userTitle?n.a.createElement("ul",{className:"contact-box"},n.a.createElement(q,{item:this.props.item,title:this.props.userTitle,avatar:k(this.props.userAvatar?this.props.userAvatar:null)})):null,n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"title_permissions",defaultMessage:"Permissions"})),n.a.createElement("table",{className:"permission-editor"},this.props.compare?n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null),n.a.createElement("th",null,this.props.modeTitle),n.a.createElement("th",null,this.props.compareTitle))):null,n.a.createElement("tbody",null,o)),n.a.createElement("br",null),n.a.createElement("div",{className:"dialog-buttons"},n.a.createElement("button",{className:"outline",onClick:this.handleCancel},n.a.createElement(r.FormattedMessage,{id:"button_cancel"})),n.a.createElement("button",{className:"blue",onClick:this.handleSubmit},n.a.createElement(r.FormattedMessage,{id:"button_ok"}))))}}var se=Object(r.injectIntl)(te);function ae(e){const t=document.getElementById("shortcut-icon"),s=document.head||document.getElementsByTagName("head")[0],a=document.createElement("link");a.type="image/png",a.id="shortcut-icon",a.rel="shortcut icon",a.href="img/logo32x32"+(e>0?"a":"")+".png",t&&s.removeChild(t),s.appendChild(a),document.title=(e>0?"("+e+") ":"")+"Tinode"}function ne(e,t){let s=null;if((e&&e.trim()||t)&&(s={},e&&(s.fn=e.trim()),t)){const e=t.indexOf(",");s.photo=e>=0?{data:t.substring(e+1),type:"jpg"}:t}return s}function ie(e,t){if(e===t)return!0;if(!Array.isArray(e)||!Array.isArray(t))return!1;if(e.length!=t.length)return!1;e.sort(),t.sort();for(let s=0,a=e.length;s<a;s++)if(e[s]!==t[s])return!1;return!0}function oe(e,t){if(!e)return null;if(e=e.replace(/[^\x20-\x7E]/gim,"").trim(),!/^([a-z][a-z0-9+.-]*:|\/\/)/i.test(e))return e;if(/^blob:http/.test(e))return e;const s=Array.isArray(t)?t.join("|"):"http|https";return new RegExp("^(("+s+"):|//)","i").test(e)?e:null}class re extends n.a.Component{constructor(e){super(e),this.state={tags:this.props.tags,tagInput:"",activated:this.props.activated},this.handleTagInput=this.handleTagInput.bind(this),this.handleAddTag=this.handleAddTag.bind(this),this.handleRemoveTag=this.handleRemoveTag.bind(this),this.handleSubmit=this.handleSubmit.bind(this),this.handleCancel=this.handleCancel.bind(this)}static getDerivedStateFromProps(e,t){return ie(e.tags,t.tags)||t.activated?null:{tags:e.tags}}handleTagInput(e){if(this.setState({tagInput:e}),e.length>0){const t=e[e.length-1];'"'==e[0]?e.length>1&&'"'==t&&this.handleAddTag(e.substring(1,e.length-1)):","!=t&&" "!=t&&";"!=t&&'"'!=t||this.handleAddTag(e.substring(0,e.length-1).trim())}}handleAddTag(e){if(e.length>0&&this.state.tags.length<16){const t=this.state.tags.slice(0);return t.push(e),this.setState({tags:t,tagInput:""}),this.props.onTagsChanged&&this.props.onTagsChanged(t),t}return this.state.tags}handleRemoveTag(e,t){const s=this.state.tags.slice(0);s.splice(t,1),this.setState({tags:s}),this.props.onTagsChanged&&this.props.onTagsChanged(s)}handleSubmit(){this.props.onSubmit(this.handleAddTag(this.state.tagInput.trim())),this.setState({activated:!1,tags:this.props.tags})}handleCancel(){this.setState({activated:!1,tagInput:"",tags:this.props.tags}),this.props.onCancel&&this.props.onCancel()}render(){let e=[];return this.state.activated?this.state.tags.map(t=>{e.push({user:t,invalid:t.length<4})}):(this.state.tags.map(t=>{e.push(n.a.createElement("span",{className:"badge",key:e.length},t))}),0==e.length&&(e=n.a.createElement("i",null,n.a.createElement(r.FormattedMessage,{id:"tags_not_found",defaultMessage:"No tags defined. Add some."})))),n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},this.props.title)),this.state.activated?n.a.createElement("div",null,n.a.createElement(r.FormattedMessage,{id:"tags_editor_no_tags",defaultMessage:"Add some tags"},t=>n.a.createElement(G,{chips:e,avatarDisabled:!0,prompt:t,onEnter:this.handleAddTag,onFocusLost:this.handleAddTag,onCancel:this.handleCancel,onChipRemoved:this.handleRemoveTag,filterFunc:this.handleTagInput})),this.props.onSubmit||this.props.onCancel?n.a.createElement("div",{id:"tag-manager-buttons",className:"dialog-buttons panel-form-row"},n.a.createElement("button",{className:"outline",onClick:this.handleCancel},n.a.createElement(r.FormattedMessage,{id:"button_cancel",defautMessage:"Cancel"})),n.a.createElement("button",{className:"blue",onClick:this.handleSubmit},n.a.createElement(r.FormattedMessage,{id:"button_ok",defautMessage:"OK"}))):null):n.a.createElement("div",{className:"quoted"},n.a.createElement("a",{href:"#",className:"flat-button",onClick:e=>{e.preventDefault(),this.setState({activated:!0})}},n.a.createElement("i",{className:"material-icons"},"edit"),"  ",n.a.createElement(r.FormattedMessage,{id:"title_manage_tags",defaultMessage:"Manage"})),n.a.createElement(n.a.Fragment,null,e)))}}const le=Object(r.defineMessages)({requested:{id:"requested_permissions",defaultMessage:"Requested"},granted:{id:"granted_permissions",defaultMessage:"Granted"},edit_permissions:{id:"menu_item_edit_permissions",defaultMessage:"Edit permissions"},other_user:{id:"label_other_user",defaultMessage:"Other"},clear_messages:{id:"action_clear_messages",defaultMessage:"Clear Messages"},clear_messages_warning:{id:"clear_messages_warning",defaultMessage:"Are you sure you want to clear all messages? It cannot be undone."},delete_messages:{id:"action_delete_messages",defaultMessage:"Clear Messages for All"},delete_messages_warning:{id:"delete_messages_warning",defaultMessage:"Are you sure you want to delete all messages for everyone? It cannot be undone."},leave_chat:{id:"action_leave_chat",defaultMessage:"Leave Conversation"},leave_chat_warning:{id:"leave_chat_warning",defaultMessage:"Are you sure you want to leave this conversation?"},block_contact:{id:"action_block_contact",defaultMessage:"Block Contact"},block_contact_warning:{id:"block_contact_warning",defaultMessage:"Are you sure you want to block this contact?"},report_chat:{id:"action_report_chat",defaultMessage:"Report Conversation"},report_chat_warning:{id:"report_chat_warning",defaultMessage:"Are you sure you want to block and report this conversation?"}});class de extends n.a.Component{constructor(e){super(e),this.state={topic:null,owner:!1,admin:!1,sharer:!1,deleter:!1,muted:!1,address:null,groupTopic:void 0,fullName:void 0,avatar:null,private:null,selectedContact:null,access:null,modeGiven:null,modeWant:null,modeGiven2:null,modeWant2:null,auth:null,anon:null,contactList:[],tags:[],showMemberPanel:!1,showPermissionEditorFor:void 0,moreInfoExpanded:!1,previousMetaDesc:void 0,previousSubsUpdated:void 0,previousTagsUpdated:void 0},this.resetSubs=this.resetSubs.bind(this),this.resetDesc=this.resetDesc.bind(this),this.onMetaDesc=this.onMetaDesc.bind(this),this.onSubsUpdated=this.onSubsUpdated.bind(this),this.onTagsUpdated=this.onTagsUpdated.bind(this),this.handleFullNameUpdate=this.handleFullNameUpdate.bind(this),this.handlePrivateUpdate=this.handlePrivateUpdate.bind(this),this.handleImageChanged=this.handleImageChanged.bind(this),this.handleMuted=this.handleMuted.bind(this),this.handlePermissionsChanged=this.handlePermissionsChanged.bind(this),this.handleLaunchPermissionsEditor=this.handleLaunchPermissionsEditor.bind(this),this.handleHidePermissionsEditor=this.handleHidePermissionsEditor.bind(this),this.handleShowAddMembers=this.handleShowAddMembers.bind(this),this.handleHideAddMembers=this.handleHideAddMembers.bind(this),this.handleMemberUpdateRequest=this.handleMemberUpdateRequest.bind(this),this.handleDeleteMessages=this.handleDeleteMessages.bind(this),this.handleLeave=this.handleLeave.bind(this),this.handleBlock=this.handleBlock.bind(this),this.handleReport=this.handleReport.bind(this),this.handleMemberSelected=this.handleMemberSelected.bind(this),this.handleMoreInfo=this.handleMoreInfo.bind(this),this.handleTagsUpdated=this.handleTagsUpdated.bind(this),this.handleContextMenu=this.handleContextMenu.bind(this)}componentDidUpdate(e){const t=this.props.tinode.getTopic(e.topic);t&&(this.onMetaDesc!=t.onMetaDesc&&(this.previousMetaDesc=t.onMetaDesc,t.onMetaDesc=this.onMetaDesc,this.previousSubsUpdated=t.onSubsUpdated,t.onSubsUpdated=this.onSubsUpdated,"grp"==t.getType()?(this.previousTagsUpdated=t.onTagsUpdated,t.onTagsUpdated=this.onTagsUpdated):this.previousTagsUpdated=void 0),this.state.topic!=e.topic&&(this.setState({topic:e.topic}),this.resetDesc(t,e),this.resetSubs(t,e)))}componentWillUnmount(){const e=this.props.tinode.getTopic(this.props.topic);e&&(this.setState({topic:null}),e.onMetaDesc=this.previousMetaDesc,e.onSubsUpdated=this.previousSubsUpdated,e.onTagsUpdated=this.previousTagsUpdated)}resetSubs(e,t){const s={contactList:[]};if("p2p"==e.getType()){const a=e.subscriber(t.topic);a?(s.modeGiven2=a.acs.getGiven(),s.modeWant2=a.acs.getWant()):(s.modeGiven2="N",s.modeWant2="N")}else e.subscribers(e=>{s.contactList.push(e)},this);this.setState(s)}resetDesc(e,t){const s=e.getDefaultAccess()||{},a=e.getAccessMode();this.setState({owner:a&&a.isOwner(),admin:a&&a.isAdmin(),sharer:a&&a.isSharer(),deleter:a&&a.isDeleter(),muted:a&&a.isMuted(),fullName:e.public?e.public.fn:void 0,avatar:k(e.public?e.public.photo:null),private:e.private?e.private.comment:null,address:e.name,groupTopic:"grp"==e.getType(),showMemberPanel:!1,access:a?a.getMode():void 0,modeGiven:a?a.getGiven():void 0,modeWant:a?a.getWant():void 0,auth:s.auth,anon:s.anon}),"grp"==e.getType()&&a&&a.isOwner()&&e.getMeta(e.startMetaQuery().withTags().build())}onMetaDesc(e){const t=this.props.tinode.getTopic(this.props.topic);t&&(this.resetDesc(t,this.props),this.previousMetaDesc&&this.previousMetaDesc!=this.onMetaDesc&&this.previousMetaDesc(e))}onSubsUpdated(e){const t=this.props.tinode.getTopic(this.props.topic);t&&(this.resetSubs(t,this.props),this.previousSubsUpdated&&this.previousSubsUpdated!=this.onSubsUpdated&&this.previousSubsUpdated(e))}onTagsUpdated(e){this.setState({tags:e}),this.previousTagsUpdated&&this.previousTagsUpdated!=this.onTagsUpdated&&this.previousTagsUpdated()}handleFullNameUpdate(e){e=e.trim().substring(0,60),this.state.fullName!==e&&(this.setState({fullName:e}),this.props.onTopicDescUpdate(this.props.topic,ne(e,null),null))}handlePrivateUpdate(e){e=e.trim().substring(0,60),this.state.private!==e&&(this.setState({private:e}),this.props.onTopicDescUpdate(this.props.topic,null,e||h.a.DEL_CHAR))}handleImageChanged(e){this.setState({avatar:e}),this.props.onTopicDescUpdate(this.props.topic,ne(null,e||h.a.DEL_CHAR),null)}handleMuted(e,t){this.setState({muted:t}),this.props.onChangePermissions(this.props.topic,t?"-P":"+P")}handlePermissionsChanged(e){switch(this.state.showPermissionEditorFor){case"auth":this.props.onTopicDescUpdate(this.props.topic,null,null,{auth:e});break;case"anon":this.props.onTopicDescUpdate(this.props.topic,null,null,{anon:e});break;case"mode":case"want":this.props.onChangePermissions(this.props.topic,e);break;case"given":this.props.onChangePermissions(this.props.topic,e,this.props.topic);break;case"user":this.props.onChangePermissions(this.props.topic,e,this.state.userPermissionsEdited)}this.setState({showPermissionEditorFor:void 0})}handleLaunchPermissionsEditor(e,t){const{formatMessage:s}=this.props.intl;let a,n,i,o,r,l,d;switch(e){case"mode":a=this.state.access;break;case"want":a=this.state.modeWant,n=this.state.modeGiven,i=this.state.owner?"O":h.a.AccessMode.encode(h.a.AccessMode.diff("ASDO",this.state.modeGiven)),o=s(le.requested),r=s(le.granted);break;case"given":a=this.state.modeGiven2,n=this.state.modeWant2,i=this.state.groupTopic?this.state.owner?"":"O":"ASDO",o=s(le.granted),r=s(le.requested);break;case"auth":a=this.state.auth,i="O";break;case"anon":a=this.state.anon,i="O";break;case"user":{let e=this.props.tinode.getTopic(this.props.topic);if(!e)return;var c=e.subscriber(t);if(!c||!c.acs)return;a=c.acs.getGiven(),n=c.acs.getWant(),i=this.state.owner?"":"O",o=s(le.granted),r=s(le.requested),c.public&&(l=c.public.fn,d=c.public.photo);break}default:console.log("Unknown permission editing mode '"+e+"'")}this.setState({showPermissionEditorFor:e,userPermissionsEdited:t,userPermissionsTitle:l,userPermissionsAvatar:d,editedPermissions:a,immutablePermissions:n,editedPermissionsTitle:o,immutablePermissionsTitle:r,editedPermissionsSkipped:i})}handleHidePermissionsEditor(){this.setState({showPermissionEditorFor:void 0})}handleShowAddMembers(e){e.preventDefault(),this.props.onInitFind(),this.setState({showMemberPanel:!0})}handleHideAddMembers(){this.setState({showMemberPanel:!1})}handleMemberUpdateRequest(e,t,s){this.props.onMemberUpdateRequest(this.props.topic,t,s),this.setState({showMemberPanel:!1})}handleDeleteMessages(e){e.preventDefault();const{formatMessage:t}=this.props.intl;this.props.onShowAlert(t(this.state.deleter?le.delete_messages:le.clear_messages),t(this.state.deleter?le.delete_messages_warning:le.clear_messages_warning),()=>{this.props.onDeleteMessages(this.props.topic)},null,!0,null)}handleLeave(e){e.preventDefault();const{formatMessage:t}=this.props.intl;this.props.onShowAlert(t(le.leave_chat),t(le.leave_chat_warning),()=>{this.props.onLeaveTopic(this.props.topic)},null,!0,null)}handleBlock(e){e.preventDefault();const{formatMessage:t}=this.props.intl;this.props.onShowAlert(t(le.block_contact),t(le.block_contact_warning),()=>{this.props.onBlockTopic(this.props.topic)},null,!0,null)}handleReport(e){e.preventDefault();const{formatMessage:t}=this.props.intl;this.props.onShowAlert(t(le.report_chat),t(le.report_chat_warning),()=>{this.props.onReportTopic(this.props.topic)},null,!0,null)}handleMemberSelected(e){this.setState({selectedContact:e})}handleMoreInfo(e){this.setState({moreInfoExpanded:e})}handleTagsUpdated(e){ie(this.state.tags.slice(0),e.slice(0))||this.props.onTopicTagsUpdate(this.props.topic,e)}handleContextMenu(e){const{formatMessage:t}=this.props.intl,s=this,a=this.props.tinode.getTopic(this.props.topic);if(!a)return;const n=a.subscriber(e.topicName);if(!n||!n.acs)return;const i=[{title:t(le.edit_permissions),handler:function(){s.handleLaunchPermissionsEditor("user",e.topicName)}},"member_delete",n.acs.isMuted()?"member_unmute":"member_mute",n.acs.isJoiner()?"member_block":"member_unblock"];this.props.showContextMenu({topicName:this.props.topic,x:e.x,y:e.y,user:e.topicName},i)}render(){const{formatMessage:e}=this.props.intl;return n.a.createElement("div",{id:"info-view"},n.a.createElement("div",{className:"caption-panel",id:"info-caption-panel"},n.a.createElement("div",{className:"panel-title",id:"info-title"},n.a.createElement(r.FormattedMessage,{id:"title_info",defaultMessage:"Info"})),n.a.createElement("div",null,n.a.createElement(K,{onCancel:this.props.onCancel}))),this.props.displayMobile?n.a.createElement(W,{level:this.props.errorLevel,text:this.props.errorText,onClearError:this.props.onError}):null,this.state.showMemberPanel?n.a.createElement(Y,{members:this.state.contactList,requiredMember:this.props.myUserId,keepInitialMembers:!this.state.admin&&!this.state.owner,myUserId:this.props.myUserId,contacts:this.props.searchableContacts,onCancel:this.handleHideAddMembers,onSubmit:this.handleMemberUpdateRequest}):this.state.showPermissionEditorFor?n.a.createElement(se,{mode:this.state.editedPermissions,compare:this.state.immutablePermissions,skip:this.state.editedPermissionsSkipped,modeTitle:this.state.editedPermissionsTitle,compareTitle:this.state.immutablePermissionsTitle,userTitle:this.state.userPermissionsTitle,item:this.state.userPermissionsEdited,userAvatar:this.state.userPermissionsAvatar,onSubmit:this.handlePermissionsChanged,onCancel:this.handleHidePermissionsEditor}):n.a.createElement("div",{id:"info-view-content",className:"scrollable-panel"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("div",null,n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_topic_name",defaultMessage:"Name"}))),n.a.createElement("div",null,n.a.createElement(X,{placeholder:this.state.groupTopic?"Group name":n.a.createElement("i",null,"Unknown"),readOnly:!this.state.owner,value:this.state.fullName,required:!0,onFinished:this.handleFullNameUpdate})),n.a.createElement("div",null,n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_private",defaultMessage:"Private comment"}))),n.a.createElement("div",null,n.a.createElement(r.FormattedMessage,{id:"private_editing_placeholder",defaultMessage:"Visible to you only"},e=>n.a.createElement(X,{placeholder:e,value:this.state.private,onFinished:this.handlePrivateUpdate})))),n.a.createElement(U,{avatar:this.state.avatar,readOnly:!this.state.owner,uid:this.props.topic,title:this.state.fullName,onImageChanged:this.handleImageChanged,onError:this.props.onError})),n.a.createElement("div",{className:"hr"}),n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",null,n.a.createElement(r.FormattedMessage,{id:"label_muting_topic",defaultMessage:"Muted:"})),n.a.createElement(x,{name:"P",checked:this.state.muted,onChange:this.handleMuted})),n.a.createElement(r.FormattedMessage,{id:"action_more",defaultMessage:"More"},e=>n.a.createElement($,{title:e,open:this.state.moreInfoExpanded,onToggle:this.handleMoreInfo})),this.state.moreInfoExpanded?n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",null,n.a.createElement(r.FormattedMessage,{id:"label_user_id"})),n.a.createElement("tt",null,this.state.address)),this.state.groupTopic?n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",null,n.a.createElement(r.FormattedMessage,{id:"label_your_permissions",defaultMessage:"Your permissions:"})),n.a.createElement("tt",{className:"clickable",onClick:this.handleLaunchPermissionsEditor.bind(this,"want")},this.state.access)):n.a.createElement("div",null,n.a.createElement("div",null,n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_permissions",defaultMessage:"Permissions:"}))),n.a.createElement("div",{className:"quoted"},n.a.createElement("div",null,n.a.createElement(r.FormattedMessage,{id:"label_you",defaultMessage:"You:"}),"  ",n.a.createElement("tt",{className:"clickable",onClick:this.handleLaunchPermissionsEditor.bind(this,"want")},this.state.access)),n.a.createElement("div",null,this.state.fullName?this.state.fullName:e(le.other_user),":  ",n.a.createElement("tt",{className:"clickable",onClick:this.handleLaunchPermissionsEditor.bind(this,"given")},this.state.modeGiven2)))),this.state.sharer&&(this.state.auth||this.state.anon)?n.a.createElement("div",null,n.a.createElement("div",null,n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_default_access",defaultMessage:"Default access mode:"}))),n.a.createElement("div",{className:"quoted"},n.a.createElement("div",null,"Auth: ",this.state.admin?n.a.createElement("tt",{className:"clickable",onClick:this.handleLaunchPermissionsEditor.bind(this,"auth")},this.state.auth):n.a.createElement("tt",null,this.state.auth)),n.a.createElement("div",null,"Anon: ",this.state.admin?n.a.createElement("tt",{className:"clickable",onClick:this.handleLaunchPermissionsEditor.bind(this,"anon")},this.state.anon):n.a.createElement("tt",null,this.state.anon)))):null):null),n.a.createElement("div",{className:"hr"}),this.state.owner?n.a.createElement(n.a.Fragment,null,n.a.createElement(r.FormattedMessage,{id:"title_tag_manager"},e=>n.a.createElement(re,{title:e,tags:this.state.tags,activated:!1,onSubmit:this.handleTagsUpdated})),n.a.createElement("div",{className:"hr"})):null,n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("a",{href:"#",className:"flat-button",onClick:this.handleDeleteMessages},n.a.createElement("i",{className:"material-icons"},"delete_outline"),"  ",e(this.state.deleter?le.delete_messages:le.clear_messages)),n.a.createElement("a",{href:"#",className:"red flat-button",onClick:this.handleLeave},n.a.createElement("i",{className:"material-icons"},"exit_to_app"),"  ",e(le.leave_chat)),this.state.groupTopic?null:n.a.createElement("a",{href:"#",className:"red flat-button",onClick:this.handleBlock},n.a.createElement("i",{className:"material-icons"},"block"),"  ",e(le.block_contact)),this.state.owner?null:n.a.createElement("a",{href:"#",className:"red flat-button",onClick:this.handleReport},n.a.createElement("i",{className:"material-icons"},"report"),"  ",e(le.report_chat))),this.state.groupTopic?n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"hr"}),n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_group_members",defaultMessage:"Group members:"}))),n.a.createElement("div",{className:"panel-form-row"},this.state.sharer?n.a.createElement("a",{href:"#",className:"flat-button",onClick:this.handleShowAddMembers},n.a.createElement("i",{className:"material-icons"},"person_add"),"  ",n.a.createElement(r.FormattedMessage,{id:"button_add_members",defaultMessage:"Add members"})):null),n.a.createElement(r.FormattedMessage,{id:"group_has_no_members",defaultMessage:"No members"},e=>n.a.createElement(z,{contacts:this.state.contactList,myUserId:this.props.myUserId,emptyListMessage:e,topicSelected:this.state.selectedContact,showOnline:!1,showUnread:!1,showMode:!0,noScroll:!0,onTopicSelected:this.handleMemberSelected,showContextMenu:!!this.state.admin&&this.handleContextMenu})))):null))}}var ce=Object(r.injectIntl)(de);class he extends n.a.PureComponent{render(){return n.a.createElement("div",{className:"uploader"},n.a.createElement("div",null,n.a.createElement("span",{style:{width:100*this.props.progress+"%"}})),this.props.progress<.999?n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onCancel()}},n.a.createElement("i",{className:"material-icons"},"close")," ",n.a.createElement(r.FormattedMessage,{id:"action_cancel",defaultMessage:"cancel"})):n.a.createElement(r.FormattedMessage,{id:"upload_finishing",defaultMessage:"finishing..."}))}}class pe extends n.a.Component{constructor(e){super(e),this.state={downloader:null,progress:0},this.downloadFile=this.downloadFile.bind(this),this.handleCancel=this.handleCancel.bind(this)}downloadFile(e,t,s){var a=this.props.tinode.getLargeFileHelper();this.setState({downloader:a}),a.download(e,t,s,e=>{this.setState({progress:e/this.props.size})}).then(()=>{this.setState({downloader:null,progress:0})}).catch(e=>{e&&this.props.onError("Error downloading file: "+e.message,"err"),this.setState({downloader:null,progress:0})})}handleCancel(){this.props.uploader?this.props.onCancelUpload():this.state.downloader&&this.state.downloader.cancel()}render(){let e=this.props.filename||"file_attachment";e.length>36&&(e=e.substr(0,16)+"..."+e.substr(-16));let t,s,a=this.props.size>0?n.a.createElement("span",{className:"small gray"},"(",w(this.props.size),")"):null;return this.props.uploader||this.state.downloader||!function(e){return!/^\s*([a-z][a-z0-9+.-]*:|\/\/)/im.test(e)}(this.props.downloadUrl)?(t=oe(this.props.downloadUrl)||"about:blank",s=null):(t="#",s=e=>{e.preventDefault(),this.downloadFile(this.props.downloadUrl,this.props.filename,this.props.mimetype)}),n.a.createElement("div",{className:"attachment"},n.a.createElement("div",null,n.a.createElement("i",{className:"material-icons big gray"},"insert_drive_file")),n.a.createElement("div",{className:"flex-column"},n.a.createElement("div",null,e," ",a),this.props.uploader||this.state.downloader?n.a.createElement(he,{progress:this.props.uploader?this.props.progress:this.state.progress,onCancel:this.handleCancel}):n.a.createElement("div",null,n.a.createElement("a",{href:t,download:this.props.filename,onClick:s},n.a.createElement("i",{className:"material-icons"},"file_download")," ",n.a.createElement(r.FormattedMessage,{id:"save_attachment",defaultMessage:"save"})))))}}const me=Object(r.defineMessages)({sending:{id:"message_sending",defaultMessage:"sending..."},failed:{id:"message_sending_failed",defaultMessage:"failed"}});class ue extends n.a.PureComponent{render(){const{formatMessage:e}=this.props.intl;let t;t=this.props.received<=h.a.MESSAGE_STATUS_SENDING?e(me.sending):this.props.received==h.a.MESSAGE_STATUS_FAILED?e(me.failed):E(this.props.timestamp,this.props.intl.locale);let s=null;return this.props.received<=h.a.MESSAGE_STATUS_SENDING?s=n.a.createElement("i",{className:"material-icons small"},"access_time"):this.props.received==h.a.MESSAGE_STATUS_FAILED?s=n.a.createElement("i",{className:"material-icons small amber"},"warning"):this.props.received==h.a.MESSAGE_STATUS_SENT?s=n.a.createElement("i",{className:"material-icons small"},"done"):this.props.received==h.a.MESSAGE_STATUS_RECEIVED?s=n.a.createElement("i",{className:"material-icons small"},"done_all"):this.props.received==h.a.MESSAGE_STATUS_READ&&(s=n.a.createElement("i",{className:"material-icons small blue"},"done_all")),n.a.createElement("span",{className:"timestamp"},t," ",s)}}var ge=Object(r.injectIntl)(ue);class _e extends n.a.Component{constructor(e){super(e),this.state={progress:0},e.uploader&&(e.uploader.onProgress=this.handleProgress.bind(this)),this.handleImagePreview=this.handleImagePreview.bind(this),this.handleFormButtonClick=this.handleFormButtonClick.bind(this),this.handleContextClick=this.handleContextClick.bind(this),this.handleCancelUpload=this.handleCancelUpload.bind(this)}handleImagePreview(e){e.preventDefault(),this.props.onImagePreview({url:e.target.src,filename:e.target.title,width:e.target.dataset.width,height:e.target.dataset.height,size:e.target.dataset.size,type:e.target.dataset.mime})}handleFormButtonClick(e){e.preventDefault();const t={seq:this.props.seq,resp:{}};e.target.dataset.name&&(t.resp[e.target.dataset.name]=e.target.dataset.val?e.target.dataset.val:void 0===e.target.dataset.val?1:""+e.target.dataset.val),"url"==e.target.dataset.act&&(t.ref=oe(e.target.dataset.ref)||"about:blank");const s=e.target.dataset.title||"unknown";this.props.onFormResponse(e.target.dataset.act,s,t)}handleContextClick(e){e.preventDefault(),e.stopPropagation();const t=this.props.received==Tinode.MESSAGE_STATUS_FAILED?["menu_item_send_retry"]:[];this.props.showContextMenu({seq:this.props.seq,content:this.props.content,y:e.pageY,x:e.pageX},t)}handleProgress(e){this.setState({progress:e})}handleCancelUpload(){this.props.uploader.cancel()}render(){const e=this.props.deleted?"center":this.props.sequence+" "+(this.props.response?"left":"right"),t="single"==this.props.sequence||"last"==this.props.sequence?"bubble tip":"bubble",s=this.props.deleted?null:this.props.userAvatar||!0,a=this.props.userFrom&&this.props.response&&("single"==this.props.sequence||"last"==this.props.sequence);let i=this.props.content;const o=[];return this.props.mimeType==c.Drafty.getContentType()&&c.Drafty.isValid(i)?(c.Drafty.attachments(i,(function(e,t){"application/json"!=e.mime&&o.push(n.a.createElement(pe,{tinode:this.props.tinode,downloadUrl:c.Drafty.getDownloadUrl(e),filename:e.name,uploader:c.Drafty.isUploading(e),mimetype:e.mime,size:c.Drafty.getEntitySize(e),progress:this.state.progress,onCancelUpload:this.handleCancelUpload,onError:this.props.onError,key:t}))}),this),i=n.a.createElement(n.a.Fragment,null,c.Drafty.format(i,be,this))):this.props.deleted?i=n.a.createElement(n.a.Fragment,null,n.a.createElement("i",{className:"material-icons gray"},"block")," ",n.a.createElement("i",{className:"gray"},n.a.createElement(r.FormattedMessage,{id:"deleted_content",defaultMessage:"content deleted"}))):"string"!=typeof i&&(i=n.a.createElement(n.a.Fragment,null,n.a.createElement("i",{className:"material-icons gray"},"error_outline")," ",n.a.createElement("i",{className:"gray"},n.a.createElement(r.FormattedMessage,{id:"invalid_content",defaultMessage:"invalid content"})))),n.a.createElement("li",{className:e},this.props.userFrom&&this.props.response?n.a.createElement("div",{className:"avatar-box"},a?n.a.createElement(C,{topic:this.props.userFrom,title:this.props.userName,avatar:s}):null):null,n.a.createElement("div",null,n.a.createElement("div",{className:t},n.a.createElement("div",{className:"message-content"},i,o,this.props.timestamp?n.a.createElement(ge,{timestamp:this.props.timestamp,received:this.props.received}):null),this.props.deleted?null:n.a.createElement("span",{className:"menuTrigger"},n.a.createElement("a",{href:"#",onClick:this.handleContextClick},n.a.createElement("i",{className:"material-icons"},"expand_more")))),a?n.a.createElement("div",{className:"author"},n.a.createElement(r.FormattedMessage,{id:"user_not_found",defaultMessage:"Not found"},e=>this.props.userName||n.a.createElement("i",null,e))):null))}}function be(e,t,s,a){let i=c.Drafty.tagName(e);if(i){let o=c.Drafty.attrValue(e,t)||{};switch(o.key=a,e){case"IM":if(t){o.className="inline-image";let e=y(t.width,t.height,Math.min(this.props.viewportWidth-52,468),312,!1);e=e||{dstWidth:32,dstHeight:32},o.style={width:e.dstWidth+"px",height:e.dstHeight+"px"},o.src=function(e){if(!e)return null;const t=oe(e);return t||(/data:image\/[a-z0-9.-]+;base64,/i.test(e.trim())?e:null)}(o.src),o.src?(o.onClick=this.handleImagePreview,o.className+=" image-clickable"):o.src="img/broken_image.png"}break;case"BN":o.onClick=this.handleFormButtonClick;let e=n.a.Children.map(s,e=>"string"==typeof e?e:void 0);e&&0!=e.length||(e=[o.name]),o["data-title"]=e.join("");break;case"FM":o.className="bot-form"}return n.a.createElement(i,o,s)}return s}const ve=Object(r.defineMessages)({messaging_disabled:{id:"messaging_disabled_prompt",defaultMessage:"Messaging disabled"},type_new_message:{id:"new_message_prompt",defaultMessage:"New message"},add_image_caption:{id:"image_caption_prompt",defaultMessage:"Image caption"},file_attachment_too_large:{id:"file_attachment_too_large",defaultMessage:"The file size {size} exceeds the {limit} limit."},cannot_initiate_upload:{id:"cannot_initiate_file_upload",defaultMessage:"Cannot initiate file upload."}});class fe extends n.a.PureComponent{constructor(e){super(e),this.state={message:"",keypressTimestamp:(new Date).getTime()-3e3-1},this.handlePasteEvent=this.handlePasteEvent.bind(this),this.handleAttachImage=this.handleAttachImage.bind(this),this.handleAttachFile=this.handleAttachFile.bind(this),this.handleSend=this.handleSend.bind(this),this.handleKeyPress=this.handleKeyPress.bind(this),this.handleMessageTyping=this.handleMessageTyping.bind(this)}componentDidMount(){this.messageEditArea&&this.messageEditArea.addEventListener("paste",this.handlePasteEvent,!1)}componentWillUnmount(){this.messageEditArea&&this.messageEditArea.removeEventListener("paste",this.handlePasteEvent,!1)}componentDidUpdate(){this.messageEditArea&&this.messageEditArea.focus()}handlePasteEvent(e){this.props.disabled||function(e,t,s,a){var n=(e.clipboardData||e.originalEvent.clipboardData||{}).items;for(var i in n){var o=n[i];if("file"===o.kind){var r=o.getAsFile();if(!r){console.log("Failed to get file object from pasted file item",o.kind,o.type);continue}return r.type&&"image"==r.type.split("/")[0]?r.size>195584||S.indexOf(r.type)<0?T(r,768,768,!1,t,a):A(r,t,a):P(r,s),!0}}return!1}(e,(e,t,s,a,n)=>{this.props.onAttachImage(t,e,s,a,n)},(e,t,s)=>{this.props.onAttachFile(e,t,s)},this.props.onError)&&e.preventDefault()}handleAttachImage(e){e.target.files&&e.target.files.length>0&&this.props.onAttachImage(e.target.files[0]),e.target.value=""}handleAttachFile(e){const{formatMessage:t}=this.props.intl;e.target.files&&e.target.files.length>0&&this.props.onAttachFile(e.target.files[0]),e.target.value=""}handleSend(e){e.preventDefault();const t=this.state.message.trim();(t||this.props.acceptBlank||this.props.noInput)&&(this.props.onSendMessage(t),this.setState({message:""}))}handleKeyPress(e){"Enter"===e.key&&(e.shiftKey||(e.preventDefault(),e.stopPropagation(),this.handleSend(e)))}handleMessageTyping(e){const t={message:e.target.value};if(this.props.onKeyPress){const e=(new Date).getTime();e-this.state.keypressTimestamp>3e3&&(this.props.onKeyPress(),t.keypressTimestamp=e)}this.setState(t)}render(){const{formatMessage:e}=this.props.intl,t=this.props.disabled?e(ve.messaging_disabled):this.props.messagePrompt?e(ve[this.props.messagePrompt]):e(ve.type_new_message);return n.a.createElement("div",{id:"send-message-panel"},this.props.disabled?n.a.createElement("div",{id:"writing-disabled"},t):n.a.createElement(n.a.Fragment,null,this.props.onAttachFile?n.a.createElement(n.a.Fragment,null,n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.attachImage.click()},title:"Add image"},n.a.createElement("i",{className:"material-icons secondary"},"photo")),n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.attachFile.click()},title:"Attach file"},n.a.createElement("i",{className:"material-icons secondary"},"attach_file"))):null,this.props.noInput?n.a.createElement("div",{className:"hr thin"}):n.a.createElement("textarea",{id:"sendMessage",placeholder:t,value:this.state.message,onChange:this.handleMessageTyping,onKeyPress:this.handleKeyPress,ref:e=>{this.messageEditArea=e},autoFocus:!0}),n.a.createElement("a",{href:"#",onClick:this.handleSend,title:"Send"},n.a.createElement("i",{className:"material-icons"},"send")),n.a.createElement("input",{type:"file",ref:e=>{this.attachFile=e},onChange:this.handleAttachFile,style:{display:"none"}}),n.a.createElement("input",{type:"file",ref:e=>{this.attachImage=e},accept:"image/*",onChange:this.handleAttachImage,style:{display:"none"}})))}}var Ee=Object(r.injectIntl)(fe);class we extends n.a.PureComponent{constructor(e){super(e),this.handleSendDoc=this.handleSendDoc.bind(this)}handleSendDoc(e){this.props.onClose(),this.props.onSendMessage(this.props.content.file)}render(){return this.props.content?n.a.createElement("div",{id:"image-preview"},n.a.createElement("div",{id:"image-preview-caption-panel"},n.a.createElement("span",null,this.props.content.filename),n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onClose()}},n.a.createElement("i",{className:"material-icons gray"},"close"))),n.a.createElement("div",{id:"image-preview-container"},n.a.createElement("div",{className:"flex-column narrow"},n.a.createElement("i",{className:"material-icons gray"},function(e){const t={default:"insert_drive_file",image:"image",text:"description",video:"theaters"};return t[e]||t[(e||"").split("/")[0]]||t.default}(this.props.content.type)),n.a.createElement("div",null,n.a.createElement("b",null,n.a.createElement(r.FormattedMessage,{id:"label_content_type"}))," ",this.props.content.type||"application/octet-stream"),n.a.createElement("div",null,n.a.createElement("b",null,n.a.createElement(r.FormattedMessage,{id:"label_size"}))," ",w(this.props.content.size)))),n.a.createElement(Ee,{noInput:!0,onSendMessage:this.handleSendDoc,onError:this.props.onError})):null}}class Ce extends n.a.Component{constructor(e){super(e)}render(){const e=[],t=(this.props.subscribers||[]).length,s=Math.min(4,t);return(this.props.subscribers||[]).some(t=>(e.push(n.a.createElement("div",{className:"avatar-box",key:t.user},n.a.createElement(C,{topic:t.user,avatar:k(t.public?t.public.photo:null)||!0,title:t.public?t.public.fn:null}))),e.length==s)),n.a.createElement("div",{id:"topic-users"},e," ",t>s?n.a.createElement("span",null,n.a.createElement(r.FormattedMessage,{id:"more_online_members",defaultMessage:"+{overflow} more",values:{overflow:t-s}})):null)}}class Se extends n.a.PureComponent{constructor(e){super(e),this.state={width:0,height:0},this.handleSendImage=this.handleSendImage.bind(this)}assignWidth(e){if(e&&!this.state.width){const t=e.getBoundingClientRect();this.setState({width:0|t.width,height:0|t.height})}}handleSendImage(e){this.props.onClose(),this.props.onSendMessage(e,this.props.content.type,this.props.content.bits,this.props.content.width,this.props.content.height,this.props.content.filename)}render(){if(!this.props.content)return null;const e=y(this.props.content.width,this.props.content.height,this.state.width,this.state.height,!1),t=e?{width:e.dstWidth+"px",height:e.dstHeight+"px"}:this.props.content.width>this.props.content.height?{width:"100%"}:{height:"100%"};t.maxWidth="100%",t.maxHeight="100%";let s=this.props.content.filename;const a=Math.max((this.state.width/13/1.5|0)-2,12);s.length>a&&(s=s.slice(0,a/2-1)+"…"+s.slice(1-a/2));const i=this.props.content.width||"-",o=this.props.content.height||"-";return n.a.createElement("div",{id:"image-preview"},n.a.createElement("div",{id:"image-preview-caption-panel"},this.props.onSendMessage?n.a.createElement("span",null,this.props.content.filename):n.a.createElement("a",{href:this.props.content.url,download:this.props.content.filename},n.a.createElement("i",{className:"material-icons"},"file_download")," ",n.a.createElement(r.FormattedMessage,{id:"download_action",defaultMessage:"download"})),n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onClose()}},n.a.createElement("i",{className:"material-icons gray"},"close"))),n.a.createElement("div",{id:"image-preview-container",ref:e=>this.assignWidth(e)},n.a.createElement("img",{src:this.props.content.url,style:t})),this.props.onSendMessage?n.a.createElement(Ee,{messagePrompt:"add_image_caption",acceptBlank:!0,onSendMessage:this.handleSendImage,onError:this.props.onError}):n.a.createElement("div",{id:"image-preview-footer"},n.a.createElement("div",null,n.a.createElement("div",null,n.a.createElement("b",null,n.a.createElement(r.FormattedMessage,{id:"label_file_name",defaultMessage:"File name:"}))),n.a.createElement("div",null,n.a.createElement("span",{title:this.props.content.filename},s||"-"))),n.a.createElement("div",null,n.a.createElement("div",null,n.a.createElement("b",null,n.a.createElement(r.FormattedMessage,{id:"label_content_type",defaultMessage:"Content type:"}))),n.a.createElement("div",null,this.props.content.type)),n.a.createElement("div",null,n.a.createElement("div",null,n.a.createElement("b",null,n.a.createElement(r.FormattedMessage,{id:"label_size",defaultMessage:"Size:"}))),n.a.createElement("div",null,i," × ",o," px; ",w(this.props.content.size)))))}}class Me extends n.a.PureComponent{constructor(e){super(e),this.handleButtonAction=this.handleButtonAction.bind(this)}handleButtonAction(e,t){e.preventDefault(),this.props.onAction(t)}render(){return n.a.createElement("div",{className:"accept-invite-panel"},n.a.createElement("div",{className:"title"},n.a.createElement(r.FormattedMessage,{id:"chat_invitation",defaultMessage:"You are invited to start a new chat. What would you like to do?"})),n.a.createElement("div",{className:"footer"},n.a.createElement("button",{className:"blue",onClick:e=>{this.handleButtonAction(e,"accept")}},n.a.createElement(r.FormattedMessage,{id:"chat_invitation_accept",defaultMessage:"Accept"})),n.a.createElement("button",{className:"white",onClick:e=>{this.handleButtonAction(e,"delete")}},n.a.createElement(r.FormattedMessage,{id:"chat_invitation_ignore",defaultMessage:"Ignore"})),n.a.createElement("button",{className:"white",onClick:e=>{this.handleButtonAction(e,"block")}},n.a.createElement(r.FormattedMessage,{id:"chat_invitation_block",defaultMessage:"Block"}))))}}class ke extends n.a.PureComponent{render(){return this.props.show?n.a.createElement("div",{className:"load-spinner-box"},n.a.createElement("div",{className:"loader-spinner"})):null}}class ye extends n.a.PureComponent{render(){const e=m+" ("+h.a.getLibrary()+")";return n.a.createElement("div",{id:"dummy-view"},n.a.createElement("div",null,n.a.createElement("a",{href:"https://github.com/tinode/chat/"},n.a.createElement("img",{id:"logo",alt:"logo",src:"img/logo.svg"}),n.a.createElement("h2",null,"Tinode Web")),n.a.createElement("p",null,n.a.createElement(r.FormattedMessage,{id:"label_client",defaultMessage:"Client:"})," ",e),n.a.createElement("p",null,n.a.createElement(r.FormattedMessage,{id:"label_server",defaultMessage:"Server:"})," ",this.props.serverVersion," (",this.props.serverAddress,")")))}}const Ne=h.a.Drafty,Te=Object(r.defineMessages)({online_now:{id:"online_now",defaultMessage:"online now"},last_seen:{id:"last_seen_timestamp",defaultMessage:"Last seen"},not_found:{id:"title_not_found",defaultMessage:"Not found"}});function Ae(e){if(e){const t=e.getExcessive()||"";return e.isJoiner("given")&&(t.includes("R")||t.includes("W"))}return!1}function Pe(e){if(e){const t=e.getMissing()||"";return e.isJoiner("want")&&(t.includes("R")||t.includes("W"))}return!1}class De extends n.a.Component{constructor(e){super(e),this.state=De.getDerivedStateFromProps(e,{}),this.leave=this.leave.bind(this),this.sendImageAttachment=this.sendImageAttachment.bind(this),this.sendFileAttachment=this.sendFileAttachment.bind(this),this.sendKeyPress=this.sendKeyPress.bind(this),this.handleScrollReference=this.handleScrollReference.bind(this),this.handleScrollEvent=this.handleScrollEvent.bind(this),this.handleDescChange=this.handleDescChange.bind(this),this.handleSubsUpdated=this.handleSubsUpdated.bind(this),this.handleNewMessage=this.handleNewMessage.bind(this),this.handleAllMessagesReceived=this.handleAllMessagesReceived.bind(this),this.handleInfoReceipt=this.handleInfoReceipt.bind(this),this.handleImagePostview=this.handleImagePostview.bind(this),this.handleClosePreview=this.handleClosePreview.bind(this),this.handleFormResponse=this.handleFormResponse.bind(this),this.handleContextClick=this.handleContextClick.bind(this),this.handleShowContextMenuMessage=this.handleShowContextMenuMessage.bind(this),this.handleNewChatAcceptance=this.handleNewChatAcceptance.bind(this),this.handleEnablePeer=this.handleEnablePeer.bind(this),this.handleAttachFile=this.handleAttachFile.bind(this),this.handleAttachImage=this.handleAttachImage.bind(this),this.postReadNotification=this.postReadNotification.bind(this),this.clearNotificationQueue=this.clearNotificationQueue.bind(this),this.readNotificationQueue=[],this.readNotificationTimer=null}componentDidMount(){this.messagesScroller&&this.messagesScroller.addEventListener("scroll",this.handleScrollEvent)}componentWillUnmount(){this.messagesScroller&&this.messagesScroller.removeEventListener("scroll",this.handleScrollEvent),this.clearNotificationQueue()}componentDidUpdate(e,t){this.messagesScroller&&(t.topic!=this.state.topic||t.messages.length!=this.state.messages.length?this.messagesScroller.scrollTop=this.messagesScroller.scrollHeight-this.state.scrollPosition:e.viewportHeight>this.props.viewportHeight&&(this.messagesScroller.scrollTop+=e.viewportHeight-this.props.viewportHeight));const s=this.props.tinode.getTopic(this.state.topic);if(this.state.topic!=t.topic&&(t.topic&&!h.a.isNewGroupTopicName(t.topic)&&this.leave(t.topic),s&&(s.onData=this.handleNewMessage,s.onAllMessagesReceived=this.handleAllMessagesReceived,s.onInfo=this.handleInfoReceipt,s.onMetaDesc=this.handleDescChange,s.onSubsUpdated=this.handleSubsUpdated,s.onPres=this.handleSubsUpdated)),this.props.applicationVisible?this.postReadNotification(0):this.clearNotificationQueue(),s&&!s.isSubscribed()&&this.props.ready&&(this.state.topic!=t.topic||!e.ready)){const e=this.props.newTopicParams&&this.props.newTopicParams._topicName==this.props.topic;let t=s.startMetaQuery().withLaterDesc().withLaterSub();(this.state.isReader||e)&&(t=t.withLaterData(24),this.state.isReader&&(t=t.withLaterDel()),this.setState({fetchingMessages:!0}));const a=e?this.props.newTopicParams:void 0;s.subscribe(t.build(),a).then(e=>{this.state.topic!=e.topic&&this.setState({topic:e.topic}),this.props.onNewTopicCreated(this.props.topic,e.topic),s.queuedMessages(e=>{!e._sending&&s.isSubscribed()&&s.publishMessage(e)})}).catch(e=>{console.log("Failed subscription to",this.state.topic),this.props.onError(e.message,"err");const t=De.getDerivedStateFromProps({},{});t.title=this.props.intl.formatMessage(Te.not_found),this.setState(t)})}}static getDerivedStateFromProps(e,t){let s={};if(e.topic){if(e.topic!=t.topic){const a=e.tinode.getTopic(e.topic);if(s={topic:e.topic,docPreview:null,imagePreview:null,imagePostview:null,typingIndicator:!1,scrollPosition:0,fetchingMessages:!1},a){const n=[],i=[];e.connected&&a.subscribers(t=>{t.online&&t.user!=e.myUserId&&i.push(t)}),a.messages((function(e){e.deleted||n.push(e)})),Object.assign(s,{messages:n,onlineSubs:i}),a.public?Object.assign(s,{title:a.public.fn,avatar:k(a.public.photo)}):Object.assign(s,{title:"",avatar:null});const o=a.p2pPeerDesc();o?Object.assign(s,{peerMessagingDisabled:Pe(o.acs)}):t.peerMessagingDisabled&&Object.assign(s,{peerMessagingDisabled:!1})}else Object.assign(s,{messages:[],onlineSubs:[],title:"",avatar:null,peerMessagingDisabled:!1})}}else s={messages:[],onlineSubs:[],topic:null,title:"",avatar:null,docPreview:null,imagePreview:null,imagePostview:null,typingIndicator:!1,scrollPosition:0,fetchingMessages:!1,peerMessagingDisabled:!1};return e.acs?(e.acs.isWriter()!=t.isWriter&&(s.isWriter=!t.isWriter),e.acs.isReader()!=t.isReader&&(s.isReader=!t.isReader),!e.acs.isReader("given")!=t.readingBlocked&&(s.readingBlocked=!t.readingBlocked)):(t.isWriter&&(s.isWriter=!1),t.isReader&&(s.isReader=!1),t.readingBlocked||(t.readingBlocked=!0)),Ae(e.acs)==!t.unconformed&&(s.unconfirmed=!t.unconformed),!e.connected&&t.onlineSubs&&t.onlineSubs.length>0&&(s.onlineSubs=[]),s}leave(e){if(!e)return;let t=this.props.tinode.getTopic(e);t&&t.isSubscribed()&&t.leave(!1).catch(()=>{}).finally(()=>{this.setState({fetchingMessages:!1}),t.onData=void 0,t.onAllMessagesReceived=void 0,t.onInfo=void 0,t.onMetaDesc=void 0,t.onSubsUpdated=void 0,t.onPres=void 0})}handleScrollReference(e){e&&(e.addEventListener("scroll",this.handleScrollEvent),this.messagesScroller=e,this.messagesScroller.scrollTop=this.messagesScroller.scrollHeight-this.state.scrollPosition)}handleScrollEvent(e){this.setState({scrollPosition:e.target.scrollHeight-e.target.scrollTop}),e.target.scrollTop<=0&&this.setState((e,t)=>{const s={};if(!e.fetchingMessages){const e=this.props.tinode.getTopic(this.state.topic);e&&e.isSubscribed()&&e.msgHasMoreMessages()&&(s.fetchingMessages=!0,e.getMessagesPage(24).catch(e=>{this.setState({fetchingMessages:!1}),this.props.onError(e.message,"err")}))}return s})}handleDescChange(e){e.public?this.setState({title:e.public.fn,avatar:k(e.public.photo)}):this.setState({title:"",avatar:null}),e.acs&&this.setState({isWriter:e.acs.isWriter(),isReader:e.acs.isReader(),readingBlocked:!e.acs.isReader("given"),unconfirmed:Ae(e.acs)})}postReadNotification(e){if(!this.props.applicationVisible)return;this.readNotificationTimer||(this.readNotificationTimer=setInterval(()=>{if(0==this.readNotificationQueue.length)return clearInterval(this.readNotificationTimer),void(this.readNotificationTimer=null);let e=-1;for(;this.readNotificationQueue.length>0;){const t=this.readNotificationQueue[0];if(t.topicName!=this.state.topic){this.readNotificationQueue.shift();continue}const s=new Date;if(!(t.sendAt<=s))break;this.readNotificationQueue.shift(),e=Math.max(e,t.seq)}if(e>=0){const t=this.props.tinode.getTopic(this.state.topic);t&&t.noteRead(e)}},300));const t=new Date;this.readNotificationQueue.push({topicName:this.state.topic,seq:e,sendAt:t.setMilliseconds(t.getMilliseconds()+1e3)})}clearNotificationQueue(){this.readNotificationQueue=[],this.readNotificationTimer&&(clearInterval(this.readNotificationTimer),this.readNotificationTimer=null)}handleSubsUpdated(){if(this.state.topic){const e=[],t=this.props.tinode.getTopic(this.state.topic);t.subscribers(t=>{t.online&&t.user!=this.props.myUserId&&e.push(t)});const s={onlineSubs:e},a=t.p2pPeerDesc();a?Object.assign(s,{peerMessagingDisabled:Pe(a.acs)}):this.state.peerMessagingDisabled&&Object.assign(s,{peerMessagingDisabled:!1}),this.setState(s)}}handleNewMessage(e){const t=this.props.tinode.getTopic(this.state.topic),s={messages:[]};if(t.messages(e=>{e.deleted||s.messages.push(e)}),e&&!e.deleted){t.isNewMessage(e.seq)&&(s.scrollPosition=0);t.msgStatus(e)>=h.a.MESSAGE_STATUS_SENT&&e.from!=this.props.myUserId&&this.postReadNotification(e.seq),this.props.onData(e)}this.setState(s)}handleAllMessagesReceived(e){this.setState({fetchingMessages:!1}),e>0&&this.postReadNotification(0)}handleInfoReceipt(e){switch(e.what){case"kp":clearTimeout(this.keyPressTimer);var t=this;this.keyPressTimer=setTimeout((function(){t.setState({typingIndicator:!1})}),4e3),this.state.typingIndicator||this.setState({typingIndicator:!0});break;case"read":case"recv":this.forceUpdate();break;default:console.log("Other change in topic: ",e.what)}}handleImagePostview(e){this.setState({imagePostview:e})}handleClosePreview(){this.setState({imagePostview:null,imagePreview:null,docPreview:null})}handleFormResponse(e,t,s){if("pub"==e)this.props.sendMessage(Ne.attachJSON(Ne.parse(t),s));else if("url"==e){const e=new URL(s.ref),t=e.searchParams;for(let e in s.resp)s.resp.hasOwnProperty(e)&&t.set(e,s.resp[e]);["name","seq"].map((function(e){s[e]&&t.set(e,s[e])})),t.set("uid",this.props.myUserId),e.search=t,window.open(e,"_blank")}else console.log("Unknown action in form",e)}handleContextClick(e){e.preventDefault(),e.stopPropagation(),this.props.showContextMenu({topicName:this.state.topic,y:e.pageY,x:e.pageX})}handleShowContextMenuMessage(e,t){e.topicName=this.state.topic;const s=t||[];s.push("message_delete");const a=this.props.tinode.getTopic(e.topicName);if(a){const e=a.getAccessMode();e&&e.isDeleter()&&s.push("message_delete_hard")}this.props.showContextMenu(e,s)}handleNewChatAcceptance(e){this.props.onNewChat(this.state.topic,e)}handleEnablePeer(e){e.preventDefault(),this.props.onChangePermissions(this.state.topic,"JRWPS",this.state.topic)}sendKeyPress(){const e=this.props.tinode.getTopic(this.state.topic);e.isSubscribed()&&e.noteKeyPress()}sendFileAttachment(e){if(e.size>195584){const t=this.props.tinode.getLargeFileHelper();if(!t)return void this.props.onError(this.props.intl.formatMessage(Te.cannot_initiate_upload));const s=t.upload(e),a=Ne.attachFile(null,e.type,null,e.name,e.size,s);this.props.sendMessage(a,s,t)}else P(e,(e,t,s)=>{this.props.sendMessage(Ne.attachFile(null,e,t,s))},this.props.onError)}handleAttachFile(e){e.size>1<<23?this.props.onError(this.props.intl.formatMessage(Te.file_attachment_too_large,{size:w(e.size),limit:w(1<<23)}),"err"):this.setState({docPreview:{file:e,filename:e.name,size:e.size,type:e.type}})}sendImageAttachment(e,t,s,a,n,i){let o=Ne.insertImage(null,0,t,s,a,n,i);e&&(o=Ne.appendLineBreak(o),o=Ne.append(o,Ne.init(e))),this.props.sendMessage(o)}handleAttachImage(e){e.size>195584||S.indexOf(e.type)<0?T(e,768,768,!1,(t,s,a,n,i)=>{this.setState({imagePreview:{url:URL.createObjectURL(e),bits:t,filename:i,width:a,height:n,size:t.length,type:s}})},e=>{this.props.onError(e,"err")}):A(e,(t,s,a,n,i)=>{this.setState({imagePreview:{url:URL.createObjectURL(e),bits:t,filename:i,width:a,height:n,size:t.length,type:s}})},e=>{this.props.onError(e,"err")})}render(){const{formatMessage:e}=this.props.intl;let t;if(this.props.hideSelf)t=null;else if(this.state.topic){let s;if(this.state.imagePreview)s=n.a.createElement(Se,{content:this.state.imagePreview,onClose:this.handleClosePreview,onSendMessage:this.sendImageAttachment});else if(this.state.imagePostview)s=n.a.createElement(Se,{content:this.state.imagePostview,onClose:this.handleClosePreview});else if(this.state.docPreview)s=n.a.createElement(we,{content:this.state.docPreview,onClose:this.handleClosePreview,onSendMessage:this.sendFileAttachment});else{const t=this.props.tinode.getTopic(this.state.topic),a="grp"==t.getType();let i=[],o=null,l=null;for(let e=0;e<this.state.messages.length;e++){let s=this.state.messages[e],r=null;e+1<this.state.messages.length&&(r=this.state.messages[e+1].from);let d="single";s.from==o?d=s.from==r?"middle":"last":s.from==r&&(d="first"),o=s.from;const c=!(s.from==this.props.myUserId),h=t.msgStatus(s);let p,m,u;if(a){const e=t.userDesc(s.from);e&&e.public&&(p=e.public.fn,m=k(e.public.photo)),u=s.from,l="chat-box group"}else l="chat-box";i.push(n.a.createElement(_e,{tinode:this.props.tinode,content:s.content,deleted:s.hi,mimeType:s.head?s.head.mime:null,timestamp:s.ts,response:c,seq:s.seq,userFrom:u,userName:p,userAvatar:m,sequence:d,received:h,uploader:s._uploader,viewportWidth:this.props.viewportWidth,showContextMenu:this.handleShowContextMenuMessage,onImagePreview:this.handleImagePostview,onFormResponse:this.handleFormResponse,onError:this.props.onError,key:s.seq}))}let d=null;const c=this.props.tinode.getMeTopic().getContact(this.state.topic);c&&"p2p"==h.a.topicType(c.topic)&&(c.online?d=e(Te.online_now):c.seen&&(d=e(Te.last_seen)+": "+E(c.seen.when,this.props.intl.locale)));const p=this.state.avatar||!0,m=this.props.online?"online"+(this.state.typingIndicator?" typing":""):"offline";s=n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{id:"topic-caption-panel",className:"caption-panel"},this.props.displayMobile?n.a.createElement("a",{href:"#",id:"hide-message-view",onClick:e=>{e.preventDefault(),this.props.onHideMessagesView()}},n.a.createElement("i",{className:"material-icons"},"arrow_back")):null,n.a.createElement("div",{className:"avatar-box"},n.a.createElement(C,{avatar:p,topic:this.state.topic,title:this.state.title}),n.a.createElement("span",{className:m})),n.a.createElement("div",{id:"topic-title-group"},n.a.createElement("div",{id:"topic-title",className:"panel-title"},this.state.title||n.a.createElement("i",null,n.a.createElement(r.FormattedMessage,{id:"unnamed_topic",defaultMessage:"Unnamed"}))),n.a.createElement("div",{id:"topic-last-seen"},d)),a?n.a.createElement(Ce,{subscribers:this.state.onlineSubs}):n.a.createElement("div",{id:"topic-users"}),n.a.createElement("div",null,n.a.createElement("a",{href:"#",onClick:this.handleContextClick},n.a.createElement("i",{className:"material-icons"},"more_vert")))),this.props.displayMobile?n.a.createElement(W,{level:this.props.errorLevel,text:this.props.errorText,onClearError:this.props.onError}):null,n.a.createElement(ke,{show:this.state.fetchingMessages}),n.a.createElement("div",{id:"messages-container"},n.a.createElement("div",{id:"messages-panel",ref:this.handleScrollReference},n.a.createElement("ul",{id:"scroller",className:l},i)),this.state.isReader?null:n.a.createElement("div",{id:"write-only-background"},this.state.readingBlocked?n.a.createElement("div",{id:"write-only-note"},n.a.createElement(r.FormattedMessage,{id:"messages_not_readable",defaultMessage:"no access to messages"})):null)),this.state.peerMessagingDisabled&&!this.state.unconfirmed?n.a.createElement("div",{id:"peer-messaging-disabled-note"},n.a.createElement("i",{className:"material-icons secondary"},"block")," ",n.a.createElement(r.FormattedMessage,{id:"peers_messaging_disabled",defaultMessage:"Peer's messaging is disabled."})," ",n.a.createElement("a",{href:"#",onClick:this.handleEnablePeer},n.a.createElement(r.FormattedMessage,{id:"enable_peers_messaging",defaultMessage:"Enable"})),"."):null,this.state.unconfirmed?n.a.createElement(Me,{onAction:this.handleNewChatAcceptance}):n.a.createElement(Ee,{disabled:!this.state.isWriter,onSendMessage:this.props.sendMessage,onKeyPress:this.sendKeyPress,onAttachFile:this.handleAttachFile,onAttachImage:this.handleAttachImage,onError:this.props.onError}))}t=n.a.createElement("div",{id:"topic-view"},s)}else t=n.a.createElement(ye,{serverVersion:this.props.serverVersion,serverAddress:this.props.serverAddress});return t}}var Fe=Object(r.injectIntl)(De);class Ue extends n.a.PureComponent{render(){return n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onBack()}},n.a.createElement("i",{className:"material-icons"},"arrow_back"))}}class xe extends n.a.PureComponent{render(){return n.a.createElement("div",null,n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onNewTopic()}},n.a.createElement("i",{className:"material-icons"},"chat"))," ",n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onSettings()}},n.a.createElement("i",{className:"material-icons"},"settings")))}}class Ie extends n.a.PureComponent{render(){return n.a.createElement("div",null,n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onSignUp()}},n.a.createElement("i",{className:"material-icons"},"person_add"))," ",n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onSettings()}},n.a.createElement("i",{className:"material-icons"},"settings")))}}class Re extends n.a.PureComponent{render(){return n.a.createElement("div",{id:"side-caption-panel",className:"caption-panel"},this.props.onCancel?n.a.createElement(Ue,{onBack:this.props.onCancel}):null,this.props.avatar?n.a.createElement("div",{id:"self-avatar",className:"avatar-box"},n.a.createElement(C,{avatar:this.props.avatar,topic:this.props.myUserId,title:this.props.title})):null,n.a.createElement("div",{id:"sidepanel-title",className:"panel-title"},this.props.title),"login"===this.props.state?n.a.createElement(Ie,{onSignUp:this.props.onSignUp,onSettings:this.props.onSettings}):"contacts"===this.props.state?n.a.createElement(xe,{onNewTopic:this.props.onNewTopic,onSettings:this.props.onSettings}):null)}}const Le=Object(r.defineMessages)({archived_contacts_title:{id:"archived_contacts",defaultMessage:"Archived contacts ({count})"}});class qe extends n.a.Component{constructor(e){super(e),this.handleAction=this.handleAction.bind(this),this.state=qe.deriveStateFromProps(e)}static deriveStateFromProps(e){const t=[];let s=0,a=0;return e.chatList.map(n=>{const i=n.acs&&!n.acs.isJoiner();i&&e.blocked&&t.push(n),i||e.blocked||(n.private&&n.private.arch?e.archive?t.push(n):a++:e.archive||(t.push(n),s+=n.unread>0?1:0))}),t.sort((e,t)=>(t.touched||0)-(e.touched||0)),a>0&&t.push({action:"archive",title:Le.archived_contacts_title,values:{count:a}}),{contactList:t,unreadThreads:s}}componentDidUpdate(e,t){if(e.chatList!=this.props.chatList||e.archive!=this.props.archive||e.blocked!=this.props.blocked){const e=qe.deriveStateFromProps(this.props);this.setState(e),e.unreadThreads!=t.unreadThreads&&ae(e.unreadThreads)}}handleAction(e){this.props.onShowArchive()}render(){return n.a.createElement(r.FormattedMessage,{id:"contacts_not_found",defaultMessage:"You have no chats<br />¯∖_(ツ)_/¯"},e=>n.a.createElement(z,{connected:this.props.connected,contacts:this.state.contactList,emptyListMessage:e,topicSelected:this.props.topicSelected,myUserId:this.props.myUserId,showOnline:!0,showUnread:!0,onTopicSelected:this.props.onTopicSelected,showContextMenu:this.props.showContextMenu,onAction:this.handleAction}))}}class Oe{static setObject(e,t){localStorage.setItem(e,JSON.stringify(t))}static getObject(e){const t=localStorage.getItem(e);return t&&JSON.parse(t)}static updateObject(e,t){const s=this.getObject(e);this.setObject(e,Object.assign(s||{},t))}static removeItem(e){localStorage.removeItem(e)}}class je extends n.a.PureComponent{constructor(e){super(e),this.state={login:"",password:"",email:"",fn:"",imageDataUrl:null,errorCleared:!1,saveToken:Oe.getObject("keep-logged-in")},this.handleLoginChange=this.handleLoginChange.bind(this),this.handlePasswordChange=this.handlePasswordChange.bind(this),this.handleEmailChange=this.handleEmailChange.bind(this),this.handleFnChange=this.handleFnChange.bind(this),this.handleImageChanged=this.handleImageChanged.bind(this),this.handleToggleSaveToken=this.handleToggleSaveToken.bind(this),this.handleSubmit=this.handleSubmit.bind(this)}handleLoginChange(e){this.setState({login:e.target.value})}handlePasswordChange(e){this.setState({password:e})}handleEmailChange(e){this.setState({email:e.target.value})}handleFnChange(e){this.setState({fn:e.target.value})}handleImageChanged(e){this.setState({imageDataUrl:e})}handleToggleSaveToken(){Oe.setObject("keep-logged-in",!this.state.saveToken),this.setState({saveToken:!this.state.saveToken})}handleSubmit(e){e.preventDefault(),this.setState({errorCleared:!1}),this.props.onCreateAccount(this.state.login.trim(),this.state.password.trim(),ne(this.state.fn.trim().substring(0,60),this.state.imageDataUrl),{meth:"email",val:this.state.email})}render(){let e="blue";return this.props.disabled&&(e+=" disabled"),n.a.createElement("form",{className:"panel-form-column",onSubmit:this.handleSubmit},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("div",{className:"panel-form-column"},n.a.createElement(r.FormattedMessage,{id:"login_prompt"},e=>n.a.createElement("input",{type:"text",placeholder:e,autoComplete:"user-name",value:this.state.login,onChange:this.handleLoginChange,required:!0,autoFocus:!0})),n.a.createElement(r.FormattedMessage,{id:"password_prompt"},e=>n.a.createElement(Z,{placeholder:e,autoComplete:"new-password",value:this.state.password,onFinished:this.handlePasswordChange,required:!0}))),n.a.createElement(U,{onImageChanged:this.handleImageChanged,onError:this.props.onError})),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement(r.FormattedMessage,{id:"full_name_prompt",defaultMessage:"Full name, e.g. John Doe"},e=>n.a.createElement("input",{type:"text",placeholder:e,autoComplete:"name",value:this.state.fn,onChange:this.handleFnChange,required:!0}))),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement(r.FormattedMessage,{id:"email_prompt",defaultMessage:"Email, e.g. <EMAIL>"},e=>n.a.createElement("input",{type:"email",placeholder:e,autoComplete:"email",value:this.state.email,onChange:this.handleEmailChange,required:!0}))),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement(x,{id:"save-token",name:"save-token",checked:this.state.saveToken,onChange:this.handleToggleSaveToken}),n.a.createElement(r.FormattedMessage,{id:"stay_logged_in"},e=>n.a.createElement("label",{htmlFor:"save-token"}," ",e))),n.a.createElement("div",{className:"dialog-buttons"},n.a.createElement("button",{className:e,type:"submit"},n.a.createElement(r.FormattedMessage,{id:"button_sign_up",defaultMessage:"Sign up"}))))}}class Be extends n.a.Component{constructor(e){super(e);const t=this.props.tinode.getMeTopic();this.state={fullName:t.public?t.public.fn:void 0,avatar:k(t.public?t.public.photo:null)}}render(){return n.a.createElement("div",{className:"scrollable-panel"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_your_name",defaultMessage:"Your name"})),n.a.createElement("div",{className:"large"},this.state.fullName),n.a.createElement("div",null,n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_user_id",defaultMessage:"ID:"}))," ",n.a.createElement("tt",null,this.props.myUserId))),n.a.createElement(U,{avatar:this.state.avatar,readOnly:!this.state.owner,uid:this.props.myUserId,title:this.state.fullName})),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("a",{href:"#",className:"flat-button",onClick:e=>{e.preventDefault(),this.props.onBasicNavigate("general")}},n.a.createElement("i",{className:"material-icons"},"edit")," ",n.a.createElement(r.FormattedMessage,{id:"button_edit",defaultMessage:"Edit"}))),n.a.createElement("div",{className:"hr"}),n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("a",{href:"#",className:"flat-button",onClick:e=>{e.preventDefault(),this.props.onBasicNavigate("notif")}},n.a.createElement("i",{className:"material-icons"},"notifications")," ",n.a.createElement(r.FormattedMessage,{id:"sidepanel_title_acc_notifications"})),n.a.createElement("a",{href:"#",className:"flat-button",onClick:e=>{e.preventDefault(),this.props.onBasicNavigate("security")}},n.a.createElement("i",{className:"material-icons"},"security")," ",n.a.createElement(r.FormattedMessage,{id:"sidepanel_title_acc_security"})),n.a.createElement("a",{href:"#",className:"flat-button",onClick:e=>{e.preventDefault(),this.props.onBasicNavigate("support")}},n.a.createElement("i",{className:"material-icons"},"contact_support")," ",n.a.createElement(r.FormattedMessage,{id:"sidepanel_title_acc_support"}))))}}class He extends n.a.Component{constructor(e){super(e);const t=this.props.tinode.getMeTopic();this.state={fullName:t.public?t.public.fn:void 0,avatar:k(t.public?t.public.photo:null),tags:t.tags(),credentials:t.getCredentials()||[],addCredActive:!1,addCredInvalid:!1,newCred:"",previousOnTags:t.onTagsUpdated},this.tnNewTags=this.tnNewTags.bind(this),this.tnCredsUpdated=this.tnCredsUpdated.bind(this),this.handleFullNameUpdate=this.handleFullNameUpdate.bind(this),this.handleImageChanged=this.handleImageChanged.bind(this),this.handleCredChange=this.handleCredChange.bind(this),this.handleCredKeyDown=this.handleCredKeyDown.bind(this),this.handleCredEntered=this.handleCredEntered.bind(this),this.handleTagsUpdated=this.handleTagsUpdated.bind(this)}componentDidMount(){const e=this.props.tinode.getMeTopic();e.onCredsUpdated=this.tnCredsUpdated,e.onTagsUpdated=this.tnNewTags}componentWillUnmount(){const e=this.props.tinode.getMeTopic();e.onTagsUpdated=this.state.previousOnTags,e.onCredsUpdated=void 0}tnNewTags(e){this.setState({tags:e})}tnCredsUpdated(e){this.setState({credentials:e||[]})}handleFullNameUpdate(e){(e=e.trim().substring(0,60))&&(this.setState({fullName:e}),this.props.onUpdateAccount(void 0,ne(e,null)))}handleImageChanged(e){this.setState({avatar:e}),this.props.onUpdateAccount(void 0,ne(null,e||h.a.DEL_CHAR))}handleCredChange(e){this.setState({newCred:e.target.value,addCredInvalid:!1})}handleCredKeyDown(e){27===e.keyCode?this.setState({newCred:"",addCredActive:!1}):13===e.keyCode&&this.handleCredEntered(e)}handleCredEntered(e){let t=this.state.newCred.trim();if(!t)return void this.setState({addCredActive:!1,addCredInvalid:!1});let s,a=function(e){return e=e.trim(),/^(?:\+?(\d{1,3}))?[- (.]*(\d{3})[- ).]*(\d{3})[- .]*(\d{2})[- .]*(\d{2})?$/.test(e)?e.replace(/[- ().]*/,""):null}(t);a?s="tel":(a=function(e){return e=e.trim(),/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/.test(e)?e:null}(t),a&&(s="email")),s?(this.props.onCredAdd(s,a),this.setState({addCredActive:!1,newCred:""})):this.setState({addCredInvalid:!0})}handleTagsUpdated(e){ie(this.state.tags.slice(0),e.slice(0))||this.props.onUpdateTags(e)}render(){const e=[];return this.state.credentials.map(t=>{e.push(n.a.createElement("div",{key:t.meth+":"+t.val+":"+t.done},t.meth,": ",n.a.createElement("tt",null,t.val),n.a.createElement("span",null," ",t.done?null:n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onCredConfirm(t.meth,t.val)}},n.a.createElement(r.FormattedMessage,{id:"validate_credential_action",defaultMessage:"confirm"}))," ",n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.props.onCredDelete(t.meth,t.val)}},n.a.createElement("i",{className:"material-icons gray"},"delete_outline")))))}),n.a.createElement("div",{className:"scrollable-panel"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_your_name"})),n.a.createElement("div",null,n.a.createElement(r.FormattedMessage,{id:"full_name_prompt"},e=>n.a.createElement(X,{placeholder:e,value:this.state.fullName,onFinished:this.handleFullNameUpdate})))),n.a.createElement(U,{avatar:this.state.avatar,uid:this.props.myUserId,title:this.state.fullName,onImageChanged:this.handleImageChanged,onError:this.props.onError})),n.a.createElement("div",{className:"hr"}),n.a.createElement(r.FormattedMessage,{id:"title_tag_manager",defaultMessage:"Tags (user discovery)"},e=>n.a.createElement(re,{title:e,activated:!1,tags:this.state.tags,onSubmit:this.handleTagsUpdated})),n.a.createElement("div",{className:"hr"}),n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_user_contacts",defaultMessage:"Contacts:"})),n.a.createElement("div",{className:"quoted"},e,this.state.addCredActive?n.a.createElement("input",{type:"text",value:this.state.value,className:this.state.addCredInvalid?"invalid":null,placeholder:"Phone number or email",required:"required",autoFocus:!0,onChange:this.handleCredChange,onKeyDown:this.handleCredKeyDown,onBlur:this.handleCredEntered}):null,n.a.createElement("div",null,n.a.createElement("a",{href:"#",onClick:e=>{e.preventDefault(),this.setState({addCredActive:!0})}},n.a.createElement("i",{className:"material-icons"},"add"),n.a.createElement(r.FormattedMessage,{id:"button_add_another",defaultMessage:"Add another"}))))))}}class ze extends n.a.PureComponent{constructor(e){super(e),this.handleCheckboxClick=this.handleCheckboxClick.bind(this)}handleCheckboxClick(e,t){"sound"==e?this.props.onToggleMessageSounds(t):"alert"==e?this.props.onTogglePushNotifications(t):"incognito"==e&&this.props.onToggleIncognitoMode(t)}render(){return n.a.createElement("div",{className:"scrollable-panel"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{htmlFor:"message-sound"},n.a.createElement(r.FormattedMessage,{id:"label_message_sound",defaultMessage:"Message sound:"})),n.a.createElement(x,{name:"sound",id:"message-sound",checked:this.props.messageSounds,onChange:this.handleCheckboxClick})),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{htmlFor:"desktop-alerts"},this.props.desktopAlertsEnabled?n.a.createElement(r.FormattedMessage,{id:"label_push_notifications",defaultMessage:"Notification alerts:"}):n.a.createElement(r.FormattedMessage,{id:"label_push_notifications_disabled",defaultMessage:"Notification alerts (requires HTTPS):"})),n.a.createElement(x,{name:"alert",id:"desktop-alerts",checked:this.props.desktopAlerts,onChange:this.props.desktopAlertsEnabled?this.handleCheckboxClick:null})),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{htmlFor:"incognito-mode"},n.a.createElement(r.FormattedMessage,{id:"label_incognito_mode",defaultMessage:"Incognito mode:"})),n.a.createElement(x,{name:"incognito",id:"incognito-mode",checked:this.props.incognitoMode,onChange:this.handleCheckboxClick})))}}const Ke=Object(r.defineMessages)({delete_account:{id:"delete_account",defaultMessage:"Delete account"},delete_account_warning:{id:"delete_account_arning",defaultMessage:"Are you sure you want to delete your account? It cannot be undone."}});class We extends n.a.Component{constructor(e){super(e);const t=this.props.tinode.getMeTopic();let s=0;t.contacts(e=>{e.acs&&!e.acs.isJoiner()&&s++});const a=t.getDefaultAccess();this.state={auth:a?a.auth:null,anon:a?a.anon:null,showPermissionEditorFor:void 0,blockedCount:s},this.handlePasswordUpdate=this.handlePasswordUpdate.bind(this),this.handleLaunchPermissionsEditor=this.handleLaunchPermissionsEditor.bind(this),this.handleHidePermissionsEditor=this.handleHidePermissionsEditor.bind(this),this.handlePermissionsChanged=this.handlePermissionsChanged.bind(this),this.handleDeleteAccount=this.handleDeleteAccount.bind(this)}handlePasswordUpdate(e){this.setState({password:e}),this.props.onUpdateAccount(e)}handleLaunchPermissionsEditor(e){this.setState({showPermissionEditorFor:e,editedPermissions:this.state[e]})}handleHidePermissionsEditor(){this.setState({showPermissionEditorFor:void 0})}handlePermissionsChanged(e){let t={};t[this.state.showPermissionEditorFor]=e,this.props.onUpdateAccount(void 0,void 0,t);let s={showPermissionEditorFor:void 0};s[this.state.showPermissionEditorFor]=e,this.setState(s)}handleDeleteAccount(e){e.preventDefault();const{formatMessage:t}=this.props.intl;this.props.onShowAlert(t(Ke.delete_account),t(Ke.delete_account_warning),()=>{this.props.onDeleteAccount()},null,!0,null)}render(){return n.a.createElement(n.a.Fragment,null,this.state.showPermissionEditorFor?n.a.createElement(se,{mode:this.state.editedPermissions,skip:"O",onSubmit:this.handlePermissionsChanged,onCancel:this.handleHidePermissionsEditor}):n.a.createElement("div",{className:"scrollable-panel"},n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_password",defaultMessage:"Password"})),n.a.createElement("div",null,n.a.createElement(r.FormattedMessage,{id:"password_unchanged_prompt",defaultMessage:"Unchanged"},e=>n.a.createElement(X,{placeholder:e,type:"password",onFinished:this.handlePasswordUpdate})))),n.a.createElement("div",{className:"hr"}),n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("a",{href:"#",className:"red flat-button",onClick:e=>{e.preventDefault(),this.props.onLogout()}},n.a.createElement("i",{className:"material-icons"},"exit_to_app"),"  ",n.a.createElement(r.FormattedMessage,{id:"button_logout",defaultMessage:"Logout"})),n.a.createElement("a",{href:"#",className:"red flat-button",onClick:e=>{this.handleDeleteAccount(e)}},n.a.createElement("i",{className:"material-icons"},"delete"),"  ",n.a.createElement(r.FormattedMessage,{id:"button_delete_account",defaultMessage:"Delete account"}))),n.a.createElement("div",{className:"hr"}),n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("div",null,n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_default_access_mode",defaultMessage:"Default access mode:"}))),n.a.createElement("div",{className:"quoted"},n.a.createElement("div",null,"Auth: ",n.a.createElement("tt",{className:"clickable",onClick:this.handleLaunchPermissionsEditor.bind(this,"auth")},this.state.auth)),n.a.createElement("div",null,"Anon: ",n.a.createElement("tt",{className:"clickable",onClick:this.handleLaunchPermissionsEditor.bind(this,"anon")},this.state.anon)))),this.state.blockedCount>0?n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"hr"}),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("i",{className:"material-icons"},"block")," ",n.a.createElement("a",{href:"#",className:"gray",onClick:e=>{e.preventDefault(),this.props.onShowBlocked()}},n.a.createElement(r.FormattedMessage,{id:"blocked_contacts_link",defaultMessage:"Blocked contacts ({count})",values:{count:this.state.blockedCount}})))):null))}}var Ve=Object(r.injectIntl)(We);class Ge extends n.a.PureComponent{render(){return n.a.createElement("div",{className:"scrollable-panel"},n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("a",{href:"email:<EMAIL>",className:"flat-button",target:"_blank"},n.a.createElement("i",{className:"material-icons"},"email"),"  ",n.a.createElement(r.FormattedMessage,{id:"link_contact_us",defaultMessage:"Contact Us"})),n.a.createElement("a",{href:"https://tinode.co/terms.html",className:"flat-button",target:"_blank"},n.a.createElement("i",{className:"material-icons"},"description"),"  ",n.a.createElement(r.FormattedMessage,{id:"link_terms_of_service",defaultMessage:"Terms of Service"})),n.a.createElement("a",{href:"https://tinode.co/privacy.html",className:"flat-button",target:"_blank"},n.a.createElement("i",{className:"material-icons"},"policy"),"  ",n.a.createElement(r.FormattedMessage,{id:"link_privacy_policy",defaultMessage:"Privacy Policy"}))),n.a.createElement("div",{className:"hr"}),n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_client"})),m),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_sdk",defaultMessage:"SDK:"})),h.a.getLibrary()),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_server"})),this.props.serverVersion),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_server_address",defaultMessage:"Server address:"})),this.props.serverAddress)))}}class Je extends n.a.Component{constructor(e){super(e),this.state={login:e.login,password:"",hostName:e.serverAddress,saveToken:Oe.getObject("keep-logged-in")},this.handleLoginChange=this.handleLoginChange.bind(this),this.handlePasswordChange=this.handlePasswordChange.bind(this),this.handleToggleSaveToken=this.handleToggleSaveToken.bind(this),this.handleSubmit=this.handleSubmit.bind(this)}handleLoginChange(e){this.setState({login:e.target.value})}handlePasswordChange(e){this.setState({password:e.target.value})}handleToggleSaveToken(){Oe.setObject("keep-logged-in",!this.state.saveToken),this.setState({saveToken:!this.state.saveToken})}handleSubmit(e){e.preventDefault(),this.props.onLogin(this.state.login.trim(),this.state.password.trim())}render(){var e="blue";return this.props.disabled&&(e+=" disabled"),n.a.createElement("form",{id:"login-form",onSubmit:this.handleSubmit},n.a.createElement(r.FormattedMessage,{id:"login_prompt",defaultMessage:"Login"},e=>n.a.createElement("input",{type:"text",id:"inputLogin",placeholder:e,autoComplete:"username",autoCorrect:"off",autoCapitalize:"none",value:this.state.login,onChange:this.handleLoginChange,required:!0,autoFocus:!0})),n.a.createElement(r.FormattedMessage,{id:"password_prompt",defaultMessage:"Password"},e=>n.a.createElement(Z,{type:"password",id:"inputPassword",placeholder:e,autoComplete:"current-password",value:this.state.password,onChange:this.handlePasswordChange,required:!0})),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement(x,{id:"save-token",name:"save-token",checked:this.state.saveToken,onChange:this.handleToggleSaveToken}),n.a.createElement("label",{htmlFor:"save-token"}," ",n.a.createElement(r.FormattedMessage,{id:"stay_logged_in",defaultMessage:"Stay logged in"})),n.a.createElement("a",{href:"#reset"},n.a.createElement(r.FormattedMessage,{id:"forgot_password_link",defaultMessage:"Forgot password?"}))),n.a.createElement("div",{className:"dialog-buttons"},n.a.createElement("button",{className:e,type:"submit"},n.a.createElement(r.FormattedMessage,{id:"button_sign_in",defaultMessage:"Sign in"}))))}}const Qe=Object(r.defineMessages)({invalid_id:{id:"error_invalid_id",defaultMessage:"Invalid ID"}});class Ye extends n.a.PureComponent{constructor(e){super(e),this.state={groupId:""},this.handleChange=this.handleChange.bind(this),this.handleKeyPress=this.handleKeyPress.bind(this),this.handleSubmit=this.handleSubmit.bind(this)}handleChange(e){this.setState({groupId:e.target.value})}handleKeyPress(e){"Enter"===e.key&&this.handleSubmit(e)}handleSubmit(e){if(e.preventDefault(),this.state.groupId){var t=this.state.groupId.trim();t.length>3&&("usr"==t.substr(0,3)||"grp"==t.substr(0,3))?this.props.onSubmit(t):this.props.onError(this.props.intl.formatMessage(Qe.invalid_id),"err")}}render(){return n.a.createElement("div",{className:"panel-form"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement(r.FormattedMessage,{id:"group_user_id_prompt",defaultMessage:"Group or User ID"},e=>n.a.createElement("input",{type:"text",placeholder:e,value:this.state.groupId,onChange:this.handleChange,onKeyPress:this.handleKeyPress,required:!0}))),n.a.createElement("div",{className:"dialog-buttons"},n.a.createElement("button",{className:"blue",onClick:this.handleSubmit},n.a.createElement(r.FormattedMessage,{id:"button_subscribe",defaultMessage:"Subscribe"}))))}}var Ze=Object(r.injectIntl)(Ye);class Xe extends n.a.PureComponent{constructor(e){super(e),this.state={fn:"",private:"",imageDataUrl:null,tags:[]},this.handleFnChange=this.handleFnChange.bind(this),this.handlePrivateChange=this.handlePrivateChange.bind(this),this.handleImageChanged=this.handleImageChanged.bind(this),this.handleTagsChanged=this.handleTagsChanged.bind(this),this.handleTagsChanged=this.handleTagsChanged.bind(this),this.handleSubmit=this.handleSubmit.bind(this)}handleFnChange(e){this.setState({fn:e.target.value})}handlePrivateChange(e){this.setState({private:e.target.value})}handleImageChanged(e){this.setState({imageDataUrl:e})}handleTagsChanged(e){this.setState({tags:e})}handleSubmit(e){e.preventDefault();const t=this.state.fn.trim().substring(0,60),s=this.state.private.trim().substring(0,60);t&&this.props.onSubmit(t,this.state.imageDataUrl,s,this.state.tags)}render(){var e="blue";return this.props.disabled&&(e+=" disabled"),n.a.createElement("form",{className:"panel-form",onSubmit:this.handleSubmit},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("div",{className:"panel-form-column"},n.a.createElement("label",{className:"small",htmlFor:"new-topic-fn"},n.a.createElement(r.FormattedMessage,{id:"label_topic_name"})),n.a.createElement(r.FormattedMessage,{id:"topic_name_editing_placeholder",defaultMessage:"Freeform name of the group"},e=>n.a.createElement("input",{type:"text",id:"new-topic-fn",placeholder:e,value:this.state.fn,onChange:this.handleFnChange,autoFocus:!0,required:!0})),n.a.createElement("br",null),n.a.createElement("label",{className:"small",htmlFor:"new-topic-priv"},n.a.createElement(r.FormattedMessage,{id:"label_private"})),n.a.createElement(r.FormattedMessage,{id:"private_editing_placeholder"},e=>n.a.createElement("input",{type:"text",id:"new-topic-priv",placeholder:e,value:this.state.private,onChange:this.handlePrivateChange}))),n.a.createElement(U,{onError:this.props.onError,onImageChanged:this.handleImageChanged})),n.a.createElement(r.FormattedMessage,{id:"title_tag_manager"},e=>n.a.createElement(re,{tags:this.state.tags,activated:!0,onTagsChanged:this.handleTagsChanged,title:e})),n.a.createElement("div",{className:"dialog-buttons"},n.a.createElement("button",{className:e},n.a.createElement(r.FormattedMessage,{id:"button_create",defaultMessage:"Create"}))))}}class $e extends n.a.PureComponent{constructor(e){super(e),this.state={edited:!1,search:""},this.handleSearchChange=this.handleSearchChange.bind(this),this.handleSearch=this.handleSearch.bind(this),this.handleClear=this.handleClear.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this)}componentWillUnmount(){this.state.edited&&(this.setState({search:"",edited:!1}),this.props.onSearchContacts(h.a.DEL_CHAR))}handleSearchChange(e){this.setState({search:e.target.value})}handleSearch(e){e.preventDefault();var t=this.state.search.trim();this.setState({edited:t.length>0}),this.props.onSearchContacts(t.length>0?t:h.a.DEL_CHAR)}handleClear(e){e.preventDefault(),this.state.edited&&this.props.onSearchContacts(h.a.DEL_CHAR),this.setState({search:"",edited:!1})}handleKeyDown(e){"Enter"===e.key?this.handleSearch(e):"Escape"===e.key&&this.handleClear()}render(){return n.a.createElement("div",{className:"panel-form"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("i",{className:"material-icons search"},"search"),n.a.createElement(r.FormattedMessage,{id:"search_placeholder",defaultMessage:"List like email:<EMAIL>, tel:***********..."},e=>n.a.createElement("input",{className:"search",type:"text",placeholder:e,value:this.state.search,onChange:this.handleSearchChange,onKeyDown:this.handleKeyDown,required:!0,autoFocus:!0})),n.a.createElement("a",{href:"#",onClick:this.handleClear},n.a.createElement("i",{className:"material-icons"},"close"))))}}const et=Object(r.defineMessages)({search_for_contacts:{id:"search_for_contacts",defaultMessage:"Use search to find contacts"},search_no_results:{id:"search_no_results",defaultMessage:"Search returned no results"}});class tt extends n.a.Component{constructor(e){super(e),this.state={tabSelected:"p2p",searchQuery:null},this.handleTabClick=this.handleTabClick.bind(this),this.handleSearchContacts=this.handleSearchContacts.bind(this),this.handleContactSelected=this.handleContactSelected.bind(this),this.handleNewGroupSubmit=this.handleNewGroupSubmit.bind(this),this.handleGroupByID=this.handleGroupByID.bind(this)}componentDidMount(){this.props.onInitFind()}handleTabClick(e){e.preventDefault(),_.navigateTo(_.addUrlParam(window.location.hash,"tab",e.currentTarget.dataset.id)),this.setState({tabSelected:e.currentTarget.dataset.id})}handleSearchContacts(e){this.props.onSearchContacts(e),this.setState({searchQuery:h.a.isNullValue(e)?null:e})}handleContactSelected(e){"p2p"===this.state.tabSelected&&(_.navigateTo(_.removeUrlParam(window.location.hash,"tab")),this.props.onCreateTopic(e,void 0))}handleNewGroupSubmit(e,t,s,a){_.navigateTo(_.removeUrlParam(window.location.hash,"tab")),this.props.onCreateTopic(void 0,ne(e,t),s,a)}handleGroupByID(e){_.navigateTo(_.removeUrlParam(window.location.hash,"tab")),this.props.onCreateTopic(e)}render(){const{formatMessage:e}=this.props.intl,t=e(this.state.searchQuery?et.search_no_results:et.search_for_contacts);return n.a.createElement("div",{className:"flex-column"},n.a.createElement("ul",{className:"tabbar"},n.a.createElement("li",{className:"p2p"===this.state.tabSelected?"active":null},n.a.createElement("a",{href:"#","data-id":"p2p",onClick:this.handleTabClick},n.a.createElement(r.FormattedMessage,{id:"tabtitle_find_user",defaultMessage:"find"}))),n.a.createElement("li",{className:"grp"===this.state.tabSelected?"active":null},n.a.createElement("a",{href:"#","data-id":"grp",onClick:this.handleTabClick},n.a.createElement(r.FormattedMessage,{id:"tabtitle_new_group",defaultMessage:"new group"}))),n.a.createElement("li",{className:"byid"===this.state.tabSelected?"active":null},n.a.createElement("a",{href:"#","data-id":"byid",onClick:this.handleTabClick},n.a.createElement(r.FormattedMessage,{id:"tabtitle_group_by_id",defaultMessage:"by id"})))),"grp"===this.state.tabSelected?n.a.createElement(Xe,{onSubmit:this.handleNewGroupSubmit}):"byid"===this.state.tabSelected?n.a.createElement(Ze,{onSubmit:this.handleGroupByID,onError:this.props.onError}):n.a.createElement("div",{className:"flex-column"},n.a.createElement($e,{type:"p2p",onSearchContacts:this.handleSearchContacts}),n.a.createElement(z,{contacts:this.props.searchResults,myUserId:this.props.myUserId,emptyListMessage:t,showOnline:!1,showUnread:!1,showContextMenu:!1,onTopicSelected:this.handleContactSelected})))}}var st=Object(r.injectIntl)(tt);class at extends n.a.PureComponent{constructor(e){super(e),this.state={email:"",password:""},this.handleSubmit=this.handleSubmit.bind(this),this.handleEmailChange=this.handleEmailChange.bind(this),this.handlePasswordChange=this.handlePasswordChange.bind(this)}componentDidMount(){let e=_.parseUrlHash(window.location.hash);this.setState({token:e.params.token,scheme:e.params.scheme})}handleSubmit(e){e.preventDefault(),this.state.token?this.props.onReset(this.state.scheme,this.state.password.trim(),this.state.token):this.props.onRequest("email",this.state.email.trim())}handleEmailChange(e){this.setState({email:e.target.value})}handlePasswordChange(e){this.setState({password:e.target.value})}render(){let e=this.state.token&&this.state.scheme;return n.a.createElement("form",{id:"password-reset-form",onSubmit:this.handleSubmit},e?n.a.createElement(r.FormattedMessage,{id:"new_password_placeholder",defaultMessage:"Enter new password"},e=>n.a.createElement(Z,{placeholder:e,autoComplete:"new-password",value:this.state.password,required:!0,autoFocus:!0,onChange:this.handlePasswordChange})):n.a.createElement(n.a.Fragment,null,n.a.createElement("label",{htmlFor:"inputEmail"},n.a.createElement(r.FormattedMessage,{id:"label_reset_password",defaultMessage:"Send a password reset email:"})),n.a.createElement(r.FormattedMessage,{id:"credential_email_prompt",defaultMessage:"Your registration email"},e=>n.a.createElement("input",{type:"email",id:"inputEmail",placeholder:e,autoComplete:"email",value:this.state.email,onChange:this.handleEmailChange,required:!0,autoFocus:!0}))),n.a.createElement("div",{className:"dialog-buttons"},n.a.createElement("button",{className:"blue",type:"submit"},e?n.a.createElement(r.FormattedMessage,{id:"button_reset",defaultMessage:"Reset"}):n.a.createElement(r.FormattedMessage,{id:"button_send_request",defaultMessage:"Send request"}))))}}class nt extends n.a.PureComponent{constructor(e){super(e),this.state={hostName:e.serverAddress,changed:!1},this.handleHostNameChange=this.handleHostNameChange.bind(this),this.handleEditingFinished=this.handleEditingFinished.bind(this)}handleHostNameChange(e){this.setState({hostName:e.target.value,changed:!0})}handleEditingFinished(){this.state.changed&&(this.setState({changed:!1}),this.props.onServerAddressChange(this.state.hostName.trim()))}render(){var e=[];for(let t in u){let s=u[t];e.push(n.a.createElement("option",{key:s,value:s}))}return n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("input",{type:"search",id:"host-name",placeholder:this.props.hostName,list:"known-hosts",className:"quoted",value:this.state.hostName,onChange:this.handleHostNameChange,onBlur:this.handleEditingFinished,required:!0}),n.a.createElement("datalist",{id:"known-hosts"},e))}}class it extends n.a.PureComponent{constructor(e){super(e),this.state={transport:e.transport||"def",serverAddress:e.serverAddress},this.handleSubmit=this.handleSubmit.bind(this),this.handleTransportSelected=this.handleTransportSelected.bind(this),this.handleServerAddressChange=this.handleServerAddressChange.bind(this)}handleSubmit(e){e.preventDefault(),this.props.onUpdate({transport:this.state.transport,serverAddress:this.state.serverAddress})}handleTransportSelected(e){this.setState({transport:e.currentTarget.value})}handleServerAddressChange(e){this.setState({serverAddress:e})}render(){const e={def:"default",ws:"websocket",lp:"long polling"};var t=[],s=this;return["def","ws","lp"].map((function(a){var i="transport-"+a,o=e[a];t.push(n.a.createElement("li",{key:a},n.a.createElement("input",{type:"radio",id:i,name:"transport-select",value:a,checked:s.state.transport===a,onChange:s.handleTransportSelected}),n.a.createElement("label",{htmlFor:i},o)))})),n.a.createElement("form",{id:"settings-form",className:"panel-form",onSubmit:this.handleSubmit},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_server_to_use",defaultMessage:"Server to use:"}))),n.a.createElement(nt,{serverAddress:this.state.serverAddress,onServerAddressChange:this.handleServerAddressChange}),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small"},n.a.createElement(r.FormattedMessage,{id:"label_wire_transport",defaultMessage:"Wire transport:"}))),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("ul",{className:"quoted"},t)),n.a.createElement("div",{className:"dialog-buttons"},n.a.createElement("button",{type:"submit",className:"blue"},n.a.createElement(r.FormattedMessage,{id:"button_update",defaultMessage:"Update"}))))}}const ot=Object(r.defineMessages)({phone:{id:"phone_dative",defaultMessage:"phone"},email:{id:"email_dative",defaultMessage:"email"}});class rt extends n.a.PureComponent{constructor(e){super(e),this.state={code:e.credCode||""},this.handleChange=this.handleChange.bind(this),this.handleKeyPress=this.handleKeyPress.bind(this),this.handleSubmit=this.handleSubmit.bind(this),this.handleCancel=this.handleCancel.bind(this)}handleChange(e){this.setState({code:e.target.value})}handleKeyPress(e){"Enter"===e.key?this.handleSubmit(e):"Escape"==e.key&&this.handleCancel(e)}handleSubmit(e){e.preventDefault(),this.state.code&&this.state.code.trim()&&this.props.onSubmit(this.props.credMethod,this.state.code.trim())}handleCancel(e){e.preventDefault(),this.props.onCancel()}render(){const{formatMessage:e}=this.props.intl;let t={email:e(ot.email),tel:e(ot.phone)}[this.props.credMethod]||this.props.credMethod;return n.a.createElement("div",{className:"panel-form"},n.a.createElement("div",{className:"panel-form-row"},n.a.createElement("label",{className:"small",htmlFor:"enter-confirmation-code"},n.a.createElement(r.FormattedMessage,{id:"enter_confirmation_code_prompt",defaultMessage:"Enter confirmation code sent to you by {method}:",values:{method:t}}))),n.a.createElement("div",{className:"panel-form-row"},n.a.createElement(r.FormattedMessage,{id:"numeric_confirmation_code_prompt",defaultMessage:"Numbers only"},e=>n.a.createElement("input",{type:"text",id:"enter-confirmation-code",placeholder:e,value:this.state.code,onChange:this.handleChange,onKeyPress:this.handleKeyPress,required:!0}))),n.a.createElement("div",{className:"dialog-buttons"},n.a.createElement("button",{className:"blue",onClick:this.handleSubmit},n.a.createElement(r.FormattedMessage,{id:"button_confirm",defaultMessage:"Confirm"})),n.a.createElement("button",{className:"white",onClick:this.handleCancel},n.a.createElement(r.FormattedMessage,{id:"button_cancel"}))))}}var lt=Object(r.injectIntl)(rt);const dt=Object(r.defineMessages)({login:{id:"sidepanel_title_login",defaultMessage:"Sign In"},register:{id:"sidepanel_title_register",defaultMessage:"Create Account"},settings:{id:"sidepanel_title_settings",defaultMessage:"Settings"},edit:{id:"sidepanel_title_account_settings",defaultMessage:"Account Settings"},general:{id:"sidepanel_title_acc_general",defaultMessage:"General"},security:{id:"sidepanel_title_acc_security",defaultMessage:"Security"},notif:{id:"sidepanel_title_acc_notifications",defaultMessage:"Notifications"},support:{id:"sidepanel_title_acc_support",defaultMessage:"Support"},newtpk:{id:"sidepanel_title_newtpk",defaultMessage:"Start New Chat"},cred:{id:"sidepanel_title_cred",defaultMessage:"Confirm Credentials"},reset:{id:"sidepanel_title_reset",defaultMessage:"Reset Password"},archive:{id:"sidepanel_title_archive",defaultMessage:"Archived Chats"},blocked:{id:"sidepanel_title_blocked",defaultMessage:"Blocked Chats"}});class ct extends n.a.Component{constructor(e){super(e),this.handleLoginRequested=this.handleLoginRequested.bind(this),this.handleNewTopic=this.handleNewTopic.bind(this)}handleLoginRequested(e,t){this.props.onLoginRequest(e,t)}handleNewTopic(){this.props.onBasicNavigate("newtpk")}render(){const{formatMessage:e}=this.props.intl,t=this.props.state||(this.props.myUserId?"contacts":"login");let s,a,i;return"contacts"==t?(s=this.props.title,a=!this.props.avatar||this.props.avatar):(s=e(dt[t]),a=!1),-1==["login","contacts"].indexOf(t)&&(i=this.props.onCancel),n.a.createElement("div",{id:"sidepanel",className:this.props.hideSelf?"nodisplay":null},n.a.createElement(Re,{state:t,title:s,avatar:a,myUserId:this.props.myUserId,onSignUp:this.props.onSignUp,onSettings:this.props.onSettings,onNewTopic:this.handleNewTopic,onCancel:i}),n.a.createElement(W,{level:this.props.errorLevel,text:this.props.errorText,action:this.props.errorAction,actionText:this.props.errorActionText,onClearError:this.props.onError}),n.a.createElement(ke,{show:this.props.loadSpinnerVisible}),"login"===t?n.a.createElement(Je,{login:this.props.login,disabled:this.props.loginDisabled,onLogin:this.handleLoginRequested}):"register"===t?n.a.createElement(je,{onCreateAccount:this.props.onCreateAccount,onCancel:this.props.onCancel,onError:this.props.onError}):"settings"===t?n.a.createElement(it,{transport:this.props.transport,serverAddress:this.props.serverAddress,onCancel:this.props.onCancel,onUpdate:this.props.onGlobalSettings}):"edit"===t?n.a.createElement(Be,{tinode:this.props.tinode,myUserId:this.props.myUserId,onBasicNavigate:this.props.onBasicNavigate}):"general"===t?n.a.createElement(He,{tinode:this.props.tinode,myUserId:this.props.myUserId,onUpdateAccount:this.props.onUpdateAccount,onUpdateTags:this.props.onUpdateAccountTags,onCredAdd:this.props.onCredAdd,onCredDelete:this.props.onCredDelete,onCredConfirm:this.props.onCredConfirm,onBasicNavigate:this.props.onBasicNavigate,onError:this.props.onError}):"notif"===t?n.a.createElement(ze,{messageSounds:this.props.messageSounds,desktopAlerts:this.props.desktopAlerts,desktopAlertsEnabled:this.props.desktopAlertsEnabled,incognitoMode:this.props.incognitoMode,onTogglePushNotifications:this.props.onTogglePushNotifications,onToggleMessageSounds:this.props.onToggleMessageSounds,onToggleIncognitoMode:this.props.onToggleIncognitoMode}):"security"===t?n.a.createElement(Ve,{tinode:this.props.tinode,onUpdateAccount:this.props.onUpdateAccount,onLogout:this.props.onLogout,onDeleteAccount:this.props.onDeleteAccount,onShowAlert:this.props.onShowAlert,onShowBlocked:this.props.onShowBlocked}):"support"===t?n.a.createElement(Ge,{serverAddress:this.props.serverAddress,serverVersion:this.props.serverVersion}):"contacts"===t||"archive"==t||"blocked"==t?n.a.createElement(qe,{tinode:this.props.tinode,myUserId:this.props.myUserId,connected:this.props.connected,topicSelected:this.props.topicSelected,archive:"archive"==t,blocked:"blocked"==t,chatList:this.props.chatList,showContextMenu:this.props.showContextMenu,onTopicSelected:this.props.onTopicSelected,onShowArchive:this.props.onShowArchive}):"newtpk"===t?n.a.createElement(st,{searchResults:this.props.searchResults,onInitFind:this.props.onInitFind,onSearchContacts:this.props.onSearchContacts,onCreateTopic:this.props.onCreateTopic,onError:this.props.onError}):"cred"===t?n.a.createElement(lt,{credCode:this.props.credCode,credMethod:this.props.credMethod,onSubmit:this.props.onValidateCredentials,onCancel:this.props.onCancel,onError:this.props.onError}):"reset"===t?n.a.createElement(at,{onRequest:this.props.onPasswordResetRequest,onReset:this.props.onResetPassword}):null)}}var ht=Object(r.injectIntl)(ct);function pt(){let e=g;return"object"==typeof window.location&&("file:"==window.location.protocol||"localhost"==window.location.hostname?e=u.local:window.location.hostname&&(e=window.location.hostname+(window.location.port?":"+window.location.port:""))),e}function mt(){return"object"==typeof window.location&&"https:"==window.location.protocol}const ut=new Audio("audio/msg.mp3"),gt=Object(r.defineMessages)({reconnect_countdown:{id:"reconnect_countdown",defaultMessage:"Disconnected. Reconnecting in {seconds}…"},reconnect_now:{id:"reconnect_now",defaultMessage:"Try now"},push_init_failed:{id:"push_init_failed",defaultMessage:"Failed to initialize push notifications"},invalid_security_token:{id:"invalid_security_token",defaultMessage:"Invalid security token"},no_connection:{id:"no_connection",defaultMessage:"No connection"},code_doesnot_match:{id:"code_doesnot_match",defaultMessage:"Code does not match"}});class _t extends n.a.Component{constructor(e){super(e),this.state=this.getBlankState(),this.handleResize=this.handleResize.bind(this),this.handleHashRoute=this.handleHashRoute.bind(this),this.handleOnline=this.handleOnline.bind(this),this.checkForAppUpdate=this.checkForAppUpdate.bind(this),this.handleVisibilityEvent=this.handleVisibilityEvent.bind(this),this.handleError=this.handleError.bind(this),this.handleLoginRequest=this.handleLoginRequest.bind(this),this.handleConnected=this.handleConnected.bind(this),this.handleAutoreconnectIteration=this.handleAutoreconnectIteration.bind(this),this.doLogin=this.doLogin.bind(this),this.handleCredentialsRequest=this.handleCredentialsRequest.bind(this),this.handleLoginSuccessful=this.handleLoginSuccessful.bind(this),this.handleDisconnect=this.handleDisconnect.bind(this),this.tnMeMetaDesc=this.tnMeMetaDesc.bind(this),this.tnMeContactUpdate=this.tnMeContactUpdate.bind(this),this.tnMeSubsUpdated=this.tnMeSubsUpdated.bind(this),this.resetContactList=this.resetContactList.bind(this),this.tnData=this.tnData.bind(this),this.tnInitFind=this.tnInitFind.bind(this),this.tnFndSubsUpdated=this.tnFndSubsUpdated.bind(this),this.handleSearchContacts=this.handleSearchContacts.bind(this),this.handleTopicSelected=this.handleTopicSelected.bind(this),this.handleHideMessagesView=this.handleHideMessagesView.bind(this),this.handleSendMessage=this.handleSendMessage.bind(this),this.handleNewChatInvitation=this.handleNewChatInvitation.bind(this),this.handleNewAccount=this.handleNewAccount.bind(this),this.handleNewAccountRequest=this.handleNewAccountRequest.bind(this),this.handleUpdateAccountRequest=this.handleUpdateAccountRequest.bind(this),this.handleUpdateAccountTagsRequest=this.handleUpdateAccountTagsRequest.bind(this),this.handleToggleIncognitoMode=this.handleToggleIncognitoMode.bind(this),this.handleSettings=this.handleSettings.bind(this),this.handleGlobalSettings=this.handleGlobalSettings.bind(this),this.handleShowArchive=this.handleShowArchive.bind(this),this.handleShowBlocked=this.handleShowBlocked.bind(this),this.handleToggleMessageSounds=this.handleToggleMessageSounds.bind(this),this.handleCredAdd=this.handleCredAdd.bind(this),this.handleCredDelete=this.handleCredDelete.bind(this),this.handleCredConfirm=this.handleCredConfirm.bind(this),this.initDesktopAlerts=this.initDesktopAlerts.bind(this),this.togglePushToken=this.togglePushToken.bind(this),this.requestPushToken=this.requestPushToken.bind(this),this.handleSidepanelCancel=this.handleSidepanelCancel.bind(this),this.handleNewTopicRequest=this.handleNewTopicRequest.bind(this),this.handleNewTopicCreated=this.handleNewTopicCreated.bind(this),this.handleTopicUpdateRequest=this.handleTopicUpdateRequest.bind(this),this.handleChangePermissions=this.handleChangePermissions.bind(this),this.handleTagsUpdated=this.handleTagsUpdated.bind(this),this.handleLogout=this.handleLogout.bind(this),this.handleDeleteAccount=this.handleDeleteAccount.bind(this),this.handleDeleteMessagesRequest=this.handleDeleteMessagesRequest.bind(this),this.handleLeaveUnsubRequest=this.handleLeaveUnsubRequest.bind(this),this.handleBlockTopicRequest=this.handleBlockTopicRequest.bind(this),this.handleReportTopic=this.handleReportTopic.bind(this),this.handleShowContextMenu=this.handleShowContextMenu.bind(this),this.defaultTopicContextMenu=this.defaultTopicContextMenu.bind(this),this.handleHideContextMenu=this.handleHideContextMenu.bind(this),this.handleShowAlert=this.handleShowAlert.bind(this),this.handleShowInfoView=this.handleShowInfoView.bind(this),this.handleHideInfoView=this.handleHideInfoView.bind(this),this.handleMemberUpdateRequest=this.handleMemberUpdateRequest.bind(this),this.handleValidateCredentialsRequest=this.handleValidateCredentialsRequest.bind(this),this.handlePasswordResetRequest=this.handlePasswordResetRequest.bind(this),this.handleResetPassword=this.handleResetPassword.bind(this),this.handleContextMenuAction=this.handleContextMenuAction.bind(this)}getBlankState(){const e=Oe.getObject("settings")||{};return{connected:!1,ready:!1,autoLogin:!1,transport:e.transport||null,serverAddress:e.serverAddress||pt(),serverVersion:"no connection",messageSounds:!e.messageSoundsOff,incognitoMode:!1,desktopAlerts:e.desktopAlerts,desktopAlertsEnabled:(mt()||"object"==typeof window.location&&"localhost"==window.location.hostname)&&void 0!==d&&"undefined"!=typeof navigator&&"undefined"!=typeof FIREBASE_INIT,firebaseToken:Oe.getObject("firebase-token"),applicationVisible:!document.hidden,errorText:"",errorLevel:null,errorAction:void 0,errorActionText:null,sidePanelSelected:"login",sidePanelTitle:null,sidePanelAvatar:null,loadSpinnerVisible:!1,login:"",password:"",myUserId:null,liveConnection:navigator.onLine,topicSelected:"",topicSelectedOnline:!1,topicSelectedAcs:null,newTopicParams:null,loginDisabled:!1,displayMobile:window.innerWidth<=640,showInfoPanel:!1,mobilePanel:"sidepanel",contextMenuVisible:!1,contextMenuBounds:null,contextMenuClickAt:null,contextMenuParams:null,contextMenuItems:[],alertVisible:!1,alertParams:{},chatList:[],searchResults:[],searchableContacts:[],credMethod:void 0,credCode:void 0}}componentDidMount(){window.addEventListener("resize",this.handleResize),window.addEventListener("online",e=>{this.handleOnline(!0)}),window.addEventListener("offline",e=>{this.handleOnline(!1)}),window.addEventListener("hashchange",this.handleHashRoute),document.addEventListener("visibilitychange",this.handleVisibilityEvent),this.setState({viewportWidth:document.documentElement.clientWidth,viewportHeight:document.documentElement.clientHeight});const{formatMessage:e,locale:t}=this.props.intl;if(this.tinode=_t.tnSetup(this.state.serverAddress,this.state.transport,t),this.tinode.onConnect=this.handleConnected,this.tinode.onDisconnect=this.handleDisconnect,this.tinode.onAutoreconnectIteration=this.handleAutoreconnectIteration,this.state.desktopAlertsEnabled)try{this.fbPush=d.initializeApp(FIREBASE_INIT,m).messaging(),this.fbPush.usePublicVapidKey(FIREBASE_INIT.messagingVapidKey),navigator.serviceWorker.register("/service-worker.js").then(e=>{this.checkForAppUpdate(e),this.fbPush.useServiceWorker(e),e.active.postMessage(JSON.stringify({locale:t})),this.initDesktopAlerts(),this.state.desktopAlerts&&(this.state.firebaseToken?this.tinode.setDeviceToken(this.state.firebaseToken,!0):this.togglePushToken(!0))}).catch(e=>{console.log("Failed to register service worker:",e)})}catch(t){this.handleError(e({id:"push_init_failed"}),"err"),console.log("Failed to initialize push notifications",t),this.setState({desktopAlertsEnabled:!1})}const s=Oe.getObject("keep-logged-in")?Oe.getObject("auth-token"):void 0,a=_.parseUrlHash(window.location.hash);s?(this.setState({autoLogin:!0}),s.expires=new Date(s.expires),this.tinode.setAuthToken(s),this.tinode.connect().catch(e=>{this.handleError(e.message,"err")}),delete a.params.info,delete a.params.tab,a.path[0]="",_.navigateTo(_.composeUrlHash(a.path,a.params))):a.params.token||_.navigateTo(""),this.readTimer=null,this.readTimerCallback=null,this.handleHashRoute()}componentWillUnmount(){window.removeEventListener("resize",this.handleResize),window.removeEventListener("hashchange",this.handleHashRoute),document.removeEventListener("visibilitychange",this.handleVisibilityEvent)}static tnSetup(e,t,s){const a=new h.a(m,e,"AQEAAAABAAD_rAp4DJh05a1HAwFT3A6K",t,mt());return a.setHumanLanguage(s),a.enableLogging(!0,!0),a}handleResize(){const e=document.documentElement.clientWidth<=640;this.setState({viewportWidth:document.documentElement.clientWidth,viewportHeight:document.documentElement.clientHeight}),this.state.displayMobile!=e&&this.setState({displayMobile:e})}checkForAppUpdate(e){e.onupdatefound=()=>{const t=e.installing;t.onstatechange=()=>{if("installed"==t.state&&navigator.serviceWorker.controller){const e=n.a.createElement(n.a.Fragment,null,n.a.createElement(r.FormattedMessage,{id:"update_available",defaultMessage:"Update available."})," ",n.a.createElement("a",{href:""},n.a.createElement(r.FormattedMessage,{id:"reload_update",defaultMessage:"Reload"})),".");this.handleError(e,"info")}}}}handleHashRoute(){const e=_.parseUrlHash(window.location.hash);e.path&&e.path.length>0?(["register","settings","edit","notif","security","support","general","cred","reset","newtpk","archive","blocked","contacts",""].includes(e.path[0])?this.setState({sidePanelSelected:e.path[0]}):console.log("Unknown sidepanel view",e.path[0]),e.path.length>1&&e.path[1]!=this.state.topicSelected&&this.setState({topicSelected:h.a.topicType(e.path[1])?e.path[1]:null})):this.setState({sidePanelSelected:""}),e.params.method&&this.setState({credMethod:e.params.method}),e.params.code&&this.setState({credCode:e.params.code}),this.setState({showInfoPanel:e.params.info,newTopicTabSelected:e.params.tab})}handleOnline(e){e?this.handleError():this.handleError(this.props.intl.formatMessage({id:"no_connection"}),"warn"),this.setState({liveConnection:e})}handleVisibilityEvent(){this.setState({applicationVisible:!document.hidden})}handleError(e,t,s,a){this.setState({errorText:e,errorLevel:t,errorAction:s,errorActionText:a})}handleLoginRequest(e,t){this.setState({loginDisabled:!0,login:e,password:t,loadSpinnerVisible:!0,autoLogin:!0}),this.handleError("",null),this.tinode.isConnected()?this.doLogin(e,t,{meth:this.state.credMethod,resp:this.state.credCode}):this.tinode.connect().catch(e=>{this.setState({loginDisabled:!1,autoLogin:!1,loadSpinnerVisible:!1}),this.handleError(e.message,"err")})}handleConnected(){const e=this.tinode.getServerInfo();this.setState({serverVersion:e.ver+" "+(e.build?e.build:"none")}),this.state.autoLogin&&this.doLogin(this.state.login,this.state.password,{meth:this.state.credMethod,resp:this.state.credCode})}handleAutoreconnectIteration(e,t){if(clearInterval(this.reconnectCountdown),e<0)return void this.handleError();if(t)return void t.then(()=>{this.handleError()}).catch(e=>{this.handleError(e.message,"err")});const{formatMessage:s}=this.props.intl;let a=e/1e3;a|=a,this.reconnectCountdown=setInterval(()=>{const e=a>99?function(e){const t=Math.floor(e/60);let s=e%60;return s=s<10?"0".concat(s):s,"".concat(t,":").concat(s)}(a):a;this.handleError(s(gt.reconnect_countdown,{seconds:e}),"warn",()=>{clearInterval(this.reconnectCountdown),this.tinode.reconnect()},s(gt.reconnect_now)),a-=1},1e3)}handleDisconnect(e){this.setState({connected:!1,ready:!1,topicSelectedOnline:!1,errorText:e&&e.message?e.message:"Disconnected",errorLevel:e&&e.message?"err":"warn",loginDisabled:!1,contextMenuVisible:!1,serverVersion:"no connection"})}doLogin(e,t,s){if(this.tinode.isAuthenticated())return void _.navigateTo("");s=h.a.credential(s);let a=null;const n=this.tinode.getAuthToken();e&&t?(this.setState({password:null}),a=this.tinode.loginBasic(e,t,s)):n&&(a=this.tinode.loginToken(n.token,s)),a?a.then(e=>{e.code>=300&&"validate credentials"===e.text?(this.setState({loadSpinnerVisible:!1}),s&&this.handleError(this.props.intl.formatMessage({id:"code_doesnot_match"}),"warn"),this.handleCredentialsRequest(e.params)):this.handleLoginSuccessful()}).catch(e=>{this.setState({loginDisabled:!1,credMethod:void 0,credCode:void 0,loadSpinnerVisible:!1,autoLogin:!1}),this.handleError(e.message,"err"),localStorage.removeItem("auth-token"),_.navigateTo("")}):(_.navigateTo(""),this.setState({loginDisabled:!1}))}handleCredentialsRequest(e){const t=_.parseUrlHash(window.location.hash);t.path[0]="cred",t.params.method=e.cred[0],_.navigateTo(_.composeUrlHash(t.path,t.params))}handleLoginSuccessful(){this.handleError(),Oe.getObject("keep-logged-in")&&Oe.setObject("auth-token",this.tinode.getAuthToken());const e=this.tinode.getMeTopic();e.onMetaDesc=this.tnMeMetaDesc,e.onContactUpdate=this.tnMeContactUpdate,e.onSubsUpdated=this.tnMeSubsUpdated,this.setState({connected:!0,credMethod:void 0,credCode:void 0,myUserId:this.tinode.getCurrentUserID(),autoLogin:!0}),e.subscribe(e.startMetaQuery().withLaterSub().withDesc().withTags().withCred().build()).catch(e=>{this.tinode.disconnect(),localStorage.removeItem("auth-token"),this.handleError(e.message,"err"),_.navigateTo("")}).finally(()=>{this.setState({loadSpinnerVisible:!1})}),_.navigateTo(_.setUrlSidePanel(window.location.hash,"contacts"))}tnMeMetaDesc(e){e&&(e.public&&this.setState({sidePanelTitle:e.public.fn,sidePanelAvatar:k(e.public.photo)}),e.acs&&this.setState({incognitoMode:!e.acs.isPresencer()}))}tnMeContactUpdate(e,t){if("on"==e||"off"==e)this.resetContactList(),this.state.topicSelected==t.topic&&this.setState({topicSelectedOnline:"on"==e});else if("read"==e)this.resetContactList();else if("msg"==e){const e=this.tinode.getTopic(t.topic),s=e&&e.isArchived();t.unread>0&&this.state.messageSounds&&!s&&(document.hidden||this.state.topicSelected!=t.topic)&&ut.play(),this.resetContactList()}else"recv"==e||("gone"==e||"unsub"==e?(this.state.topicSelected==t.topic&&this.handleTopicSelected(null),this.resetContactList()):"acs"==e?this.state.topicSelected==t.topic&&this.setState({topicSelectedAcs:t.acs}):"del"==e||"upd"==e||console.log("Unsupported (yet) presence update:"+e+" in: "+t.topic))}tnMeSubsUpdated(e){this.resetContactList()}static prepareSearchableContacts(e,t){const s={};for(const t of e)"p2p"==h.a.topicType(t.topic)&&(s[t.topic]={user:t.topic,updated:t.updated,public:t.public,private:t.private,acs:t.acs});for(const e of t)s[e.user]||(s[e.user]=e);return Object.values(s)}resetContactList(){const e={chatList:[]};this.state.ready||(e.ready=!0),this.tinode.getMeTopic().contacts(t=>{e.chatList.push(t),this.state.topicSelected==t.topic&&(e.topicSelectedOnline=t.online,e.topicSelectedAcs=t.acs)}),e.searchableContacts=_t.prepareSearchableContacts(e.chatList,this.state.searchResults),this.setState(e)}tnData(e){const t=this.tinode.getTopic(e.topic);t.msgStatus(e)>=h.a.MESSAGE_STATUS_SENT&&e.from!=this.state.myUserId&&(clearTimeout(this.receivedTimer),this.receivedTimer=setTimeout(()=>{this.receivedTimer=void 0,t.noteRecv(e.seq)},500))}tnInitFind(){const e=this.tinode.getFndTopic();e.onSubsUpdated=this.tnFndSubsUpdated,e.isSubscribed()?this.tnFndSubsUpdated():e.subscribe(e.startMetaQuery().withSub().build()).catch(e=>{this.handleError(e.message,"err")})}tnFndSubsUpdated(){const e=[];this.tinode.getFndTopic().contacts(t=>{e.push(t)}),this.setState({searchResults:e,searchableContacts:_t.prepareSearchableContacts(this.state.chatList,e)})}handleSearchContacts(e){const t=this.tinode.getFndTopic();t.setMeta({desc:{public:e}}).then(e=>t.getMeta(t.startMetaQuery().withSub().build())).catch(e=>{this.handleError(e.message,"err")})}handleTopicSelected(e,t,s,a){this.state.newTopicParams&&this.state.newTopicParams._topicName!=e&&this.setState({newTopicParams:null}),e?(this.setState({errorText:"",errorLevel:null,mobilePanel:"topic-view",showInfoPanel:!1}),this.state.topicSelected!=e&&(this.setState({topicSelectedOnline:s,topicSelectedAcs:a}),_.navigateTo(_.setUrlTopic("",e)))):(this.setState({errorText:"",errorLevel:null,mobilePanel:"sidepanel",topicSelectedOnline:!1,topicSelectedAcs:null,showInfoPanel:!1}),_.navigateTo(_.setUrlTopic("",null)))}handleHideMessagesView(){this.setState({mobilePanel:"sidepanel"}),_.navigateTo(_.setUrlTopic(window.location.hash,null))}handleSendMessage(e,t,s){const a=this.tinode.getTopic(this.state.topicSelected);(e=a.createMessage(e,!1))._uploader=s,a.isSubscribed()||(t||(t=Promise.resolve()),t=t.then(()=>a.subscribe())),t&&(t=t.catch(e=>{this.handleError(e.message,"err")})),a.publishDraft(e,t).then(e=>{if(a.isArchived())return a.archive(!1)}).catch(e=>{this.handleError(e.message,"err")})}handleNewChatInvitation(e,t){const s=this.tinode.getTopic(e);let a=null;switch(t){case"accept":const n=s.getAccessMode().getGiven();a=s.setMeta({sub:{mode:n}}),"p2p"==s.getType()&&(a=a.then(t=>{s.setMeta({sub:{user:e,mode:n}})}));break;case"delete":a=s.delTopic(!0);break;case"block":const i=s.getAccessMode().updateWant("-JP").getWant();a=s.setMeta({sub:{mode:i}}).then(e=>this.handleTopicSelected(null));break;default:console.log("Unknown invitation action",'"'+t+'""')}null!=a&&a.catch(e=>{this.handleError(e.message,"err")})}handleNewAccount(){this.handleError(),_.navigateTo(_.setUrlSidePanel(window.location.hash,"register"))}handleNewAccountRequest(e,t,s,a,n){this.handleError(),this.tinode.connect(this.state.serverAddress).then(()=>this.tinode.createAccountBasic(e,t,{public:s,tags:n,cred:h.a.credential(a)})).then(e=>{e.code>=300&&"validate credentials"==e.text?this.handleCredentialsRequest(e.params):this.handleLoginSuccessful(this)}).catch(e=>{this.handleError(e.message,"err")})}handleUpdateAccountRequest(e,t,s){if(this.handleError(),t||s){const e={};t&&(e.public=t),s&&(e.defacs=s),this.tinode.getMeTopic().setMeta({desc:e}).catch(e=>{this.handleError(e.message,"err")})}e&&this.tinode.updateAccountBasic(null,this.tinode.getCurrentLogin(),e).catch(e=>{this.handleError(e.message,"err")})}handleToggleIncognitoMode(e){const t=this.tinode.getMeTopic(),s=t.getAccessMode().updateWant(e?"-P":"+P").getWant();t.setMeta({sub:{mode:s}}).catch(e=>{this.handleError(e.message,"err")})}handleUpdateAccountTagsRequest(e){this.tinode.getMeTopic().setMeta({tags:e}).catch(e=>{this.handleError(e.message,"err")})}handleSettings(){this.handleError(),_.navigateTo(_.setUrlSidePanel(window.location.hash,this.state.myUserId?"edit":"settings"))}handleGlobalSettings(e){const t=e.serverAddress||this.state.serverAddress,s=e.transport||this.state.transport;this.tinode&&(this.tinode.onDisconnect=void 0,this.tinode.disconnect()),this.tinode=_t.tnSetup(t,s,this.props.intl.locale),this.tinode.onConnect=this.handleConnected,this.tinode.onDisconnect=this.handleDisconnect,this.setState({serverAddress:t,transport:s}),Oe.setObject("settings",{serverAddress:t,transport:s}),_.navigateTo(_.setUrlSidePanel(window.location.hash,""))}handleShowArchive(){_.navigateTo(_.setUrlSidePanel(window.location.hash,this.state.myUserId?"archive":""))}handleShowBlocked(){_.navigateTo(_.setUrlSidePanel(window.location.hash,this.state.myUserId?"blocked":""))}initDesktopAlerts(){this.fbPush.onTokenRefresh(()=>{this.requestPushToken()}),this.fbPush.onMessage(e=>{})}togglePushToken(e){e?this.state.firebaseToken?(this.setState({desktopAlerts:!0}),Oe.updateObject("settings",{desktopAlerts:!0})):this.fbPush.requestPermission().then(()=>{this.requestPushToken()}).catch(e=>{this.handleError(e.message,"err"),this.setState({desktopAlerts:!1,firebaseToken:null}),Oe.updateObject("settings",{desktopAlerts:!1}),console.log("Failed to get permission to notify.",e)}):this.state.firebaseToken?this.fbPush.deleteToken(this.state.firebaseToken).catch(e=>{console.log("Unable to delete token.",e)}).finally(()=>{Oe.updateObject("settings",{desktopAlerts:!1}),localStorage.removeItem("firebase-token"),this.setState({desktopAlerts:!1,firebaseToken:null})}):(this.setState({desktopAlerts:!1,firebaseToken:null}),Oe.updateObject("settings",{desktopAlerts:!1}))}requestPushToken(){this.fbPush.getToken().then(e=>{e!=this.state.firebaseToken&&(this.tinode.setDeviceToken(e,!0),Oe.setObject("firebase-token",e)),this.setState({firebaseToken:e,desktopAlerts:!0}),Oe.updateObject("settings",{desktopAlerts:!0})}).catch(e=>{this.handleError(e.message,"err"),console.log("Failed to retrieve firebase token",e)})}handleToggleMessageSounds(e){this.setState({messageSounds:e}),Oe.updateObject("settings",{messageSoundsOff:!e})}handleCredAdd(e,t){this.tinode.getMeTopic().setMeta({cred:{meth:e,val:t}}).catch(e=>{this.handleError(e.message,"err")})}handleCredDelete(e,t){this.tinode.getMeTopic().delCredential(e,t).catch(e=>{this.handleError(e.message,"err")})}handleCredConfirm(e,t){this.handleCredentialsRequest({cred:[e]})}handleSidepanelCancel(){const e=_.parseUrlHash(window.location.hash);let t="";["security","support","general","notif"].includes(e.path[0])?t="edit":"blocked"==e.path[0]?t="security":this.state.myUserId&&(t="contacts"),e.path[0]=t,e.params&&(delete e.params.code,delete e.params.method,delete e.params.tab),_.navigateTo(_.composeUrlHash(e.path,e.params)),this.setState({errorText:"",errorLevel:null})}basicNavigator(e){_.navigateTo(_.setUrlSidePanel(window.location.hash,e))}handleNewTopicRequest(e,t,s,a){const n=e||this.tinode.newGroupTopicName(),i={_topicName:n};e?(i.sub={mode:"JRWPS"},i.desc={defacs:{auth:"JRWPS"}}):(i.desc={public:t,private:{comment:s}},i.tags=a),this.setState({newTopicParams:i},()=>{this.handleTopicSelected(n)})}handleNewTopicCreated(e,t){this.state.topicSelected==e&&e!=t&&this.setState({topicSelected:t},()=>{_.navigateTo(_.setUrlTopic("",t))})}handleTopicUpdateRequest(e,t,s,a){const n=this.tinode.getTopic(e);if(n){const e={};t&&(e.public=t),s&&(e.private=s===h.a.DEL_CHAR?h.a.DEL_CHAR:{comment:s}),a&&(e.defacs=a),n.setMeta({desc:e}).catch(e=>{this.handleError(e.message,"err")})}}handleChangePermissions(e,t,s){const a=this.tinode.getTopic(e);if(a){const e=a.getAccessMode();s?(e.updateGiven(t),t=e.getGiven()):(e.updateWant(t),t=e.getWant()),a.setMeta({sub:{user:s,mode:t}}).catch(e=>{this.handleError(e.message,"err")})}}handleTagsUpdated(e,t){const s=this.tinode.getTopic(e);s&&s.setMeta({tags:t}).catch(e=>{this.handleError(e.message,"err")})}handleLogout(){ae(0),localStorage.removeItem("auth-token"),localStorage.removeItem("firebase-token"),localStorage.removeItem("settings"),this.state.firebaseToken&&this.fbPush.deleteToken(this.state.firebaseToken),this.tinode&&(this.tinode.onDisconnect=void 0,this.tinode.disconnect()),this.setState(this.getBlankState()),this.tinode=_t.tnSetup(this.state.serverAddress,this.state.transport,this.props.intl.locale),this.tinode.onConnect=this.handleConnected,this.tinode.onDisconnect=this.handleDisconnect,_.navigateTo("")}handleDeleteAccount(){this.tinode.delCurrentUser(!0).then(e=>{this.handleLogout()})}handleDeleteMessagesRequest(e){const t=this.tinode.getTopic(e);t&&t.delMessagesAll(!0).catch(e=>{this.handleError(e.message,"err")})}handleLeaveUnsubRequest(e){const t=this.tinode.getTopic(e);t&&t.leave(!0).then(e=>{_.navigateTo(_.setUrlTopic(window.location.hash,""))}).catch(e=>{this.handleError(e.message,"err")})}handleBlockTopicRequest(e){const t=this.tinode.getTopic(e);t&&t.updateMode(null,"-JP").then(e=>{_.navigateTo(_.setUrlTopic(window.location.hash,""))}).catch(e=>{this.handleError(e.message,"err")})}handleReportTopic(e){const t=this.tinode.getTopic(e);t&&(this.tinode.publish(h.a.TOPIC_SYS,h.a.Drafty.attachJSON(null,{action:"report",target:e})),t.updateMode(null,"-JP").then(e=>{_.navigateTo(_.setUrlTopic(window.location.hash,""))}).catch(e=>{this.handleError(e.message,"err")}))}handleShowContextMenu(e,t){this.setState({contextMenuVisible:!0,contextMenuClickAt:{x:e.x,y:e.y},contextMenuParams:e,contextMenuItems:t||this.defaultTopicContextMenu(e.topicName),contextMenuBounds:o.a.findDOMNode(this).getBoundingClientRect()})}defaultTopicContextMenu(e){const t=this.tinode.getTopic(e);let s=!1,a=!1,n=!1,i=!1,o=!1,r=!1;if(t){i=t.isSubscribed(),r=t.isArchived();const e=t.getAccessMode();e&&(s=e.isMuted(),a=!e.isJoiner(),n=!e.isJoiner("want"),o=e.isDeleter())}return[i?{title:this.props.intl.formatMessage({id:"menu_item_info"}),handler:this.handleShowInfoView}:null,i?"messages_clear":null,i&&o?"messages_clear_hard":null,s?a?null:"topic_unmute":"topic_mute",n?"topic_unblock":"topic_block",r?null:"topic_archive","topic_delete"]}handleHideContextMenu(){this.setState({contextMenuVisible:!1,contextMenuClickAt:null,contextMenuParams:null,contextMenuBounds:null})}handleContextMenuAction(e,t,s){"topic_archive"==e&&t&&s.topicName&&s.topicName==this.state.topicSelected&&t.then(()=>{this.handleTopicSelected(null)})}handleShowAlert(e,t,s,a,n,i){this.setState({alertVisible:!0,alertParams:{title:e,content:t,onConfirm:s,confirm:a,onReject:n,reject:i}})}handleShowInfoView(){_.navigateTo(_.addUrlParam(window.location.hash,"info",!0)),this.setState({showInfoPanel:!0})}handleHideInfoView(){_.navigateTo(_.removeUrlParam(window.location.hash,"info")),this.setState({showInfoPanel:!1})}handleMemberUpdateRequest(e,t,s){if(!e)return;const a=this.tinode.getTopic(e);a&&(t&&t.length>0&&t.map(e=>{a.invite(e,null).catch(e=>{this.handleError(e.message,"err")})}),s&&s.length>0&&s.map(e=>{a.delSubscription(e).catch(e=>{this.handleError(e.message,"err")})}))}handleValidateCredentialsRequest(e,t){if(this.tinode.isAuthenticated()){this.tinode.getMeTopic().setMeta({cred:{meth:e,resp:t}}).then(()=>{_.navigateTo("")}).catch(e=>{this.handleError(e.message,"err")})}else this.setState({credMethod:e,credCode:t}),this.doLogin(null,null,{meth:e,resp:t})}handlePasswordResetRequest(e,t){this.tinode.connect().then(()=>this.tinode.requestResetAuthSecret("basic",e,t)).catch(e=>{this.handleError(e.message,"err")})}handleResetPassword(e,t,s){(s=function(e){if(e){e=e.replace(/-/g,"+").replace(/_/g,"/");try{e=btoa(atob(e))}catch(t){console.log("Failed to base64 re-encode string.",t),e=null}}return e}(s))?this.tinode.connect().then(()=>this.tinode.updateAccountBasic(null,null,t,{token:s})).catch(e=>{this.handleError(e.message,"err")}):this.handleError(this.props.intl.formatMessage({id:"invalid_security_token"}),"err")}render(){return n.a.createElement("div",{id:"app-container"},this.state.contextMenuVisible?n.a.createElement(f,{tinode:this.tinode,bounds:this.state.contextMenuBounds,clickAt:this.state.contextMenuClickAt,params:this.state.contextMenuParams,items:this.state.contextMenuItems,hide:this.handleHideContextMenu,onShowAlert:this.handleShowAlert,onAction:this.handleContextMenuAction,onTopicRemoved:e=>{e==this.state.topicSelected&&this.handleTopicSelected(null)},onError:this.handleError}):null,n.a.createElement(p,{visible:this.state.alertVisible,title:this.state.alertParams.title,content:this.state.alertParams.content,onReject:this.state.alertParams.onReject?()=>{this.setState({alertVisible:!1})}:null,reject:this.state.alertParams.reject,onConfirm:()=>{this.setState({alertVisible:!1}),this.state.alertParams.onConfirm()},confirm:this.state.alertParams.confirm}),n.a.createElement(ht,{tinode:this.tinode,connected:this.state.connected,displayMobile:this.state.displayMobile,hideSelf:this.state.displayMobile&&"sidepanel"!==this.state.mobilePanel,state:this.state.sidePanelSelected,title:this.state.sidePanelTitle,avatar:this.state.sidePanelAvatar,login:this.state.login,myUserId:this.state.myUserId,loginDisabled:this.state.loginDisabled,loadSpinnerVisible:this.state.loadSpinnerVisible,errorText:this.state.errorText,errorLevel:this.state.errorLevel,errorAction:this.state.errorAction,errorActionText:this.state.errorActionText,topicSelected:this.state.topicSelected,chatList:this.state.chatList,credMethod:this.state.credMethod,credCode:this.state.credCode,transport:this.state.transport,messageSounds:this.state.messageSounds,desktopAlerts:this.state.desktopAlerts,desktopAlertsEnabled:this.state.desktopAlertsEnabled,incognitoMode:this.state.incognitoMode,serverAddress:this.state.serverAddress,serverVersion:this.state.serverVersion,onGlobalSettings:this.handleGlobalSettings,onSignUp:this.handleNewAccount,onSettings:this.handleSettings,onBasicNavigate:this.basicNavigator,onLoginRequest:this.handleLoginRequest,onCreateAccount:this.handleNewAccountRequest,onUpdateAccount:this.handleUpdateAccountRequest,onUpdateAccountTags:this.handleUpdateAccountTagsRequest,onTogglePushNotifications:this.togglePushToken,onToggleMessageSounds:this.handleToggleMessageSounds,onToggleIncognitoMode:this.handleToggleIncognitoMode,onCredAdd:this.handleCredAdd,onCredDelete:this.handleCredDelete,onCredConfirm:this.handleCredConfirm,onTopicSelected:this.handleTopicSelected,onCreateTopic:this.handleNewTopicRequest,onLogout:this.handleLogout,onDeleteAccount:this.handleDeleteAccount,onShowAlert:this.handleShowAlert,onCancel:this.handleSidepanelCancel,onError:this.handleError,onValidateCredentials:this.handleValidateCredentialsRequest,onPasswordResetRequest:this.handlePasswordResetRequest,onResetPassword:this.handleResetPassword,onShowArchive:this.handleShowArchive,onShowBlocked:this.handleShowBlocked,onInitFind:this.tnInitFind,searchResults:this.state.searchResults,onSearchContacts:this.handleSearchContacts,showContextMenu:this.handleShowContextMenu}),n.a.createElement(Fe,{tinode:this.tinode,connected:this.state.connected,ready:this.state.ready,online:this.state.topicSelectedOnline,acs:this.state.topicSelectedAcs,displayMobile:this.state.displayMobile,viewportWidth:this.state.viewportWidth,viewportHeight:this.state.viewportHeight,hideSelf:this.state.displayMobile&&("topic-view"!==this.state.mobilePanel||this.state.showInfoPanel),topic:this.state.topicSelected,myUserId:this.state.myUserId,serverVersion:this.state.serverVersion,serverAddress:this.state.serverAddress,applicationVisible:this.state.applicationVisible,errorText:this.state.errorText,errorLevel:this.state.errorLevel,errorAction:this.state.errorAction,errorActionText:this.state.errorActionText,newTopicParams:this.state.newTopicParams,onHideMessagesView:this.handleHideMessagesView,onData:this.tnData,onError:this.handleError,onNewTopicCreated:this.handleNewTopicCreated,showContextMenu:this.handleShowContextMenu,onChangePermissions:this.handleChangePermissions,onNewChat:this.handleNewChatInvitation,sendMessage:this.handleSendMessage}),this.state.showInfoPanel?n.a.createElement(ce,{tinode:this.tinode,connected:this.state.connected,displayMobile:this.state.displayMobile,topic:this.state.topicSelected,searchableContacts:this.state.searchableContacts,myUserId:this.state.myUserId,errorText:this.state.errorText,errorLevel:this.state.errorLevel,errorAction:this.state.errorAction,errorActionText:this.state.errorActionText,onTopicDescUpdate:this.handleTopicUpdateRequest,onCancel:this.handleHideInfoView,onShowAlert:this.handleShowAlert,onChangePermissions:this.handleChangePermissions,onMemberUpdateRequest:this.handleMemberUpdateRequest,onDeleteMessages:this.handleDeleteMessagesRequest,onLeaveTopic:this.handleLeaveUnsubRequest,onBlockTopic:this.handleBlockTopicRequest,onReportTopic:this.handleReportTopic,onAddMember:this.handleManageGroupMembers,onTopicTagsUpdate:this.handleTagsUpdated,onInitFind:this.tnInitFind,onError:this.handleError,showContextMenu:this.handleShowContextMenu}):null)}}var bt=Object(r.injectIntl)(_t);const{params:vt}=_.parseUrlHash(window.location.hash),ft=vt&&vt.hl||navigator.languages&&navigator.languages[0]||navigator.language||navigator.userLanguage||"en",Et=ft.toLowerCase().split(/[-_]/)[0],wt=l[ft]||l[Et]||l.en;o.a.render(n.a.createElement(r.IntlProvider,{locale:ft,messages:wt,textComponent:n.a.Fragment},n.a.createElement(bt,null)),document.getElementById("mountPoint"))}]);
//# sourceMappingURL=index.prod.js.map