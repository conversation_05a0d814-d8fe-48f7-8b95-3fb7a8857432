{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///external \"React\"", "webpack:///external \"ReactIntl\"", "webpack:///external \"Tinode\"", "webpack:///external \"ReactDOM\"", "webpack:///external \"firebase\"", "webpack:///external [\"firebase\",\"messaging\"]", "webpack:///./src/widgets/alert.jsx", "webpack:///./src/version.js", "webpack:///./src/config.js", "webpack:///./src/lib/navigation.js", "webpack:///./src/widgets/context-menu.jsx", "webpack:///./src/lib/strformat.js", "webpack:///./src/widgets/letter-tile.jsx", "webpack:///./src/lib/blob-helpers.js", "webpack:///./src/widgets/avatar-upload.jsx", "webpack:///./src/widgets/checkbox.jsx", "webpack:///./src/widgets/contact-badges.jsx", "webpack:///./src/widgets/unread-badge.jsx", "webpack:///./src/widgets/contact.jsx", "webpack:///./src/widgets/contact-action.jsx", "webpack:///./src/widgets/contact-list.jsx", "webpack:///./src/widgets/menu-cancel.jsx", "webpack:///./src/widgets/error-panel.jsx", "webpack:///./src/widgets/chip.jsx", "webpack:///./src/widgets/chip-input.jsx", "webpack:///./src/widgets/group-manager.jsx", "webpack:///./src/widgets/visible-password.jsx", "webpack:///./src/widgets/in-place-edit.jsx", "webpack:///./src/widgets/more-button.jsx", "webpack:///./src/widgets/permissions-editor.jsx", "webpack:///./src/lib/utils.js", "webpack:///./src/widgets/tag-manager.jsx", "webpack:///./src/views/info-view.jsx", "webpack:///./src/widgets/file-progress.jsx", "webpack:///./src/widgets/attachment.jsx", "webpack:///./src/widgets/received-marker.jsx", "webpack:///./src/widgets/chat-message.jsx", "webpack:///./src/widgets/send-message.jsx", "webpack:///./src/widgets/doc-preview.jsx", "webpack:///./src/widgets/group-subs.jsx", "webpack:///./src/widgets/image-preview.jsx", "webpack:///./src/widgets/invitation.jsx", "webpack:///./src/widgets/load-spinner.jsx", "webpack:///./src/views/logo-view.jsx", "webpack:///./src/views/messages-view.jsx", "webpack:///./src/widgets/button-back.jsx", "webpack:///./src/widgets/menu-contacts.jsx", "webpack:///./src/widgets/menu-start.jsx", "webpack:///./src/widgets/side-navbar.jsx", "webpack:///./src/views/contacts-view.jsx", "webpack:///./src/lib/local-storage.js", "webpack:///./src/views/create-account-view.jsx", "webpack:///./src/views/edit-account-view.jsx", "webpack:///./src/views/acc-general-view.jsx", "webpack:///./src/views/acc-notifications-view.jsx", "webpack:///./src/views/acc-security-view.jsx", "webpack:///./src/views/acc-support-view.jsx", "webpack:///./src/views/login-view.jsx", "webpack:///./src/widgets/new-topic-by-id.jsx", "webpack:///./src/widgets/new-topic-group.jsx", "webpack:///./src/widgets/search-contacts.jsx", "webpack:///./src/views/new-topic-view.jsx", "webpack:///./src/views/password-reset-view.jsx", "webpack:///./src/widgets/host-selector.jsx", "webpack:///./src/views/settings-view.jsx", "webpack:///./src/views/validation-view.jsx", "webpack:///./src/views/sidepanel-view.jsx", "webpack:///./src/lib/host-name.js", "webpack:///./src/views/tinode-web.jsx", "webpack:///./src/index.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "React", "ReactIntl", "Tinode", "ReactDOM", "firebase", "<PERSON><PERSON>", "PureComponent", "render", "this", "props", "visible", "className", "title", "content", "onReject", "onClick", "reject", "id", "onConfirm", "confirm", "APP_NAME", "KNOWN_HOSTS", "hosted", "local", "DEFAULT_HOST", "HashNavigation", "hash", "parts", "split", "params", "path", "substr", "for<PERSON>ach", "part", "item", "decodeURIComponent", "url", "window", "location", "join", "args", "push", "length", "parsed", "parseUrlHash", "composeUrlHash", "sidepanel", "topic", "info", "messages", "defineMessages", "clear_messages", "clear_for_all", "delete_for_all", "send_retry", "mute", "unmute", "topic_delete", "topic_delete_warning", "unblock", "block", "topic_block_warning", "member_delete", "archive", "ContextMenu", "Component", "constructor", "super", "formatMessage", "intl", "handlePageClick", "handleEscapeKey", "handleClick", "MenuItems", "handler", "<PERSON><PERSON><PERSON><PERSON>", "onShowAlert", "deleteMessages", "delete", "retryMessage", "topicPermissionSetter", "then", "ctrl", "onTopicRemoved", "topicName", "tinode", "getTopic", "delTopic", "catch", "err", "message", "console", "log", "user", "delSubscription", "componentDidMount", "document", "addEventListener", "componentWillUnmount", "removeEventListener", "e", "findDOMNode", "contains", "target", "preventDefault", "stopPropagation", "hide", "keyCode", "items", "currentTarget", "dataset", "onAction", "onError", "all", "hard", "cancelSend", "seq", "delMessagesAll", "delMessagesList", "flushMessage", "msg", "createMessage", "publishDraft", "result", "updateMode", "count", "menu", "map", "data-id", "vSize", "position", "left", "bounds", "right", "clickAt", "x", "top", "bottom", "y", "style", "injectIntl", "shortDateFormat", "locale", "navigator", "userLanguage", "language", "now", "Date", "getFullYear", "getMonth", "getDate", "toLocaleTimeString", "hour12", "hour", "minute", "toLocaleDateString", "month", "day", "year", "bytesToHumanSize", "bytes", "sizes", "bucket", "Math", "min", "floor", "log2", "pow", "round", "toFixed", "LetterTile", "avatar", "isGroup", "topicType", "iconColor", "abs", "charCodeAt", "stringHash", "trim", "letter", "char<PERSON>t", "alt", "src", "onerror", "SUPPORTED_IMAGE_FORMATS", "MIME_EXTENSIONS", "makeImageUrl", "photo", "type", "data", "fitImageSize", "width", "height", "max<PERSON><PERSON><PERSON>", "maxHeight", "forceSquare", "scale", "size", "dstWidth", "dstHeight", "srcWidth", "srcHeight", "xoffset", "yoffset", "fileNameForMime", "fname", "mime", "idx", "indexOf", "ext", "at", "lastIndexOf", "substring", "imageFileScaledToBase64", "file", "onSuccess", "img", "Image", "crossOrigin", "onload", "dim", "canvas", "createElement", "ctx", "getContext", "imageSmoothingEnabled", "drawImage", "imageBits", "toDataURL", "getMimeType", "quality", "base64DecodedLen", "URL", "createObjectURL", "imageFileToBase64", "reader", "FileReader", "readAsDataURL", "fileToBase64", "header", "exec", "AvatarUpload", "state", "dataUrl", "handleFileUpload", "componentDidUpdate", "prevProps", "setState", "files", "base64bits", "du", "onImageChanged", "randId", "random", "readOnly", "href", "uid", "accept", "onChange", "htmlFor", "CheckBox", "handleChange", "checked", "icon_mapping", "ContactBadges", "badges", "b", "icon", "color", "UnreadBadge", "Contact", "handleContextClick", "onSelected", "index", "acs", "showContextMenu", "pageY", "pageX", "online", "slice", "icon_badges", "showMode", "getMode", "isMuted", "<PERSON><PERSON><PERSON><PERSON>", "showCheckmark", "selected", "showOnline", "unread", "comment", "ContactAction", "action", "values", "badge_you", "badge_owner", "ContactList", "Array", "isArray", "topicSelected", "contactNodes", "contactsCount", "contacts", "filterFunc", "filter", "private", "toLowerCase", "public", "fn", "myUserId", "isOwner", "showUnread", "connected", "onTopicSelected", "noScroll", "dangerouslySetInnerHTML", "__html", "emptyListMessage", "MenuCancel", "onCancel", "ErrorPanel", "show", "level", "onClearError", "warn", "text", "whiteSpace", "actionText", "Chip", "handleCancel", "invalid", "noAvatar", "required", "ChipInput", "deriveStateFromProps", "input", "focused", "handleTextInput", "removeChipAt", "handleChipCancel", "handleFocusGained", "handleFocusLost", "handleKeyDown", "placeholder", "chips", "prompt", "sortedChips", "sortChips", "staticMembers", "chipIndex", "indexChips", "prevState", "keep", "normal", "includes", "concat", "removed", "onChipRemoved", "onFocusLost", "onEnter", "undefined", "avatarDisabled", "onFocus", "onBlur", "onKeyDown", "autoFocus", "no_contacts", "contacts_not_found_short", "GroupManager", "members", "indexMembers", "keepInitialMembers", "requiredMember", "contactFilter", "noContactsMessage", "selectedContacts", "handleContactSelected", "handleMemberRemoved", "handleContactFilter", "handleSubmit", "delta", "present", "keepInitial", "stat", "sel", "userId", "status", "splice", "val", "query", "instance", "added", "keys", "k", "onSubmit", "defaultMessage", "doContactFiltering", "VisiblePassword", "handleVisibility", "handeTextChange", "handleEditingFinished", "onFinished", "setTimeout", "activeElement", "tabIndex", "autoComplete", "InPlaceEdit", "active", "initialValue", "handleStartEditing", "handlePasswordFinished", "newValue", "focus", "event", "checkValidity", "fieldType", "spanText", "spanClass", "MoreButton", "open", "handleToggle", "onToggle", "PermissionsEditor", "replace", "sort", "names", "joiner", "writer", "preser", "approver", "sharer", "deleter", "owner", "skip", "compare", "userTitle", "userAvatar", "modeTitle", "compareTitle", "updateFavicon", "oldIcon", "getElementById", "head", "getElementsByTagName", "newIcon", "rel", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "vcard", "imageDataUrl", "card", "dataStart", "arrayEqual", "a", "sanitizeUrl", "allowedSchemes", "test", "schemes", "RegExp", "TagManager", "tags", "tagInput", "activated", "handleTagInput", "handleAddTag", "handleRemoveTag", "nextProps", "last", "tag", "onTagsChanged", "add_tags_prompt", "defautMessage", "requested", "granted", "edit_permissions", "other_user", "clear_messages_warning", "delete_messages", "delete_messages_warning", "leave_chat", "leave_chat_warning", "block_contact", "block_contact_warning", "report_chat", "report_chat_warning", "InfoView", "admin", "muted", "address", "groupTopic", "fullName", "selectedContact", "access", "modeGiven", "modeWant", "modeGiven2", "modeWant2", "auth", "anon", "contactList", "showMemberPanel", "showPermissionEditorFor", "moreInfoExpanded", "previousMetaDesc", "previousSubsUpdated", "previousTagsUpdated", "resetSubs", "resetDesc", "onMetaDesc", "onSubsUpdated", "onTagsUpdated", "handleFullNameUpdate", "handlePrivateUpdate", "handleImageChanged", "handleMuted", "handlePermissionsChanged", "handleLaunchPermissionsEditor", "handleHidePermissionsEditor", "handleShowAddMembers", "handleHideAddMembers", "handleMemberUpdateRequest", "handleDeleteMessages", "handleLeave", "handleBlock", "handleReport", "handleMemberSelected", "handleMoreInfo", "handleTagsUpdated", "handleContextMenu", "getType", "newState", "user2", "subscriber", "getGiven", "getWant", "subscribers", "sub", "defacs", "getDefaultAccess", "getAccessMode", "isAdmin", "isSharer", "isDeleter", "getMeta", "startMetaQuery", "withTags", "build", "desc", "subs", "onTopicDescUpdate", "DEL_CHAR", "ignored", "onChangePermissions", "perm", "userPermissionsEdited", "which", "toEdit", "toCompare", "toSkip", "titleEdit", "titleCompare", "AccessMode", "encode", "diff", "userPermissionsTitle", "userPermissionsAvatar", "editedPermissions", "immutablePermissions", "editedPermissionsTitle", "immutablePermissionsTitle", "editedPermissionsSkipped", "onInitFind", "onMemberUpdateRequest", "onDeleteMessages", "onLeaveTopic", "onBlockTopic", "onReportTopic", "onTopicTagsUpdate", "menuItems", "displayMobile", "errorLevel", "errorText", "searchableContacts", "private_placeholder", "more", "no_members", "FileProgress", "progress", "Attachment", "downloader", "downloadFile", "filename", "mimetype", "getLargeFile<PERSON>elper", "download", "loaded", "uploader", "onCancelUpload", "cancel", "helper<PERSON><PERSON><PERSON>", "isUrlRelative", "downloadUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp", "received", "MESSAGE_STATUS_SENDING", "sending", "MESSAGE_STATUS_FAILED", "failed", "marker", "MESSAGE_STATUS_SENT", "MESSAGE_STATUS_RECEIVED", "MESSAGE_STATUS_READ", "ChatMessage", "onProgress", "handleProgress", "handleImagePreview", "handleFormButtonClick", "handleCancelUpload", "onImagePreview", "resp", "act", "ref", "onFormResponse", "ratio", "sideClass", "deleted", "sequence", "response", "bubbleClass", "fullDisplay", "userFrom", "attachments", "mimeType", "<PERSON>y", "getContentType", "<PERSON><PERSON><PERSON><PERSON>", "att", "getDownloadUrl", "isUploading", "getEntitySize", "Fragment", "format", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userName", "notFound", "el", "tagName", "attr", "attrValue", "viewportWidth", "REM_SIZE", "sanitizedUrl", "sanitizeImageUrl", "inner", "Children", "child", "SendMessage", "keypressTimestamp", "getTime", "handlePasteEvent", "handleAttachImage", "handleAttachFile", "handleSend", "handleKeyPress", "handleMessageTyping", "messageEditArea", "disabled", "onImageSuccess", "onAttachmentSuccess", "clipboardData", "originalEvent", "kind", "getAsFile", "filePasted", "bits", "onAttachImage", "onAttachFile", "acceptBlank", "noInput", "onSendMessage", "shift<PERSON>ey", "onKeyPress", "messaging_disabled", "messagePrompt", "type_new_message", "attachImage", "click", "attachFile", "display", "DocPreview", "handleSendDoc", "caption", "onClose", "mimeToIcon", "default", "image", "video", "iconFromMime", "GroupSubs", "usersOnline", "totalCount", "countToShow", "some", "overflow", "ImagePreview", "handleSendImage", "assignWidth", "node", "getBoundingClientRect", "maxlength", "max", "Invitation", "handleButtonAction", "evt", "<PERSON>ad<PERSON><PERSON><PERSON>", "LogoView", "version", "getLibrary", "serverVersion", "serverAddress", "online_now", "last_seen", "isUnconfirmed", "ex", "getExcessive", "isPeerRestricted", "ms", "getMissing", "MessagesView", "getDerivedStateFromProps", "leave", "sendImageAttachment", "sendFileAttachment", "send<PERSON>eyPress", "handleScrollReference", "handleScrollEvent", "handleDescChange", "handleSubsUpdated", "handleNewMessage", "handleAllMessagesReceived", "handleInfoReceipt", "handleImagePostview", "handleClosePreview", "handleFormResponse", "handleShowContextMenuMessage", "handleNewChatAcceptance", "handleEnablePeer", "postReadNotification", "clearNotificationQueue", "readNotificationQueue", "readNotificationTimer", "messagesScroller", "scrollTop", "scrollHeight", "scrollPosition", "viewportHeight", "isNewGroupTopicName", "onData", "onAllMessagesReceived", "onInfo", "onPres", "applicationVisible", "isSubscribed", "ready", "newTopic", "newTopicParams", "_topicName", "<PERSON><PERSON><PERSON><PERSON>", "withLaterDesc", "withLaterSub", "is<PERSON><PERSON>er", "withLaterData", "withLaterDel", "fetchingMessages", "<PERSON><PERSON><PERSON><PERSON>", "subscribe", "onNewTopicCreated", "queuedMessages", "pub", "_sending", "publishMessage", "blankState", "not_found", "nextState", "docPreview", "imagePreview", "imagePostview", "typingIndicator", "msgs", "assign", "onlineSubs", "peer", "p2pPeerDesc", "peerMessagingDisabled", "isWriter", "readingBlocked", "unconformed", "unconfirmed", "oldTopicName", "oldTopic", "finally", "msgHasMoreMessages", "getMessagesPage", "setInterval", "clearInterval", "shift", "sendAt", "noteRead", "setMilliseconds", "getMilliseconds", "isNewMessage", "msgStatus", "from", "what", "clearTimeout", "keyPress<PERSON><PERSON>r", "KEYPRESS_DELAY", "forceUpdate", "sendMessage", "attachJSON", "parse", "searchParams", "set", "search", "messageSpecificMenuItems", "onNewChat", "noteKeyPress", "cannot_initiate_upload", "uploadCompletionPromise", "upload", "file_attachment_too_large", "limit", "insertImage", "appendLineBreak", "append", "init", "component", "hideSelf", "component2", "messageNodes", "previousFrom", "chatBoxClass", "nextFrom", "isReply", "deliveryStatus", "userDesc", "hi", "ts", "_uploader", "lastSeen", "cont", "getMeTopic", "getContact", "seen", "when", "onHideMessagesView", "ButtonBack", "onBack", "MenuContacts", "onNewTopic", "onSettings", "MenuStart", "onSignUp", "SideNavbar", "archived_contacts_title", "ContactsView", "handleAction", "unreadThreads", "archivedCount", "chatList", "blocked", "arch", "touched", "action_ignored", "onShowArchive", "LocalStorageUtil", "localStorage", "setItem", "JSON", "stringify", "getItem", "oldVal", "getObject", "setObject", "removeItem", "CreateAccountView", "login", "password", "email", "errorCleared", "saveToken", "handleLoginChange", "handlePasswordChange", "handleEmailChange", "handleFnChange", "handleToggleSaveToken", "onCreateAccount", "submitClasses", "login_prompt", "password_prompt", "full_name_prompt", "email_prompt", "stay_logged_in", "EditAccountView", "me", "onBasicNavigate", "AccGeneralView", "credentials", "getCredentials", "addCredActive", "addCredInvalid", "new<PERSON>red", "previousOnTags", "tnNewTags", "tnCredsUpdated", "handleCredChange", "handleCredKeyDown", "handleCredEntered", "onCredsUpdated", "creds", "onUpdateAccount", "method", "asPhone", "asEmail", "onCredAdd", "onUpdateTags", "cred", "meth", "done", "onCredConfirm", "onCredDelete", "full_name_placeholder", "title_tag_manager", "handleCheckboxClick", "onToggleMessageSounds", "onTogglePushNotifications", "onToggleIncognitoMode", "messageSounds", "desktopAlertsEnabled", "desktopAlerts", "incognitoMode", "delete_account", "delete_account_warning", "AccSecurityView", "blockedCount", "handlePasswordUpdate", "handleDeleteAccount", "pwd", "onDeleteAccount", "password_unchanged", "onLogout", "onShowBlocked", "AccSupportView", "<PERSON><PERSON><PERSON>ie<PERSON>", "hostName", "onLogin", "autoCorrect", "autoCapitalize", "invalid_id", "NewTopicById", "groupId", "NewTopicGroup", "handlePrivateChange", "handleTagsChanged", "SearchContacts", "edited", "handleSearchChange", "handleSearch", "handleClear", "onSearchContacts", "search_placeholder", "search_for_contacts", "search_no_results", "NewTopicView", "tabSelected", "searchQuery", "handleTabClick", "handleSearchContacts", "handleNewGroupSubmit", "handleGroupByID", "navigateTo", "addUrlParam", "isNullValue", "removeUrlParam", "onCreateTopic", "priv", "no_contacts_placeholder", "searchResults", "PasswordResetView", "token", "scheme", "onReset", "onRequest", "reset", "HostSelector", "changed", "handleHostNameChange", "onServerAddressChange", "hostOptions", "list", "SettingsView", "transport", "handleTransportSelected", "handleServerAddressChange", "onUpdate", "def", "ws", "lp", "transportOptions", "phone", "ValidationView", "code", "credCode", "cred<PERSON><PERSON><PERSON>", "numbers_only", "SidepanelView", "handleLoginRequested", "handleNewTopic", "onLoginRequest", "view", "errorAction", "errorActionText", "loadSpinnerVisible", "loginDisabled", "onGlobalSettings", "onUpdateAccountTags", "onValidateCredentials", "onPasswordResetRequest", "onResetPassword", "detectServerAddress", "host", "protocol", "hostname", "port", "isSecureConnection", "POP_SOUND", "Audio", "reconnect_countdown", "reconnect_now", "push_init_failed", "invalid_security_token", "no_connection", "code_doesnot_match", "TinodeWeb", "getBlankState", "handleResize", "handleHashRoute", "handleOnline", "checkForAppUpdate", "handleVisibilityEvent", "handleError", "handleLoginRequest", "handleConnected", "handleAutoreconnectIteration", "do<PERSON><PERSON><PERSON>", "handleCredentialsRequest", "handleLoginSuccessful", "handleDisconnect", "tnMeMetaDesc", "tnMeContactUpdate", "tnMeSubsUpdated", "resetContactList", "tnData", "tnInitFind", "tnFndSubsUpdated", "handleTopicSelected", "handleHideMessagesView", "handleSendMessage", "handleNewChatInvitation", "handleNewAccount", "handleNewAccountRequest", "handleUpdateAccountRequest", "handleUpdateAccountTagsRequest", "handleToggleIncognitoMode", "handleSettings", "handleGlobalSettings", "handleShowArchive", "handleShowBlocked", "handleToggleMessageSounds", "handleCredAdd", "handleCredDelete", "handleCredConfirm", "initDesktopAlerts", "togglePushToken", "requestPushToken", "handleSidepanelCancel", "handleNewTopicRequest", "handleNewTopicCreated", "handleTopicUpdateRequest", "handleChangePermissions", "handleLogout", "handleDeleteMessagesRequest", "handleLeaveUnsubRequest", "handleBlockTopicRequest", "handleReportTopic", "handleShowContextMenu", "defaultTopicContextMenu", "handleHideContextMenu", "handleShowAlert", "handleShowInfoView", "handleHideInfoView", "handleValidateCredentialsRequest", "handlePasswordResetRequest", "handleResetPassword", "handleContextMenuAction", "settings", "autoLogin", "messageSoundsOff", "FIREBASE_INIT", "firebaseToken", "hidden", "sidePanelSelected", "sidePanelTitle", "sidePanelAvatar", "liveConnection", "onLine", "topicSelectedOnline", "topicSelectedAcs", "innerWidth", "showInfoPanel", "mobilePanel", "contextMenuVisible", "contextMenuBounds", "contextMenuClickAt", "contextMenuParams", "contextMenuItems", "alertVisible", "alertParams", "documentElement", "clientWidth", "clientHeight", "tnSetup", "onConnect", "onDisconnect", "onAutoreconnectIteration", "fbPush", "messaging", "usePublicVapidKey", "messagingVapidKey", "serviceWorker", "register", "reg", "useServiceWorker", "postMessage", "setDeviceToken", "parsedNav", "expires", "setAuthToken", "connect", "tab", "readTimer", "readTimerCallback", "setHumanLanguage", "enableLogging", "mobile", "onupdatefound", "installingWorker", "installing", "onstatechange", "controller", "newTopicTabSelected", "isConnected", "getServerInfo", "ver", "sec", "prom", "reconnectCountdown", "timeLeft", "seconds", "secondsToTime", "reconnect", "isAuthenticated", "credential", "promise", "getAuthToken", "loginBasic", "loginToken", "onContactUpdate", "getCurrentUserID", "withDesc", "with<PERSON>red", "disconnect", "setUrlSidePanel", "isPresencer", "archived", "isArchived", "play", "unused", "foundContacts", "merged", "updated", "prepareSearchableContacts", "received<PERSON>imer", "noteRecv", "fnd", "getFndTopic", "with<PERSON><PERSON>", "setMeta", "unused_index", "setUrlTopic", "Promise", "resolve", "am", "updateWant", "login_", "password_", "public_", "cred_", "tags_", "createAccountBasic", "updateAccountBasic", "getCurrentLogin", "on", "onTokenRefresh", "onMessage", "payload", "enabled", "updateObject", "requestPermission", "deleteToken", "getToken", "refreshedToken", "delCredential", "basicNavigator", "<PERSON><PERSON><PERSON>", "newGroupTopicName", "old<PERSON>ame", "newName", "permissions", "updateGiven", "delCurrentUser", "publish", "TOPIC_SYS", "self_blocked", "subscribed", "confirmText", "rejectText", "invite", "requestResetAuthSecret", "newPassword", "str", "btoa", "atob", "base64ReEncode", "onAddMember", "handleManageGroupMembers", "hl", "languages", "baseLanguage", "allMessages", "en", "textComponent"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,QAIjBlC,EAAoBA,EAAoBmC,EAAI,G,gBClFrDhC,EAAOD,QAAUkC,O,cCAjBjC,EAAOD,QAAUmC,W,cCAjBlC,EAAOD,QAAUoC,Q,cCAjBnC,EAAOD,QAAUqC,U,8juCCAjBpC,EAAOD,QAAUsC,U,cCAjBrC,EAAOD,QAAUsC,SAAoB,W,oHCGtB,MAAMC,UAAcL,IAAMM,cACvCC,SACE,OAAOC,KAAKC,MAAMC,QAChB,yBAAKC,UAAU,mBACb,yBAAKA,UAAU,SACb,yBAAKA,UAAU,SAASH,KAAKC,MAAMG,OACnC,yBAAKD,UAAU,WAAWH,KAAKC,MAAMI,SACrC,yBAAKF,UAAU,kBACZH,KAAKC,MAAMK,SACV,4BAAQH,UAAU,UAAUI,QAASP,KAAKC,MAAMK,UAC7CN,KAAKC,MAAMO,QAAU,kBAAC,mBAAD,CAAkBC,GAAG,mBAG7C,KAEF,4BAAQN,UAAU,OAAOI,QAASP,KAAKC,MAAMS,WAC1CV,KAAKC,MAAMU,SAAW,kBAAC,mBAAD,CAAkBF,GAAG,kBAMtD,MCvBG,MCCMG,EAAW,mBAMXC,EAAc,CAACC,OAAQ,gBAAiBC,MAAO,kBAG/CC,EAAeH,EAAYC,OCRzB,MAAMG,EACnB,oBAAoBC,GAElB,IAAIC,EAAQD,EAAKE,MAAM,IAAK,GACxBC,EAAS,GACTC,EAAO,GAYX,OAXIH,EAAM,KACRG,EAAOH,EAAM,GAAGI,OAAO,GAAGH,MAAM,MAE9BD,EAAM,IACRA,EAAM,GAAGC,MAAM,KAAKI,SAAQ,SAASC,GACnC,IAAIC,EAAOD,EAAKL,MAAM,KAClBM,EAAK,KACPL,EAAOM,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,QAI7D,CAACJ,KAAMA,EAAMD,OAAQA,GAG9B,kBAAkBO,GAChBC,OAAOC,SAASZ,KAAOU,EAGzB,sBAAsBN,EAAMD,GAC1B,IAAIO,EAAMN,EAAKS,KAAK,KAChBC,EAAO,GACX,IAAK,IAAIjD,KAAOsC,EACVA,EAAOhC,eAAeN,IACxBiD,EAAKC,KAAKlD,EAAM,IAAMsC,EAAOtC,IAMjC,OAHIiD,EAAKE,OAAS,IAChBN,GAAO,IAAMI,EAAKD,KAAK,MAElBH,EAGT,mBAAmBV,EAAMnC,EAAKN,GAC5B,IAAI0D,EAASnC,KAAKoC,aAAalB,GAE/B,OADAiB,EAAOd,OAAOtC,GAAON,EACduB,KAAKqC,eAAeF,EAAOb,KAAMa,EAAOd,QAGjD,sBAAsBH,EAAMnC,GAC1B,IAAIoD,EAASnC,KAAKoC,aAAalB,GAE/B,cADOiB,EAAOd,OAAOtC,GACdiB,KAAKqC,eAAeF,EAAOb,KAAMa,EAAOd,QAGjD,uBAAuBH,EAAMoB,GAC3B,IAAIH,EAASnC,KAAKoC,aAAalB,GAE/B,OADAiB,EAAOb,KAAK,GAAKgB,EACVtC,KAAKqC,eAAeF,EAAOb,KAAMa,EAAOd,QAGjD,mBAAmBH,EAAMqB,GACvB,IAAIJ,EAASnC,KAAKoC,aAAalB,GAI/B,OAHAiB,EAAOb,KAAK,GAAKiB,SAEVJ,EAAOd,OAAOmB,KACdxC,KAAKqC,eAAeF,EAAOb,KAAMa,EAAOd,SCzDnD,MAAMoB,EAAWC,yBAAe,CAC9BF,KAAM,CAAF,2CAKJG,eAAgB,CAAF,+DAKdC,cAAe,CAAF,sEAKb,uDAKAC,eAAgB,CAAF,+DAKdC,WAAY,CAAF,kDAKVC,KAAM,CAAF,2CAKJC,OAAQ,CAAF,+CAKNC,aAAc,CAAF,qDAKZC,qBAAsB,CAAF,+FAKpBC,QAAS,CAAF,iDAMPC,MAAO,CAAF,6CAKLC,oBAAqB,CAAF,6FAKnBC,cAAe,CAAF,sDAKbC,QAAS,CAAF,yDAOT,MAAMC,UAAoBhE,IAAMiE,UAC9BC,YAAYzD,GACV0D,MAAM1D,GAEN,MAAM,cAAC2D,GAAiB3D,EAAM4D,KAE9B7D,KAAK8D,gBAAkB9D,KAAK8D,gBAAgB9E,KAAKgB,MACjDA,KAAK+D,gBAAkB/D,KAAK+D,gBAAgB/E,KAAKgB,MACjDA,KAAKgE,YAAchE,KAAKgE,YAAYhF,KAAKgB,MAGzCA,KAAKiE,UAAY,CACf,WAAc,CACZxD,GAAI,aACJL,MAAOwD,EAAcnB,EAASD,MAC9B0B,QAAS,MAEX,eAAkB,CAChBzD,GAAI,iBACJL,MAAOwD,EAAcnB,EAASE,gBAC9BuB,QAAS,CAAC7C,EAAQ8C,IACTlE,EAAMmE,YACXR,EAAc,CAACnD,GAAI,6BACnBmD,EAAc,CAACnD,GAAI,2BAClB,KAAQT,KAAKqE,gBAAe,GAAM,EAAOhD,EAAQ8C,IAClD,MACA,EACA,OAIN,oBAAuB,CACrB1D,GAAI,sBACJL,MAAOwD,EAAcnB,EAASG,eAC9BsB,QAAS,CAAC7C,EAAQ8C,IACTlE,EAAMmE,YACXR,EAAc,CAACnD,GAAI,qCACnBmD,EAAc,CAACnD,GAAI,4BAClB,IAAeT,KAAKqE,gBAAe,GAAM,EAAMhD,EAAQ8C,GACxD,MACA,EACA,OAIN,eAAkB,CAChB1D,GAAI,iBACJL,MAAOwD,EAAcnB,EAAS6B,QAC9BJ,QAAS,CAAC7C,EAAQ8C,IACTnE,KAAKqE,gBAAe,GAAO,EAAOhD,EAAQ8C,IAGrD,oBAAuB,CACrB1D,GAAI,sBACJL,MAAOwD,EAAcnB,EAASI,gBAC9BqB,QAAS,CAAC7C,EAAQ8C,IACTnE,KAAKqE,gBAAe,GAAO,EAAMhD,EAAQ8C,IAGpD,qBAAwB,CACtB1D,GAAI,uBACJL,MAAOwD,EAAcnB,EAASK,YAC9BoB,QAAS,CAAC7C,EAAQ8C,IACTnE,KAAKuE,aAAalD,EAAQ8C,IAGrC,aAAgB,CACd1D,GAAI,eACJL,MAAOwD,EAAcnB,EAASO,QAC9BkB,QAASlE,KAAKwE,sBAAsBxF,KAAKgB,KAAM,OAEjD,WAAc,CACZS,GAAI,aACJL,MAAOwD,EAAcnB,EAASM,MAC9BmB,QAASlE,KAAKwE,sBAAsBxF,KAAKgB,KAAM,OAEjD,cAAiB,CACfS,GAAI,gBACJL,MAAOwD,EAAcnB,EAASU,SAC9Be,QAASlE,KAAKwE,sBAAsBxF,KAAKgB,KAAM,QAEjD,YAAe,CACbS,GAAI,cACJL,MAAOwD,EAAcnB,EAASW,OAC9Bc,QAAS,CAAC7C,EAAQ8C,IACTlE,EAAMmE,YACXR,EAAc,CAACnD,GAAI,oBACnBmD,EAAcnB,EAASY,qBACtB,IACQrD,KAAKwE,sBAAsB,MAAOnD,EAAQ8C,GAAcM,KAAMC,IACnE1E,KAAKC,MAAM0E,eAAetD,EAAOuD,WAC1BF,IAGX,MACA,EACA,OAIN,aAAgB,CACdjE,GAAI,eACJL,MAAOwD,EAAcnB,EAASQ,cAC9BiB,QAAS,CAAC7C,EAAQ8C,IACTlE,EAAMmE,YACXR,EAAc,CAACnD,GAAI,2BACnBmD,EAAcnB,EAASS,sBACtB,KACC,MAAMX,EAAQvC,KAAKC,MAAM4E,OAAOC,SAASzD,EAAOuD,WAChD,GAAKrC,EAIL,OAAOA,EAAMwC,UAAS,GAAMC,MAAOC,IAC7Bd,GACFA,EAAac,EAAIC,QAAS,SAL5BC,QAAQC,IAAI,oBAAqB/D,EAAOuD,YAS5C,MACA,EACA,OAIN,cAAiB,CACfnE,GAAI,gBACJL,MAAOwD,EAAcnB,EAASc,SAC9BW,QAAS,CAAC7C,EAAQ8C,KAChB,MAAM5B,EAAQvC,KAAKC,MAAM4E,OAAOC,SAASzD,EAAOuD,WAChD,GAAKrC,EAIL,OAAOA,EAAMgB,SAAQ,GAAMyB,MAAOC,IAC5Bd,GACFA,EAAac,EAAIC,QAAS,SAL5BC,QAAQC,IAAI,oBAAqB/D,EAAOuD,aAW9C,YAAe,CACbnE,GAAI,cACJL,MAAOwD,EAAc,CAACnD,GAAI,+BAC1ByD,QAAS,MAEX,cAAiB,CACfzD,GAAI,gBACJL,MAAOwD,EAAcnB,EAASa,eAC9BY,QAAS,CAAC7C,EAAQ8C,KAChB,MAAM5B,EAAQvC,KAAKC,MAAM4E,OAAOC,SAASzD,EAAOuD,WAChD,GAAKrC,GAAUlB,EAAOgE,KAItB,OAAO9C,EAAM+C,gBAAgBjE,EAAOgE,MAAML,MAAOC,IAC3Cd,GACFA,EAAac,EAAIC,QAAS,SAL5BC,QAAQC,IAAI,6BAA+B/D,EAAOuD,UAAY,OAASvD,EAAOgE,KAAO,OAU3F,YAAe,CACb5E,GAAI,cACJL,MAAOwD,EAAcnB,EAASM,MAC9BmB,QAASlE,KAAKwE,sBAAsBxF,KAAKgB,KAAM,OAEjD,cAAiB,CACfS,GAAI,gBACJL,MAAOwD,EAAcnB,EAASO,QAC9BkB,QAASlE,KAAKwE,sBAAsBxF,KAAKgB,KAAM,OAEjD,aAAgB,CACdS,GAAI,eACJL,MAAOwD,EAAcnB,EAASW,OAC9Bc,QAASlE,KAAKwE,sBAAsBxF,KAAKgB,KAAM,QAEjD,eAAkB,CAChBS,GAAI,iBACJL,MAAOwD,EAAcnB,EAASU,SAC9Be,QAASlE,KAAKwE,sBAAsBxF,KAAKgB,KAAM,SAKrDuF,oBACEC,SAASC,iBAAiB,YAAazF,KAAK8D,iBAAiB,GAC7D0B,SAASC,iBAAiB,QAASzF,KAAK+D,iBAAiB,GAG3D2B,uBACEF,SAASG,oBAAoB,YAAa3F,KAAK8D,iBAAiB,GAChE0B,SAASG,oBAAoB,QAAS3F,KAAK+D,iBAAiB,GAG9DD,gBAAgB8B,GACVjG,IAASkG,YAAY7F,MAAM8F,SAASF,EAAEG,UAG1CH,EAAEI,iBACFJ,EAAEK,kBACFjG,KAAKC,MAAMiG,QAGbnC,gBAAgB6B,GACI,KAAdA,EAAEO,SACJnG,KAAKC,MAAMiG,OAIflC,YAAY4B,GACVA,EAAEI,iBACFJ,EAAEK,kBACFjG,KAAKC,MAAMiG,OACX,IAAIxE,EAAO1B,KAAKC,MAAMmG,MAAMR,EAAES,cAAcC,QAAQ7F,IACjC,iBAARiB,IACTA,EAAO1B,KAAKiE,UAAUvC,IAGnBA,EAGH1B,KAAKC,MAAMsG,SACT7E,EAAKjB,GACLiB,EAAKwC,QAAQlE,KAAKC,MAAMoB,OAAQrB,KAAKC,MAAMuG,SAC3CxG,KAAKC,MAAMoB,QALb8D,QAAQC,IAAI,uBAAwBQ,EAAES,cAAcC,QAAQ7F,IAWhE4D,eAAeoC,EAAKC,EAAMrF,EAAQ8C,GAChC,MAAM5B,EAAQvC,KAAKC,MAAM4E,OAAOC,SAASzD,EAAOuD,WAChD,IAAKrC,EAEH,YADA4C,QAAQC,IAAI,oBAAqB/D,EAAOuD,WAQ1C,IAAK6B,GAAOlE,EAAMoE,WAAWtF,EAAOuF,KAClC,OAOF,OAJgBH,EACdlE,EAAMsE,eAAeH,GACrBnE,EAAMuE,gBAAgB,CAACzF,EAAOuF,KAAMF,IAEvB1B,MAAOC,IAChBd,GACFA,EAAac,EAAIC,QAAS,SAMhCX,aAAalD,EAAQ8C,GACnB,MAAM5B,EAAQvC,KAAKC,MAAM4E,OAAOC,SAASzD,EAAOuD,WAEhD,IAAKrC,IAAUA,EAAMwE,aAAa1F,EAAOuF,KACvC,OAEF,MAAMI,EAAMzE,EAAM0E,cAAc5F,EAAOhB,SAAS,GAChD,OAAOkC,EAAM2E,aAAaF,GAAKhC,MAAOC,IAChCd,GACFA,EAAac,EAAIC,QAAS,SAMhCV,sBAAsB7F,EAAM0C,EAAQ8C,GAClC,MAAM5B,EAAQvC,KAAKC,MAAM4E,OAAOC,SAASzD,EAAOuD,WAChD,IAAKrC,EAEH,YADA4C,QAAQC,IAAI,kBAAmB/D,EAAOuD,WAIxC,IAAIuC,EAAS5E,EAAM6E,WAAW/F,EAAOgE,KAAM1G,GAM3C,OALIwF,IACFgD,EAASA,EAAOnC,MAAOC,IACrBd,EAAac,EAAIC,QAAS,UAGvBiC,EAGTpH,SACE,IAAIsH,EAAQ,EACRC,EAAO,GACXtH,KAAKC,MAAMmG,MAAMmB,IAAK7F,IACD,iBAARA,IACTA,EAAO1B,KAAKiE,UAAUvC,IAEpBA,GAAQA,EAAKtB,OACfkH,EAAKrF,KACW,KAAdP,EAAKtB,MACH,wBAAID,UAAU,YAAYpB,IAAKsI,IAE/B,wBAAI9G,QAASP,KAAKgE,YAAawD,UAASH,EAAOtI,IAAKsI,GAAQ3F,EAAKtB,QAGvEiH,MAIF,MACMI,EFpWc,IEoWM,GAAoB,IAAdH,EAAKpF,QAQ/BwF,EAAW,CACfC,MARY3H,KAAKC,MAAM2H,OAAOC,MAAQ7H,KAAKC,MAAM6H,QAAQC,EAF7C,IAGT/H,KAAKC,MAAM6H,QAAQC,EAAI/H,KAAKC,MAAM2H,OAAOD,KAHhC,IAIT3H,KAAKC,MAAM6H,QAAQC,EAAI/H,KAAKC,MAAM2H,OAAOD,MAM/B,KACbK,KANWhI,KAAKC,MAAM2H,OAAOK,OAASjI,KAAKC,MAAM6H,QAAQI,EAAIT,EAC1DzH,KAAKC,MAAM6H,QAAQI,EAAIlI,KAAKC,MAAM2H,OAAOI,IAAMP,EAC/CzH,KAAKC,MAAM6H,QAAQI,EAAIlI,KAAKC,MAAM2H,OAAOI,KAIjC,MAGb,OACE,wBAAI7H,UAAU,OAAOgI,MAAOT,GACzBJ,IAMMc,2BAAW5E,GCjanB,SAAS6E,EAAgB5D,EAAM6D,GACpCA,EAASA,GAAUzG,OAAO0G,UAAUC,cAAgB3G,OAAO0G,UAAUE,SACrE,MAAMC,EAAM,IAAIC,KAChB,OAAIlE,EAAKmE,eAAiBF,EAAIE,cACxBnE,EAAKoE,YAAcH,EAAIG,YAAcpE,EAAKqE,WAAaJ,EAAII,UACvDrE,EAAKsE,mBAAmBT,EAAQ,CAACU,QAAQ,EAAOC,KAAM,UAAWC,OAAQ,YAEzEzE,EAAK0E,mBAAmBb,EAC5B,CAACU,QAAQ,EAAOI,MAAO,QAASC,IAAK,UAAWJ,KAAM,UAAWC,OAAQ,YAGxEzE,EAAK0E,mBAAmBb,EAC7B,CAACU,QAAQ,EAAOM,KAAM,UAAWF,MAAO,QAASC,IAAK,UAAWJ,KAAM,UAAWC,OAAQ,YAYvF,SAASK,EAAiBC,GAC/B,IAAKA,GAAkB,GAATA,EACZ,MAAO,UAGT,MAAMC,EAAQ,CAAC,QAAS,KAAM,KAAM,KAAM,KAAM,MAC1CC,EAASC,KAAKC,IAAwC,EAApCD,KAAKE,MAAMF,KAAKG,KAAKN,GAAS,IAASC,EAAMvH,OAAO,GACtEmF,EAAQmC,EAAQG,KAAKI,IAAI,KAAML,GAC/BM,EAAQN,EAAS,EAAKrC,EAAQ,EAAI,EAAKA,EAAQ,GAAK,EAAI,EAAM,EACpE,OAAOA,EAAM4C,QAAQD,GAAS,IAAMP,EAAMC,GC5B7B,MAAMQ,UAAmB1K,IAAMM,cAC5CC,SACE,IAAIoK,EACJ,IAA0B,IAAtBnK,KAAKC,MAAMkK,OAAiB,CAC9B,MAAMC,EAAiD,OAAtC1K,IAAO2K,UAAUrK,KAAKC,MAAMsC,OACvC+H,GAAaF,EAAU,cAAgB,cACxCT,KAAKY,ID2BT,SAAoB9L,GACzB,IAAIyC,EAAO,EACXzC,EAAQ,GAAKA,EACb,IAAK,IAAIjB,EAAI,EAAGA,EAAIiB,EAAMyD,OAAQ1E,IAChC0D,GAASA,GAAQ,GAAKA,EAAQzC,EAAM+L,WAAWhN,GAC/C0D,GAAcA,EAEhB,OAAOA,EClCWuJ,CAAWzK,KAAKC,MAAMsC,QAAU,GAC9C,GAAIvC,KAAKC,MAAMsC,OAASvC,KAAKC,MAAMG,OAASJ,KAAKC,MAAMG,MAAMsK,OAAQ,CACnE,MAAMC,EAAS3K,KAAKC,MAAMG,MAAMsK,OAAOE,OAAO,GACxCzK,EAAY,cAAgBmK,EAClCH,EAAU,yBAAKhK,UAAWA,GAAW,6BAAMwK,QACtC,CACL,MAAMxK,EAAY,kBAAoBmK,EACtCH,EAASC,EACP,uBAAGjK,UAAWA,GAAd,SAAqC,uBAAGA,UAAWA,GAAd,gBAIzCgK,EAFSnK,KAAKC,MAAMkK,OAEX,yBAAKhK,UAAU,SAAS0K,IAAI,SAASC,IAAK9K,KAAKC,MAAMkK,OAC5D3D,QAAUZ,IAAKA,EAAEG,OAAOgF,QAAU,KAAMnF,EAAEG,OAAO+E,IAAI,2BAE9C,KAEX,OAAOX,GCzBJ,MAAMa,EAA0B,CAAC,aAAc,YAAa,YAAa,YAAa,iBAChFC,EAA0B,CAAC,MAAc,MAAa,MAAa,MAAa,OAGtF,SAASC,EAAaC,GAC3B,OAAQA,GAASA,EAAMC,MAAQD,EAAME,KACnC,cAAgBF,EAAMC,KAAO,WAAaD,EAAME,KAAO,KAMpD,SAASC,EAAaC,EAAOC,EAAQC,EAAUC,EAAWC,GAO/D,GAJAH,GAAkB,EAClBC,GAAsB,EACtBC,GAAwB,GAHxBH,GAAgB,IAKH,GAAKC,GAAU,GAAKC,GAAY,GAAKC,GAAa,EAC7D,OAAO,KAGLC,IACFF,EAAWC,EAAY/B,KAAKC,IAAI6B,EAAUC,IAG5C,IAAIE,EAAQjC,KAAKC,IACfD,KAAKC,IAAI2B,EAAOE,GAAYF,EAC5B5B,KAAKC,IAAI4B,EAAQE,GAAaF,GAG5BK,EAAO,CACTC,SAAWP,EAAQK,EAAS,EAC5BG,UAAYP,EAASI,EAAS,GAchC,OAXID,GAEFE,EAAKC,SAAWD,EAAKE,UAAYpC,KAAKC,IAAIiC,EAAKC,SAAUD,EAAKE,WAC9DF,EAAKG,SAAWH,EAAKI,UAAYtC,KAAKC,IAAI2B,EAAOC,GACjDK,EAAKK,SAAYX,EAAQM,EAAKG,UAAY,EAAK,EAC/CH,EAAKM,SAAYX,EAASK,EAAKG,UAAY,EAAK,IAEhDH,EAAKK,QAAUL,EAAKM,QAAU,EAC9BN,EAAKG,SAAWT,EAChBM,EAAKI,UAAYT,GAEZK,EAIF,SAASO,EAAgBC,EAAOC,GACrC,IAAIC,EAAMvB,EAAwBwB,QAAQF,GACtCG,EAAMxB,EAAgBsB,GAEtBG,EAAKL,EAAMM,YAAY,KAI3B,OAHID,GAAM,IACRL,EAAQA,EAAMO,UAAU,EAAGF,IAEtBL,EAAQ,IAAMI,EAKhB,SAASI,EAAwBC,EAAMvB,EAAOC,EAAQG,EAAaoB,EAAWvG,GACnF,IAAIwG,EAAM,IAAIC,MACdD,EAAIE,YAAc,YAClBF,EAAIjC,QAAU,SAAS9F,GACrBuB,EAAQ,8BAEVwG,EAAIG,OAAS,WACX,IAAIC,EAAM9B,EAAatL,KAAKuL,MAAOvL,KAAKwL,OAAQD,EAAOC,EAAQG,GAC/D,GAAKyB,EAAL,CAIA,IAAIC,EAAS7H,SAAS8H,cAAc,UACpCD,EAAO9B,MAAQ6B,EAAItB,SACnBuB,EAAO7B,OAAS4B,EAAIrB,UACpB,IAAIwB,EAAMF,EAAOG,WAAW,MAC5BD,EAAIE,uBAAwB,EAC5BF,EAAIG,UAAU1N,KAAMoN,EAAIlB,QAASkB,EAAIjB,QAASiB,EAAIpB,SAAUoB,EAAInB,UAC9D,EAAG,EAAGmB,EAAItB,SAAUsB,EAAIrB,WAE1B,IAAIO,EAAQtM,KAAKuL,OAAS6B,EAAItB,UAC5B9L,KAAKwL,QAAU4B,EAAIrB,WACnBf,EAAwBwB,QAAQM,EAAK1B,MAAQ,EAAK,aAAe0B,EAAK1B,KACpEuC,EAAYN,EAAOO,UAAUtB,GAIjC,GADAA,EAAOuB,EAFKF,EAAUvM,MAAM,KAEH,IACzB,CAKA,IAAI0M,EAAU,IAId,GAHIC,EAAiBJ,EAAUzL,QL3CO,SK4CpCoK,EAAO,cAEG,cAARA,EAEF,KAAOyB,EAAiBJ,EAAUzL,QLhDE,QKgDsC4L,EAAU,KAClFH,EAAYN,EAAOO,UAAUtB,EAAMwB,GACnCA,GAAW,IAGXC,EAAiBJ,EAAUzL,QLrDO,OKsDpCsE,EAAQ,kBAAoB+C,iBAAiBwE,EAAiBJ,EAAUzL,SACtE,gBAAmBqH,iBLvDe,QKuDgC,UAAW,QAGjF8D,EAAS,KACTN,EAAUY,EAAUvM,MAAM,KAAK,GAAIkL,EAAMc,EAAItB,SAAUsB,EAAIrB,UAAWK,EAAgBU,EAAK/O,KAAMuO,UArB/F9F,EAAQ,iCAnBRA,EAAQ,kBA0CZwG,EAAIlC,IAAMkD,IAAIC,gBAAgBnB,GAIzB,SAASoB,EAAkBpB,EAAMC,EAAWvG,GACjD,IAAI2H,EAAS,IAAIC,WACjBD,EAAO1I,iBAAiB,QAAQ,WAC9B,IAAItE,EAAQgN,EAAOhH,OAAO/F,MAAM,KAC5BkL,EAAOuB,EAAY1M,EAAM,IAC7B,GAAKmL,EAAL,CAMA,IAAIU,EAAM,IAAIC,MACdD,EAAIE,YAAc,YAClBF,EAAIG,OAAS,WACXJ,EAAU5L,EAAM,GAAImL,EAAMtM,KAAKuL,MAAOvL,KAAKwL,OAAQY,EAAgBU,EAAK/O,KAAMuO,KAEhFU,EAAIjC,QAAU,SAAS9F,GACrBuB,EAAQ,gCAEVwG,EAAIlC,IAAMkD,IAAIC,gBAAgBnB,QAb5BtG,EAAQ,mCAcT,GACH2H,EAAOE,cAAcvB,GAGhB,SAASwB,EAAaxB,EAAMC,EAAWvG,GAC5C,IAAI2H,EAAS,IAAIC,WACjBD,EAAO1I,iBAAiB,QAAQ,WAC9BsH,EAAUD,EAAK1B,KAAM+C,EAAOhH,OAAO/F,MAAM,KAAK,GAAI0L,EAAK/O,SAEzDoQ,EAAOE,cAAcvB,GAmChB,SAASe,EAAYU,GAC1B,IAAIjC,EAAO,qCAAqCkC,KAAKD,GACrD,OAAQjC,GAAQA,EAAKpK,OAAS,EAAKoK,EAAK,GAAK,KAWxC,SAASyB,EAAiB9O,GAC/B,OAA2B,EAApB0K,KAAKE,MAAM5K,EAAI,GCnMT,MAAMwP,UAAqBjP,IAAMiE,UAC9CC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXC,QAAS1O,EAAMkK,QAGjBnK,KAAK4O,iBAAmB5O,KAAK4O,iBAAiB5P,KAAKgB,MAGrD6O,mBAAmBC,GACb9O,KAAKC,MAAMkK,QAAU2E,EAAU3E,QACjCnK,KAAK+O,SAAS,CAACJ,QAAS3O,KAAKC,MAAMkK,SAIvCyE,iBAAiBhJ,GACfiH,EAAwBjH,EAAEG,OAAOiJ,MAAM,GNoBhB,SMpB8C,EAEnE,CAACC,EAAY3C,KACX,IAAI4C,EAAKhE,EAAa,CAACG,KAAM4D,EAAY7D,KAAMkB,IAC/CtM,KAAK+O,SAAS,CAACJ,QAASO,IACxBlP,KAAKC,MAAMkP,eAAeD,IAG3BjK,IACCjF,KAAKC,MAAMuG,QAAQvB,EAAK,SAG5BW,EAAEG,OAAOtH,MAAQ,GAGnBsB,SAGE,MAAMqP,EAAS,sBAAwBzF,KAAK0F,SAAW,IAAI9N,OAAO,GAC5DpB,EAAY,iBAAmBH,KAAKC,MAAMqP,SAAW,aAAe,IAC1E,OACE,yBAAKnP,UAAWA,GACbH,KAAKC,MAAMqP,WAAatP,KAAK0O,MAAMC,QAClC,KACA,uBAAGY,KAAK,IAAIpP,UAAU,eAAeI,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMkP,eAAe,QAClG,uBAAGhP,UAAU,kBAAb,UAEHH,KAAK0O,MAAMC,QACV,yBAAK7D,IAAK9K,KAAK0O,MAAMC,QAASxO,UAAU,YACxCH,KAAKC,MAAMqP,UAAYtP,KAAKC,MAAMuP,IAChC,yBAAKrP,UAAU,cACb,kBAAC,EAAD,CACEgK,QAAQ,EACR5H,MAAOvC,KAAKC,MAAMuP,IAClBpP,MAAOJ,KAAKC,MAAMG,SAGtB,yBAAKD,UAAU,SNjBA,IMiBf,INjBe,KMkBlBH,KAAKC,MAAMqP,SAAW,KACrB,2BAAOlE,KAAK,OAAO3K,GAAI2O,EAAQjP,UAAU,mBACvCsP,OAAO,UAAUC,SAAU1P,KAAK4O,mBACnC5O,KAAKC,MAAMqP,SAAW,KACvB,2BAAOK,QAASP,EAAQjP,UAAU,SAChC,uBAAGA,UAAU,kBAAb,kBCjEK,MAAMyP,UAAiBpQ,IAAMM,cAC1C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK6P,aAAe7P,KAAK6P,aAAa7Q,KAAKgB,MAG7C6P,eACE7P,KAAKC,MAAMyP,SAAS1P,KAAKC,MAAMlC,MAAOiC,KAAKC,MAAM6P,SAGnD/P,SACE,OACEC,KAAKC,MAAMyP,SACT1P,KAAKC,MAAM6P,QACT,uBAAG3P,UAAU,gCAAgCI,QAASP,KAAK6P,cAA3D,aACA,uBAAG1P,UAAU,gCAAgCI,QAASP,KAAK6P,cAA3D,2BAEA7P,KAAKC,MAAM6P,QACT,uBAAG3P,UAAU,kBAAb,aACA,uBAAGA,UAAU,kBAAb,4BCrBZ,MAAM4P,EAAe,CAAC,MAAS,oBAAqB,OAAU,SAG/C,MAAMC,UAAsBxQ,IAAMM,cAC7CC,SACE,IAAIkQ,EAAS,KACb,OAAIjQ,KAAKC,MAAMgQ,QAAUjQ,KAAKC,MAAMgQ,OAAO/N,OAAS,GAClD+N,EAAS,GACTjQ,KAAKC,MAAMgQ,OAAO1I,KAAI,SAAS2I,GAC7B,GAAIA,EAAEC,KACJF,EAAOhO,KAAK,uBAAG9B,UAAU,0BAA0BpB,IAAKmR,EAAEnR,KAAOmR,EAAEC,MAAOJ,EAAaG,EAAEC,YACpF,CACL,MAAMhI,EAAQ,SAAW+H,EAAEE,MAAQ,IAAMF,EAAEE,MAAQ,IACnDH,EAAOhO,KAAK,0BAAM9B,UAAWgI,EAAOpJ,IAAKmR,EAAEnR,KAAOmR,EAAEnS,MAAOmS,EAAEnS,WAG1D,oCAAGkS,IAEL,MCjBE,MAAMI,UAAoB7Q,IAAMM,cAC7CC,SACE,OAAQC,KAAKC,MAAMoH,MAAQ,EACzB,0BAAMlH,UAAU,UAAUH,KAAKC,MAAMoH,MAAQ,EAAI,KAAOrH,KAAKC,MAAMoH,OACjE,MCCO,MAAMiJ,UAAgB9Q,IAAMiE,UACzCC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAKgE,YAAchE,KAAKgE,YAAYhF,KAAKgB,MACzCA,KAAKuQ,mBAAqBvQ,KAAKuQ,mBAAmBvR,KAAKgB,MAGzDgE,YAAY4B,GACVA,EAAEI,iBACFJ,EAAEK,kBACEjG,KAAKC,MAAMuQ,YACbxQ,KAAKC,MAAMuQ,WAAWxQ,KAAKC,MAAMyB,KAAM1B,KAAKC,MAAMwQ,MAAOzQ,KAAKC,MAAMyI,IAAK1I,KAAKC,MAAMyQ,KAIxFH,mBAAmB3K,GACjBA,EAAEI,iBACFJ,EAAEK,kBACFjG,KAAKC,MAAM0Q,gBAAgB,CAAE/L,UAAW5E,KAAKC,MAAMyB,KAAMwG,EAAGtC,EAAEgL,MAAO7I,EAAGnC,EAAEiL,QAG5E9Q,SACE,IAAIK,EAAQJ,KAAKC,MAAMG,MAClBA,EAEMA,EAAM8B,OAAS,KAExB9B,EAAQA,EAAMwM,UAAU,EAAG,IAAM,OAHjCxM,EAAQ,2BAAG,kBAAC,mBAAD,CAAkBK,GAAG,mBAKlC,MAAMqQ,EAAS9Q,KAAKC,MAAMyI,IAAM,SAAW,UACrCyB,GAASnK,KAAKC,MAAMkK,QAASnK,KAAKC,MAAMkK,OACxC8F,EAASjQ,KAAKC,MAAMgQ,OAASjQ,KAAKC,MAAMgQ,OAAOc,QAAU,GACzDC,EAAc,GAapB,OAZIhR,KAAKC,MAAMyQ,MACT1Q,KAAKC,MAAMgR,UACbhB,EAAOhO,KAAK,CAAClE,KAAMiC,KAAKC,MAAMyQ,IAAIQ,UAAWnS,IAAK,SAEhDiB,KAAKC,MAAMyQ,IAAIS,WACjBH,EAAY/O,KAAK,CAACkO,KAAM,UAErBnQ,KAAKC,MAAMyQ,IAAIU,YAClBJ,EAAY/O,KAAK,CAACkO,KAAM,YAK1B,wBAAIhQ,WAAYH,KAAKC,MAAMoR,eAAiBrR,KAAKC,MAAMqR,SAAW,WAAa,KAC7E/Q,QAASP,KAAKgE,aACd,yBAAK7D,UAAU,cACb,kBAAC,EAAD,CACEgK,OAAQA,EACR/J,MAAOJ,KAAKC,MAAMG,MAClBmC,MAAOvC,KAAKC,MAAMyB,OACnB1B,KAAKC,MAAMsR,WAAa,0BAAMpR,UAAW2Q,IACvC9Q,KAAKC,MAAMoR,eAAiBrR,KAAKC,MAAMqR,SACxC,uBAAGnR,UAAU,4BAAb,gBACE,MAEN,yBAAKA,UAAU,YACb,6BAAK,0BAAMA,UAAU,iBAAiBC,GACpC,kBAAC,EAAD,CAAaiH,MAAOrH,KAAKC,MAAMuR,SAAU,kBAAC,EAAD,CAAevB,OAAQe,KAEjEhR,KAAKC,MAAMwR,QAAU,yBAAKtR,UAAU,mBAAmBH,KAAKC,MAAMwR,SAAiB,KACpF,8BAAM,kBAAC,EAAD,CAAexB,OAAQA,MAE9BjQ,KAAKC,MAAM0Q,gBACV,0BAAMxQ,UAAU,eACd,uBAAGoP,KAAK,IAAIhP,QAASP,KAAKuQ,oBACxB,uBAAGpQ,UAAU,kBAAb,iBAEM,OC3EpB,MAAMuR,UAAsBlS,IAAMM,cAChC4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAKgE,YAAchE,KAAKgE,YAAYhF,KAAKgB,MAG3CgE,YAAY4B,GACVA,EAAEI,iBACFJ,EAAEK,kBACFjG,KAAKC,MAAMsG,SAASvG,KAAKC,MAAM0R,QAGjC5R,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KACnC,OACE,wBAAItD,QAASP,KAAKgE,YAAa7D,UAAU,UACvC,yBAAKA,UAAU,eAAeyD,EAAc5D,KAAKC,MAAMG,MAAOJ,KAAKC,MAAM2R,WAMlExJ,2BAAWsJ,GCjB1B,MAAMjP,EAAWC,yBAAe,CAC9BmP,UAAW,CAAF,qCAKTC,YAAa,CAAF,2CAOb,MAAMC,UAAoBvS,IAAMiE,UAC9B1D,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KAC7BwN,EAAgBW,MAAMC,QAAQjS,KAAKC,MAAMiS,eACzCC,EAAe,GACrB,IAAIC,EAAgB,EAoEpB,OAnEIpS,KAAKC,MAAMoS,UAAYrS,KAAKC,MAAMoS,SAASnQ,OAAS,GACtDlC,KAAKC,MAAMoS,SAAS9K,IAAK1J,IACvB,GAAIA,EAAE8T,OAEJQ,EAAalQ,KACX,kBAAC,EAAD,CACE7B,MAAOvC,EAAEuC,MAAOuR,OAAQ9T,EAAE8T,OAAQC,OAAQ/T,EAAE+T,OAC5C7S,IAAKlB,EAAE8T,OACPpL,SAAUvG,KAAKC,MAAMsG,gBAEpB,CAEL,MAAMxH,EAAMiB,KAAKC,MAAMgR,SAAWpT,EAAEwH,KAAQxH,EAAE0E,OAAS1E,EAAEwH,KAGzD,GAAIrF,KAAKC,MAAMqS,YAActS,KAAKC,MAAMsS,OAAQ,CAC9C,IAAIlS,EAAU,CAACtB,GAOf,GANIlB,EAAE2U,SAAW3U,EAAE2U,QAAQf,SACzBpR,EAAQ4B,MAAM,GAAKpE,EAAE2U,QAAQf,SAASgB,eAEpC5U,EAAE6U,QAAU7U,EAAE6U,OAAOC,IACvBtS,EAAQ4B,MAAM,GAAKpE,EAAE6U,OAAOC,IAAIF,gBAE7BzS,KAAKC,MAAMqS,WAAWtS,KAAKC,MAAMsS,OAAQlS,GAC5C,OAIJ,MAAMiR,EAAWD,EACdrR,KAAKC,MAAMiS,cAAc1F,QAAQzN,IAAQ,EACzCiB,KAAKC,MAAMiS,gBAAkBnT,EAC1BkR,EAAS,GACXjQ,KAAKC,MAAMgR,WACTlS,GAAOiB,KAAKC,MAAM2S,UACpB3C,EAAOhO,KAAK,CAAClE,KAAM6F,EAAcnB,EAASoP,WAAYzB,MAAO,UAE3DvS,EAAE6S,KAAO7S,EAAE6S,IAAImC,WACjB5C,EAAOhO,KAAK,CAAClE,KAAM6F,EAAcnB,EAASqP,aAAc1B,MAAO,UAGnE,MAAMqB,EAAUO,MAAMC,QAAQpU,EAAE2U,SAC9B3U,EAAE2U,QAAQzQ,KAAK,KAAQlE,EAAE2U,QAAU3U,EAAE2U,QAAQf,QAAU,KAEzDU,EAAalQ,KACX,kBAAC,EAAD,CACE7B,MAAOvC,EAAE6U,OAAS7U,EAAE6U,OAAOC,GAAK,KAChCxI,OAAQe,EAAarN,EAAE6U,OAAS7U,EAAE6U,OAAOvH,MAAQ,MACjDsG,QAASA,EACTD,OAAQxR,KAAKC,MAAM6S,WAAajV,EAAE2T,OAAS,EAC3C9I,IAAK7K,EAAEiT,QAAU9Q,KAAKC,MAAM8S,UAC5BrC,IAAK7S,EAAE6S,IACPO,SAAUjR,KAAKC,MAAMgR,SACrBhB,OAAQA,EACRoB,cAAeA,EACfC,SAAUA,EACVC,WAAYvR,KAAKC,MAAMsR,WACvBf,WAAYxQ,KAAKC,MAAM+S,gBACvBrC,gBAAiB3Q,KAAKC,MAAM0Q,gBAC5BjP,KAAM3C,EACN0R,MAAO0B,EAAajQ,OACpBnD,IAAKA,KAETqT,MAEDpS,MAIH,yBAAKG,UAAWH,KAAKC,MAAMgT,SAAW,KAAO,oBACzB,GAAjBb,EACC,yBAAKjS,UAAU,qBACb+S,wBAAyB,CAACC,OAAQnT,KAAKC,MAAMmT,oBAE/C,KAEDjB,EAAajQ,OAAS,EACrB,wBAAI/B,UAAU,eACXgS,GAGH,OAOK/J,2BAAW2J,GCjHX,MAAMsB,UAAmB7T,IAAMM,cAC5CC,SACE,OACE,uBAAGwP,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMqT,aAC1D,uBAAGnT,UAAU,kBAAb,WCHO,MAAMoT,UAAmB/T,IAAMM,cAC5C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACX8E,MAAM,GAGRxT,KAAKkG,KAAOlG,KAAKkG,KAAKlH,KAAKgB,MAG7B6O,mBAAmBC,GACbA,EAAU2E,QAAUzT,KAAKC,MAAMwT,OACjCzT,KAAK+O,SAAS,CACZyE,OAASxT,KAAKC,MAAMwT,QAK1BvN,OACElG,KAAK+O,SAAS,CAACyE,MAAM,IACjBxT,KAAKC,MAAMyT,cACb1T,KAAKC,MAAMyT,eAIf3T,SACE,MACM0T,EADQ,CAACxO,IAAK,QAAS0O,KAAM,UAAWnR,KAAM,QAChCxC,KAAKC,MAAMwT,QAAU,GACnCtT,EAAY,YAAcsT,EAChC,OACE,yBAAKtT,UAAWA,GACd,yBAAKA,UAAU,QAAO,uBAAGA,UAAU,kBAAkBsT,IACrD,8BACGzT,KAAKC,MAAM2T,KACX5T,KAAKC,MAAM0R,OACV,oCACO,uBAAGpC,KAAK,IACXpH,MAAO,CAAE0L,WAAY,UACrBtT,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAM0R,WAC/C3R,KAAKC,MAAM6T,aAGhB,MAEJ,yBAAK3T,UAAU,UAAS,kBAAC,EAAD,CAAYmT,SAAUtT,KAAKkG,UC7C5C,MAAM6N,UAAavU,IAAMM,cACtC4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAKgU,aAAehU,KAAKgU,aAAahV,KAAKgB,MAG7CgU,aAAapO,GACXA,EAAEI,iBACFhG,KAAKC,MAAMqT,SAAStT,KAAKC,MAAMsC,MAAOvC,KAAKC,MAAMwQ,OAGnD1Q,SACE,MAAMK,EAAQJ,KAAKC,MAAMG,OAASJ,KAAKC,MAAMsC,MACvCpC,EAAYH,KAAKC,MAAMgU,QAAU,eAAiB,OACxD,OACE,yBAAK9T,UAAWA,GACbH,KAAKC,MAAMiU,SACV,0BAAM/T,UAAU,WAChB,yBAAKA,UAAU,cACb,kBAAC,EAAD,CACEgK,OAAQnK,KAAKC,MAAMkK,SAAU,EAC7B5H,MAAOvC,KAAKC,MAAMsC,MAClBnC,MAAOJ,KAAKC,MAAMG,SAGxB,8BAAOA,GACNJ,KAAKC,MAAMqT,WAAatT,KAAKC,MAAMkU,SAClC,uBAAG5E,KAAK,IAAIhP,QAASP,KAAKgU,cAA1B,KACE,0BAAM7T,UAAU,aC1Bb,MAAMiU,UAAkB5U,IAAMiE,UAC3CC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ0F,EAAUC,qBAAqBpU,GAC5CD,KAAK0O,MAAM4F,MAAQ,GACnBtU,KAAK0O,MAAM6F,SAAU,EAErBvU,KAAKwU,gBAAkBxU,KAAKwU,gBAAgBxV,KAAKgB,MACjDA,KAAKyU,aAAezU,KAAKyU,aAAazV,KAAKgB,MAC3CA,KAAK0U,iBAAmB1U,KAAK0U,iBAAiB1V,KAAKgB,MACnDA,KAAK2U,kBAAoB3U,KAAK2U,kBAAkB3V,KAAKgB,MACrDA,KAAK4U,gBAAkB5U,KAAK4U,gBAAgB5V,KAAKgB,MACjDA,KAAK6U,cAAgB7U,KAAK6U,cAAc7V,KAAKgB,MAG/C,4BAA4BC,GAC1B,MAAO,CACL6U,YAAa7U,EAAM8U,MAAQ,GAAK9U,EAAM+U,OACtCC,YAAab,EAAUc,UAAUjV,EAAM8U,MAAO9U,EAAMkV,eACpDC,UAAWhB,EAAUiB,WAAWpV,EAAM8U,QAI1ClG,mBAAmBC,EAAWwG,GACxBxG,EAAUiG,OAAS/U,KAAKC,MAAM8U,OAChCjG,EAAUqG,eAAiBnV,KAAKC,MAAMkV,eACtCrG,EAAUkG,QAAUhV,KAAKC,MAAM+U,QAC/BhV,KAAK+O,SAASqF,EAAUC,qBAAqBrU,KAAKC,UAE/CqV,GAAatV,KAAKC,MAAM8U,MAAM7S,OAASoT,EAAUL,YAAY/S,SAChElC,KAAK+O,SAAS,CAACuF,MAAO,KAK1B,kBAAkBS,GAChB,MAAMtE,EAAQ,GACd,IAAIpJ,EAAQ,EAKZ,OAJA0N,EAAMxN,IAAK7F,IACT+O,EAAM/O,EAAK2D,MAAQgC,EACnBA,MAEKoJ,EAIT,iBAAiBsE,EAAOQ,GACtB,MAAMpB,EAAW,GACXqB,EAAS,GAQf,OAPAT,EAAMxN,IAAK7F,IACL6T,GAAQA,EAAKE,SAAS/T,EAAK2D,MAC7B8O,EAASlS,KAAKP,GAEd8T,EAAOvT,KAAKP,KAGTyS,EAASuB,OAAOF,GAGzBhB,gBAAgB5O,GACd5F,KAAK+O,SAAS,CAACuF,MAAO1O,EAAEG,OAAOtH,QAC3BuB,KAAKC,MAAMqS,YACbtS,KAAKC,MAAMqS,WAAW1M,EAAEG,OAAOtH,OAInCgW,aAAalI,GACX,MAAMoJ,EAAU3V,KAAK0O,MAAMuG,YAAY1I,GACvCvM,KAAKC,MAAM2V,cAAcD,EAAQtQ,KAAMrF,KAAK0O,MAAM0G,UAAUO,EAAQtQ,OAGtEqP,iBAAiBhT,EAAM6K,GACrBvM,KAAKyU,aAAalI,GAGpBoI,oBACE3U,KAAK+O,SAAS,CAACwF,SAAS,IAG1BK,kBACE5U,KAAK+O,SAAS,CAACwF,SAAS,IACpBvU,KAAKC,MAAM4V,aACb7V,KAAKC,MAAM4V,YAAY7V,KAAK0O,MAAM4F,OAItCO,cAAcjP,GACZ,GAAc,cAAVA,EAAE7G,KACJ,GAA+B,GAA3BiB,KAAK0O,MAAM4F,MAAMpS,QAAelC,KAAK0O,MAAMuG,YAAY/S,OAAS,EAAG,CACrE,MAAMwK,EAAK1M,KAAK0O,MAAMuG,YAAY/S,OAAS,EACvClC,KAAK0O,MAAMuG,YAAYvI,GAAIrH,OAASrF,KAAKC,MAAMkV,eACjDnV,KAAKyU,aAAa/H,QAGH,UAAV9G,EAAE7G,IACPiB,KAAKC,MAAM6V,SACb9V,KAAKC,MAAM6V,QAAQ9V,KAAK0O,MAAM4F,OAEb,WAAV1O,EAAE7G,KACPiB,KAAKC,MAAMqT,UACbtT,KAAKC,MAAMqT,WAKjBvT,SACE,MAAMgV,EAAQ,GACd,IAAI1N,EAAQ,EACZ,MAAM8N,EAAgBnV,KAAKC,MAAMkV,eAAiB,GAClDnV,KAAK0O,MAAMuG,YAAY1N,IAAK7F,IAC1BqT,EAAM9S,KACJ,kBAAC,EAAD,CACEqR,SAAUtT,KAAK0U,iBACfvK,OAAQe,EAAaxJ,EAAKgR,OAAShR,EAAKgR,OAAOvH,MAAQ,MACvD/K,MAAOsB,EAAKgR,OAAShR,EAAKgR,OAAOC,QAAKoD,EACtC7B,SAAUlU,KAAKC,MAAM+V,eACrBzT,MAAOb,EAAK2D,KACZ8O,SAAUgB,EAAcM,SAAS/T,EAAK2D,MACtC4O,QAASvS,EAAKuS,QACdxD,MAAOpJ,EACPtI,IAAK2C,EAAK2D,QAEdgC,MAEF,MAAMlH,EAAY,cAAgBH,KAAK0O,MAAM6F,QAAU,WAAa,IACpE,OACE,yBAAKpU,UAAWA,GACb4U,EACD,2BAAO3J,KAAK,OACV0J,YAAa9U,KAAK0O,MAAMoG,YACxBpF,SAAU1P,KAAKwU,gBACfyB,QAASjW,KAAK2U,kBACduB,OAAQlW,KAAK4U,gBACbuB,UAAWnW,KAAK6U,cAChBpW,MAAOuB,KAAK0O,MAAM4F,MAClB8B,WAAS,MCxInB,MAAM3T,EAAWC,yBAAe,CAC9B2T,YAAa,CAAF,4DAKXC,yBAA0B,CAAF,gFAO1B,MAAMC,UAAqB/W,IAAMiE,UAC/BC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CAEX8H,QAASvW,EAAMuW,QACf/F,MAAO8F,EAAaE,aAAaxW,EAAMuW,SACvCrB,cAAeoB,EAAapB,cAAclV,EAAMuW,QAASvW,EAAMyW,mBAAoBzW,EAAM0W,gBACzFC,cAAe,GACfC,kBAAmB5W,EAAM4D,KAAKD,cAAcnB,EAAS4T,aACrDS,iBAAkBP,EAAaO,iBAAiB7W,EAAMuW,UAGxDxW,KAAK+W,sBAAwB/W,KAAK+W,sBAAsB/X,KAAKgB,MAC7DA,KAAKgX,oBAAsBhX,KAAKgX,oBAAoBhY,KAAKgB,MACzDA,KAAKiX,oBAAsBjX,KAAKiX,oBAAoBjY,KAAKgB,MACzDA,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAC3CA,KAAKgU,aAAehU,KAAKgU,aAAahV,KAAKgB,MAG7C,oBAAoBwW,GAClB,IAAI/F,EAAQ,GAIZ,OAHA+F,EAAQjP,IAAK3J,IACX6S,EAAM7S,EAAEyH,MAAQ,CAAC8R,MAAO,EAAGC,SAAS,KAE/B3G,EAGT,qBAAqB+F,EAASa,EAAaV,GACzC,IAAIW,EAAO,GAMX,OALAd,EAAQjP,IAAK3J,KACPyZ,GAAezZ,EAAEyH,MAAQsR,IAC3BW,EAAKrV,KAAKrE,EAAEyH,QAGTiS,EAGT,wBAAwBd,GACtB,IAAIe,EAAM,GAIV,OAHAf,EAAQjP,IAAK3J,IACX2Z,EAAItV,KAAKrE,EAAEyH,QAENkS,EAGTR,sBAAsBS,EAAQ/G,GAC5B,IAAIgH,EAASzX,KAAK0O,MAAM+B,MAAM+G,GAC9B,GAAIC,EAAQ,CACV,GAAIA,EAAOL,QAET,OAEFK,EAAON,OAAS,EAChBM,EAAOL,SAAU,OAEjBK,EAAS,CAACN,MAAO,EAAGC,SAAS,GAG/B,IAAIxZ,EAAIoC,KAAK0O,MAAM8H,QAAQzF,QAC3BnT,EAAEqE,KAAKjC,KAAKC,MAAMoS,SAAS5B,IAE3B,MAAM8G,EAAMhB,EAAaO,iBAAiBlZ,GAEpCJ,EAAIwC,KAAK0O,MAAM+B,MACrBjT,EAAEga,GAAUC,EAEZzX,KAAK+O,SAAS,CAACyH,QAAS5Y,EAAG6S,MAAOjT,EAAGsZ,iBAAkBS,IAGzDP,oBAAoBQ,EAAQ/G,GAC1B,MAAMgH,EAASzX,KAAK0O,MAAM+B,MAAM+G,GAChC,IAAKC,IAAWA,EAAOL,QACrB,OAEFK,EAAOL,SAAU,EACjBK,EAAON,OAAS,EAEhB,IAAIvZ,EAAIoC,KAAK0O,MAAM8H,QAAQzF,QAC3BnT,EAAE8Z,OAAOjH,EAAO,GAEhB,MAAM8G,EAAMhB,EAAaO,iBAAiBlZ,GAEpCJ,EAAIwC,KAAK0O,MAAM+B,MACrBjT,EAAEga,GAAUC,EAEZzX,KAAK+O,SAAS,CAACyH,QAAS5Y,EAAG6S,MAAOjT,EAAGsZ,iBAAkBS,IAGzDN,oBAAoBU,GAClB,MAAM,cAAC/T,GAAiB5D,KAAKC,MAAM4D,KAC7BmD,EAAO2Q,EAEX/T,EAAcnB,EAAS6T,yBAA0B,CAACsB,MAAOD,IADzD/T,EAAcnB,EAAS4T,aAGzBrW,KAAK+O,SAAS,CAAC6H,cAAee,EAAKd,kBAAmB7P,IAGxD,0BAA0BuL,EAAQX,GAChC,GAAIW,EAAQ,CACV,IAAK,IAAI/U,EAAE,EAAGA,EAAEoU,EAAO1P,OAAQ1E,IAC7B,GAAIoU,EAAOpU,GAAGgP,QAAQ+F,IAAW,EAC/B,OAAO,EAGX,OAAO,EAET,OAAO,EAGT2E,eACE,IAAIW,EAAW7X,KACXwW,EAAU,GACVsB,EAAQ,GACRnC,EAAU,GAEHzX,OAAO6Z,KAAK/X,KAAK0O,MAAM+B,OAC7BlJ,KAAI,SAASyQ,GACZH,EAASnJ,MAAM+B,MAAMuH,GAAGZ,SAC1BZ,EAAQvU,KAAK+V,GAGXH,EAASnJ,MAAM+B,MAAMuH,GAAGb,MAAQ,EAClCW,EAAM7V,KAAK+V,GACFH,EAASnJ,MAAM+B,MAAMuH,GAAGb,MAAQ,GACzCxB,EAAQ1T,KAAK+V,MAGjBhY,KAAKC,MAAMgY,SAASzB,EAASsB,EAAOnC,GAGtC3B,eACEhU,KAAKC,MAAMqT,WAGbvT,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KACnC,OACE,yBAAKpD,GAAG,iBACN,yBAAKN,UAAU,kBACb,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,sBAAsByX,eAAe,oBAG9D,yBAAK/X,UAAU,kBACb,kBAAC,EAAD,CACE4U,MAAO/U,KAAK0O,MAAM8H,QAClBrB,cAAenV,KAAK0O,MAAMyG,cAC1BH,OAAO,cACP1C,WAAYtS,KAAKiX,oBACjBrB,cAAe5V,KAAKgX,uBAExB,yBAAK7W,UAAU,kBACb,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,qBAAqByX,eAAe,mBAI7D,kBAAC,EAAD,CACE7F,SAAUrS,KAAKC,MAAMoS,SACrBO,SAAU5S,KAAKC,MAAM2S,SACrBV,cAAelS,KAAK0O,MAAMoI,iBAC1BvE,OAAQvS,KAAK0O,MAAMkI,cACnBtE,WAAYiE,EAAa4B,mBACzB/E,iBAAkBpT,KAAK0O,MAAMmI,kBAC7BtF,YAAY,EACZuB,YAAY,EACZE,gBAAiBhT,KAAK+W,wBACxB,yBAAKtW,GAAG,wBAAwBN,UAAU,kBACxC,4BAAQA,UAAU,OAAOI,QAASP,KAAKkX,cACrC,kBAAC,mBAAD,CAAkBzW,GAAG,YAAYyX,eAAe,QAElD,4BAAQ/X,UAAU,QAAQI,QAASP,KAAKgU,cACtC,kBAAC,mBAAD,CAAkBvT,GAAG,gBAAgByX,eAAe,eAQjD9P,2BAAWmO,GCvMX,MAAM6B,UAAwB5Y,IAAMM,cACjD4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXjQ,MAAOuB,KAAKC,MAAMxB,MAClByB,SAAS,GAGXF,KAAKqY,iBAAmBrY,KAAKqY,iBAAiBrZ,KAAKgB,MACnDA,KAAKsY,gBAAkBtY,KAAKsY,gBAAgBtZ,KAAKgB,MACjDA,KAAK6U,cAAgB7U,KAAK6U,cAAc7V,KAAKgB,MAC7CA,KAAKuY,sBAAwBvY,KAAKuY,sBAAsBvZ,KAAKgB,MAG/DsY,gBAAgB1S,GACd5F,KAAK+O,SAAS,CAACtQ,MAAOmH,EAAEG,OAAOtH,QAC3BuB,KAAKC,MAAMyP,UACb1P,KAAKC,MAAMyP,SAAS9J,GAIxByS,iBAAiBzS,GACfA,EAAEI,iBACFhG,KAAK+O,SAAS,CAAC7O,SAAUF,KAAK0O,MAAMxO,UAGtC2U,cAAcjP,GACK,IAAbA,EAAEO,SAEJnG,KAAK+O,SAAS,CAACtQ,MAAOuB,KAAKC,MAAMxB,MAAOyB,SAAS,IAC7CF,KAAKC,MAAMuY,YACbxY,KAAKC,MAAMuY,cAES,IAAb5S,EAAEO,SAEXnG,KAAKuY,wBAITA,sBAAsB3S,GACpB,GAAIA,EAAG,CACL,IAAIS,EAAgBT,EAAES,cACtBoS,WAAW,KACJpS,EAAcP,SAASN,SAASkT,gBAC/B1Y,KAAKC,MAAMuY,YACbxY,KAAKC,MAAMuY,WAAWxY,KAAK0O,MAAMjQ,QAGpC,QACMuB,KAAKC,MAAMuY,YACpBxY,KAAKC,MAAMuY,WAAWxY,KAAK0O,MAAMjQ,MAAMiM,QAI3C3K,SACE,OACE,yBAAK4Y,SAAS,KAAKxY,UAAU,cAC3B+V,OAAQlW,KAAKuY,uBACb,2BAAOpY,UAAU,kBACfiL,KAAMpL,KAAK0O,MAAMxO,QAAU,OAAS,WACpCzB,MAAOuB,KAAK0O,MAAMjQ,MAClBqW,YAAa9U,KAAKC,MAAM6U,YACxBX,SAAUnU,KAAKC,MAAMkU,SAAW,WAAa,GAC7CiC,UAAWpW,KAAKC,MAAMmW,UAAY,YAAc,GAChDwC,aAAc5Y,KAAKC,MAAM2Y,aACzBlJ,SAAU1P,KAAKsY,gBACfnC,UAAWnW,KAAK6U,gBAClB,0BAAMtU,QAASP,KAAKqY,kBAClB,uBAAGlY,UAAU,uCACVH,KAAK0O,MAAMxO,QAAU,aAAe,qBCnElC,MAAM2Y,UAAoBrZ,IAAMiE,UAC7CC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXoK,OAAQ7Y,EAAM6Y,OACdC,aAAc9Y,EAAMxB,OAAS,GAC7BA,MAAOwB,EAAMxB,OAAS,IAGxBuB,KAAKsY,gBAAkBtY,KAAKsY,gBAAgBtZ,KAAKgB,MACjDA,KAAK6U,cAAgB7U,KAAK6U,cAAc7V,KAAKgB,MAC7CA,KAAKgZ,mBAAqBhZ,KAAKgZ,mBAAmBha,KAAKgB,MACvDA,KAAKuY,sBAAwBvY,KAAKuY,sBAAsBvZ,KAAKgB,MAC7DA,KAAKiZ,uBAAyBjZ,KAAKiZ,uBAAuBja,KAAKgB,MAGjE6O,mBAAmBC,EAAWwG,GAG5B,MAAM4D,EAAWlZ,KAAKC,MAAMxB,OAAS,GACjC6W,EAAUyD,cAAgBG,GAAa5D,EAAUwD,QACnD9Y,KAAK+O,SAAS,CACZgK,aAAcG,EACdza,MAAOya,IAKbZ,gBAAgB1S,GACd5F,KAAK+O,SAAS,CAACtQ,MAAOmH,EAAEG,OAAOtH,QAGjCoW,cAAcjP,GACM,KAAdA,EAAEO,QAEJnG,KAAK+O,SAAS,CAACtQ,MAAOuB,KAAKC,MAAMxB,MAAOqa,QAAQ,IACzB,KAAdlT,EAAEO,SAEXnG,KAAKuY,sBAAsB3S,GAI/BoT,qBACOhZ,KAAKC,MAAMqP,WACd3P,IAASkG,YAAY7F,MAAMmZ,QAC3BnZ,KAAK+O,SAAS,CAAC+J,QAAQ,KAI3BP,sBAAsBa,GACpB,GAAIpZ,KAAKC,MAAMkU,WAAaiF,EAAMrT,OAAOsT,gBAGvC,YADArZ,KAAK+O,SAAS,CAACtQ,MAAOuB,KAAKC,MAAMxB,MAAOqa,QAAQ,IAGlD9Y,KAAK+O,SAAS,CAAC+J,QAAQ,IACvB,IAAIra,EAAQuB,KAAK0O,MAAMjQ,MAAMiM,QACxBjM,GAASuB,KAAKC,MAAMxB,QAAWA,IAAUuB,KAAKC,MAAMxB,OACvDuB,KAAKC,MAAMuY,WAAW/Z,GAI1Bwa,uBAAuBxa,GACrBuB,KAAK+O,SAAS,CAAC+J,QAAQ,IACnBra,GAAUA,IAAUuB,KAAKC,MAAMxB,OACjCuB,KAAKC,MAAMuY,WAAW/Z,GAI1BsB,SACE,GAAIC,KAAK0O,MAAMoK,OACb,IAAIQ,EAAYtZ,KAAKC,MAAMmL,MAAQ,WAC9B,CACL,IAAImO,EAA8B,YAAnBvZ,KAAKC,MAAMmL,KAAqB,WAAapL,KAAK0O,MAAMjQ,MACnE+a,EAAY,iBACbxZ,KAAKC,MAAMqP,SAAW,YAAc,IAClCiK,IACHA,EAAWvZ,KAAKC,MAAM6U,YACtB0E,GAAa,gBAEXD,EAASrX,OAAS,KAEpBqX,EAAWA,EAAS3M,UAAU,EAAG,IAAM,OAG3C,OACE5M,KAAK0O,MAAMoK,OACK,YAAbQ,EACC,kBAAC,EAAD,CACE7a,MAAOuB,KAAK0O,MAAMjQ,MAClBqW,YAAa9U,KAAKC,MAAM6U,YACxBX,SAAUnU,KAAKC,MAAMkU,SAAW,WAAa,GAC7CyE,aAAc5Y,KAAKC,MAAM2Y,aACzBxC,WAAW,EACXoC,WAAYxY,KAAKiZ,yBAEnB,2BAAO7N,KAAMkO,EACX7a,MAAOuB,KAAK0O,MAAMjQ,MAClBqW,YAAa9U,KAAKC,MAAM6U,YACxBX,SAAUnU,KAAKC,MAAMkU,SAAW,WAAa,GAC7CyE,aAAc5Y,KAAKC,MAAM2Y,aACzBxC,WAAS,EACT1G,SAAU1P,KAAKsY,gBACfnC,UAAWnW,KAAK6U,cAChBqB,OAAQlW,KAAKuY,wBAGjB,0BAAMpY,UAAWqZ,EAAWjZ,QAASP,KAAKgZ,oBACxC,0BAAM7Y,UAAU,WAAWoZ,KC/GtB,MAAME,UAAmBja,IAAMM,cAC5C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXgL,KAAMzZ,EAAMyZ,MAEd1Z,KAAK2Z,aAAe3Z,KAAK2Z,aAAa3a,KAAKgB,MAG7C2Z,eACE,MAAMD,GAAQ1Z,KAAK0O,MAAMgL,KACzB1Z,KAAK+O,SAAS,CAAC2K,KAAMA,IACjB1Z,KAAKC,MAAM2Z,UACb5Z,KAAKC,MAAM2Z,SAASF,GAIxB3Z,SACE,OAAQ,2BAAOI,UAAU,wBAAwBI,QAASP,KAAK2Z,cAAe3Z,KAAKC,MAAMG,MAAjF,MACLJ,KAAK0O,MAAMgL,KAAO,uBAAGvZ,UAAU,kBAAb,eACjB,uBAAGA,UAAU,kBAAb,mBCdR,MAAMsC,GAAWC,yBAAe,CAC9B,4DAKA,4DAKA,8DAKA,oEAKA,kEAKA,8DAKA,iEAKA,+DAOF,MAAMmX,WAA0Bra,IAAMiE,UACpCC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACX/P,MAAOsB,EAAMtB,MAAQ,IAAImb,QAAQ,IAAK,KAGxC9Z,KAAK6P,aAAe7P,KAAK6P,aAAa7Q,KAAKgB,MAC3CA,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAC3CA,KAAKgU,aAAehU,KAAKgU,aAAahV,KAAKgB,MAG7C6P,aAAa8H,GACX,IAAIhZ,EAAOqB,KAAK0O,MAAM/P,MAEV,GADFA,EAAK6N,QAAQmL,GAErBhZ,GAAQgZ,EAERhZ,EAAOA,EAAKmb,QAAQnC,EAAK,IAE3B3X,KAAK+O,SAAS,CAACpQ,KAAMA,IAGvBuY,eAEE,IAAIvY,GAAQqB,KAAK0O,MAAM/P,MAAQ,KAAKyC,MAAM,IAAI2Y,OAAOhY,KAAK,IAEtDpD,KADUqB,KAAKC,MAAMtB,MAAQ,KAAKyC,MAAM,IAAI2Y,OAAOhY,KAAK,IAE1D/B,KAAKC,MAAMgY,SAAStZ,GAEpBqB,KAAKC,MAAMqT,WAIfU,eACEhU,KAAKC,MAAMqT,WAGbvT,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KAE7BmW,EAAQ,CACZ,EAAKpW,EAAcnB,GAASwX,OAAQ,CAACtC,IAAK,MAC1C,EAAK/T,EAAcnB,GAAS0L,OAAQ,CAACwJ,IAAK,MAC1C,EAAK/T,EAAcnB,GAASyX,OAAQ,CAACvC,IAAK,MAC1C,EAAK/T,EAAcnB,GAAS0X,OAAQ,CAACxC,IAAK,MAC1C,EAAK/T,EAAcnB,GAAS2X,SAAU,CAACzC,IAAK,MAC5C,EAAK/T,EAAcnB,GAAS4X,OAAQ,CAAC1C,IAAK,MAC1C,EAAK/T,EAAcnB,GAAS6X,QAAS,CAAC3C,IAAK,MAC3C,EAAK/T,EAAcnB,GAAS8X,MAAO,CAAC5C,IAAK,OAG3C,IAAI6C,EAAOxa,KAAKC,MAAMua,MAAQ,GAC1B7b,EAAOqB,KAAK0O,MAAM/P,KAClB8b,GAAWza,KAAKC,MAAMwa,SAAW,IAAIX,QAAQ,IAAK,IAClD1T,EAAQ,GACZ,IAAK,IAAI5I,EAAE,EAAGA,EAhBF,WAgBQ0E,OAAQ1E,IAAK,CAC/B,IAAIK,EAjBM,WAiBE+M,OAAOpN,GACfgd,EAAKhO,QAAQ3O,IAAM,GAAKc,EAAK6N,QAAQ3O,GAAK,GAI9CuI,EAAMnE,KACJ,wBAAIlD,IAAKlB,GACP,4BAAKmc,EAAMnc,IACX,wBAAIsC,UAAU,YAAYqa,EAAKhO,QAAQ3O,GAAK,EAC1C,kBAAC,EAAD,CAAUE,KAAMF,EAAGiS,QAAUnR,EAAK6N,QAAQ3O,IAAM,EAAI6R,SAAU1P,KAAK6P,eAEnE,kBAAC,EAAD,CAAU9R,KAAMF,EAAGiS,QAAUnR,EAAK6N,QAAQ3O,IAAM,KAC3CmC,KAAKC,MAAMwa,QAAU,wBAAIta,UAAU,YACxC,kBAAC,EAAD,CAAUpC,KAAMF,EAAGiS,QAAU2K,EAAQjO,QAAQ3O,IAAM,KAC7C,OAKd,OACE,yBAAKsC,UAAU,qBACZH,KAAKC,MAAMya,UACV,wBAAIva,UAAU,eAAc,kBAAC,EAAD,CAC1BuB,KAAM1B,KAAKC,MAAMyB,KACjBtB,MAAOJ,KAAKC,MAAMya,UAClBvQ,OAAQe,EAAalL,KAAKC,MAAM0a,WAAa3a,KAAKC,MAAM0a,WAAa,SAAiB,KAC1F,2BAAOxa,UAAU,SAAQ,kBAAC,mBAAD,CAAkBM,GAAG,oBAC5CyX,eAAe,iBACjB,2BAAO/X,UAAU,qBAChBH,KAAKC,MAAMwa,QACV,+BAAO,4BACL,6BAAS,4BAAKza,KAAKC,MAAM2a,WACzB,4BAAK5a,KAAKC,MAAM4a,gBAElB,KACF,+BACGzU,IAEH,6BACA,yBAAKjG,UAAU,kBACb,4BAAQA,UAAU,UAAUI,QAASP,KAAKgU,cACxC,kBAAC,mBAAD,CAAkBvT,GAAG,mBAEvB,4BAAQN,UAAU,OAAOI,QAASP,KAAKkX,cACrC,kBAAC,mBAAD,CAAkBzW,GAAG,kBAQlB2H,4BAAWyR,ICjKnB,SAASiB,GAAczT,GAC5B,MAAM0T,EAAUvV,SAASwV,eAAe,iBAClCC,EAAOzV,SAASyV,MAAQzV,SAAS0V,qBAAqB,QAAQ,GAC9DC,EAAU3V,SAAS8H,cAAc,QACvC6N,EAAQ/P,KAAO,YACf+P,EAAQ1a,GAAK,gBACb0a,EAAQC,IAAM,gBACdD,EAAQ5L,KAAO,iBAAmBlI,EAAQ,EAAI,IAAM,IAAM,OACtD0T,GACFE,EAAKI,YAAYN,GAEnBE,EAAKK,YAAYH,GAEjB3V,SAASpF,OAASiH,EAAQ,EAAI,IAAIA,EAAM,KAAO,IAAM,SAIhD,SAASkU,GAAM5I,EAAI6I,GACxB,IAAIC,EAAO,KAEX,IAAK9I,GAAMA,EAAGjI,QAAW8Q,KACvBC,EAAO,GACH9I,IACF8I,EAAK9I,GAAKA,EAAGjI,QAEX8Q,GAAc,CAChB,MAAME,EAAYF,EAAahP,QAAQ,KACvCiP,EAAKtQ,MAAQuQ,GAAa,EAAI,CAC5BrQ,KAAMmQ,EAAa5O,UAAU8O,EAAU,GACvCtQ,KAAM,OACJoQ,EAGR,OAAOC,EAIF,SAASE,GAAWC,EAAG1L,GAC5B,GAAI0L,IAAM1L,EACR,OAAO,EAGT,IAAK8B,MAAMC,QAAQ2J,KAAO5J,MAAMC,QAAQ/B,GACtC,OAAO,EAIT,GAAI0L,EAAE1Z,QAAUgO,EAAEhO,OAChB,OAAO,EAGT0Z,EAAE7B,OACF7J,EAAE6J,OACF,IAAK,IAAIvc,EAAI,EAAGC,EAAIme,EAAE1Z,OAAQ1E,EAAIC,EAAGD,IACnC,GAAIoe,EAAEpe,KAAO0S,EAAE1S,GACb,OAAO,EAGX,OAAO,EAkCF,SAASqe,GAAYja,EAAKka,GAC/B,IAAKla,EACH,OAAO,KAOT,GAHAA,EAAMA,EAAIkY,QAAQ,kBAAmB,IAAIpP,QAGpC,8BAA8BqR,KAAKna,GACtC,OAAOA,EAGT,GAAI,aAAama,KAAKna,GACpB,OAAOA,EAIT,MAAMoa,EAAUhK,MAAMC,QAAQ6J,GAAkBA,EAAe/Z,KAAK,KAAO,aAE3E,OADW,IAAIka,OAAO,MAAQD,EAAU,SAAU,KAC1CD,KAAKna,GAINA,EAHE,KC3GI,MAAMsa,WAAmB1c,IAAMiE,UAC5CC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXyN,KAAMnc,KAAKC,MAAMkc,KACjBC,SAAU,GACVC,UAAWrc,KAAKC,MAAMoc,WAGxBrc,KAAKsc,eAAiBtc,KAAKsc,eAAetd,KAAKgB,MAC/CA,KAAKuc,aAAevc,KAAKuc,aAAavd,KAAKgB,MAC3CA,KAAKwc,gBAAkBxc,KAAKwc,gBAAgBxd,KAAKgB,MACjDA,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAC3CA,KAAKgU,aAAehU,KAAKgU,aAAahV,KAAKgB,MAG7C,gCAAgCyc,EAAWnH,GACzC,OAAKqG,GAAWc,EAAUN,KAAM7G,EAAU6G,OAAU7G,EAAU+G,UAGvD,KAFE,CAACF,KAAMM,EAAUN,MAK5BG,eAAe1I,GAEb,GADA5T,KAAK+O,SAAS,CAACqN,SAAUxI,IACrBA,EAAK1R,OAAS,EAAG,CACnB,MAAMwa,EAAO9I,EAAKA,EAAK1R,OAAO,GACf,KAAX0R,EAAK,GAEHA,EAAK1R,OAAS,GAAa,KAARwa,GACrB1c,KAAKuc,aAAa3I,EAAKhH,UAAU,EAAGgH,EAAK1R,OAAO,IAEjC,KAARwa,GAAuB,KAARA,GAAuB,KAARA,GAAuB,KAARA,GAEtD1c,KAAKuc,aAAa3I,EAAKhH,UAAU,EAAGgH,EAAK1R,OAAO,GAAGwI,SAKzD6R,aAAaI,GACX,GAAIA,EAAIza,OAAS,GAAKlC,KAAK0O,MAAMyN,KAAKja,OvBvBb,GuBuBqC,CAC5D,MAAMia,EAAOnc,KAAK0O,MAAMyN,KAAKpL,MAAM,GAOnC,OANAoL,EAAKla,KAAK0a,GAEV3c,KAAK+O,SAAS,CAACoN,KAAMA,EAAMC,SAAU,KACjCpc,KAAKC,MAAM2c,eACb5c,KAAKC,MAAM2c,cAAcT,GAEpBA,EAET,OAAOnc,KAAK0O,MAAMyN,KAGpBK,gBAAgBG,EAAKlM,GACnB,MAAM0L,EAAOnc,KAAK0O,MAAMyN,KAAKpL,MAAM,GACnCoL,EAAKzE,OAAOjH,EAAO,GACnBzQ,KAAK+O,SAAS,CAACoN,KAAMA,IACjBnc,KAAKC,MAAM2c,eACb5c,KAAKC,MAAM2c,cAAcT,GAI7BjF,eAEElX,KAAKC,MAAMgY,SAASjY,KAAKuc,aAAavc,KAAK0O,MAAM0N,SAAS1R,SAC1D1K,KAAK+O,SAAS,CAACsN,WAAW,EAAOF,KAAMnc,KAAKC,MAAMkc,OAGpDnI,eACEhU,KAAK+O,SAAS,CAACsN,WAAW,EAAOD,SAAU,GAAID,KAAMnc,KAAKC,MAAMkc,OAC5Dnc,KAAKC,MAAMqT,UACbtT,KAAKC,MAAMqT,WAIfvT,SACE,IAAIoc,EAAO,GAiBX,OAhBInc,KAAK0O,MAAM2N,UACbrc,KAAK0O,MAAMyN,KAAK5U,IAAKoV,IACnBR,EAAKla,KAAK,CAACoD,KAAMsX,EAAK1I,QAAU0I,EAAIza,OvBhEd,OuBmExBlC,KAAK0O,MAAMyN,KAAK5U,IAAKoV,IACnBR,EAAKla,KAAK,0BAAM9B,UAAU,QAAQpB,IAAKod,EAAKja,QAASya,MAEpC,GAAfR,EAAKja,SACPia,EACE,2BACE,kBAAC,mBAAD,CAAkB1b,GAAG,iBAAiByX,eAAe,kCAM3D,yBAAK/X,UAAU,qBACb,yBAAKA,UAAU,kBACb,2BAAOA,UAAU,SAASH,KAAKC,MAAMG,QAEtCJ,KAAK0O,MAAM2N,UACZ,6BACE,kBAAC,mBAAD,CAAkB5b,GAAG,sBAAsByX,eAAe,iBAEvD2E,GAAoB,kBAAC,EAAD,CACnB9H,MAAOoH,EACPnG,gBAAgB,EAChBhB,OAAQ6H,EACR/G,QAAS9V,KAAKuc,aACd1G,YAAa7V,KAAKuc,aAClBjJ,SAAUtT,KAAKgU,aACf4B,cAAe5V,KAAKwc,gBACpBlK,WAAYtS,KAAKsc,kBAEpBtc,KAAKC,MAAMgY,UAAYjY,KAAKC,MAAMqT,SACjC,yBAAK7S,GAAG,sBAAsBN,UAAU,iCACtC,4BAAQA,UAAU,UAAUI,QAASP,KAAKgU,cACxC,kBAAC,mBAAD,CAAkBvT,GAAG,gBAAgBqc,cAAc,YAErD,4BAAQ3c,UAAU,OAAOI,QAASP,KAAKkX,cACrC,kBAAC,mBAAD,CAAkBzW,GAAG,YAAYqc,cAAc,SAGnD,MAGJ,yBAAK3c,UAAU,UACb,uBAAGoP,KAAK,IAAIpP,UAAU,cAAcI,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAK+O,SAAS,CAACsN,WAAW,MACjG,uBAAGlc,UAAU,kBAAb,QADF,KAC+C,kBAAC,mBAAD,CAAkBM,GAAG,oBAAoByX,eAAe,YAGvG,oCAAGiE,MCrHb,MAAM1Z,GAAWC,yBAAe,CAC9Bqa,UAAW,CAAF,uDAKTC,QAAS,CAAF,mDAKPC,iBAAkB,CAAF,mEAKhBC,WAAY,CAAF,8CAKVva,eAAgB,CAAF,4DAKdwa,uBAAwB,CAAF,gHAKtBC,gBAAiB,CAAF,qEAKfC,wBAAyB,CAAF,+HAKvBC,WAAY,CAAF,4DAKVC,mBAAoB,CAAF,4FAKlBC,cAAe,CAAF,0DAKbC,sBAAuB,CAAF,0FAKrBC,YAAa,CAAF,8DAKXC,oBAAqB,CAAF,0GAOrB,MAAMC,WAAiBpe,IAAMiE,UAC3BC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXnM,MAAO,KACPgY,OAAO,EACPsD,OAAO,EACPxD,QAAQ,EACRC,SAAS,EACTwD,OAAO,EACPC,QAAS,KACTC,gBAAYjI,EACZkI,cAAUlI,EACV5L,OAAQ,KACRqI,QAAS,KACT0L,gBAAiB,KACjBC,OAAQ,KACRC,UAAW,KACXC,SAAU,KACVC,WAAY,KACZC,UAAW,KACXC,KAAM,KACNC,KAAM,KACNC,YAAa,GACbvC,KAAM,GACNwC,iBAAiB,EACjBC,6BAAyB7I,EACzB8I,kBAAkB,EAClBC,sBAAkB/I,EAClBgJ,yBAAqBhJ,EACrBiJ,yBAAqBjJ,GAGvB/V,KAAKif,UAAYjf,KAAKif,UAAUjgB,KAAKgB,MACrCA,KAAKkf,UAAYlf,KAAKkf,UAAUlgB,KAAKgB,MACrCA,KAAKmf,WAAanf,KAAKmf,WAAWngB,KAAKgB,MACvCA,KAAKof,cAAgBpf,KAAKof,cAAcpgB,KAAKgB,MAC7CA,KAAKqf,cAAgBrf,KAAKqf,cAAcrgB,KAAKgB,MAC7CA,KAAKsf,qBAAuBtf,KAAKsf,qBAAqBtgB,KAAKgB,MAC3DA,KAAKuf,oBAAsBvf,KAAKuf,oBAAoBvgB,KAAKgB,MACzDA,KAAKwf,mBAAqBxf,KAAKwf,mBAAmBxgB,KAAKgB,MACvDA,KAAKyf,YAAczf,KAAKyf,YAAYzgB,KAAKgB,MACzCA,KAAK0f,yBAA2B1f,KAAK0f,yBAAyB1gB,KAAKgB,MACnEA,KAAK2f,8BAAgC3f,KAAK2f,8BAA8B3gB,KAAKgB,MAC7EA,KAAK4f,4BAA8B5f,KAAK4f,4BAA4B5gB,KAAKgB,MACzEA,KAAK6f,qBAAuB7f,KAAK6f,qBAAqB7gB,KAAKgB,MAC3DA,KAAK8f,qBAAuB9f,KAAK8f,qBAAqB9gB,KAAKgB,MAC3DA,KAAK+f,0BAA4B/f,KAAK+f,0BAA0B/gB,KAAKgB,MACrEA,KAAKggB,qBAAuBhgB,KAAKggB,qBAAqBhhB,KAAKgB,MAC3DA,KAAKigB,YAAcjgB,KAAKigB,YAAYjhB,KAAKgB,MACzCA,KAAKkgB,YAAclgB,KAAKkgB,YAAYlhB,KAAKgB,MACzCA,KAAKmgB,aAAengB,KAAKmgB,aAAanhB,KAAKgB,MAC3CA,KAAKogB,qBAAuBpgB,KAAKogB,qBAAqBphB,KAAKgB,MAC3DA,KAAKqgB,eAAiBrgB,KAAKqgB,eAAerhB,KAAKgB,MAC/CA,KAAKsgB,kBAAoBtgB,KAAKsgB,kBAAkBthB,KAAKgB,MACrDA,KAAKugB,kBAAoBvgB,KAAKugB,kBAAkBvhB,KAAKgB,MAIvD6O,mBAAmB5O,GACjB,MAAMsC,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS7E,EAAMsC,OAC1CA,IAIDvC,KAAKmf,YAAc5c,EAAM4c,aAC3Bnf,KAAK8e,iBAAmBvc,EAAM4c,WAC9B5c,EAAM4c,WAAanf,KAAKmf,WAExBnf,KAAK+e,oBAAsBxc,EAAM6c,cACjC7c,EAAM6c,cAAgBpf,KAAKof,cAEJ,OAAnB7c,EAAMie,WACRxgB,KAAKgf,oBAAsBzc,EAAM8c,cACjC9c,EAAM8c,cAAgBrf,KAAKqf,eAE3Brf,KAAKgf,yBAAsBjJ,GAI3B/V,KAAK0O,MAAMnM,OAAStC,EAAMsC,QAC5BvC,KAAK+O,SAAS,CAACxM,MAAOtC,EAAMsC,QAC5BvC,KAAKkf,UAAU3c,EAAOtC,GACtBD,KAAKif,UAAU1c,EAAOtC,KAI1ByF,uBACE,MAAMnD,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAKC,MAAMsC,OAC/CA,IAGLvC,KAAK+O,SAAS,CAACxM,MAAO,OACtBA,EAAM4c,WAAanf,KAAK8e,iBACxBvc,EAAM6c,cAAgBpf,KAAK+e,oBAC3Bxc,EAAM8c,cAAgBrf,KAAKgf,qBAG7BC,UAAU1c,EAAOtC,GACf,MAAMwgB,EAAW,CAAC/B,YAAa,IAC/B,GAAuB,OAAnBnc,EAAMie,UAAoB,CAG5B,MAAME,EAAQne,EAAMoe,WAAW1gB,EAAMsC,OACjCme,GACFD,EAASnC,WAAaoC,EAAMhQ,IAAIkQ,WAChCH,EAASlC,UAAYmC,EAAMhQ,IAAImQ,YAE/BJ,EAASnC,WxBzKa,IwB0KtBmC,EAASlC,UxB1Ka,UwB6KxBhc,EAAMue,YAAaC,IACjBN,EAAS/B,YAAYzc,KAAK8e,IACzB/gB,MAGLA,KAAK+O,SAAS0R,GAGhBvB,UAAU3c,EAAOtC,GACf,MAAM+gB,EAASze,EAAM0e,oBAAsB,GACrCvQ,EAAMnO,EAAM2e,gBAElBlhB,KAAK+O,SAAS,CACZwL,MAAO7J,GAAOA,EAAImC,UAClBgL,MAAOnN,GAAOA,EAAIyQ,UAClB9G,OAAQ3J,GAAOA,EAAI0Q,WACnB9G,QAAS5J,GAAOA,EAAI2Q,YACpBvD,MAAOpN,GAAOA,EAAIS,UAElB8M,SAAU1b,EAAMmQ,OAASnQ,EAAMmQ,OAAOC,QAAKoD,EAC3C5L,OAAQe,EAAa3I,EAAMmQ,OAASnQ,EAAMmQ,OAAOvH,MAAQ,MACzDqH,QAASjQ,EAAMiQ,QAAUjQ,EAAMiQ,QAAQf,QAAU,KACjDsM,QAASxb,EAAMxE,KACfigB,WAAgC,OAAnBzb,EAAMie,UACnB7B,iBAAiB,EACjBR,OAAQzN,EAAMA,EAAIQ,eAAY6E,EAC9BqI,UAAW1N,EAAMA,EAAIkQ,gBAAa7K,EAClCsI,SAAU3N,EAAMA,EAAImQ,eAAY9K,EAChCyI,KAAMwC,EAAOxC,KACbC,KAAMuC,EAAOvC,OAGQ,OAAnBlc,EAAMie,WAAsB9P,GAAOA,EAAImC,WAEzCtQ,EAAM+e,QAAQ/e,EAAMgf,iBAAiBC,WAAWC,SAIpDtC,WAAWuC,GACT,MAAMnf,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAKC,MAAMsC,OAC/CA,IAGLvC,KAAKkf,UAAU3c,EAAOvC,KAAKC,OAEvBD,KAAK8e,kBAAoB9e,KAAK8e,kBAAoB9e,KAAKmf,YACzDnf,KAAK8e,iBAAiB4C,IAI1BtC,cAAcuC,GACZ,MAAMpf,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAKC,MAAMsC,OAC/CA,IAGLvC,KAAKif,UAAU1c,EAAOvC,KAAKC,OAEvBD,KAAK+e,qBAAuB/e,KAAK+e,qBAAuB/e,KAAKof,eAC/Dpf,KAAK+e,oBAAoB4C,IAI7BtC,cAAclD,GACZnc,KAAK+O,SAAS,CAACoN,KAAMA,IAEjBnc,KAAKgf,qBAAuBhf,KAAKgf,qBAAuBhf,KAAKqf,eAC/Drf,KAAKgf,sBAITM,qBAAqB3M,GACnBA,EAAKA,EAAGjI,OAAOkC,UAAU,ExB7MG,IwB8MxB5M,KAAK0O,MAAMuP,WAAatL,IAC1B3S,KAAK+O,SAAS,CAACkP,SAAUtL,IACzB3S,KAAKC,MAAM2hB,kBAAkB5hB,KAAKC,MAAMsC,MAAOgZ,GAAM5I,EAAI,MAAO,OAIpE4M,oBAAoB9N,GAClBA,EAAUA,EAAQ/G,OAAOkC,UAAU,ExBrNP,IwBsNxB5M,KAAK0O,MAAM8D,UAAYf,IACzBzR,KAAK+O,SAAS,CAACyD,QAASf,IACxBzR,KAAKC,MAAM2hB,kBAAkB5hB,KAAKC,MAAMsC,MAAO,KAAMkP,GAAW/R,IAAOmiB,WAI3ErC,mBAAmBxS,GACjBhN,KAAK+O,SAAS,CAAC5E,OAAQ6C,IACvBhN,KAAKC,MAAM2hB,kBAAkB5hB,KAAKC,MAAMsC,MAAOgZ,GAAM,KAAMvO,GAAOtN,IAAOmiB,UAAW,MAGtFpC,YAAYqC,EAAShS,GACnB9P,KAAK+O,SAAS,CAAC+O,MAAOhO,IACtB9P,KAAKC,MAAM8hB,oBAAoB/hB,KAAKC,MAAMsC,MAAOuN,EAAU,KAAO,MAGpE4P,yBAAyBsC,GACvB,OAAQhiB,KAAK0O,MAAMkQ,yBACjB,IAAK,OACH5e,KAAKC,MAAM2hB,kBAAkB5hB,KAAKC,MAAMsC,MAAO,KAAM,KAAM,CAACic,KAAMwD,IAClE,MACF,IAAK,OACHhiB,KAAKC,MAAM2hB,kBAAkB5hB,KAAKC,MAAMsC,MAAO,KAAM,KAAM,CAACkc,KAAMuD,IAClE,MACF,IAAK,OACL,IAAK,OACHhiB,KAAKC,MAAM8hB,oBAAoB/hB,KAAKC,MAAMsC,MAAOyf,GACjD,MACF,IAAK,QACHhiB,KAAKC,MAAM8hB,oBAAoB/hB,KAAKC,MAAMsC,MAAOyf,EAAMhiB,KAAKC,MAAMsC,OAClE,MACF,IAAK,OACHvC,KAAKC,MAAM8hB,oBAAoB/hB,KAAKC,MAAMsC,MAAOyf,EAAMhiB,KAAK0O,MAAMuT,uBAItEjiB,KAAK+O,SAAS,CAAC6P,6BAAyB7I,IAG1C4J,8BAA8BuC,EAAO1S,GACnC,MAAM,cAAC5L,GAAiB5D,KAAKC,MAAM4D,KACnC,IAAIse,EAAQC,EAAWC,EAAQC,EAAWC,EAAc7H,EAAWC,EACnE,OAAQuH,GACN,IAAK,OACHC,EAASniB,KAAK0O,MAAMyP,OACpB,MACF,IAAK,OACHgE,EAASniB,KAAK0O,MAAM2P,SACpB+D,EAAYpiB,KAAK0O,MAAM0P,UACvBiE,EAASriB,KAAK0O,MAAM6L,MAAQ,IAE1B7a,IAAO8iB,WAAWC,OAAO/iB,IAAO8iB,WAAWE,KAAK,OAAQ1iB,KAAK0O,MAAM0P,YACrEkE,EAAY1e,EAAcnB,GAASsa,WACnCwF,EAAe3e,EAAcnB,GAASua,SACtC,MACF,IAAK,QACHmF,EAASniB,KAAK0O,MAAM4P,WACpB8D,EAAYpiB,KAAK0O,MAAM6P,UACvB8D,EAASriB,KAAK0O,MAAMsP,WAAche,KAAK0O,MAAM6L,MAAQ,GAAK,IAAO,OACjE+H,EAAY1e,EAAcnB,GAASua,SACnCuF,EAAe3e,EAAcnB,GAASsa,WACtC,MACF,IAAK,OACHoF,EAASniB,KAAK0O,MAAM8P,KACpB6D,EAAS,IACT,MACF,IAAK,OACHF,EAASniB,KAAK0O,MAAM+P,KACpB4D,EAAS,IACT,MACF,IAAK,OAAQ,CACX,IAAI9f,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAKC,MAAMsC,OAClD,IAAKA,EACH,OAEF,IAAI8C,EAAO9C,EAAMoe,WAAWnR,GAC5B,IAAKnK,IAASA,EAAKqL,IACjB,OAEFyR,EAAS9c,EAAKqL,IAAIkQ,WAClBwB,EAAY/c,EAAKqL,IAAImQ,UACrBwB,EAASriB,KAAK0O,MAAM6L,MAAQ,GAAK,IACjC+H,EAAY1e,EAAcnB,GAASua,SACnCuF,EAAe3e,EAAcnB,GAASsa,WAClC1X,EAAKqN,SACPgI,EAAYrV,EAAKqN,OAAOC,GACxBgI,EAAatV,EAAKqN,OAAOvH,OAE3B,MAEF,QACEhG,QAAQC,IAAI,oCAAsC8c,EAAQ,KAG9DliB,KAAK+O,SAAS,CACZ6P,wBAAyBsD,EACzBD,sBAAuBzS,EACvBmT,qBAAsBjI,EACtBkI,sBAAuBjI,EACvBkI,kBAAmBV,EACnBW,qBAAsBV,EACtBW,uBAAwBT,EACxBU,0BAA2BT,EAC3BU,yBAA0BZ,IAI9BzC,8BACE5f,KAAK+O,SAAS,CAAC6P,6BAAyB7I,IAG1C8J,qBAAqBja,GACnBA,EAAEI,iBACFhG,KAAKC,MAAMijB,aACXljB,KAAK+O,SAAS,CAAC4P,iBAAiB,IAGlCmB,uBACE9f,KAAK+O,SAAS,CAAC4P,iBAAiB,IAGlCoB,0BAA0BvJ,EAASsB,EAAOnC,GACxC3V,KAAKC,MAAMkjB,sBAAsBnjB,KAAKC,MAAMsC,MAAOuV,EAAOnC,GAC1D3V,KAAK+O,SAAS,CAAC4P,iBAAiB,IAGlCqB,qBAAqBpa,GACnBA,EAAEI,iBACF,MAAM,cAACpC,GAAiB5D,KAAKC,MAAM4D,KACnC7D,KAAKC,MAAMmE,YACTR,EAAc5D,KAAK0O,MAAM4L,QAAU7X,GAAS2a,gBAAkB3a,GAASE,gBACvEiB,EAAc5D,KAAK0O,MAAM4L,QAAU7X,GAAS4a,wBAA0B5a,GAAS0a,wBAC9E,KAAQnd,KAAKC,MAAMmjB,iBAAiBpjB,KAAKC,MAAMsC,QAChD,MACA,EACA,MAIJ0d,YAAYra,GACVA,EAAEI,iBACF,MAAM,cAACpC,GAAiB5D,KAAKC,MAAM4D,KACnC7D,KAAKC,MAAMmE,YACTR,EAAcnB,GAAS6a,YACvB1Z,EAAcnB,GAAS8a,oBACtB,KAAQvd,KAAKC,MAAMojB,aAAarjB,KAAKC,MAAMsC,QAC5C,MACA,EACA,MAIJ2d,YAAYta,GACVA,EAAEI,iBACF,MAAM,cAACpC,GAAiB5D,KAAKC,MAAM4D,KACnC7D,KAAKC,MAAMmE,YACTR,EAAcnB,GAAS+a,eACvB5Z,EAAcnB,GAASgb,uBACtB,KAAQzd,KAAKC,MAAMqjB,aAAatjB,KAAKC,MAAMsC,QAC5C,MACA,EACA,MAIJ4d,aAAava,GACXA,EAAEI,iBACF,MAAM,cAACpC,GAAiB5D,KAAKC,MAAM4D,KACnC7D,KAAKC,MAAMmE,YACTR,EAAcnB,GAASib,aACvB9Z,EAAcnB,GAASkb,qBACtB,KAAQ3d,KAAKC,MAAMsjB,cAAcvjB,KAAKC,MAAMsC,QAC7C,MACA,EACA,MAIJ6d,qBAAqB5Q,GACnBxP,KAAK+O,SAAS,CAACmP,gBAAiB1O,IAGlC6Q,eAAe3G,GACb1Z,KAAK+O,SAAS,CAAC8P,iBAAkBnF,IAGnC4G,kBAAkBnE,GACXR,GAAW3b,KAAK0O,MAAMyN,KAAKpL,MAAM,GAAIoL,EAAKpL,MAAM,KACnD/Q,KAAKC,MAAMujB,kBAAkBxjB,KAAKC,MAAMsC,MAAO4Z,GAInDoE,kBAAkBlf,GAChB,MAAM,cAACuC,GAAiB5D,KAAKC,MAAM4D,KAC7BgU,EAAW7X,KACXuC,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAKC,MAAMsC,OACpD,IAAKA,EACH,OAEF,MAAM8C,EAAO9C,EAAMoe,WAAWtf,EAAOuD,WACrC,IAAKS,IAASA,EAAKqL,IACjB,OAGF,MAAM+S,EAAY,CAChB,CAACrjB,MAAOwD,EAAcnB,GAASwa,kBAAmB/Y,QAAS,WACzD2T,EAAS8H,8BAA8B,OAAQte,EAAOuD,aAExD,gBACAS,EAAKqL,IAAIS,UAAY,gBAAkB,cACvC9L,EAAKqL,IAAIU,WAAa,eAAiB,kBAEzCpR,KAAKC,MAAM0Q,gBAAgB,CACzB/L,UAAW5E,KAAKC,MAAMsC,MACtBwF,EAAG1G,EAAO0G,EACVG,EAAG7G,EAAO6G,EACV7C,KAAMhE,EAAOuD,WAAY6e,GAG7B1jB,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KAEnC,OACE,yBAAKpD,GAAG,aACN,yBAAKN,UAAU,gBAAgBM,GAAG,sBAChC,yBAAKN,UAAU,cAAcM,GAAG,cAC9B,kBAAC,mBAAD,CAAkBA,GAAG,aAAayX,eAAe,UAEnD,6BACE,kBAAC,EAAD,CAAY5E,SAAUtT,KAAKC,MAAMqT,aAGpCtT,KAAKC,MAAMyjB,cACV,kBAAC,EAAD,CACEjQ,MAAOzT,KAAKC,MAAM0jB,WAClB/P,KAAM5T,KAAKC,MAAM2jB,UACjBlQ,aAAc1T,KAAKC,MAAMuG,UAAc,KAC1CxG,KAAK0O,MAAMiQ,gBACV,kBAAC,EAAD,CACEnI,QAASxW,KAAK0O,MAAMgQ,YACpB/H,eAAgB3W,KAAKC,MAAM2S,SAC3B8D,oBAAqB1W,KAAK0O,MAAMmP,QAAU7d,KAAK0O,MAAM6L,MACrD3H,SAAU5S,KAAKC,MAAM2S,SACrBP,SAAUrS,KAAKC,MAAM4jB,mBACrBvQ,SAAUtT,KAAK8f,qBACf7H,SAAUjY,KAAK+f,4BAEnB/f,KAAK0O,MAAMkQ,wBACT,kBAAC,GAAD,CACEjgB,KAAMqB,KAAK0O,MAAMmU,kBACjBpI,QAASza,KAAK0O,MAAMoU,qBACpBtI,KAAMxa,KAAK0O,MAAMuU,yBACjBrI,UAAW5a,KAAK0O,MAAMqU,uBACtBlI,aAAc7a,KAAK0O,MAAMsU,0BACzBtI,UAAW1a,KAAK0O,MAAMiU,qBACtBjhB,KAAM1B,KAAK0O,MAAMuT,sBACjBtH,WAAY3a,KAAK0O,MAAMkU,sBACvB3K,SAAUjY,KAAK0f,yBACfpM,SAAUtT,KAAK4f,8BAGjB,yBAAKnf,GAAG,oBAAoBN,UAAU,oBACpC,yBAAKA,UAAU,kBACb,yBAAKA,UAAU,qBACb,6BAAK,2BAAOA,UAAU,SACpB,kBAAC,mBAAD,CAAkBM,GAAG,mBAAmByX,eAAe,WAGzD,6BAAK,kBAAC,EAAD,CACDpD,YAAa9U,KAAK0O,MAAMsP,WAAa,aAAe,sCACpD1O,UAAWtP,KAAK0O,MAAM6L,MACtB9b,MAAOuB,KAAK0O,MAAMuP,SAClB9J,UAAU,EACVqE,WAAYxY,KAAKsf,wBACrB,6BACE,2BAAOnf,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,gBAAgByX,eAAe,sBAIxD,6BACE,kBAAC,mBAAD,CAAkBzX,GAAG,8BACnByX,eAAe,uBAEd4L,GAAwB,kBAAC,EAAD,CACvBhP,YAAagP,EACbrlB,MAAOuB,KAAK0O,MAAM8D,QAClBgG,WAAYxY,KAAKuf,yBAIzB,kBAAC,EAAD,CACEpV,OAAQnK,KAAK0O,MAAMvE,OACnBmF,UAAWtP,KAAK0O,MAAM6L,MACtB/K,IAAKxP,KAAKC,MAAMsC,MAChBnC,MAAOJ,KAAK0O,MAAMuP,SAClB9O,eAAgBnP,KAAKwf,mBACrBhZ,QAASxG,KAAKC,MAAMuG,WAExB,yBAAKrG,UAAU,OACf,yBAAKA,UAAU,qBACb,yBAAKA,UAAU,kBACb,+BACE,kBAAC,mBAAD,CAAkBM,GAAG,qBAAqByX,eAAe,YAG3D,kBAAC,EAAD,CAAUna,KAAK,IAAI+R,QAAS9P,KAAK0O,MAAMoP,MACrCpO,SAAU1P,KAAKyf,eAEnB,kBAAC,mBAAD,CAAkBhf,GAAG,cAAcyX,eAAe,QAE/C6L,GAAS,kBAAC,EAAD,CACR3jB,MAAO2jB,EACPrK,KAAM1Z,KAAK0O,MAAMmQ,iBACjBjF,SAAU5Z,KAAKqgB,kBAElBrgB,KAAK0O,MAAMmQ,iBACV,yBAAK1e,UAAU,qBACb,yBAAKA,UAAU,kBACb,+BAAO,kBAAC,mBAAD,CAAkBM,GAAG,mBAC5B,4BAAKT,KAAK0O,MAAMqP,UAEjB/d,KAAK0O,MAAMsP,WACV,yBAAK7d,UAAU,kBACb,+BACE,kBAAC,mBAAD,CAAkBM,GAAG,yBAAyByX,eAAe,uBAG/D,wBAAI/X,UAAU,YACZI,QAASP,KAAK2f,8BAA8B3gB,KAAKgB,KAAM,SACtDA,KAAK0O,MAAMyP,SAIhB,6BACE,6BACE,2BAAOhe,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,oBAAoByX,eAAe,mBAI5D,yBAAK/X,UAAU,UACb,6BACE,kBAAC,mBAAD,CAAkBM,GAAG,YAAYyX,eAAe,SADlD,KAEsD,wBAAI/X,UAAU,YAClEI,QAASP,KAAK2f,8BAA8B3gB,KAAKgB,KAAM,SACtDA,KAAK0O,MAAMyP,SAEd,6BAAMne,KAAK0O,MAAMuP,SAAWje,KAAK0O,MAAMuP,SAAWra,EAAcnB,GAASya,YAAzE,MACQ,wBAAI/c,UAAU,YAAYI,QAASP,KAAK2f,8BAA8B3gB,KAAKgB,KAAM,UACtFA,KAAK0O,MAAM4P,eAMnBte,KAAK0O,MAAM2L,SAAWra,KAAK0O,MAAM8P,MAAQxe,KAAK0O,MAAM+P,MACrD,6BACE,6BACE,2BAAOte,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,uBAAuByX,eAAe,2BAI/D,yBAAK/X,UAAU,UACb,sCAAYH,KAAK0O,MAAMmP,MACrB,wBAAI1d,UAAU,YACZI,QAASP,KAAK2f,8BAA8B3gB,KAAKgB,KAAM,SACtDA,KAAK0O,MAAM8P,MAGd,4BAAKxe,KAAK0O,MAAM8P,OAGlB,sCAAYxe,KAAK0O,MAAMmP,MACrB,wBAAI1d,UAAU,YACZI,QAASP,KAAK2f,8BAA8B3gB,KAAKgB,KAAM,SACtDA,KAAK0O,MAAM+P,MAGd,4BAAKze,KAAK0O,MAAM+P,SAMtB,MAIJ,MAGF,yBAAKte,UAAU,OACdH,KAAK0O,MAAM6L,MACV,oCACE,kBAAC,mBAAD,CAAkB9Z,GAAG,qBAClB0b,GAAS,kBAAC,GAAD,CACR/b,MAAO+b,EACPA,KAAMnc,KAAK0O,MAAMyN,KACjBE,WAAW,EACXpE,SAAUjY,KAAKsgB,qBAEnB,yBAAKngB,UAAU,QAGjB,KAEF,yBAAKA,UAAU,qBACb,uBAAGoP,KAAK,IAAIpP,UAAU,cAAcI,QAASP,KAAKggB,sBAChD,uBAAG7f,UAAU,kBAAb,kBADF,KAEIyD,EAAc5D,KAAK0O,MAAM4L,QAAU7X,GAAS2a,gBAAkB3a,GAASE,iBAG3E,uBAAG4M,KAAK,IAAIpP,UAAU,kBAAkBI,QAASP,KAAKigB,aACpD,uBAAG9f,UAAU,kBAAb,eADF,KACuDyD,EAAcnB,GAAS6a,aAE5Etd,KAAK0O,MAAMsP,WAKX,KAJA,uBAAGzO,KAAK,IAAIpP,UAAU,kBAAkBI,QAASP,KAAKkgB,aACpD,uBAAG/f,UAAU,kBAAb,SADF,KACiDyD,EAAcnB,GAAS+a,gBAKxExd,KAAK0O,MAAM6L,MAKX,KAJA,uBAAGhL,KAAK,IAAIpP,UAAU,kBAAkBI,QAASP,KAAKmgB,cACpD,uBAAGhgB,UAAU,kBAAb,UADF,KACkDyD,EAAcnB,GAASib,eAM5E1d,KAAK0O,MAAMsP,WACV,oCACE,yBAAK7d,UAAU,OACf,yBAAKA,UAAU,qBACb,yBAAKA,UAAU,kBACb,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,sBAAsByX,eAAe,qBAI9D,yBAAK/X,UAAU,kBACZH,KAAK0O,MAAM2L,OACV,uBAAG9K,KAAK,IAAIpP,UAAU,cAAcI,QAASP,KAAK6f,sBAChD,uBAAG1f,UAAU,kBAAb,cADF,KACqD,kBAAC,mBAAD,CAAkBM,GAAG,qBACtEyX,eAAe,iBAEjB,MAEN,kBAAC,mBAAD,CAAkBzX,GAAG,uBAAuByX,eAAe,cAExD8L,GAAe,kBAAC,EAAD,CACd3R,SAAUrS,KAAK0O,MAAMgQ,YACrB9L,SAAU5S,KAAKC,MAAM2S,SACrBQ,iBAAkB4Q,EAClB9R,cAAelS,KAAK0O,MAAMwP,gBAC1B3M,YAAY,EACZuB,YAAY,EACZ7B,UAAU,EACVgC,UAAU,EACVD,gBAAiBhT,KAAKogB,qBACtBzP,kBAAiB3Q,KAAK0O,MAAMmP,OAAQ7d,KAAKugB,uBAMjD,QASCnY,4BAAWwV,ICzvBX,MAAMqG,WAAqBzkB,IAAMM,cAC9CC,SACE,OACE,yBAAKI,UAAU,YACb,6BAAK,0BAAMgI,MAAO,CAACoD,MAA8B,IAAtBvL,KAAKC,MAAMikB,SAAkB,QACvDlkB,KAAKC,MAAMikB,SAAW,KACrB,uBAAG3U,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMqT,aAC1D,uBAAGnT,UAAU,kBAAb,SADF,IAC0C,kBAAC,mBAAD,CAAkBM,GAAG,gBAC3DyX,eAAe,YAGnB,kBAAC,mBAAD,CAAkBzX,GAAG,mBAAmByX,eAAe,mBCRlD,MAAMiM,WAAmB3kB,IAAMiE,UAC5CC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACX0V,WAAY,KACZF,SAAU,GAGZlkB,KAAKqkB,aAAerkB,KAAKqkB,aAAarlB,KAAKgB,MAC3CA,KAAKgU,aAAehU,KAAKgU,aAAahV,KAAKgB,MAG7CqkB,aAAaziB,EAAK0iB,EAAUC,GAC1B,IAAIH,EAAapkB,KAAKC,MAAM4E,OAAO2f,qBACnCxkB,KAAK+O,SAAS,CAACqV,WAAYA,IAC3BA,EAAWK,SAAS7iB,EAAK0iB,EAAUC,EAAWG,IAC5C1kB,KAAK+O,SAAS,CAACmV,SAAUQ,EAAS1kB,KAAKC,MAAM4L,SAC5CpH,KAAK,KACNzE,KAAK+O,SAAS,CAACqV,WAAY,KAAMF,SAAU,MAC1Clf,MAAOC,IACJA,GACFjF,KAAKC,MAAMuG,QAAQ,2BAA6BvB,EAAIC,QAAS,OAE/DlF,KAAK+O,SAAS,CAACqV,WAAY,KAAMF,SAAU,MAI/ClQ,eACMhU,KAAKC,MAAM0kB,SACb3kB,KAAKC,MAAM2kB,iBACF5kB,KAAK0O,MAAM0V,YACpBpkB,KAAK0O,MAAM0V,WAAWS,SAI1B9kB,SACE,IAAIukB,EAAWtkB,KAAKC,MAAMqkB,UAAY,kBAClCA,EAASpiB,OAAS,KACpBoiB,EAAWA,EAAS/iB,OAAO,EAAG,IAAM,MAAQ+iB,EAAS/iB,QAAQ,KAE/D,IAOIK,EAAKkjB,EAPLjZ,EAAO7L,KAAKC,MAAM4L,KAAO,EAC3B,0BAAM1L,UAAU,cAAhB,IAA+BoJ,EAAiBvJ,KAAKC,MAAM4L,MAA3D,KACA,KAiBF,OAXK7L,KAAKC,MAAM0kB,UAAa3kB,KAAK0O,MAAM0V,aJiCrC,SAAuBxiB,GAC5B,OAAQ,kCAAkCma,KAAKna,GIlCSmjB,CAAc/kB,KAAKC,MAAM+kB,cAQ7EpjB,EAAMia,GAAY7b,KAAKC,MAAM+kB,cAAgB,cAC7CF,EAAa,OAPbljB,EAAM,IACNkjB,EAAclf,IACZA,EAAEI,iBACFhG,KAAKqkB,aAAarkB,KAAKC,MAAM+kB,YAAahlB,KAAKC,MAAMqkB,SAAUtkB,KAAKC,MAAMskB,YAO5E,yBAAKpkB,UAAU,cACb,6BAAK,uBAAGA,UAAU,2BAAb,sBACL,yBAAKA,UAAU,eACb,6BAAMmkB,EAAN,IAAiBzY,GAChB7L,KAAKC,MAAM0kB,UAAY3kB,KAAK0O,MAAM0V,WACjC,kBAAC,GAAD,CAAcF,SAAUlkB,KAAKC,MAAM0kB,SAAW3kB,KAAKC,MAAMikB,SAAWlkB,KAAK0O,MAAMwV,SAC7E5Q,SAAUtT,KAAKgU,eAEjB,6BAAK,uBAAGzE,KAAM3N,EAAK6iB,SAAUzkB,KAAKC,MAAMqkB,SAAU/jB,QAASukB,GACzD,uBAAG3kB,UAAU,kBAAb,iBADG,IAC6C,kBAAC,mBAAD,CAAkBM,GAAG,kBACnEyX,eAAe,cCtE/B,MAAMzV,GAAWC,yBAAe,CAC9B,2DAKA,+DAOF,MAAMuiB,WAAuBzlB,IAAMM,cACjCC,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KACnC,IAAIqhB,EAEFA,EADEllB,KAAKC,MAAMklB,UAAYzlB,IAAO0lB,uBACpBxhB,EAAcnB,GAAS4iB,SAC1BrlB,KAAKC,MAAMklB,UAAYzlB,IAAO4lB,sBAC3B1hB,EAAcnB,GAAS8iB,QAEvBld,EAAgBrI,KAAKC,MAAMilB,UAAWllB,KAAKC,MAAM4D,KAAKyE,QAGpE,IAAIkd,EAAS,KAab,OAZIxlB,KAAKC,MAAMklB,UAAYzlB,IAAO0lB,uBAChCI,EAAU,uBAAGrlB,UAAU,wBAAb,eACDH,KAAKC,MAAMklB,UAAYzlB,IAAO4lB,sBACvCE,EAAU,uBAAGrlB,UAAU,8BAAb,WACDH,KAAKC,MAAMklB,UAAYzlB,IAAO+lB,oBACvCD,EAAU,uBAAGrlB,UAAU,wBAAb,QACDH,KAAKC,MAAMklB,UAAYzlB,IAAOgmB,wBACvCF,EAAU,uBAAGrlB,UAAU,wBAAb,YACDH,KAAKC,MAAMklB,UAAYzlB,IAAOimB,sBACvCH,EAAU,uBAAGrlB,UAAU,6BAAb,aAIV,0BAAMA,UAAU,aACb+kB,EAAW,IAAUM,IAMfpd,4BAAW6c,IC5CX,MAAMW,WAAoBpmB,IAAMiE,UAC7CC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXwV,SAAU,GAGRjkB,EAAM0kB,WACR1kB,EAAM0kB,SAASkB,WAAa7lB,KAAK8lB,eAAe9mB,KAAKgB,OAGvDA,KAAK+lB,mBAAqB/lB,KAAK+lB,mBAAmB/mB,KAAKgB,MACvDA,KAAKgmB,sBAAwBhmB,KAAKgmB,sBAAsBhnB,KAAKgB,MAC7DA,KAAKuQ,mBAAqBvQ,KAAKuQ,mBAAmBvR,KAAKgB,MACvDA,KAAKimB,mBAAqBjmB,KAAKimB,mBAAmBjnB,KAAKgB,MAGzD+lB,mBAAmBngB,GACjBA,EAAEI,iBACFhG,KAAKC,MAAMimB,eAAe,CACxBtkB,IAAKgE,EAAEG,OAAO+E,IACdwZ,SAAU1e,EAAEG,OAAO3F,MACnBmL,MAAO3F,EAAEG,OAAOO,QAAQiF,MACxBC,OAAQ5F,EAAEG,OAAOO,QAAQkF,OACzBK,KAAMjG,EAAEG,OAAOO,QAAQuF,KACvBT,KAAMxF,EAAEG,OAAOO,QAAQgG,OAI3B0Z,sBAAsBpgB,GACpBA,EAAEI,iBACF,MAAMqF,EAAO,CACXzE,IAAK5G,KAAKC,MAAM2G,IAElByE,KAAY,IACRzF,EAAEG,OAAOO,QAAQvI,OACnBsN,EAAK8a,KAAKvgB,EAAEG,OAAOO,QAAQvI,MAAQ6H,EAAEG,OAAOO,QAAQqR,IAAM/R,EAAEG,OAAOO,QAAQqR,SAChD5B,IAAzBnQ,EAAEG,OAAOO,QAAQqR,IAAoB,EAAI,GAAK/R,EAAEG,OAAOO,QAAQqR,KAEvC,OAAxB/R,EAAEG,OAAOO,QAAQ8f,MACnB/a,EAAKgb,IAAMxK,GAAYjW,EAAEG,OAAOO,QAAQ+f,MAAQ,eAElD,MAAMzS,EAAOhO,EAAEG,OAAOO,QAAQlG,OAAS,UACvCJ,KAAKC,MAAMqmB,eAAe1gB,EAAEG,OAAOO,QAAQ8f,IAAKxS,EAAMvI,GAGxDkF,mBAAmB3K,GACjBA,EAAEI,iBACFJ,EAAEK,kBACF,MAAMwd,EAAYzjB,KAAKC,MAAMklB,UAAYzlB,OAAO4lB,sBAAwB,CAAC,wBAA0B,GACnGtlB,KAAKC,MAAM0Q,gBAAgB,CAAE/J,IAAK5G,KAAKC,MAAM2G,IAAKvG,QAASL,KAAKC,MAAMI,QACzC6H,EAAGtC,EAAEgL,MAAO7I,EAAGnC,EAAEiL,OAAS4S,GAGzDqC,eAAeS,GACbvmB,KAAK+O,SAAS,CAACmV,SAAUqC,IAG3BN,qBACEjmB,KAAKC,MAAM0kB,SAASE,SAGtB9kB,SACE,MAAMymB,EAAYxmB,KAAKC,MAAMwmB,QAAU,SACpCzmB,KAAKC,MAAMymB,SAAW,KAAO1mB,KAAKC,MAAM0mB,SAAW,OAAS,SACzDC,EAAsC,UAAvB5mB,KAAKC,MAAMymB,UAA+C,QAAvB1mB,KAAKC,MAAMymB,SAAsB,aAAe,SAClGvc,EAASnK,KAAKC,MAAMwmB,QAAU,KAAQzmB,KAAKC,MAAM0a,aAAc,EAC/DkM,EAAe7mB,KAAKC,MAAM6mB,UAAY9mB,KAAKC,MAAM0mB,WAC7B,UAAvB3mB,KAAKC,MAAMymB,UAA+C,QAAvB1mB,KAAKC,MAAMymB,UAEjD,IAAIrmB,EAAUL,KAAKC,MAAMI,QACzB,MAAM0mB,EAAc,GAiCpB,OAhCI/mB,KAAKC,MAAM+mB,UAAYC,SAAOC,kBAAoBD,SAAOE,QAAQ9mB,IACnE4mB,SAAOF,YAAY1mB,GAAS,SAAS+mB,EAAK5pB,GACxB,oBAAZ4pB,EAAI9a,MAKRya,EAAY9kB,KAAK,kBAAC,GAAD,CACf4C,OAAQ7E,KAAKC,MAAM4E,OACnBmgB,YAAaiC,SAAOI,eAAeD,GACnC9C,SAAU8C,EAAIrpB,KAAM4mB,SAAUsC,SAAOK,YAAYF,GACjD7C,SAAU6C,EAAI9a,KAAMT,KAAMob,SAAOM,cAAcH,GAC/ClD,SAAUlkB,KAAK0O,MAAMwV,SACrBU,eAAgB5kB,KAAKimB,mBACrBzf,QAASxG,KAAKC,MAAMuG,QACpBzH,IAAKvB,OACNwC,MACHK,EAAUb,IAAM8N,cAAc9N,IAAMgoB,SAAU,KAAMP,SAAOQ,OAAOpnB,EAASqnB,GAAiB1nB,QACnFA,KAAKC,MAAMwmB,QAEpBpmB,EAAU,oCAAE,uBAAGF,UAAU,uBAAb,SAAF,IAA+C,uBAAGA,UAAU,QACpE,kBAAC,mBAAD,CAAkBM,GAAG,kBACnByX,eAAe,sBAEQ,iBAAX7X,IAChBA,EAAU,oCACN,uBAAGF,UAAU,uBAAb,iBADM,IAC+C,uBAAGA,UAAU,QAChE,kBAAC,mBAAD,CAAkBM,GAAG,kBACnByX,eAAe,uBAKvB,wBAAI/X,UAAWqmB,GACZxmB,KAAKC,MAAM6mB,UAAY9mB,KAAKC,MAAM0mB,SACjC,yBAAKxmB,UAAU,cACZ0mB,EACC,kBAAC,EAAD,CACEtkB,MAAOvC,KAAKC,MAAM6mB,SAClB1mB,MAAOJ,KAAKC,MAAM0nB,SAClBxd,OAAQA,IACV,MAEJ,KACF,6BACE,yBAAKhK,UAAWymB,GACd,yBAAKzmB,UAAU,mBACZE,EACA0mB,EACA/mB,KAAKC,MAAMilB,UACV,kBAAC,GAAD,CACEA,UAAWllB,KAAKC,MAAMilB,UACtBC,SAAUnlB,KAAKC,MAAMklB,WACrB,MAELnlB,KAAKC,MAAMwmB,QACV,KACA,0BAAMtmB,UAAU,eACd,uBAAGoP,KAAK,IAAIhP,QAASP,KAAKuQ,oBACxB,uBAAGpQ,UAAU,kBAAb,kBAKP0mB,EACC,yBAAK1mB,UAAU,UACb,kBAAC,mBAAD,CAAkBM,GAAG,iBAAiByX,eAAe,aAE9C0P,GAAqB5nB,KAAKC,MAAM0nB,UAAY,2BAAIC,KAGvD,QAYd,SAASF,GAAgBvf,EAAOkD,EAAMuG,EAAQ7S,GAC5C,IAAI8oB,EAAKZ,SAAOa,QAAQ3f,GACxB,GAAI0f,EAAI,CACN,IAAIE,EAAOd,SAAOe,UAAU7f,EAAOkD,IAAS,GAE5C,OADA0c,EAAKhpB,IAAMA,EACHoJ,GACN,IAAK,KAEH,GAAIkD,EAAM,CACR0c,EAAK5nB,UAAY,eACjB,IAAIiN,EAAM9B,EAAaD,EAAKE,MAAOF,EAAKG,OACtC7B,KAAKC,IAAI5J,KAAKC,MAAMgoB,cAAgBC,GAAcA,KAAgBA,KAAe,GACnF9a,EAAMA,GAAO,CAACtB,S5BlIS,G4BkIoBC,U5BlIpB,I4BmIvBgc,EAAK5f,MAAQ,CAAEoD,MAAO6B,EAAItB,SAAW,KAAMN,OAAQ4B,EAAIrB,UAAY,MACnEgc,EAAKjd,INvDR,SAA0BlJ,GAC/B,IAAKA,EACH,OAAO,KAGT,MAAMumB,EAAetM,GAAYja,GACjC,OAAIumB,IAKA,mCAAmCpM,KAAKna,EAAI8I,QACvC9I,EAGF,MMwCYwmB,CAAiBL,EAAKjd,KAC7Bid,EAAKjd,KACPid,EAAKxnB,QAAUP,KAAK+lB,mBACpBgC,EAAK5nB,WAAa,oBAElB4nB,EAAKjd,IAAM,uBAGf,MACF,IAAK,KAEHid,EAAKxnB,QAAUP,KAAKgmB,sBACpB,IAAIqC,EAAQ7oB,IAAM8oB,SAAS/gB,IAAIqK,EAAS2W,GACf,iBAATA,EAAoBA,OAAQxS,GAEvCsS,GAAyB,GAAhBA,EAAMnmB,SAClBmmB,EAAQ,CAACN,EAAKhqB,OAGhBgqB,EAAK,cAAgBM,EAAMtmB,KAAK,IAChC,MACF,IAAK,KAEHgmB,EAAK5nB,UAAY,WAMrB,OAAOX,IAAM8N,cAAcua,EAAIE,EAAMnW,GAErC,OAAOA,EC3MX,MAAMnP,GAAWC,yBAAe,CAC9B,wFAKA,wEAKA,6EAKA,4HAKA,0GAOF,MAAM8lB,WAAoBhpB,IAAMM,cAC9B4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXxJ,QAAS,GAETujB,mBAAmB,IAAI9f,MAAO+f,U7BzBN,I6ByBmC,GAG7D1oB,KAAK2oB,iBAAmB3oB,KAAK2oB,iBAAiB3pB,KAAKgB,MACnDA,KAAK4oB,kBAAoB5oB,KAAK4oB,kBAAkB5pB,KAAKgB,MACrDA,KAAK6oB,iBAAmB7oB,KAAK6oB,iBAAiB7pB,KAAKgB,MACnDA,KAAK8oB,WAAa9oB,KAAK8oB,WAAW9pB,KAAKgB,MACvCA,KAAK+oB,eAAiB/oB,KAAK+oB,eAAe/pB,KAAKgB,MAC/CA,KAAKgpB,oBAAsBhpB,KAAKgpB,oBAAoBhqB,KAAKgB,MAG3DuF,oBACMvF,KAAKipB,iBACPjpB,KAAKipB,gBAAgBxjB,iBAAiB,QAASzF,KAAK2oB,kBAAkB,GAI1EjjB,uBACM1F,KAAKipB,iBACPjpB,KAAKipB,gBAAgBtjB,oBAAoB,QAAS3F,KAAK2oB,kBAAkB,GAI7E9Z,qBACM7O,KAAKipB,iBACPjpB,KAAKipB,gBAAgB9P,QAIzBwP,iBAAiB/iB,GACX5F,KAAKC,MAAMipB,UxBqFZ,SAAoB9P,EAAO+P,EAAgBC,EAAqB5iB,GACrE,IAAIJ,GAASgT,EAAMiQ,eAAiBjQ,EAAMkQ,cAAcD,eAAiB,IAAIjjB,MAC7E,IAAK,IAAI5I,KAAK4I,EAAO,CACnB,IAAI1E,EAAO0E,EAAM5I,GACjB,GAAkB,SAAdkE,EAAK6nB,KAAiB,CACxB,IAAIzc,EAAOpL,EAAK8nB,YAChB,IAAK1c,EAAM,CACT3H,QAAQC,IAAI,kDAAmD1D,EAAK6nB,KAAM7nB,EAAK0J,MAC/E,SAcF,OAZI0B,EAAK1B,MAAmC,SAA3B0B,EAAK1B,KAAKhK,MAAM,KAAK,GAEhC0L,EAAKjB,KL/GyB,QK+GYb,EAAwBwB,QAAQM,EAAK1B,MAAQ,EACzFyB,EAAwBC,ELxGL,SKwGyC,EAAOqc,EAAgB3iB,GAEnF0H,EAAkBpB,EAAMqc,EAAgB3iB,GAI1C8H,EAAaxB,EAAMsc,IAGd,GAIX,OAAO,EwB3GDK,CAAW7jB,EACb,CAAC8jB,EAAMpd,EAAMf,EAAOC,EAAQa,KAC1BrM,KAAKC,MAAM0pB,cAAcrd,EAAMod,EAAMne,EAAOC,EAAQa,IAEtD,CAACC,EAAMod,EAAMrd,KACXrM,KAAKC,MAAM2pB,aAAatd,EAAMod,EAAMrd,IAEtCrM,KAAKC,MAAMuG,UAGXZ,EAAEI,iBAIN4iB,kBAAkBhjB,GACZA,EAAEG,OAAOiJ,OAASpJ,EAAEG,OAAOiJ,MAAM9M,OAAS,GAC5ClC,KAAKC,MAAM0pB,cAAc/jB,EAAEG,OAAOiJ,MAAM,IAG1CpJ,EAAEG,OAAOtH,MAAQ,GAGnBoqB,iBAAiBjjB,GACf,MAAM,cAAChC,GAAiB5D,KAAKC,MAAM4D,KAC/B+B,EAAEG,OAAOiJ,OAASpJ,EAAEG,OAAOiJ,MAAM9M,OAAS,GAC5ClC,KAAKC,MAAM2pB,aAAahkB,EAAEG,OAAOiJ,MAAM,IAGzCpJ,EAAEG,OAAOtH,MAAQ,GAGnBqqB,WAAWljB,GACTA,EAAEI,iBACF,MAAMd,EAAUlF,KAAK0O,MAAMxJ,QAAQwF,QAC/BxF,GAAWlF,KAAKC,MAAM4pB,aAAe7pB,KAAKC,MAAM6pB,WAClD9pB,KAAKC,MAAM8pB,cAAc7kB,GACzBlF,KAAK+O,SAAS,CAAC7J,QAAS,MAK5B6jB,eAAenjB,GAEC,UAAVA,EAAE7G,MAEC6G,EAAEokB,WACLpkB,EAAEI,iBACFJ,EAAEK,kBAEFjG,KAAK8oB,WAAWljB,KAKtBojB,oBAAoBpjB,GAClB,MAAM6a,EAAW,CAACvb,QAASU,EAAEG,OAAOtH,OACpC,GAAIuB,KAAKC,MAAMgqB,WAAY,CACzB,MAAMvhB,GAAM,IAAIC,MAAO+f,UACnBhgB,EAAM1I,KAAK0O,MAAM+Z,kB7BrHG,M6BsHtBzoB,KAAKC,MAAMgqB,aACXxJ,EAASgI,kBAAoB/f,GAGjC1I,KAAK+O,SAAS0R,GAGhB1gB,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KAC7BmR,EAAShV,KAAKC,MAAMipB,SACxBtlB,EAAcnB,GAASynB,oBACtBlqB,KAAKC,MAAMkqB,cACVvmB,EAAcnB,GAASzC,KAAKC,MAAMkqB,gBAClCvmB,EAAcnB,GAAS2nB,kBAC3B,OACE,yBAAK3pB,GAAG,sBACJT,KAAKC,MAAMipB,SA6BX,yBAAKzoB,GAAG,oBAAoBuU,GA5B5B,oCACGhV,KAAKC,MAAM2pB,aACV,oCACE,uBAAGra,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKqqB,YAAYC,SAAWlqB,MAAM,aACjF,uBAAGD,UAAU,4BAAb,UAEF,uBAAGoP,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKuqB,WAAWD,SAAWlqB,MAAM,eAChF,uBAAGD,UAAU,4BAAb,iBAIJ,KACDH,KAAKC,MAAM6pB,QACV,yBAAK3pB,UAAU,YACf,8BAAUM,GAAG,cAAcqU,YAAaE,EACtCvW,MAAOuB,KAAK0O,MAAMxJ,QAASwK,SAAU1P,KAAKgpB,oBAC1CiB,WAAYjqB,KAAK+oB,eACjB1C,IAAMA,IAASrmB,KAAKipB,gBAAkB5C,GACtCjQ,WAAS,IACb,uBAAG7G,KAAK,IAAIhP,QAASP,KAAK8oB,WAAY1oB,MAAM,QAC1C,uBAAGD,UAAU,kBAAb,SAEF,2BAAOiL,KAAK,OAAOib,IAAMA,IAASrmB,KAAKuqB,WAAalE,GAClD3W,SAAU1P,KAAK6oB,iBAAkB1gB,MAAO,CAACqiB,QAAS,UACpD,2BAAOpf,KAAK,OAAOib,IAAMA,IAASrmB,KAAKqqB,YAAchE,GAAO5W,OAAO,UACjEC,SAAU1P,KAAK4oB,kBAAmBzgB,MAAO,CAACqiB,QAAS,aAUlDpiB,4BAAWogB,IC9KX,MAAMiC,WAAmBjrB,IAAMM,cAC5C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0qB,cAAgB1qB,KAAK0qB,cAAc1rB,KAAKgB,MAG/C0qB,cAAcC,GACZ3qB,KAAKC,MAAM2qB,UACX5qB,KAAKC,MAAM8pB,cAAc/pB,KAAKC,MAAMI,QAAQyM,MAG9C/M,SACE,OAAKC,KAAKC,MAAMI,QAId,yBAAKI,GAAG,iBACN,yBAAKA,GAAG,+BACN,8BAAOT,KAAKC,MAAMI,QAAQikB,UAC1B,uBAAG/U,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAM2qB,YAAa,uBAAGzqB,UAAU,uBAAb,WAE3E,yBAAKM,GAAG,2BACN,yBAAKN,UAAU,sBACb,uBAAGA,UAAU,uBA/BzB,SAAsBmM,GAEpB,MAAMue,EAAa,CAACC,QAAS,oBAAqBC,MAAO,QAASnX,KAAM,cAAeoX,MAAO,YAE9F,OAAOH,EAAWve,IAASue,GAAYve,GAAQ,IAAIlL,MAAM,KAAK,KAAOypB,EAAU,QA2BjCI,CAAajrB,KAAKC,MAAMI,QAAQ+K,OACpE,6BAAK,2BAAG,kBAAC,mBAAD,CAAkB3K,GAAG,wBAA7B,IAA0DT,KAAKC,MAAMI,QAAQ+K,MAAQ,4BACrF,6BAAK,2BAAG,kBAAC,mBAAD,CAAkB3K,GAAG,gBAA7B,IAAkD8I,EAAiBvJ,KAAKC,MAAMI,QAAQwL,SAG1F,kBAAC,GAAD,CACEie,SAAS,EACTC,cAAe/pB,KAAK0qB,cACpBlkB,QAASxG,KAAKC,MAAMuG,WAlBjB,MClBE,MAAM0kB,WAAkB1rB,IAAMiE,UAC3CC,YAAYzD,GACV0D,MAAM1D,GAGRF,SACE,MAAMorB,EAAc,GACdC,GAAcprB,KAAKC,MAAM6gB,aAAe,IAAI5e,OAC5CmpB,EAAc1hB,KAAKC,I/BqDM,E+BrDmBwhB,GAclD,OAZCprB,KAAKC,MAAM6gB,aAAe,IAAIwK,KAAMvK,IACnCoK,EAAYlpB,KACV,yBAAK9B,UAAU,aAAapB,IAAKgiB,EAAI1b,MACnC,kBAAC,EAAD,CACE9C,MAAOwe,EAAI1b,KACX8E,OAAQe,EAAa6V,EAAIrO,OAASqO,EAAIrO,OAAOvH,MAAQ,QAAS,EAC9D/K,MAAO2gB,EAAIrO,OAASqO,EAAIrO,OAAOC,GAAK,SAGnCwY,EAAYjpB,QAAUmpB,IAI7B,yBAAK5qB,GAAG,eAAe0qB,EAAvB,IAAqCC,EAAaC,EAChD,8BACE,kBAAC,mBAAD,CAAkB5qB,GAAG,sBAAsByX,eAAe,mBAEtDtG,OAAQ,CAAE2Z,SAAWH,EAAaC,MAC9B,OC9BH,MAAMG,WAAqBhsB,IAAMM,cAC9C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXnD,MAAO,EACPC,OAAQ,GAGVxL,KAAKyrB,gBAAkBzrB,KAAKyrB,gBAAgBzsB,KAAKgB,MAGnD0rB,YAAYC,GACV,GAAIA,IAAS3rB,KAAK0O,MAAMnD,MAAO,CAC7B,MAAM3D,EAAS+jB,EAAKC,wBACpB5rB,KAAK+O,SAAS,CACZxD,MAAsB,EAAf3D,EAAO2D,MACdC,OAAwB,EAAhB5D,EAAO4D,UAKrBigB,gBAAgBd,GACd3qB,KAAKC,MAAM2qB,UACX5qB,KAAKC,MAAM8pB,cAAcY,EAAS3qB,KAAKC,MAAMI,QAAQ+K,KAAMpL,KAAKC,MAAMI,QAAQqpB,KAC5E1pB,KAAKC,MAAMI,QAAQkL,MAAOvL,KAAKC,MAAMI,QAAQmL,OAAQxL,KAAKC,MAAMI,QAAQikB,UAG5EvkB,SACE,IAAKC,KAAKC,MAAMI,QACd,OAAO,KAGT,MAAM+M,EAAM9B,EAAatL,KAAKC,MAAMI,QAAQkL,MAAOvL,KAAKC,MAAMI,QAAQmL,OACpExL,KAAK0O,MAAMnD,MAAOvL,KAAK0O,MAAMlD,QAAQ,GACjCK,EAAOuB,EAAM,CAAE7B,MAAO6B,EAAItB,SAAW,KAAMN,OAAQ4B,EAAIrB,UAAY,MACrE/L,KAAKC,MAAMI,QAAQkL,MAAQvL,KAAKC,MAAMI,QAAQmL,OAAU,CAACD,MAAO,QAAU,CAACC,OAAQ,QACvFK,EAAKJ,SAAW,OAChBI,EAAKH,UAAY,OAEjB,IAAI4Y,EAAWtkB,KAAKC,MAAMI,QAAQikB,SAElC,MAAMuH,EAAYliB,KAAKmiB,KAAM9rB,KAAK0O,MAAMnD,MhCTpB,GgCSuC,IAAO,GAAK,EAAG,IACtE+Y,EAASpiB,OAAS2pB,IACpBvH,EAAWA,EAASvT,MAAM,EAAG8a,EAAU,EAAI,GAAK,IAAMvH,EAASvT,MAAM,EAAI8a,EAAU,IAErF,MAAMtgB,EAAQvL,KAAKC,MAAMI,QAAQkL,OAAS,IACpCC,EAASxL,KAAKC,MAAMI,QAAQmL,QAAU,IAC5C,OACE,yBAAK/K,GAAG,iBACN,yBAAKA,GAAG,+BACJT,KAAKC,MAAM8pB,cAMX,8BAAO/pB,KAAKC,MAAMI,QAAQikB,UAL1B,uBAAG/U,KAAMvP,KAAKC,MAAMI,QAAQuB,IAAK6iB,SAAUzkB,KAAKC,MAAMI,QAAQikB,UAC5D,uBAAGnkB,UAAU,kBAAb,iBADF,IACkD,kBAAC,mBAAD,CAC9CM,GAAG,kBAAkByX,eAAe,cAK1C,uBAAG3I,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAM2qB,YAAa,uBAAGzqB,UAAU,uBAAb,WAE3E,yBAAKM,GAAG,0BAA0B4lB,IAAMsF,GAAS3rB,KAAK0rB,YAAYC,IAChE,yBAAK7gB,IAAK9K,KAAKC,MAAMI,QAAQuB,IAAKuG,MAAO0D,KAE1C7L,KAAKC,MAAM8pB,cACV,kBAAC,GAAD,CACEI,cAAc,oBACdN,aAAa,EACbE,cAAe/pB,KAAKyrB,gBACpBjlB,QAASxG,KAAKC,MAAMuG,UAEtB,yBAAK/F,GAAG,wBACN,6BACE,6BAAK,2BAAG,kBAAC,mBAAD,CAAkBA,GAAG,kBAAkByX,eAAe,iBAE9D,6BAAK,0BAAM9X,MAAOJ,KAAKC,MAAMI,QAAQikB,UAAWA,GAAsB,OAExE,6BACE,6BAAK,2BAAG,kBAAC,mBAAD,CAAkB7jB,GAAG,qBAAqByX,eAAe,oBAEjE,6BAAMlY,KAAKC,MAAMI,QAAQ+K,OAE3B,6BACE,6BAAK,2BAAG,kBAAC,mBAAD,CAAkB3K,GAAG,aAAayX,eAAe,YAEzD,6BAAM3M,EAAN,MAAsBC,EAAtB,QAAmCjC,EAAiBvJ,KAAKC,MAAMI,QAAQwL,WCzFtE,MAAMkgB,WAAmBvsB,IAAMM,cAC5C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAKgsB,mBAAqBhsB,KAAKgsB,mBAAmBhtB,KAAKgB,MAGzDgsB,mBAAmBC,EAAK5gB,GACtB4gB,EAAIjmB,iBACJhG,KAAKC,MAAMsG,SAAS8E,GAGtBtL,SACE,OACE,yBAAKI,UAAU,uBACb,yBAAKA,UAAU,SACb,kBAAC,mBAAD,CAAkBM,GAAG,kBACnByX,eAAe,qEAGnB,yBAAK/X,UAAU,UACb,4BAAQA,UAAU,OAAOI,QAAS6Y,IAAWpZ,KAAKgsB,mBAAmB5S,EAAO,YAC1E,kBAAC,mBAAD,CAAkB3Y,GAAG,yBACnByX,eAAe,YAEnB,4BAAQ/X,UAAU,QAAQI,QAAS6Y,IAAWpZ,KAAKgsB,mBAAmB5S,EAAO,YAC3E,kBAAC,mBAAD,CAAkB3Y,GAAG,yBACnByX,eAAe,YAEnB,4BAAQ/X,UAAU,QAAQI,QAAS6Y,IAAWpZ,KAAKgsB,mBAAmB5S,EAAO,WAC3E,kBAAC,mBAAD,CAAkB3Y,GAAG,wBACnByX,eAAe,cCjCd,MAAMgU,WAAoB1sB,IAAMM,cAC7CC,SACE,OAAQC,KAAKC,MAAMuT,KACjB,yBAAKrT,UAAU,oBAAmB,yBAAKA,UAAU,oBAAgC,MCGxE,MAAMgsB,WAAiB3sB,IAAMM,cAC1CC,SACE,MAAMqsB,EAAUxrB,EAAW,KAAOlB,IAAO2sB,aAAe,IACxD,OACE,yBAAK5rB,GAAG,cACN,6BACE,uBAAG8O,KAAK,mCACN,yBAAK9O,GAAG,OAAOoK,IAAI,OAAOC,IAAI,iBAC9B,2CAEF,2BAAG,kBAAC,mBAAD,CAAkBrK,GAAG,eAAeyX,eAAe,YAAtD,IAAoEkU,GACpE,2BAAG,kBAAC,mBAAD,CAAkB3rB,GAAG,eAAeyX,eAAe,YAAtD,IAAoElY,KAAKC,MAAMqsB,cAA/E,KAAgGtsB,KAAKC,MAAMssB,cAA3G,QCfV,MAAMtF,GAASvnB,IAAOunB,OAsBhBxkB,GAAWC,yBAAe,CAC9B8pB,WAAY,CAAF,6CAKVC,UAAW,CAAF,qDAKT,8DAQF,SAASC,GAAchc,GACrB,GAAIA,EAAK,CACP,MAAMic,EAAKjc,EAAIkc,gBAAkB,GACjC,OAAOlc,EAAIU,SAAS,WAAaub,EAAGlX,SAAS,MAAQkX,EAAGlX,SAAS,MAEnE,OAAO,EAGT,SAASoX,GAAiBnc,GACxB,GAAIA,EAAK,CACP,MAAMoc,EAAKpc,EAAIqc,cAAgB,GAC/B,OAAOrc,EAAIU,SAAS,UAAY0b,EAAGrX,SAAS,MAAQqX,EAAGrX,SAAS,MAElE,OAAO,EAGT,MAAMuX,WAAqBxtB,IAAMiE,UAC/BC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQse,GAAaC,yBAAyBhtB,EAAO,IAE1DD,KAAKktB,MAAQltB,KAAKktB,MAAMluB,KAAKgB,MAC7BA,KAAKmtB,oBAAsBntB,KAAKmtB,oBAAoBnuB,KAAKgB,MACzDA,KAAKotB,mBAAqBptB,KAAKotB,mBAAmBpuB,KAAKgB,MACvDA,KAAKqtB,aAAertB,KAAKqtB,aAAaruB,KAAKgB,MAC3CA,KAAKstB,sBAAwBttB,KAAKstB,sBAAsBtuB,KAAKgB,MAC7DA,KAAKutB,kBAAoBvtB,KAAKutB,kBAAkBvuB,KAAKgB,MACrDA,KAAKwtB,iBAAmBxtB,KAAKwtB,iBAAiBxuB,KAAKgB,MACnDA,KAAKytB,kBAAoBztB,KAAKytB,kBAAkBzuB,KAAKgB,MACrDA,KAAK0tB,iBAAmB1tB,KAAK0tB,iBAAiB1uB,KAAKgB,MACnDA,KAAK2tB,0BAA4B3tB,KAAK2tB,0BAA0B3uB,KAAKgB,MACrEA,KAAK4tB,kBAAoB5tB,KAAK4tB,kBAAkB5uB,KAAKgB,MACrDA,KAAK6tB,oBAAsB7tB,KAAK6tB,oBAAoB7uB,KAAKgB,MACzDA,KAAK8tB,mBAAqB9tB,KAAK8tB,mBAAmB9uB,KAAKgB,MACvDA,KAAK+tB,mBAAqB/tB,KAAK+tB,mBAAmB/uB,KAAKgB,MACvDA,KAAKuQ,mBAAqBvQ,KAAKuQ,mBAAmBvR,KAAKgB,MACvDA,KAAKguB,6BAA+BhuB,KAAKguB,6BAA6BhvB,KAAKgB,MAC3EA,KAAKiuB,wBAA0BjuB,KAAKiuB,wBAAwBjvB,KAAKgB,MACjEA,KAAKkuB,iBAAmBluB,KAAKkuB,iBAAiBlvB,KAAKgB,MACnDA,KAAK6oB,iBAAmB7oB,KAAK6oB,iBAAiB7pB,KAAKgB,MACnDA,KAAK4oB,kBAAoB5oB,KAAK4oB,kBAAkB5pB,KAAKgB,MACrDA,KAAKmuB,qBAAuBnuB,KAAKmuB,qBAAqBnvB,KAAKgB,MAC3DA,KAAKouB,uBAAyBpuB,KAAKouB,uBAAuBpvB,KAAKgB,MAE/DA,KAAKquB,sBAAwB,GAC7BruB,KAAKsuB,sBAAwB,KAG/B/oB,oBAEMvF,KAAKuuB,kBACPvuB,KAAKuuB,iBAAiB9oB,iBAAiB,SAAUzF,KAAKutB,mBAI1D7nB,uBACM1F,KAAKuuB,kBACPvuB,KAAKuuB,iBAAiB5oB,oBAAoB,SAAU3F,KAAKutB,mBAI3DvtB,KAAKouB,yBAKPvf,mBAAmBC,EAAWwG,GACxBtV,KAAKuuB,mBACHjZ,EAAU/S,OAASvC,KAAK0O,MAAMnM,OAAS+S,EAAU7S,SAASP,QAAUlC,KAAK0O,MAAMjM,SAASP,OAE1FlC,KAAKuuB,iBAAiBC,UAAYxuB,KAAKuuB,iBAAiBE,aAAezuB,KAAK0O,MAAMggB,eACzE5f,EAAU6f,eAAiB3uB,KAAKC,MAAM0uB,iBAE/C3uB,KAAKuuB,iBAAiBC,WAAa1f,EAAU6f,eAAiB3uB,KAAKC,MAAM0uB,iBAI7E,MAAMpsB,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAK0O,MAAMnM,OAwBpD,GAvBIvC,KAAK0O,MAAMnM,OAAS+S,EAAU/S,QAC5B+S,EAAU/S,QAAU7C,IAAOkvB,oBAAoBtZ,EAAU/S,QAC3DvC,KAAKktB,MAAM5X,EAAU/S,OAGnBA,IACFA,EAAMssB,OAAS7uB,KAAK0tB,iBACpBnrB,EAAMusB,sBAAwB9uB,KAAK2tB,0BACnCprB,EAAMwsB,OAAS/uB,KAAK4tB,kBACpBrrB,EAAM4c,WAAanf,KAAKwtB,iBACxBjrB,EAAM6c,cAAgBpf,KAAKytB,kBAC3BlrB,EAAMysB,OAAShvB,KAAKytB,oBAInBztB,KAAKC,MAAMgvB,mBAKdjvB,KAAKmuB,qBAAqB,GAH1BnuB,KAAKouB,yBAMH7rB,IAAUA,EAAM2sB,gBAAkBlvB,KAAKC,MAAMkvB,QAC3CnvB,KAAK0O,MAAMnM,OAAS+S,EAAU/S,QAAWuM,EAAUqgB,OAAQ,CAE/D,MAAMC,EAAYpvB,KAAKC,MAAMovB,gBAAkBrvB,KAAKC,MAAMovB,eAAeC,YAActvB,KAAKC,MAAMsC,MAIlG,IAAIgtB,EAAWhtB,EAAMgf,iBAAiBiO,gBAAgBC,gBAClDzvB,KAAK0O,MAAMghB,UAAYN,KAEzBG,EAAWA,EAASI,cpC1GC,IoC2GjB3vB,KAAK0O,MAAMghB,WACbH,EAAWA,EAASK,gBAGtB5vB,KAAK+O,SAAS,CAAE8gB,kBAAkB,KAEpC,MAAMC,EAAWV,EAAWpvB,KAAKC,MAAMovB,oBAAiBtZ,EACxDxT,EAAMwtB,UAAUR,EAAS9N,QAASqO,GAC/BrrB,KAAMC,IACD1E,KAAK0O,MAAMnM,OAASmC,EAAKnC,OAC3BvC,KAAK+O,SAAS,CAACxM,MAAOmC,EAAKnC,QAE7BvC,KAAKC,MAAM+vB,kBAAkBhwB,KAAKC,MAAMsC,MAAOmC,EAAKnC,OAEpDA,EAAM0tB,eAAgBC,KACfA,EAAIC,UAAY5tB,EAAM2sB,gBACzB3sB,EAAM6tB,eAAeF,OAI1BlrB,MAAOC,IACNE,QAAQC,IAAI,yBAA0BpF,KAAK0O,MAAMnM,OACjDvC,KAAKC,MAAMuG,QAAQvB,EAAIC,QAAS,OAChC,MAAMmrB,EAAarD,GAAaC,yBAAyB,GAAI,IAC7DoD,EAAWjwB,MAAQJ,KAAKC,MAAM4D,KAAKD,cAAcnB,GAAS6tB,WAC1DtwB,KAAK+O,SAASshB,MAKtB,gCAAgC5T,EAAWnH,GACzC,IAAIib,EAAY,GAChB,GAAK9T,EAAUla,OAgBR,GAAIka,EAAUla,OAAS+S,EAAU/S,MAAO,CAC7C,MAAMA,EAAQka,EAAU5X,OAAOC,SAAS2X,EAAUla,OAWlD,GAVAguB,EAAY,CACVhuB,MAAOka,EAAUla,MACjBiuB,WAAY,KACZC,aAAc,KACdC,cAAe,KACfC,iBAAiB,EACjBjC,eAAgB,EAChBmB,kBAAkB,GAGhBttB,EAAO,CAET,MAAMquB,EAAO,GACPjP,EAAO,GAETlF,EAAU1J,WACZxQ,EAAMue,YAAaC,IACbA,EAAIjQ,QAAUiQ,EAAI1b,MAAQoX,EAAU7J,UACtC+O,EAAK1f,KAAK8e,KAKhBxe,EAAME,UAAS,SAASuE,GACjBA,EAAIyf,SACPmK,EAAK3uB,KAAK+E,MAId9I,OAAO2yB,OAAON,EAAW,CACvB9tB,SAAUmuB,EACVE,WAAYnP,IAGVpf,EAAMmQ,OACRxU,OAAO2yB,OAAON,EAAW,CACvBnwB,MAAOmC,EAAMmQ,OAAOC,GACpBxI,OAAQe,EAAa3I,EAAMmQ,OAAOvH,SAGpCjN,OAAO2yB,OAAON,EAAW,CACvBnwB,MAAO,GACP+J,OAAQ,OAIZ,MAAM4mB,EAAOxuB,EAAMyuB,cACfD,EACF7yB,OAAO2yB,OAAON,EAAW,CACvBU,sBAAuBpE,GAAiBkE,EAAKrgB,OAEtC4E,EAAU2b,uBACnB/yB,OAAO2yB,OAAON,EAAW,CACvBU,uBAAuB,SAK3B/yB,OAAO2yB,OAAON,EAAW,CACvB9tB,SAAU,GACVquB,WAAY,GACZ1wB,MAAO,GACP+J,OAAQ,KACR8mB,uBAAuB,UA/E3BV,EAAY,CACV9tB,SAAU,GACVquB,WAAY,GACZvuB,MAAO,KACPnC,MAAO,GACP+J,OAAQ,KACRqmB,WAAY,KACZC,aAAc,KACdC,cAAe,KACfC,iBAAiB,EACjBjC,eAAgB,EAChBmB,kBAAkB,EAClBoB,uBAAuB,GAuG3B,OA/BIxU,EAAU/L,KACR+L,EAAU/L,IAAIwgB,YAAc5b,EAAU4b,WACxCX,EAAUW,UAAY5b,EAAU4b,UAE9BzU,EAAU/L,IAAIgf,YAAcpa,EAAUoa,WACxCa,EAAUb,UAAYpa,EAAUoa,WAE7BjT,EAAU/L,IAAIgf,SAAS,UAAYpa,EAAU6b,iBAChDZ,EAAUY,gBAAkB7b,EAAU6b,kBAGpC7b,EAAU4b,WACZX,EAAUW,UAAW,GAEnB5b,EAAUoa,WACZa,EAAUb,UAAW,GAElBpa,EAAU6b,iBACb7b,EAAU6b,gBAAiB,IAI3BzE,GAAcjQ,EAAU/L,OAAS4E,EAAU8b,cAC7Cb,EAAUc,aAAe/b,EAAU8b,cAIhC3U,EAAU1J,WAAauC,EAAUwb,YAAcxb,EAAUwb,WAAW5uB,OAAS,IAChFquB,EAAUO,WAAa,IAGlBP,EAGTrD,MAAMoE,GACJ,IAAKA,EACH,OAEF,IAAIC,EAAWvxB,KAAKC,MAAM4E,OAAOC,SAASwsB,GACtCC,GAAYA,EAASrC,gBACvBqC,EAASrE,OAAM,GACZloB,MAAM,QACNwsB,QAAQ,KAGPxxB,KAAK+O,SAAS,CAAC8gB,kBAAkB,IACjC0B,EAAS1C,YAAS9Y,EAClBwb,EAASzC,2BAAwB/Y,EACjCwb,EAASxC,YAAShZ,EAClBwb,EAASpS,gBAAapJ,EACtBwb,EAASnS,mBAAgBrJ,EACzBwb,EAASvC,YAASjZ,IAK1BuX,sBAAsB3B,GAChBA,IACFA,EAAKlmB,iBAAiB,SAAUzF,KAAKutB,mBACrCvtB,KAAKuuB,iBAAmB5C,EACxB3rB,KAAKuuB,iBAAiBC,UAAYxuB,KAAKuuB,iBAAiBE,aAAezuB,KAAK0O,MAAMggB,gBAKtFnB,kBAAkBnU,GAChBpZ,KAAK+O,SAAS,CAAC2f,eAAgBtV,EAAMrT,OAAO0oB,aAAerV,EAAMrT,OAAOyoB,YACpEpV,EAAMrT,OAAOyoB,WAAa,GAC5BxuB,KAAK+O,SAAS,CAACuG,EAAWrV,KACxB,MAAMwgB,EAAW,GACjB,IAAKnL,EAAUua,iBAAkB,CAC/B,MAAMttB,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAK0O,MAAMnM,OAChDA,GAASA,EAAM2sB,gBAAkB3sB,EAAMkvB,uBACzChR,EAASoP,kBAAmB,EAC5BttB,EAAMmvB,gBpC3SW,IoC2SoB1sB,MAAOC,IAC1CjF,KAAK+O,SAAS,CAAC8gB,kBAAkB,IACjC7vB,KAAKC,MAAMuG,QAAQvB,EAAIC,QAAS,UAItC,OAAOub,IAKb+M,iBAAiB9L,GACXA,EAAKhP,OACP1S,KAAK+O,SAAS,CACZ3O,MAAOshB,EAAKhP,OAAOC,GACnBxI,OAAQe,EAAawW,EAAKhP,OAAOvH,SAGnCnL,KAAK+O,SAAS,CACZ3O,MAAO,GACP+J,OAAQ,OAIRuX,EAAKhR,KACP1Q,KAAK+O,SAAS,CACZmiB,SAAUxP,EAAKhR,IAAIwgB,WACnBxB,SAAUhO,EAAKhR,IAAIgf,WACnByB,gBAAiBzP,EAAKhR,IAAIgf,SAAS,SACnC2B,YAAa3E,GAAchL,EAAKhR,OAKtCyd,qBAAqBvnB,GAEnB,IAAK5G,KAAKC,MAAMgvB,mBACd,OAIGjvB,KAAKsuB,wBACRtuB,KAAKsuB,sBAAwBqD,YAAY,KACvC,GAAyC,GAArC3xB,KAAKquB,sBAAsBnsB,OAI7B,OAFA0vB,cAAc5xB,KAAKsuB,4BACnBtuB,KAAKsuB,sBAAwB,MAI/B,IAAI1nB,GAAO,EACX,KAAO5G,KAAKquB,sBAAsBnsB,OAAS,GAAG,CAC5C,MAAMjD,EAAIe,KAAKquB,sBAAsB,GACrC,GAAIpvB,EAAE2F,WAAa5E,KAAK0O,MAAMnM,MAAO,CAEnCvC,KAAKquB,sBAAsBwD,QAC3B,SAGF,MAAMnpB,EAAM,IAAIC,KAChB,KAAI1J,EAAE6yB,QAAUppB,GAKd,MAHA1I,KAAKquB,sBAAsBwD,QAC3BjrB,EAAM+C,KAAKmiB,IAAIllB,EAAK3H,EAAE2H,KAO1B,GAAIA,GAAO,EAAG,CACZ,MAAMrE,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAK0O,MAAMnM,OAChDA,GACFA,EAAMwvB,SAASnrB,KA/YQ,MAqZ/B,MAAM8B,EAAM,IAAIC,KAChB3I,KAAKquB,sBAAsBpsB,KAAK,CAC9B2C,UAAW5E,KAAK0O,MAAMnM,MACtBqE,IAAKA,EACLkrB,OAAQppB,EAAIspB,gBAAgBtpB,EAAIupB,kBpC3ZZ,OoCgaxB7D,yBACEpuB,KAAKquB,sBAAwB,GACzBruB,KAAKsuB,wBACPsD,cAAc5xB,KAAKsuB,uBACnBtuB,KAAKsuB,sBAAwB,MAIjCb,oBACE,GAAIztB,KAAK0O,MAAMnM,MAAO,CACpB,MAAMof,EAAO,GACPpf,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAK0O,MAAMnM,OACpDA,EAAMue,YAAaC,IACbA,EAAIjQ,QAAUiQ,EAAI1b,MAAQrF,KAAKC,MAAM2S,UACvC+O,EAAK1f,KAAK8e,KAGd,MAAMN,EAAW,CAACqQ,WAAYnP,GACxBoP,EAAOxuB,EAAMyuB,cACfD,EACF7yB,OAAO2yB,OAAOpQ,EAAU,CACtBwQ,sBAAuBpE,GAAiBkE,EAAKrgB,OAEtC1Q,KAAK0O,MAAMuiB,uBACpB/yB,OAAO2yB,OAAOpQ,EAAU,CACtBwQ,uBAAuB,IAG3BjxB,KAAK+O,SAAS0R,IAIlBiN,iBAAiB1mB,GAEf,MAAMzE,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAK0O,MAAMnM,OAC9Cke,EAAW,CAAChe,SAAU,IAQ5B,GAPAF,EAAME,SAAU7E,IACTA,EAAE6oB,SACLhG,EAAShe,SAASR,KAAKrE,KAKvBoJ,IAAQA,EAAIyf,QAAS,CAGnBlkB,EAAM2vB,aAAalrB,EAAIJ,OACzB6Z,EAASiO,eAAiB,GAKbnsB,EAAM4vB,UAAUnrB,IACjBtH,IAAO+lB,qBAAuBze,EAAIorB,MAAQpyB,KAAKC,MAAM2S,UACjE5S,KAAKmuB,qBAAqBnnB,EAAIJ,KAEhC5G,KAAKC,MAAM4uB,OAAO7nB,GAEpBhH,KAAK+O,SAAS0R,GAGhBkN,0BAA0BtmB,GACxBrH,KAAK+O,SAAS,CAAC8gB,kBAAkB,IAC7BxoB,EAAQ,GAEVrH,KAAKmuB,qBAAqB,GAI9BP,kBAAkBprB,GAChB,OAAQA,EAAK6vB,MACX,IAAK,KACHC,aAAatyB,KAAKuyB,eAClB,IAAI1a,EAAW7X,KACfA,KAAKuyB,cAAgB9Z,YAAW,WAC9BZ,EAAS9I,SAAS,CAAC4hB,iBAAiB,MACnC6B,KACExyB,KAAK0O,MAAMiiB,iBACd3wB,KAAK+O,SAAS,CAAC4hB,iBAAiB,IAElC,MAEF,IAAK,OACL,IAAK,OAEH3wB,KAAKyyB,cACL,MACF,QACEttB,QAAQC,IAAI,0BAA2B5C,EAAK6vB,OAIlDxE,oBAAoBxtB,GAClBL,KAAK+O,SAAS,CAAE2hB,cAAerwB,IAGjCytB,qBACE9tB,KAAK+O,SAAS,CAAE2hB,cAAe,KAAMD,aAAc,KAAMD,WAAY,OAGvEzC,mBAAmBpc,EAAQiC,EAAMvI,GAC/B,GAAc,OAAVsG,EACF3R,KAAKC,MAAMyyB,YAAYzL,GAAO0L,WAAW1L,GAAO2L,MAAMhf,GAAOvI,SACxD,GAAc,OAAVsG,EAAiB,CAC1B,MAAM/P,EAAM,IAAIoM,IAAI3C,EAAKgb,KACnBhlB,EAASO,EAAIixB,aACnB,IAAK,IAAI9zB,KAAOsM,EAAK8a,KACf9a,EAAK8a,KAAK9mB,eAAeN,IAC3BsC,EAAOyxB,IAAI/zB,EAAKsM,EAAK8a,KAAKpnB,IAG9B,CAAC,OAAQ,OAAOwI,KAAI,SAASxI,GACvBsM,EAAKtM,IACPsC,EAAOyxB,IAAI/zB,EAAKsM,EAAKtM,OAGzBsC,EAAOyxB,IAAI,MAAO9yB,KAAKC,MAAM2S,UAC7BhR,EAAImxB,OAAS1xB,EACbQ,OAAO6X,KAAK9X,EAAK,eAEjBuD,QAAQC,IAAI,yBAA0BuM,GAI1CpB,mBAAmB3K,GACjBA,EAAEI,iBACFJ,EAAEK,kBACFjG,KAAKC,MAAM0Q,gBAAgB,CAAE/L,UAAW5E,KAAK0O,MAAMnM,MAAO2F,EAAGtC,EAAEgL,MAAO7I,EAAGnC,EAAEiL,QAG7Emd,6BAA6B3sB,EAAQ2xB,GACnC3xB,EAAOuD,UAAY5E,KAAK0O,MAAMnM,MAC9B,MAAMkhB,EAAYuP,GAA4B,GAC9CvP,EAAUxhB,KAAK,kBACf,MAAMM,EAAQvC,KAAKC,MAAM4E,OAAOC,SAASzD,EAAOuD,WAChD,GAAIrC,EAAO,CACT,MAAMmO,EAAMnO,EAAM2e,gBACdxQ,GAAOA,EAAI2Q,aACboC,EAAUxhB,KAAK,uBAGnBjC,KAAKC,MAAM0Q,gBAAgBtP,EAAQoiB,GAGrCwK,wBAAwBtc,GACtB3R,KAAKC,MAAMgzB,UAAUjzB,KAAK0O,MAAMnM,MAAOoP,GAGzCuc,iBAAiBtoB,GACfA,EAAEI,iBACFhG,KAAKC,MAAM8hB,oBAAoB/hB,KAAK0O,MAAMnM,MpC9iBP,QoC8iBuCvC,KAAK0O,MAAMnM,OAGvF8qB,eACE,MAAM9qB,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAK0O,MAAMnM,OAChDA,EAAM2sB,gBACR3sB,EAAM2wB,eAIV9F,mBAAmBtgB,GACjB,GAAIA,EAAKjB,KpC5hB6B,OoC4hBM,CAE1C,MAAM8Y,EAAW3kB,KAAKC,MAAM4E,OAAO2f,qBACnC,IAAKG,EAEH,YADA3kB,KAAKC,MAAMuG,QAAQxG,KAAKC,MAAM4D,KAAKD,cAAcnB,GAAS0wB,yBAG5D,MAAMC,EAA0BzO,EAAS0O,OAAOvmB,GAC1C9F,EAAMigB,GAAOsD,WAAW,KAAMzd,EAAK1B,KAAM,KAAM0B,EAAK/O,KAAM+O,EAAKjB,KAAMunB,GAE3EpzB,KAAKC,MAAMyyB,YAAY1rB,EAAKosB,EAAyBzO,QAGrDrW,EAAaxB,EACX,CAACR,EAAMod,EAAMrd,KACXrM,KAAKC,MAAMyyB,YAAYzL,GAAOsD,WAAW,KAAMje,EAAMod,EAAMrd,KAE7DrM,KAAKC,MAAMuG,SAKjBqiB,iBAAiB/b,GACXA,EAAKjB,KpC/iB6B,GAAK,GoCijBzC7L,KAAKC,MAAMuG,QAAQxG,KAAKC,MAAM4D,KAAKD,cAAcnB,GAAS6wB,0BACtD,CAACznB,KAAMtC,EAAiBuD,EAAKjB,MAAO0nB,MAAOhqB,EpCljBX,GAAK,MoCkjBsD,OAE/FvJ,KAAK+O,SAAS,CAAEyhB,WAAY,CAC1B1jB,KAAMA,EACNwX,SAAUxX,EAAK/O,KACf8N,KAAMiB,EAAKjB,KACXT,KAAM0B,EAAK1B,QAKjB+hB,oBAAoBxC,EAASre,EAAMod,EAAMne,EAAOC,EAAQa,GACtD,IAAIrF,EAAMigB,GAAOuM,YAAY,KAAM,EAAGlnB,EAAMod,EAAMne,EAAOC,EAAQa,GAC7Dse,IACF3jB,EAAMigB,GAAOwM,gBAAgBzsB,GAC7BA,EAAMigB,GAAOyM,OAAO1sB,EAAKigB,GAAO0M,KAAKhJ,KAEvC3qB,KAAKC,MAAMyyB,YAAY1rB,GAGzB4hB,kBAAkB9b,GAEZA,EAAKjB,KpC5kB6B,QoC4kBQb,EAAwBwB,QAAQM,EAAK1B,MAAQ,EAEzFyB,EAAwBC,EpCtkBD,SoCskBqC,EAE1D,CAAC4c,EAAMpd,EAAMf,EAAOC,EAAQa,KAC1BrM,KAAK+O,SAAS,CAAC0hB,aAAc,CAC3B7uB,IAAKoM,IAAIC,gBAAgBnB,GACzB4c,KAAMA,EACNpF,SAAUjY,EACVd,MAAOA,EACPC,OAAQA,EACRK,KAAM6d,EAAKxnB,OACXkJ,KAAMkB,MAITrH,IACCjF,KAAKC,MAAMuG,QAAQvB,EAAK,SAI5BiJ,EAAkBpB,EAEhB,CAAC4c,EAAMpd,EAAMf,EAAOC,EAAQa,KAC1BrM,KAAK+O,SAAS,CAAC0hB,aAAc,CAC3B7uB,IAAKoM,IAAIC,gBAAgBnB,GACzB4c,KAAMA,EACNpF,SAAUjY,EACVd,MAAOA,EACPC,OAAQA,EACRK,KAAM6d,EAAKxnB,OACXkJ,KAAMkB,MAITrH,IACCjF,KAAKC,MAAMuG,QAAQvB,EAAK,SAMhClF,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KAEnC,IAAI+vB,EACJ,GAAI5zB,KAAKC,MAAM4zB,SACbD,EAAY,UACP,GAAK5zB,KAAK0O,MAAMnM,MAMhB,CACL,IAAIuxB,EACJ,GAAI9zB,KAAK0O,MAAM+hB,aAEbqD,EACE,kBAAC,GAAD,CACEzzB,QAASL,KAAK0O,MAAM+hB,aACpB7F,QAAS5qB,KAAK8tB,mBACd/D,cAAe/pB,KAAKmtB,2BAEnB,GAAIntB,KAAK0O,MAAMgiB,cAEpBoD,EACE,kBAAC,GAAD,CACEzzB,QAASL,KAAK0O,MAAMgiB,cACpB9F,QAAS5qB,KAAK8tB,0BAEb,GAAI9tB,KAAK0O,MAAM8hB,WAEpBsD,EACE,kBAAC,GAAD,CACEzzB,QAASL,KAAK0O,MAAM8hB,WACpB5F,QAAS5qB,KAAK8tB,mBACd/D,cAAe/pB,KAAKotB,yBAEnB,CACL,MAAM7qB,EAAQvC,KAAKC,MAAM4E,OAAOC,SAAS9E,KAAK0O,MAAMnM,OAC9Cyb,EAAgC,OAAnBzb,EAAMie,UACzB,IAAIuT,EAAe,GACfC,EAAe,KACfC,EAAe,KACnB,IAAK,IAAIz2B,EAAE,EAAGA,EAAEwC,KAAK0O,MAAMjM,SAASP,OAAQ1E,IAAK,CAC/C,IAAIwJ,EAAMhH,KAAK0O,MAAMjM,SAASjF,GAC1B02B,EAAW,KAEX12B,EAAI,EAAIwC,KAAK0O,MAAMjM,SAASP,SAC9BgyB,EAAWl0B,KAAK0O,MAAMjM,SAASjF,EAAE,GAAG40B,MAGtC,IAAI1L,EAAW,SACX1f,EAAIorB,MAAQ4B,EAEZtN,EADE1f,EAAIorB,MAAQ8B,EACH,SAEA,OAEJltB,EAAIorB,MAAQ8B,IACrBxN,EAAW,SAEbsN,EAAehtB,EAAIorB,KAEnB,MAAM+B,IAAYntB,EAAIorB,MAAQpyB,KAAKC,MAAM2S,UACnCwhB,EAAiB7xB,EAAM4vB,UAAUnrB,GAEvC,IAAI2gB,EAAUhN,EAAYmM,EAC1B,GAAI9I,EAAY,CACd,MAAM3Y,EAAO9C,EAAM8xB,SAASrtB,EAAIorB,MAC5B/sB,GAAQA,EAAKqN,SACfiV,EAAWtiB,EAAKqN,OAAOC,GACvBgI,EAAazP,EAAa7F,EAAKqN,OAAOvH,QAExC2b,EAAW9f,EAAIorB,KACf6B,EAAa,sBAEbA,EAAa,WAGfF,EAAa9xB,KACX,kBAAC,GAAD,CACE4C,OAAQ7E,KAAKC,MAAM4E,OACnBxE,QAAS2G,EAAI3G,QAASomB,QAASzf,EAAIstB,GACnCtN,SAAUhgB,EAAIiU,KAAOjU,EAAIiU,KAAK3O,KAAO,KACrC4Y,UAAWle,EAAIutB,GAAI5N,SAAUwN,EAASvtB,IAAKI,EAAIJ,IAC/CkgB,SAAUA,EAAUa,SAAUA,EAAUhN,WAAYA,EACpD+L,SAAUA,EAAUvB,SAAUiP,EAAgBzP,SAAU3d,EAAIwtB,UAC5DvM,cAAejoB,KAAKC,MAAMgoB,cAC1BtX,gBAAiB3Q,KAAKguB,6BACtB9H,eAAgBlmB,KAAK6tB,oBACrBvH,eAAgBtmB,KAAK+tB,mBACrBvnB,QAASxG,KAAKC,MAAMuG,QACpBzH,IAAKiI,EAAIJ,OAIf,IAAI6tB,EAAW,KACf,MAAMC,EAAO10B,KAAKC,MAAM4E,OAAO8vB,aAAaC,WAAW50B,KAAK0O,MAAMnM,OAC9DmyB,GAAwC,OAAhCh1B,IAAO2K,UAAUqqB,EAAKnyB,SAC5BmyB,EAAK5jB,OACP2jB,EAAW7wB,EAAcnB,GAAS+pB,YACzBkI,EAAKG,OACdJ,EAAW7wB,EAAcnB,GAASgqB,WAAa,KAC7CpkB,EAAgBqsB,EAAKG,KAAKC,KAAM90B,KAAKC,MAAM4D,KAAKyE,UAItD,MAAM6B,EAASnK,KAAK0O,MAAMvE,SAAU,EAC9B2G,EAAS9Q,KAAKC,MAAM6Q,OAAS,UAAY9Q,KAAK0O,MAAMiiB,gBAAkB,UAAY,IAAM,UAE9FmD,EACE,oCACE,yBAAKrzB,GAAG,sBAAsBN,UAAU,iBACrCH,KAAKC,MAAMyjB,cACV,uBAAGnU,KAAK,IAAI9O,GAAG,oBAAoBF,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAM80B,uBACjF,uBAAG50B,UAAU,kBAAb,eAGF,KACF,yBAAKA,UAAU,cACb,kBAAC,EAAD,CACEgK,OAAQA,EACR5H,MAAOvC,KAAK0O,MAAMnM,MAClBnC,MAAOJ,KAAK0O,MAAMtO,QACpB,0BAAMD,UAAW2Q,KAEnB,yBAAKrQ,GAAG,qBACN,yBAAKA,GAAG,cAAcN,UAAU,eAC9BH,KAAK0O,MAAMtO,OACX,2BAAG,kBAAC,mBAAD,CAAkBK,GAAG,gBAAgByX,eAAe,cAGzD,yBAAKzX,GAAG,mBAAmBg0B,IAE5BzW,EACC,kBAAC,GAAD,CACE8C,YAAa9gB,KAAK0O,MAAMoiB,aAC1B,yBAAKrwB,GAAG,gBAEV,6BACE,uBAAG8O,KAAK,IAAIhP,QAASP,KAAKuQ,oBACxB,uBAAGpQ,UAAU,kBAAb,gBAILH,KAAKC,MAAMyjB,cACV,kBAAC,EAAD,CACEjQ,MAAOzT,KAAKC,MAAM0jB,WAClB/P,KAAM5T,KAAKC,MAAM2jB,UACjBlQ,aAAc1T,KAAKC,MAAMuG,UACzB,KACJ,kBAAC,GAAD,CAAagN,KAAMxT,KAAK0O,MAAMmhB,mBAC9B,yBAAKpvB,GAAG,sBACN,yBAAKA,GAAG,iBAAiB4lB,IAAKrmB,KAAKstB,uBACjC,wBAAI7sB,GAAG,WAAWN,UAAW8zB,GAC1BF,IAGH/zB,KAAK0O,MAAMghB,SASX,KARF,yBAAKjvB,GAAG,yBACLT,KAAK0O,MAAMyiB,eACZ,yBAAK1wB,GAAG,mBACN,kBAAC,mBAAD,CAAkBA,GAAG,wBAAwByX,eAAe,2BAG5D,OAILlY,KAAK0O,MAAMuiB,wBAA0BjxB,KAAK0O,MAAM2iB,YAC/C,yBAAK5wB,GAAG,gCACN,uBAAGN,UAAU,4BAAb,SADF,IACoD,kBAAC,mBAAD,CAChDM,GAAG,2BAA2ByX,eAAe,kCAFjD,IAGqE,uBAAG3I,KAAK,IACvEhP,QAASP,KAAKkuB,kBAAkB,kBAAC,mBAAD,CAAkBztB,GAAG,yBACrDyX,eAAe,YALrB,KAMS,KACVlY,KAAK0O,MAAM2iB,YACV,kBAAC,GAAD,CAAY9qB,SAAUvG,KAAKiuB,0BAE3B,kBAAC,GAAD,CACE/E,UAAWlpB,KAAK0O,MAAMwiB,SACtBnH,cAAe/pB,KAAKC,MAAMyyB,YAC1BzI,WAAYjqB,KAAKqtB,aACjBzD,aAAc5pB,KAAK6oB,iBACnBc,cAAe3pB,KAAK4oB,kBACpBpiB,QAASxG,KAAKC,MAAMuG,WAK9BotB,EAAY,yBAAKnzB,GAAG,cAAcqzB,QAxLlCF,EACE,kBAAC,GAAD,CACEtH,cAAetsB,KAAKC,MAAMqsB,cAC1BC,cAAevsB,KAAKC,MAAMssB,gBAuLhC,OAAOqH,GAIIxrB,4BAAW4kB,ICn3BX,MAAMgI,WAAmBx1B,IAAMM,cAC5CC,SACE,OACE,uBAAGwP,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMg1B,WAC1D,uBAAG90B,UAAU,kBAAb,gBCLO,MAAM+0B,WAAqB11B,IAAMM,cAC9CC,SACE,OACE,6BACE,uBAAGwP,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMk1B,eAAgB,uBAAGh1B,UAAU,kBAAb,SAD9E,IAGE,uBAAGoP,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMm1B,eAAgB,uBAAGj1B,UAAU,kBAAb,eCNrE,MAAMk1B,WAAkB71B,IAAMM,cAC3CC,SACE,OACI,6BACE,uBAAGwP,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMq1B,aAAc,uBAAGn1B,UAAU,kBAAb,eAD5E,IAGE,uBAAGoP,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMm1B,eAAgB,uBAAGj1B,UAAU,kBAAb,eCDvE,MAAMo1B,WAAmB/1B,IAAMM,cAC5CC,SACE,OACI,yBAAKU,GAAG,qBAAqBN,UAAU,iBACpCH,KAAKC,MAAMqT,SAAW,kBAAC,GAAD,CAAY2hB,OAAQj1B,KAAKC,MAAMqT,WAAe,KACpEtT,KAAKC,MAAMkK,OACV,yBAAK1J,GAAG,cAAcN,UAAU,cAC9B,kBAAC,EAAD,CACEgK,OAAQnK,KAAKC,MAAMkK,OACnB5H,MAAOvC,KAAKC,MAAM2S,SAClBxS,MAAOJ,KAAKC,MAAMG,SAGtB,KACF,yBAAKK,GAAG,kBAAkBN,UAAU,eAAeH,KAAKC,MAAMG,OACxC,UAArBJ,KAAKC,MAAMyO,MACR,kBAAC,GAAD,CAAW4mB,SAAUt1B,KAAKC,MAAMq1B,SAAUF,WAAYp1B,KAAKC,MAAMm1B,aAC9C,aAArBp1B,KAAKC,MAAMyO,MACT,kBAAC,GAAD,CAAcymB,WAAYn1B,KAAKC,MAAMk1B,WAAYC,WAAYp1B,KAAKC,MAAMm1B,aAC1E,OClBZ,MAAM3yB,GAAWC,yBAAe,CAC9B8yB,wBAAyB,CAAF,uEAOV,MAAMC,WAAqBj2B,IAAMiE,UAC9CC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK01B,aAAe11B,KAAK01B,aAAa12B,KAAKgB,MAE3CA,KAAK0O,MAAQ+mB,GAAaphB,qBAAqBpU,GAGjD,4BAA4BA,GAC1B,MAAMoS,EAAW,GACjB,IAAIsjB,EAAgB,EAChBC,EAAgB,EAmCpB,OAlCA31B,EAAM41B,SAAStuB,IAAK1J,IAClB,MAAMi4B,EAAUj4B,EAAE6S,MAAQ7S,EAAE6S,IAAIU,WAE5B0kB,GAAW71B,EAAM61B,SACnBzjB,EAASpQ,KAAKpE,GAEZi4B,GAAW71B,EAAM61B,UAIjBj4B,EAAE2U,SAAW3U,EAAE2U,QAAQujB,KACrB91B,EAAMsD,QACR8O,EAASpQ,KAAKpE,GAEd+3B,IAEQ31B,EAAMsD,UAChB8O,EAASpQ,KAAKpE,GACd83B,GAAiB93B,EAAE2T,OAAS,EAAI,EAAI,MAIxCa,EAAS0H,KAAK,CAAC6B,EAAG1L,KACRA,EAAE8lB,SAAW,IAAMpa,EAAEoa,SAAW,IAGtCJ,EAAgB,GAClBvjB,EAASpQ,KAAK,CACZ0P,OAAQ,UACRvR,MAAOqC,GAAS+yB,wBAChB5jB,OAAQ,CAACvK,MAAOuuB,KAIb,CACLlX,YAAarM,EACbsjB,cAAeA,GAInB9mB,mBAAmBC,EAAWwG,GAC5B,GAAIxG,EAAU+mB,UAAY71B,KAAKC,MAAM41B,UACjC/mB,EAAUvL,SAAWvD,KAAKC,MAAMsD,SAChCuL,EAAUgnB,SAAW91B,KAAKC,MAAM61B,QAAS,CAC3C,MAAMrV,EAAWgV,GAAaphB,qBAAqBrU,KAAKC,OACxDD,KAAK+O,SAAS0R,GACVA,EAASkV,eAAiBrgB,EAAUqgB,eACtC7a,GAAc2F,EAASkV,gBAK7BD,aAAaO,GACXj2B,KAAKC,MAAMi2B,gBAGbn2B,SACE,OACE,kBAAC,mBAAD,CAAkBU,GAAG,qBACnByX,eAAe,oCAEd7B,GAAgB,kBAAC,EAAD,CACftD,UAAW/S,KAAKC,MAAM8S,UACtBV,SAAUrS,KAAK0O,MAAMgQ,YACrBtL,iBAAkBiD,EAClBnE,cAAelS,KAAKC,MAAMiS,cAC1BU,SAAU5S,KAAKC,MAAM2S,SACrBrB,YAAY,EACZuB,YAAY,EACZE,gBAAiBhT,KAAKC,MAAM+S,gBAC5BrC,gBAAiB3Q,KAAKC,MAAM0Q,gBAC5BpK,SAAUvG,KAAK01B,iBCjGV,MAAMS,GAEnB,iBAAiBp3B,EAAKN,GACpB23B,aAAaC,QAAQt3B,EAAKu3B,KAAKC,UAAU93B,IAI3C,iBAAiBM,GACf,MAAMN,EAAQ23B,aAAaI,QAAQz3B,GACnC,OAAON,GAAS63B,KAAK1D,MAAMn0B,GAI7B,oBAAoBM,EAAKN,GACvB,MAAMg4B,EAASz2B,KAAK02B,UAAU33B,GAC9BiB,KAAK22B,UAAU53B,EAAKb,OAAO2yB,OAAO4F,GAAU,GAAIh4B,IAIlD,kBAAkBM,GAChBq3B,aAAaQ,WAAW73B,ICVb,MAAM83B,WAA0Br3B,IAAMM,cACnD4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXooB,MAAO,GACPC,SAAU,GACVC,MAAO,GACPrkB,GAAI,GACJ6I,aAAc,KACdyb,cAAc,EACdC,UAAWf,GAAiBO,UAAU,mBAGxC12B,KAAKm3B,kBAAoBn3B,KAAKm3B,kBAAkBn4B,KAAKgB,MACrDA,KAAKo3B,qBAAuBp3B,KAAKo3B,qBAAqBp4B,KAAKgB,MAC3DA,KAAKq3B,kBAAoBr3B,KAAKq3B,kBAAkBr4B,KAAKgB,MACrDA,KAAKs3B,eAAiBt3B,KAAKs3B,eAAet4B,KAAKgB,MAC/CA,KAAKwf,mBAAqBxf,KAAKwf,mBAAmBxgB,KAAKgB,MACvDA,KAAKu3B,sBAAwBv3B,KAAKu3B,sBAAsBv4B,KAAKgB,MAC7DA,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAG7Cm3B,kBAAkBvxB,GAChB5F,KAAK+O,SAAS,CAAC+nB,MAAOlxB,EAAEG,OAAOtH,QAGjC24B,qBAAqBL,GACnB/2B,KAAK+O,SAAS,CAACgoB,SAAUA,IAG3BM,kBAAkBzxB,GAChB5F,KAAK+O,SAAS,CAACioB,MAAOpxB,EAAEG,OAAOtH,QAGjC64B,eAAe1xB,GACb5F,KAAK+O,SAAS,CAAC4D,GAAI/M,EAAEG,OAAOtH,QAG9B+gB,mBAAmBxS,GACjBhN,KAAK+O,SAAS,CAACyM,aAAcxO,IAG/BuqB,wBACEpB,GAAiBQ,UAAU,kBAAmB32B,KAAK0O,MAAMwoB,WACzDl3B,KAAK+O,SAAS,CAACmoB,WAAYl3B,KAAK0O,MAAMwoB,YAGxChgB,aAAatR,GACXA,EAAEI,iBACFhG,KAAK+O,SAAS,CAACkoB,cAAc,IAC7Bj3B,KAAKC,MAAMu3B,gBACTx3B,KAAK0O,MAAMooB,MAAMpsB,OACjB1K,KAAK0O,MAAMqoB,SAASrsB,OACpB6Q,GAAMvb,KAAK0O,MAAMiE,GAAGjI,OAAOkC,UAAU,E3COX,I2CPiC5M,KAAK0O,MAAM8M,cACtE,CAAC,KAAQ,QAAS,IAAOxb,KAAK0O,MAAMsoB,QAGxCj3B,SACE,IAAI03B,EAAgB,OAKpB,OAJIz3B,KAAKC,MAAMipB,WACbuO,GAAiB,aAIjB,0BAAMt3B,UAAU,oBAAoB8X,SAAUjY,KAAKkX,cACjD,yBAAK/W,UAAU,kBACb,yBAAKA,UAAU,qBACb,kBAAC,mBAAD,CAAkBM,GAAG,gBAClBi3B,GAAiB,2BAAOtsB,KAAK,OAAO0J,YAAa4iB,EAAc9e,aAAa,YAC3Ena,MAAOuB,KAAK0O,MAAMooB,MAAOpnB,SAAU1P,KAAKm3B,kBAAmBhjB,UAAQ,EAACiC,WAAS,KAEjF,kBAAC,mBAAD,CAAkB3V,GAAG,mBAClBk3B,GAAoB,kBAAC,EAAD,CAAiB7iB,YAAa6iB,EAAiB/e,aAAa,eAC/Ena,MAAOuB,KAAK0O,MAAMqoB,SAAUve,WAAYxY,KAAKo3B,qBAC7CjjB,UAAU,MAGhB,kBAAC,EAAD,CACEhF,eAAgBnP,KAAKwf,mBACrBhZ,QAASxG,KAAKC,MAAMuG,WAExB,yBAAMrG,UAAU,kBACd,kBAAC,mBAAD,CAAkBM,GAAG,mBAAmByX,eAAe,4BAEpD0f,GAAqB,2BAAOxsB,KAAK,OAAO0J,YAAa8iB,EAAkBhf,aAAa,OACnFna,MAAOuB,KAAK0O,MAAMiE,GAAIjD,SAAU1P,KAAKs3B,eAAgBnjB,UAAQ,MAGnE,yBAAKhU,UAAU,kBACb,kBAAC,mBAAD,CAAkBM,GAAG,eAAeyX,eAAe,gCAEhD2f,GAAiB,2BAAOzsB,KAAK,QAAQ0J,YAAa+iB,EAAcjf,aAAa,QAC5Ena,MAAOuB,KAAK0O,MAAMsoB,MAAOtnB,SAAU1P,KAAKq3B,kBAAmBljB,UAAQ,MAGzE,yBAAKhU,UAAU,kBACb,kBAAC,EAAD,CAAUM,GAAG,aAAa1C,KAAK,aAAa+R,QAAS9P,KAAK0O,MAAMwoB,UAC9DxnB,SAAU1P,KAAKu3B,wBACjB,kBAAC,mBAAD,CAAkB92B,GAAG,kBAClBq3B,GAAmB,2BAAOnoB,QAAQ,cAAf,IAAmCmoB,KAG3D,yBAAK33B,UAAU,kBACb,4BAAQA,UAAWs3B,EAAersB,KAAK,UACrC,kBAAC,mBAAD,CAAkB3K,GAAG,iBAAiByX,eAAe,gBC9GlD,MAAM6f,WAAwBv4B,IAAMiE,UACjDC,YAAYzD,GACV0D,MAAM1D,GAEN,MAAM+3B,EAAKh4B,KAAKC,MAAM4E,OAAO8vB,aAC7B30B,KAAK0O,MAAQ,CACXuP,SAAU+Z,EAAGtlB,OAASslB,EAAGtlB,OAAOC,QAAKoD,EACrC5L,OAAQe,EAAa8sB,EAAGtlB,OAASslB,EAAGtlB,OAAOvH,MAAQ,OAIvDpL,SACE,OACE,yBAAKI,UAAU,oBACb,yBAAKA,UAAU,kBACb,yBAAKA,UAAU,qBACb,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,kBAAkByX,eAAe,eAGxD,yBAAK/X,UAAU,SAASH,KAAK0O,MAAMuP,UACnC,6BACE,2BAAO9d,UAAU,SAAQ,kBAAC,mBAAD,CAAkBM,GAAG,gBAAgByX,eAAe,SAD/E,IAGE,4BAAKlY,KAAKC,MAAM2S,YAGpB,kBAAC,EAAD,CACEzI,OAAQnK,KAAK0O,MAAMvE,OACnBmF,UAAWtP,KAAK0O,MAAM6L,MACtB/K,IAAKxP,KAAKC,MAAM2S,SAChBxS,MAAOJ,KAAK0O,MAAMuP,YAEtB,yBAAK9d,UAAU,kBACb,uBAAGoP,KAAK,IAAIpP,UAAU,cAAcI,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMg4B,gBAAgB,aAClG,uBAAG93B,UAAU,kBAAb,QADF,IAEE,kBAAC,mBAAD,CAAkBM,GAAG,cAAcyX,eAAe,WAGtD,yBAAK/X,UAAU,OACf,yBAAKA,UAAU,qBACb,uBAAGoP,KAAK,IAAIpP,UAAU,cAAcI,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMg4B,gBAAgB,WAClG,uBAAG93B,UAAU,kBAAb,iBADF,IACuD,kBAAC,mBAAD,CAAkBM,GAAG,uCAE5E,uBAAG8O,KAAK,IAAIpP,UAAU,cAAcI,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMg4B,gBAAgB,cAClG,uBAAG93B,UAAU,kBAAb,YADF,IACkD,kBAAC,mBAAD,CAAkBM,GAAG,kCAEvE,uBAAG8O,KAAK,IAAIpP,UAAU,cAAcI,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMg4B,gBAAgB,aAClG,uBAAG93B,UAAU,kBAAb,mBADF,IACyD,kBAAC,mBAAD,CAAkBM,GAAG,oCC1CzE,MAAMy3B,WAAuB14B,IAAMiE,UAChDC,YAAYzD,GACV0D,MAAM1D,GAEN,MAAM+3B,EAAKh4B,KAAKC,MAAM4E,OAAO8vB,aAC7B30B,KAAK0O,MAAQ,CACXuP,SAAU+Z,EAAGtlB,OAASslB,EAAGtlB,OAAOC,QAAKoD,EACrC5L,OAAQe,EAAa8sB,EAAGtlB,OAASslB,EAAGtlB,OAAOvH,MAAQ,MACnDgR,KAAM6b,EAAG7b,OACTgc,YAAaH,EAAGI,kBAAoB,GACpCC,eAAe,EACfC,gBAAgB,EAChBC,QAAS,GACTC,eAAgBR,EAAG3Y,eAGrBrf,KAAKy4B,UAAYz4B,KAAKy4B,UAAUz5B,KAAKgB,MACrCA,KAAK04B,eAAiB14B,KAAK04B,eAAe15B,KAAKgB,MAC/CA,KAAKsf,qBAAuBtf,KAAKsf,qBAAqBtgB,KAAKgB,MAC3DA,KAAKwf,mBAAqBxf,KAAKwf,mBAAmBxgB,KAAKgB,MACvDA,KAAK24B,iBAAmB34B,KAAK24B,iBAAiB35B,KAAKgB,MACnDA,KAAK44B,kBAAoB54B,KAAK44B,kBAAkB55B,KAAKgB,MACrDA,KAAK64B,kBAAoB74B,KAAK64B,kBAAkB75B,KAAKgB,MACrDA,KAAKsgB,kBAAoBtgB,KAAKsgB,kBAAkBthB,KAAKgB,MAGvDuF,oBACE,MAAMyyB,EAAKh4B,KAAKC,MAAM4E,OAAO8vB,aAC7BqD,EAAGc,eAAiB94B,KAAK04B,eACzBV,EAAG3Y,cAAgBrf,KAAKy4B,UAG1B/yB,uBACE,MAAMsyB,EAAKh4B,KAAKC,MAAM4E,OAAO8vB,aAC7BqD,EAAG3Y,cAAgBrf,KAAK0O,MAAM8pB,eAC9BR,EAAGc,oBAAiB/iB,EAGtB0iB,UAAUtc,GACRnc,KAAK+O,SAAS,CAACoN,KAAMA,IAGvBuc,eAAeK,GACb/4B,KAAK+O,SAAS,CAACopB,YAAaY,GAAS,KAGvCzZ,qBAAqB3M,IACnBA,EAAKA,EAAGjI,OAAOkC,UAAU,E7CaG,O6CX1B5M,KAAK+O,SAAS,CAACkP,SAAUtL,IACzB3S,KAAKC,MAAM+4B,qBAAgBjjB,EAAWwF,GAAM5I,EAAI,QAIpD6M,mBAAmBxS,GACjBhN,KAAK+O,SAAS,CAAC5E,OAAQ6C,IACvBhN,KAAKC,MAAM+4B,qBAAgBjjB,EAAWwF,GAAM,KAAMvO,GAAOtN,IAAOmiB,WAGlE8W,iBAAiB/yB,GACf5F,KAAK+O,SAAS,CAACwpB,QAAS3yB,EAAEG,OAAOtH,MAAO65B,gBAAgB,IAG1DM,kBAAkBhzB,GACE,KAAdA,EAAEO,QAEJnG,KAAK+O,SAAS,CAACwpB,QAAS,GAAIF,eAAe,IACpB,KAAdzyB,EAAEO,SAEXnG,KAAK64B,kBAAkBjzB,GAI3BizB,kBAAkBjzB,GAChB,IAAInH,EAAQuB,KAAK0O,MAAM6pB,QAAQ7tB,OAC/B,IAAKjM,EAEH,YADAuB,KAAK+O,SAAS,CAACspB,eAAe,EAAOC,gBAAgB,IAIvD,IACIW,EADAthB,EvB5BD,SAAiBA,GAEtB,OADAA,EAAMA,EAAIjN,OACN,6EAA6EqR,KAAKpE,GAC7EA,EAAImC,QAAQ,WAAY,IAE1B,KuBuBKof,CAAQz6B,GAEdkZ,EACFshB,EAAS,OAETthB,EvBxBC,SAAiBA,GAEtB,OADAA,EAAMA,EAAIjN,OACN,mDAAmDqR,KAAKpE,GACnDA,EAEF,KuBmBGwhB,CAAQ16B,GACVkZ,IACFshB,EAAS,UAGTA,GACFj5B,KAAKC,MAAMm5B,UAAUH,EAAQthB,GAC7B3X,KAAK+O,SAAS,CAACspB,eAAe,EAAOE,QAAS,MAE9Cv4B,KAAK+O,SAAS,CAACupB,gBAAgB,IAInChY,kBAAkBnE,GAEZR,GAAW3b,KAAK0O,MAAMyN,KAAKpL,MAAM,GAAIoL,EAAKpL,MAAM,KAGpD/Q,KAAKC,MAAMo5B,aAAald,GAG1Bpc,SACE,MAAMo4B,EAAc,GAYpB,OAXAn4B,KAAK0O,MAAMypB,YAAY5wB,IAAK+xB,IAC1BnB,EAAYl2B,KAAK,yBAAKlD,IAAKu6B,EAAKC,KAAO,IAAMD,EAAK3hB,IAAM,IAAM2hB,EAAKE,MAAOF,EAAKC,KAA9D,KAAqE,4BAAKD,EAAK3hB,KAC9F,kCAAU2hB,EAAKE,KAKX,KAJF,uBAAGjqB,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMw5B,cAAcH,EAAKC,KAAMD,EAAK3hB,OACtF,kBAAC,mBAAD,CAAkBlX,GAAG,6BAA6ByX,eAAe,aAFvE,IAKU,uBAAG3I,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAMy5B,aAAaJ,EAAKC,KAAMD,EAAK3hB,OAAQ,uBACvGxX,UAAU,uBAD6F,wBAK7G,yBAAKA,UAAU,oBACb,yBAAKA,UAAU,kBACb,yBAAKA,UAAU,qBACb,2BAAOA,UAAU,SAAQ,kBAAC,mBAAD,CAAkBM,GAAG,qBAC9C,6BAAK,kBAAC,mBAAD,CAAkBA,GAAG,oBACvBk5B,GAA0B,kBAAC,EAAD,CACzB7kB,YAAa6kB,EACbl7B,MAAOuB,KAAK0O,MAAMuP,SAClBzF,WAAYxY,KAAKsf,0BAGvB,kBAAC,EAAD,CACEnV,OAAQnK,KAAK0O,MAAMvE,OACnBqF,IAAKxP,KAAKC,MAAM2S,SAChBxS,MAAOJ,KAAK0O,MAAMuP,SAClB9O,eAAgBnP,KAAKwf,mBACrBhZ,QAASxG,KAAKC,MAAMuG,WAExB,yBAAKrG,UAAU,OACf,kBAAC,mBAAD,CAAkBM,GAAG,oBAAoByX,eAAe,yBAErD0hB,GAAsB,kBAAC,GAAD,CACrBx5B,MAAOw5B,EACPvd,WAAW,EACXF,KAAMnc,KAAK0O,MAAMyN,KACjBlE,SAAUjY,KAAKsgB,qBAEnB,yBAAKngB,UAAU,OACf,yBAAKA,UAAU,qBACb,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,sBAAsByX,eAAe,eAG5D,yBAAK/X,UAAU,UACZg4B,EACAn4B,KAAK0O,MAAM2pB,cACV,2BAAOjtB,KAAK,OAAO3M,MAAOuB,KAAK0O,MAAMjQ,MACnC0B,UAAWH,KAAK0O,MAAM4pB,eAAiB,UAAY,KACnDxjB,YAAY,wBAAwBX,SAAS,WAAWiC,WAAS,EACjE1G,SAAU1P,KAAK24B,iBAAkBxiB,UAAWnW,KAAK44B,kBAAmB1iB,OAAQlW,KAAK64B,oBACjF,KACJ,6BACE,uBAAGtpB,KAAK,IAAIhP,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAK+O,SAAS,CAACspB,eAAe,MAC7E,uBAAGl4B,UAAU,kBAAb,OACA,kBAAC,mBAAD,CAAkBM,GAAG,qBAAqByX,eAAe,sBC5K1D,MAAM6f,WAAwBv4B,IAAMM,cACjD4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK65B,oBAAsB75B,KAAK65B,oBAAoB76B,KAAKgB,MAG3D65B,oBAAoBxH,EAAMviB,GACZ,SAARuiB,EACFryB,KAAKC,MAAM65B,sBAAsBhqB,GAChB,SAARuiB,EACTryB,KAAKC,MAAM85B,0BAA0BjqB,GACpB,aAARuiB,GACTryB,KAAKC,MAAM+5B,sBAAsBlqB,GAIrC/P,SACE,OACI,yBAAKI,UAAU,oBACb,yBAAKA,UAAU,kBACb,2BAAOwP,QAAQ,iBACb,kBAAC,mBAAD,CAAkBlP,GAAG,sBAAsByX,eAAe,oBAG5D,kBAAC,EAAD,CAAUna,KAAK,QAAQ0C,GAAG,gBACxBqP,QAAS9P,KAAKC,MAAMg6B,cAAevqB,SAAU1P,KAAK65B,uBAEtD,yBAAK15B,UAAU,kBACb,2BAAOwP,QAAQ,kBACd3P,KAAKC,MAAMi6B,qBACV,kBAAC,mBAAD,CAAkBz5B,GAAG,2BAA2ByX,eAAe,yBAG/D,kBAAC,mBAAD,CAAkBzX,GAAG,oCACnByX,eAAe,2CAInB,kBAAC,EAAD,CAAUna,KAAK,QAAQ0C,GAAG,iBACxBqP,QAAS9P,KAAKC,MAAMk6B,cACpBzqB,SAAU1P,KAAKC,MAAMi6B,qBAAuBl6B,KAAK65B,oBAAsB,QAE3E,yBAAK15B,UAAU,kBACb,2BAAOwP,QAAQ,kBACb,kBAAC,mBAAD,CAAkBlP,GAAG,uBAAuByX,eAAe,qBAG7D,kBAAC,EAAD,CAAUna,KAAK,YAAY0C,GAAG,iBAC5BqP,QAAS9P,KAAKC,MAAMm6B,cAAe1qB,SAAU1P,KAAK65B,yBChDhE,MAAMp3B,GAAWC,yBAAe,CAC9B23B,eAAgB,CAAF,qDAKdC,uBAAwB,CAAF,kHAOxB,MAAMC,WAAwB/6B,IAAMiE,UAClCC,YAAYzD,GACV0D,MAAM1D,GAEN,MAAM+3B,EAAKh4B,KAAKC,MAAM4E,OAAO8vB,aAC7B,IAAI6F,EAAe,EACnBxC,EAAG3lB,SAAUxU,IACPA,EAAE6S,MAAQ7S,EAAE6S,IAAIU,YAClBopB,MAGJ,MAAMxZ,EAASgX,EAAG/W,mBAClBjhB,KAAK0O,MAAQ,CACX8P,KAAMwC,EAASA,EAAOxC,KAAO,KAC7BC,KAAMuC,EAASA,EAAOvC,KAAO,KAC7BG,6BAAyB7I,EACzBykB,aAAcA,GAGhBx6B,KAAKy6B,qBAAuBz6B,KAAKy6B,qBAAqBz7B,KAAKgB,MAC3DA,KAAK2f,8BAAgC3f,KAAK2f,8BAA8B3gB,KAAKgB,MAC7EA,KAAK4f,4BAA8B5f,KAAK4f,4BAA4B5gB,KAAKgB,MACzEA,KAAK0f,yBAA2B1f,KAAK0f,yBAAyB1gB,KAAKgB,MACnEA,KAAK06B,oBAAsB16B,KAAK06B,oBAAoB17B,KAAKgB,MAG3Dy6B,qBAAqBE,GACnB36B,KAAK+O,SAAS,CAACgoB,SAAU4D,IACzB36B,KAAKC,MAAM+4B,gBAAgB2B,GAG7Bhb,8BAA8BuC,GAC5BliB,KAAK+O,SAAS,CACZ6P,wBAAyBsD,EACzBW,kBAAmB7iB,KAAK0O,MAAMwT,KAIlCtC,8BACE5f,KAAK+O,SAAS,CAAC6P,6BAAyB7I,IAG1C2J,yBAAyBsC,GACvB,IAAIhB,EAAS,GACbA,EAAOhhB,KAAK0O,MAAMkQ,yBAA2BoD,EAC7ChiB,KAAKC,MAAM+4B,qBAAgBjjB,OAAWA,EAAWiL,GAEjD,IAAIP,EAAW,CAAC7B,6BAAyB7I,GACzC0K,EAASzgB,KAAK0O,MAAMkQ,yBAA2BoD,EAC/ChiB,KAAK+O,SAAS0R,GAGhBia,oBAAoB90B,GAClBA,EAAEI,iBACF,MAAM,cAACpC,GAAiB5D,KAAKC,MAAM4D,KACnC7D,KAAKC,MAAMmE,YACTR,EAAcnB,GAAS43B,gBACvBz2B,EAAcnB,GAAS63B,wBACtB,KAAQt6B,KAAKC,MAAM26B,mBACpB,MACA,EACA,MAIJ76B,SACE,OACE,kBAAC,IAAMynB,SAAP,KAAiBxnB,KAAK0O,MAAMkQ,wBAC1B,kBAAC,GAAD,CACEjgB,KAAMqB,KAAK0O,MAAMmU,kBACjBrI,KAAK,IACLvC,SAAUjY,KAAK0f,yBACfpM,SAAUtT,KAAK4f,8BAEjB,yBAAKzf,UAAU,oBACb,yBAAKA,UAAU,qBACb,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,iBAAiByX,eAAe,cAGvD,6BACE,kBAAC,mBAAD,CAAkBzX,GAAG,4BAA4ByX,eAAe,aAE7D2iB,GAAuB,kBAAC,EAAD,CACtB/lB,YAAa+lB,EACbzvB,KAAK,WACLoN,WAAYxY,KAAKy6B,0BAIzB,yBAAKt6B,UAAU,OACf,yBAAKA,UAAU,qBACb,uBAAGoP,KAAK,IAAIpP,UAAU,kBAAkBI,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAM66B,aACtF,uBAAG36B,UAAU,kBAAb,eADF,KACsD,kBAAC,mBAAD,CAAkBM,GAAG,gBACvEyX,eAAe,YAEnB,uBAAG3I,KAAK,IAAIpP,UAAU,kBAAkBI,QAAUqF,IAAO5F,KAAK06B,oBAAoB90B,KAChF,uBAAGzF,UAAU,kBAAb,UADF,KACiD,kBAAC,mBAAD,CAAkBM,GAAG,wBAClEyX,eAAe,qBAGrB,yBAAK/X,UAAU,OACf,yBAAKA,UAAU,qBACb,6BACE,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,4BAA4ByX,eAAe,2BAIpE,yBAAK/X,UAAU,UACb,sCAAW,wBAAIA,UAAU,YACvBI,QAASP,KAAK2f,8BAA8B3gB,KAAKgB,KAAM,SAAUA,KAAK0O,MAAM8P,OAC9E,sCAAW,wBAAIre,UAAU,YACvBI,QAASP,KAAK2f,8BAA8B3gB,KAAKgB,KAAM,SAAUA,KAAK0O,MAAM+P,SAGjFze,KAAK0O,MAAM8rB,aAAe,EACzB,oCACE,yBAAKr6B,UAAU,OACf,yBAAKA,UAAU,kBACb,uBAAGA,UAAU,kBAAb,SADF,IAEE,uBAAGoP,KAAK,IAAIpP,UAAU,OAAOI,QAAUqF,IAAOA,EAAEI,iBAAkBhG,KAAKC,MAAM86B,kBAC3E,kBAAC,mBAAD,CAAkBt6B,GAAG,wBAAwByX,eAAe,6BAC1DtG,OAAQ,CAAEvK,MAAOrH,KAAK0O,MAAM8rB,mBAIlC,QAOCpyB,4BAAWmyB,IClJX,MAAMS,WAAuBx7B,IAAMM,cAChDC,SACE,OACE,yBAAKI,UAAU,oBACb,yBAAKA,UAAU,qBACb,uBAAGoP,KhDgEkB,uBgDhEKpP,UAAU,cAAc4F,OAAO,UACvD,uBAAG5F,UAAU,kBAAb,SADF,KACgD,kBAAC,mBAAD,CAAkBM,GAAG,kBACjEyX,eAAe,gBAEnB,uBAAG3I,KhDkEwB,+BgDlEKpP,UAAU,cAAc4F,OAAO,UAC7D,uBAAG5F,UAAU,kBAAb,eADF,KACsD,kBAAC,mBAAD,CAAkBM,GAAG,wBACvEyX,eAAe,sBAEnB,uBAAG3I,KhD2DsB,iCgD3DKpP,UAAU,cAAc4F,OAAO,UAC3D,uBAAG5F,UAAU,kBAAb,UADF,KACiD,kBAAC,mBAAD,CAAkBM,GAAG,sBAClEyX,eAAe,qBAGrB,yBAAK/X,UAAU,OACf,yBAAKA,UAAU,qBACb,yBAAKA,UAAU,kBACb,2BAAOA,UAAU,SAAQ,kBAAC,mBAAD,CAAkBM,GAAG,kBAC7CG,GAEH,yBAAKT,UAAU,kBACb,2BAAOA,UAAU,SAAQ,kBAAC,mBAAD,CAAkBM,GAAG,YAAYyX,eAAe,UACxExY,IAAO2sB,cAEV,yBAAKlsB,UAAU,kBACb,2BAAOA,UAAU,SAAQ,kBAAC,mBAAD,CAAkBM,GAAG,kBAC7CT,KAAKC,MAAMqsB,eAEd,yBAAKnsB,UAAU,kBACb,2BAAOA,UAAU,SAAQ,kBAAC,mBAAD,CAAkBM,GAAG,uBAAuByX,eAAe,qBACnFlY,KAAKC,MAAMssB,kBCjCT,MAAM0O,WAAkBz7B,IAAMiE,UAC3CC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXooB,MAAO72B,EAAM62B,MACbC,SAAU,GACVmE,SAAUj7B,EAAMssB,cAChB2K,UAAWf,GAAiBO,UAAU,mBAExC12B,KAAKm3B,kBAAoBn3B,KAAKm3B,kBAAkBn4B,KAAKgB,MACrDA,KAAKo3B,qBAAuBp3B,KAAKo3B,qBAAqBp4B,KAAKgB,MAC3DA,KAAKu3B,sBAAwBv3B,KAAKu3B,sBAAsBv4B,KAAKgB,MAC7DA,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAG7Cm3B,kBAAkBvxB,GAChB5F,KAAK+O,SAAS,CAAC+nB,MAAOlxB,EAAEG,OAAOtH,QAGjC24B,qBAAqBxxB,GACnB5F,KAAK+O,SAAS,CAACgoB,SAAUnxB,EAAEG,OAAOtH,QAGpC84B,wBACEpB,GAAiBQ,UAAU,kBAAmB32B,KAAK0O,MAAMwoB,WACzDl3B,KAAK+O,SAAS,CAACmoB,WAAYl3B,KAAK0O,MAAMwoB,YAGxChgB,aAAatR,GACXA,EAAEI,iBACFhG,KAAKC,MAAMk7B,QAAQn7B,KAAK0O,MAAMooB,MAAMpsB,OAAQ1K,KAAK0O,MAAMqoB,SAASrsB,QAGlE3K,SACE,IAAI03B,EAAgB,OAKpB,OAJIz3B,KAAKC,MAAMipB,WACbuO,GAAiB,aAIjB,0BAAMh3B,GAAG,aAAawX,SAAUjY,KAAKkX,cACnC,kBAAC,mBAAD,CAAkBzW,GAAG,eAAeyX,eAAe,SAGhDwf,GAAiB,2BAAOtsB,KAAK,OAAO3K,GAAG,aACtCqU,YAAa4iB,EACb9e,aAAa,WACbwiB,YAAY,MACZC,eAAe,OACf58B,MAAOuB,KAAK0O,MAAMooB,MAClBpnB,SAAU1P,KAAKm3B,kBACfhjB,UAAQ,EAACiC,WAAS,KAGtB,kBAAC,mBAAD,CAAkB3V,GAAG,kBAAkByX,eAAe,YAGnDyf,GAAoB,kBAAC,EAAD,CAAiBvsB,KAAK,WAAW3K,GAAG,gBACvDqU,YAAa6iB,EACb/e,aAAa,mBACbna,MAAOuB,KAAK0O,MAAMqoB,SAClBrnB,SAAU1P,KAAKo3B,qBACfjjB,UAAU,KAGd,yBAAKhU,UAAU,kBACb,kBAAC,EAAD,CAAUM,GAAG,aAAa1C,KAAK,aAAa+R,QAAS9P,KAAK0O,MAAMwoB,UAC9DxnB,SAAU1P,KAAKu3B,wBACjB,2BAAO5nB,QAAQ,cAAf,IACE,kBAAC,mBAAD,CAAkBlP,GAAG,iBAAiByX,eAAe,oBAGvD,uBAAG3I,KAAK,UACN,kBAAC,mBAAD,CAAkB9O,GAAG,uBAAuByX,eAAe,uBAI/D,yBAAK/X,UAAU,kBACb,4BAAQA,UAAWs3B,EAAersB,KAAK,UACrC,kBAAC,mBAAD,CAAkB3K,GAAG,iBAAiByX,eAAe,gBCtFjE,MAAMzV,GAAWC,yBAAe,CAC9B44B,WAAY,CAAF,qDAOZ,MAAMC,WAAqB/7B,IAAMM,cAC/B4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACX8sB,QAAS,IAGXx7B,KAAK6P,aAAe7P,KAAK6P,aAAa7Q,KAAKgB,MAC3CA,KAAK+oB,eAAiB/oB,KAAK+oB,eAAe/pB,KAAKgB,MAC/CA,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAG7C6P,aAAajK,GACX5F,KAAK+O,SAAS,CAACysB,QAAS51B,EAAEG,OAAOtH,QAGnCsqB,eAAenjB,GACC,UAAVA,EAAE7G,KACJiB,KAAKkX,aAAatR,GAItBsR,aAAatR,GAEX,GADAA,EAAEI,iBACEhG,KAAK0O,MAAM8sB,QAAS,CACtB,IAAIz9B,EAAOiC,KAAK0O,MAAM8sB,QAAQ9wB,OAC1B3M,EAAKmE,OAAS,IAA2B,OAArBnE,EAAKwD,OAAO,EAAG,IAAoC,OAArBxD,EAAKwD,OAAO,EAAG,IACnEvB,KAAKC,MAAMgY,SAASla,GAEpBiC,KAAKC,MAAMuG,QAAQxG,KAAKC,MAAM4D,KAAKD,cAAcnB,GAAS64B,YAAa,QAK7Ev7B,SACE,OACE,yBAAKI,UAAU,cACb,yBAAKA,UAAU,kBACf,kBAAC,mBAAD,CAAkBM,GAAG,uBAAuByX,eAAe,oBAExDlD,GAAW,2BAAO5J,KAAK,OAAO0J,YAAaE,EAC1CvW,MAAOuB,KAAK0O,MAAM8sB,QAAS9rB,SAAU1P,KAAK6P,aAC1Coa,WAAYjqB,KAAK+oB,eAAgB5U,UAAQ,MAG7C,yBAAKhU,UAAU,kBACb,4BAAQA,UAAU,OAAOI,QAASP,KAAKkX,cACrC,kBAAC,mBAAD,CAAkBzW,GAAG,mBAAmByX,eAAe,kBASpD9P,4BAAWmzB,IC5DX,MAAME,WAAsBj8B,IAAMM,cAC/C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXiE,GAAI,GACJH,QAAS,GACTgJ,aAAc,KACdW,KAAM,IAGRnc,KAAKs3B,eAAiBt3B,KAAKs3B,eAAet4B,KAAKgB,MAC/CA,KAAK07B,oBAAsB17B,KAAK07B,oBAAoB18B,KAAKgB,MACzDA,KAAKwf,mBAAqBxf,KAAKwf,mBAAmBxgB,KAAKgB,MACvDA,KAAK27B,kBAAoB37B,KAAK27B,kBAAkB38B,KAAKgB,MACrDA,KAAK27B,kBAAoB37B,KAAK27B,kBAAkB38B,KAAKgB,MACrDA,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAG7Cs3B,eAAe1xB,GACb5F,KAAK+O,SAAS,CAAC4D,GAAI/M,EAAEG,OAAOtH,QAG9Bi9B,oBAAoB91B,GAClB5F,KAAK+O,SAAS,CAACyD,QAAS5M,EAAEG,OAAOtH,QAGnC+gB,mBAAmBxS,GACjBhN,KAAK+O,SAAS,CAACyM,aAAcxO,IAG/B2uB,kBAAkBxf,GAChBnc,KAAK+O,SAAS,CAACoN,KAAMA,IAGvBjF,aAAatR,GACXA,EAAEI,iBAEF,MAAM2M,EAAK3S,KAAK0O,MAAMiE,GAAGjI,OAAOkC,UAAU,EnD4Bd,ImD3BtB6E,EAAUzR,KAAK0O,MAAM8D,QAAQ9H,OAAOkC,UAAU,EnD2BxB,ImD1BxB+F,GACF3S,KAAKC,MAAMgY,SAAStF,EAAI3S,KAAK0O,MAAM8M,aAAc/J,EAASzR,KAAK0O,MAAMyN,MAIzEpc,SACE,IAAI03B,EAAgB,OAIpB,OAHIz3B,KAAKC,MAAMipB,WACbuO,GAAiB,aAGjB,0BAAMt3B,UAAU,aAAa8X,SAAUjY,KAAKkX,cAC1C,yBAAK/W,UAAU,kBACb,yBAAKA,UAAU,qBACb,2BAAOA,UAAU,QAAQwP,QAAQ,gBAC/B,kBAAC,mBAAD,CAAkBlP,GAAG,sBAEvB,kBAAC,mBAAD,CAAkBA,GAAG,iCAAiCyX,eAAe,8BAElEpD,GAAgB,2BAAO1J,KAAK,OAAO3K,GAAG,eAAeqU,YAAaA,EACjErW,MAAOuB,KAAK0O,MAAMiE,GAAIjD,SAAU1P,KAAKs3B,eAAgBlhB,WAAS,EAACjC,UAAQ,KAE3E,6BACA,2BAAOhU,UAAU,QAAQwP,QAAQ,kBAC/B,kBAAC,mBAAD,CAAkBlP,GAAG,mBAEvB,kBAAC,mBAAD,CAAkBA,GAAG,+BAClBqU,GAAgB,2BAAO1J,KAAK,OAAO3K,GAAG,iBAAiBqU,YAAaA,EACnErW,MAAOuB,KAAK0O,MAAM8D,QAAS9C,SAAU1P,KAAK07B,wBAGhD,kBAAC,EAAD,CACEl1B,QAASxG,KAAKC,MAAMuG,QACpB2I,eAAgBnP,KAAKwf,sBAEzB,kBAAC,mBAAD,CAAkB/e,GAAG,qBAClBL,GAAU,kBAAC,GAAD,CACT+b,KAAMnc,KAAK0O,MAAMyN,KACjBE,WAAW,EACXO,cAAe5c,KAAK27B,kBACpBv7B,MAAOA,KAEX,yBAAKD,UAAU,kBACb,4BAAQA,UAAWs3B,GACjB,kBAAC,mBAAD,CAAkBh3B,GAAG,gBAAgByX,eAAe,eCvFjD,MAAM0jB,WAAuBp8B,IAAMM,cAChD4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXmtB,QAAQ,EACR9I,OAAQ,IAGV/yB,KAAK87B,mBAAqB97B,KAAK87B,mBAAmB98B,KAAKgB,MACvDA,KAAK+7B,aAAe/7B,KAAK+7B,aAAa/8B,KAAKgB,MAC3CA,KAAKg8B,YAAch8B,KAAKg8B,YAAYh9B,KAAKgB,MACzCA,KAAK6U,cAAgB7U,KAAK6U,cAAc7V,KAAKgB,MAG/C0F,uBACM1F,KAAK0O,MAAMmtB,SACb77B,KAAK+O,SAAS,CAACgkB,OAAQ,GAAI8I,QAAQ,IACnC77B,KAAKC,MAAMg8B,iBAAiBv8B,IAAOmiB,WAIvCia,mBAAmBl2B,GACjB5F,KAAK+O,SAAS,CAACgkB,OAAQntB,EAAEG,OAAOtH,QAGlCs9B,aAAan2B,GACXA,EAAEI,iBACF,IAAI4R,EAAQ5X,KAAK0O,MAAMqkB,OAAOroB,OAC9B1K,KAAK+O,SAAS,CAAC8sB,OAASjkB,EAAM1V,OAAS,IACvClC,KAAKC,MAAMg8B,iBAAiBrkB,EAAM1V,OAAS,EAAI0V,EAAQlY,IAAOmiB,UAGhEma,YAAYp2B,GACVA,EAAEI,iBACEhG,KAAK0O,MAAMmtB,QACb77B,KAAKC,MAAMg8B,iBAAiBv8B,IAAOmiB,UAErC7hB,KAAK+O,SAAS,CAACgkB,OAAQ,GAAI8I,QAAQ,IAGrChnB,cAAcjP,GACE,UAAVA,EAAE7G,IACJiB,KAAK+7B,aAAan2B,GACC,WAAVA,EAAE7G,KACXiB,KAAKg8B,cAITj8B,SACE,OACE,yBAAKI,UAAU,cACb,yBAAKA,UAAU,kBACb,uBAAGA,UAAU,yBAAb,UACA,kBAAC,mBAAD,CAAkBM,GAAG,qBAAqByX,eAAe,yDAEtDgkB,GAAuB,2BAAO/7B,UAAU,SAASiL,KAAK,OACrD0J,YAAaonB,EACbz9B,MAAOuB,KAAK0O,MAAMqkB,OAAQrjB,SAAU1P,KAAK87B,mBACzC3lB,UAAWnW,KAAK6U,cAAeV,UAAQ,EAACiC,WAAS,KAErD,uBAAG7G,KAAK,IAAIhP,QAASP,KAAKg8B,aACxB,uBAAG77B,UAAU,kBAAb,aCrDZ,MAAMsC,GAAWC,yBAAe,CAC9By5B,oBAAqB,CAAF,uEAKnBC,kBAAmB,CAAF,sEAOnB,MAAMC,WAAqB78B,IAAMiE,UAC/BC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACX4tB,YAAa,MACbC,YAAa,MAGfv8B,KAAKw8B,eAAiBx8B,KAAKw8B,eAAex9B,KAAKgB,MAC/CA,KAAKy8B,qBAAuBz8B,KAAKy8B,qBAAqBz9B,KAAKgB,MAC3DA,KAAK+W,sBAAwB/W,KAAK+W,sBAAsB/X,KAAKgB,MAC7DA,KAAK08B,qBAAuB18B,KAAK08B,qBAAqB19B,KAAKgB,MAC3DA,KAAK28B,gBAAkB38B,KAAK28B,gBAAgB39B,KAAKgB,MAGnDuF,oBACEvF,KAAKC,MAAMijB,aAGbsZ,eAAe52B,GACbA,EAAEI,iBACF/E,EAAe27B,WAAW37B,EAAe47B,YAAYh7B,OAAOC,SAASZ,KAAM,MAAO0E,EAAES,cAAcC,QAAQ7F,KAC1GT,KAAK+O,SAAS,CAACutB,YAAa12B,EAAES,cAAcC,QAAQ7F,KAGtDg8B,qBAAqB7kB,GACnB5X,KAAKC,MAAMg8B,iBAAiBrkB,GAC5B5X,KAAK+O,SAAS,CAACwtB,YAAa78B,IAAOo9B,YAAYllB,GAAS,KAAOA,IAGjEb,sBAAsBQ,GACW,QAA3BvX,KAAK0O,MAAM4tB,cACbr7B,EAAe27B,WAAW37B,EAAe87B,eAAel7B,OAAOC,SAASZ,KAAM,QAC9ElB,KAAKC,MAAM+8B,cAAczlB,OAAKxB,IAIlC2mB,qBAAqB3+B,EAAM4Q,EAASsuB,EAAM9gB,GACxClb,EAAe27B,WAAW37B,EAAe87B,eAAel7B,OAAOC,SAASZ,KAAM,QAC9ElB,KAAKC,MAAM+8B,mBAAcjnB,EAAWwF,GAAMxd,EAAM4Q,GAAUsuB,EAAM9gB,GAGlEwgB,gBAAgB/3B,GACd3D,EAAe27B,WAAW37B,EAAe87B,eAAel7B,OAAOC,SAASZ,KAAM,QAC9ElB,KAAKC,MAAM+8B,cAAcp4B,GAG3B7E,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KAC7Bq5B,EAA0Bt5B,EAAc5D,KAAK0O,MAAM6tB,YACvD95B,GAAS25B,kBAAoB35B,GAAS05B,qBACxC,OACE,yBAAKh8B,UAAU,eACb,wBAAIA,UAAU,UACZ,wBAAIA,UAAsC,QAA3BH,KAAK0O,MAAM4tB,YAAwB,SAAW,MAC3D,uBAAG/sB,KAAK,IAAI/H,UAAQ,MAAMjH,QAASP,KAAKw8B,gBACtC,kBAAC,mBAAD,CAAkB/7B,GAAG,qBAAqByX,eAAe,WAI7D,wBAAI/X,UAAsC,QAA3BH,KAAK0O,MAAM4tB,YAAwB,SAAW,MAC3D,uBAAG/sB,KAAK,IAAI/H,UAAQ,MAAMjH,QAASP,KAAKw8B,gBACtC,kBAAC,mBAAD,CAAkB/7B,GAAG,qBAAqByX,eAAe,gBAI7D,wBAAI/X,UAAsC,SAA3BH,KAAK0O,MAAM4tB,YAAyB,SAAW,MAC5D,uBAAG/sB,KAAK,IAAI/H,UAAQ,OAAOjH,QAASP,KAAKw8B,gBACvC,kBAAC,mBAAD,CAAkB/7B,GAAG,uBAAuByX,eAAe,aAKrC,QAA3BlY,KAAK0O,MAAM4tB,YACV,kBAAC,GAAD,CAAerkB,SAAUjY,KAAK08B,uBACH,SAA3B18B,KAAK0O,MAAM4tB,YACX,kBAAC,GAAD,CACErkB,SAAUjY,KAAK28B,gBACfn2B,QAASxG,KAAKC,MAAMuG,UACtB,yBAAKrG,UAAU,eACb,kBAAC,GAAD,CACEiL,KAAK,MACL6wB,iBAAkBj8B,KAAKy8B,uBACzB,kBAAC,EAAD,CACEpqB,SAAUrS,KAAKC,MAAMk9B,cACrBvqB,SAAU5S,KAAKC,MAAM2S,SACrBQ,iBAAkB8pB,EAClB3rB,YAAY,EACZuB,YAAY,EACZnC,iBAAiB,EACjBqC,gBAAiBhT,KAAK+W,2BAOrB3O,4BAAWi0B,ICrHX,MAAMe,WAA0B59B,IAAMM,cACnD4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXsoB,MAAO,GACPD,SAAU,IAGZ/2B,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAC3CA,KAAKq3B,kBAAoBr3B,KAAKq3B,kBAAkBr4B,KAAKgB,MACrDA,KAAKo3B,qBAAuBp3B,KAAKo3B,qBAAqBp4B,KAAKgB,MAG7DuF,oBACE,IAAIpD,EAASlB,EAAemB,aAAaP,OAAOC,SAASZ,MACzDlB,KAAK+O,SAAS,CAACsuB,MAAOl7B,EAAOd,OAAOg8B,MAAOC,OAAQn7B,EAAOd,OAAOi8B,SAGnEpmB,aAAatR,GACXA,EAAEI,iBACEhG,KAAK0O,MAAM2uB,MACbr9B,KAAKC,MAAMs9B,QAAQv9B,KAAK0O,MAAM4uB,OAAQt9B,KAAK0O,MAAMqoB,SAASrsB,OAAQ1K,KAAK0O,MAAM2uB,OAE7Er9B,KAAKC,MAAMu9B,UAAU,QAASx9B,KAAK0O,MAAMsoB,MAAMtsB,QAInD2sB,kBAAkBzxB,GAChB5F,KAAK+O,SAAS,CAACioB,MAAOpxB,EAAEG,OAAOtH,QAGjC24B,qBAAqBxxB,GACnB5F,KAAK+O,SAAS,CAACgoB,SAAUnxB,EAAEG,OAAOtH,QAGpCsB,SACE,IAAI09B,EAASz9B,KAAK0O,MAAM2uB,OAASr9B,KAAK0O,MAAM4uB,OAC5C,OACE,0BAAM78B,GAAG,sBAAsBwX,SAAUjY,KAAKkX,cAC3CumB,EACC,kBAAC,mBAAD,CAAkBh9B,GAAG,2BAA2ByX,eAAe,sBAE5DpD,GAAgB,kBAAC,EAAD,CACfA,YAAaA,EACb8D,aAAa,eACbna,MAAOuB,KAAK0O,MAAMqoB,SAClB5iB,UAAU,EAAMiC,WAAW,EAC3B1G,SAAU1P,KAAKo3B,wBAGnB,oCACE,2BAAOznB,QAAQ,cACb,kBAAC,mBAAD,CAAkBlP,GAAG,uBACnByX,eAAe,kCAGnB,kBAAC,mBAAD,CAAkBzX,GAAG,0BAA0ByX,eAAe,2BAE3DpD,GAAgB,2BAAO1J,KAAK,QAAQ3K,GAAG,aACtCqU,YAAaA,EACb8D,aAAa,QACbna,MAAOuB,KAAK0O,MAAMsoB,MAClBtnB,SAAU1P,KAAKq3B,kBACfljB,UAAQ,EAACiC,WAAS,MAI1B,yBAAKjW,UAAU,kBACb,4BAAQA,UAAU,OAAOiL,KAAK,UAAUqyB,EACtC,kBAAC,mBAAD,CAAkBh9B,GAAG,eAAeyX,eAAe,UACnD,kBAAC,mBAAD,CAAkBzX,GAAG,sBAAsByX,eAAe,qBCzEvD,MAAMwlB,WAAqBl+B,IAAMM,cAC9C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXwsB,SAAUj7B,EAAMssB,cAChBoR,SAAS,GAGX39B,KAAK49B,qBAAuB59B,KAAK49B,qBAAqB5+B,KAAKgB,MAC3DA,KAAKuY,sBAAwBvY,KAAKuY,sBAAsBvZ,KAAKgB,MAG/D49B,qBAAqBh4B,GACnB5F,KAAK+O,SAAS,CAACmsB,SAAUt1B,EAAEG,OAAOtH,MAAOk/B,SAAS,IAGpDplB,wBACMvY,KAAK0O,MAAMivB,UACb39B,KAAK+O,SAAS,CAAC4uB,SAAS,IACxB39B,KAAKC,MAAM49B,sBAAsB79B,KAAK0O,MAAMwsB,SAASxwB,SAIzD3K,SACE,IAAI+9B,EAAc,GAClB,IAAK,IAAI/+B,KAAO8B,EAAa,CAC3B,IAAIa,EAAOb,EAAY9B,GACvB++B,EAAY77B,KACV,4BAAQlD,IAAK2C,EAAMjD,MAAOiD,KAG9B,OACE,yBAAKvB,UAAU,kBACb,2BAAOiL,KAAK,SAAS3K,GAAG,YAAYqU,YAAa9U,KAAKC,MAAMi7B,SAAU6C,KAAK,cACzE59B,UAAU,SAAS1B,MAAOuB,KAAK0O,MAAMwsB,SAAUxrB,SAAU1P,KAAK49B,qBAC9D1nB,OAAQlW,KAAKuY,sBAAuBpE,UAAQ,IAC9C,8BAAU1T,GAAG,eACVq9B,KCtCI,MAAME,WAAqBx+B,IAAMM,cAC9C4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXuvB,UAAWh+B,EAAMg+B,WAAa,MAC9B1R,cAAetsB,EAAMssB,eAGvBvsB,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAC3CA,KAAKk+B,wBAA0Bl+B,KAAKk+B,wBAAwBl/B,KAAKgB,MACjEA,KAAKm+B,0BAA4Bn+B,KAAKm+B,0BAA0Bn/B,KAAKgB,MAGvEkX,aAAatR,GACXA,EAAEI,iBACFhG,KAAKC,MAAMm+B,SAAS,CAClBH,UAAWj+B,KAAK0O,MAAMuvB,UACtB1R,cAAevsB,KAAK0O,MAAM6d,gBAI9B2R,wBAAwBt4B,GACtB5F,KAAK+O,SAAS,CAACkvB,UAAWr4B,EAAES,cAAc5H,QAG5C0/B,0BAA0BpgC,GACxBiC,KAAK+O,SAAS,CAACwd,cAAexuB,IAGhCgC,SACE,MAAMia,EAAQ,CAACqkB,IAAK,UAAWC,GAAI,YAAaC,GAAI,gBACpD,IAAIC,EAAmB,GACnB3mB,EAAW7X,KAcf,MAbA,CAAC,MAAO,KAAM,MAAMuH,KAAI,SAAS7F,GAC/B,IAAIjB,EAAK,aAAeiB,EACpB3D,EAAOic,EAAMtY,GACjB88B,EAAiBv8B,KACf,wBAAIlD,IAAK2C,GACP,2BAAO0J,KAAK,QAAQ3K,GAAIA,EAAI1C,KAAK,mBAAmBU,MAAOiD,EACzDoO,QAAS+H,EAASnJ,MAAMuvB,YAAcv8B,EACtCgO,SAAUmI,EAASqmB,0BACrB,2BAAOvuB,QAASlP,GAAK1C,QAMzB,0BAAM0C,GAAG,gBAAgBN,UAAU,aAAa8X,SAAUjY,KAAKkX,cAC7D,yBAAK/W,UAAU,kBACb,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,sBAAsByX,eAAe,qBAI9D,kBAAC,GAAD,CAAcqU,cAAevsB,KAAK0O,MAAM6d,cACtCsR,sBAAuB79B,KAAKm+B,4BAC9B,yBAAKh+B,UAAU,kBACb,2BAAOA,UAAU,SACf,kBAAC,mBAAD,CAAkBM,GAAG,uBAAuByX,eAAe,sBAI/D,yBAAK/X,UAAU,kBACb,wBAAIA,UAAU,UACXq+B,IAGL,yBAAKr+B,UAAU,kBACb,4BAAQiL,KAAK,SAASjL,UAAU,QAC9B,kBAAC,mBAAD,CAAkBM,GAAG,gBAAgByX,eAAe,eCxEhE,MAAMzV,GAAWC,yBAAe,CAC9B+7B,MAAO,CAAF,0CAKLzH,MAAO,CAAF,4CAOP,MAAM0H,WAAuBl/B,IAAMM,cACjC4D,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ,CACXiwB,KAAM1+B,EAAM2+B,UAAY,IAG1B5+B,KAAK6P,aAAe7P,KAAK6P,aAAa7Q,KAAKgB,MAC3CA,KAAK+oB,eAAiB/oB,KAAK+oB,eAAe/pB,KAAKgB,MAC/CA,KAAKkX,aAAelX,KAAKkX,aAAalY,KAAKgB,MAC3CA,KAAKgU,aAAehU,KAAKgU,aAAahV,KAAKgB,MAG7C6P,aAAajK,GACX5F,KAAK+O,SAAS,CAAC4vB,KAAM/4B,EAAEG,OAAOtH,QAGhCsqB,eAAenjB,GACC,UAAVA,EAAE7G,IACJiB,KAAKkX,aAAatR,GACA,UAATA,EAAE7G,KACXiB,KAAKgU,aAAapO,GAItBsR,aAAatR,GACXA,EAAEI,iBACEhG,KAAK0O,MAAMiwB,MAAQ3+B,KAAK0O,MAAMiwB,KAAKj0B,QACrC1K,KAAKC,MAAMgY,SAASjY,KAAKC,MAAM4+B,WAAY7+B,KAAK0O,MAAMiwB,KAAKj0B,QAI/DsJ,aAAapO,GACXA,EAAEI,iBACFhG,KAAKC,MAAMqT,WAIbvT,SACE,MAAM,cAAE6D,GAAkB5D,KAAKC,MAAM4D,KAErC,IAAIo1B,EADY,CAAC,MAASr1B,EAAcnB,GAASu0B,OAAQ,IAAOpzB,EAAcnB,GAASg8B,QAClEz+B,KAAKC,MAAM4+B,aAAe7+B,KAAKC,MAAM4+B,WAC1D,OACE,yBAAK1+B,UAAU,cACb,yBAAKA,UAAU,kBACb,2BAAOA,UAAU,QAAQwP,QAAQ,2BAC/B,kBAAC,mBAAD,CAAkBlP,GAAG,iCACnByX,eAAe,mDAEftG,OAAQ,CAACqnB,OAAQA,OAGvB,yBAAK94B,UAAU,kBACf,kBAAC,mBAAD,CAAkBM,GAAG,mCACnByX,eAAe,gBACd4mB,GAAiB,2BAAO1zB,KAAK,OAAO3K,GAAG,0BACtCqU,YAAagqB,EACbrgC,MAAOuB,KAAK0O,MAAMiwB,KAAMjvB,SAAU1P,KAAK6P,aACvCoa,WAAYjqB,KAAK+oB,eAAgB5U,UAAQ,MAG7C,yBAAKhU,UAAU,kBACb,4BAAQA,UAAU,OAAOI,QAASP,KAAKkX,cACrC,kBAAC,mBAAD,CAAkBzW,GAAG,iBAAiByX,eAAe,aAEvD,4BAAQ/X,UAAU,QAAQI,QAASP,KAAKgU,cACtC,kBAAC,mBAAD,CAAkBvT,GAAG,sBAQlB2H,4BAAWs2B,ICvE1B,MAAMj8B,GAAWC,yBAAe,CAC9B,4DAKA,yEAKA,mEAKA,+EAKA,oEAKA,uEAKA,8EAKA,oEAKA,qEAKA,sEAKA,mEAKA,uEAKA,wEAOF,MAAMq8B,WAAsBv/B,IAAMiE,UAChCC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAKg/B,qBAAuBh/B,KAAKg/B,qBAAqBhgC,KAAKgB,MAC3DA,KAAKi/B,eAAiBj/B,KAAKi/B,eAAejgC,KAAKgB,MAGjDg/B,qBAAqBlI,EAAOC,GAC1B/2B,KAAKC,MAAMi/B,eAAepI,EAAOC,GAGnCkI,iBACEj/B,KAAKC,MAAMg4B,gBAAgB,UAG7Bl4B,SACE,MAAM,cAAC6D,GAAiB5D,KAAKC,MAAM4D,KAC7Bs7B,EAAOn/B,KAAKC,MAAMyO,QAAU1O,KAAKC,MAAM2S,SAAW,WAAa,SAErE,IAAIxS,EAAO+J,EASPmJ,EAKJ,MAbY,YAAR6rB,GACF/+B,EAAQJ,KAAKC,MAAMG,MACnB+J,GAASnK,KAAKC,MAAMkK,QAASnK,KAAKC,MAAMkK,SAExC/J,EAAQwD,EAAcnB,GAAS08B,IAC/Bh1B,GAAS,IAIiC,GAAxC,CAAC,QAAS,YAAYqC,QAAQ2yB,KAChC7rB,EAAWtT,KAAKC,MAAMqT,UAItB,yBAAK7S,GAAG,YAAYN,UAAWH,KAAKC,MAAM4zB,SAAW,YAAc,MACjE,kBAAC,GAAD,CACEnlB,MAAOywB,EACP/+B,MAAOA,EACP+J,OAAQA,EACRyI,SAAU5S,KAAKC,MAAM2S,SACrB0iB,SAAUt1B,KAAKC,MAAMq1B,SACrBF,WAAYp1B,KAAKC,MAAMm1B,WACvBD,WAAYn1B,KAAKi/B,eACjB3rB,SAAUA,IAEZ,kBAAC,EAAD,CACEG,MAAOzT,KAAKC,MAAM0jB,WAClB/P,KAAM5T,KAAKC,MAAM2jB,UACjBjS,OAAQ3R,KAAKC,MAAMm/B,YACnBtrB,WAAY9T,KAAKC,MAAMo/B,gBACvB3rB,aAAc1T,KAAKC,MAAMuG,UAE3B,kBAAC,GAAD,CAAagN,KAAMxT,KAAKC,MAAMq/B,qBAEpB,UAATH,EACC,kBAAC,GAAD,CACErI,MAAO92B,KAAKC,MAAM62B,MAClB5N,SAAUlpB,KAAKC,MAAMs/B,cACrBpE,QAASn7B,KAAKg/B,uBAEP,aAATG,EACA,kBAAC,GAAD,CACE3H,gBAAiBx3B,KAAKC,MAAMu3B,gBAC5BlkB,SAAUtT,KAAKC,MAAMqT,SACrB9M,QAASxG,KAAKC,MAAMuG,UAEb,aAAT24B,EACA,kBAAC,GAAD,CACElB,UAAWj+B,KAAKC,MAAMg+B,UACtB1R,cAAevsB,KAAKC,MAAMssB,cAC1BjZ,SAAUtT,KAAKC,MAAMqT,SACrB8qB,SAAUp+B,KAAKC,MAAMu/B,mBAEd,SAATL,EACA,kBAAC,GAAD,CACEt6B,OAAQ7E,KAAKC,MAAM4E,OACnB+N,SAAU5S,KAAKC,MAAM2S,SACrBqlB,gBAAiBj4B,KAAKC,MAAMg4B,kBAErB,YAATkH,EACA,kBAAC,GAAD,CACEt6B,OAAQ7E,KAAKC,MAAM4E,OACnB+N,SAAU5S,KAAKC,MAAM2S,SACrBomB,gBAAiBh5B,KAAKC,MAAM+4B,gBAC5BK,aAAcr5B,KAAKC,MAAMw/B,oBACzBrG,UAAWp5B,KAAKC,MAAMm5B,UACtBM,aAAc15B,KAAKC,MAAMy5B,aACzBD,cAAez5B,KAAKC,MAAMw5B,cAC1BxB,gBAAiBj4B,KAAKC,MAAMg4B,gBAC5BzxB,QAASxG,KAAKC,MAAMuG,UAEb,UAAT24B,EACA,kBAAC,GAAD,CACElF,cAAej6B,KAAKC,MAAMg6B,cAC1BE,cAAen6B,KAAKC,MAAMk6B,cAC1BD,qBAAsBl6B,KAAKC,MAAMi6B,qBACjCE,cAAep6B,KAAKC,MAAMm6B,cAC1BL,0BAA2B/5B,KAAKC,MAAM85B,0BACtCD,sBAAuB95B,KAAKC,MAAM65B,sBAClCE,sBAAuBh6B,KAAKC,MAAM+5B,wBAE3B,aAATmF,EACA,kBAAC,GAAD,CACEt6B,OAAQ7E,KAAKC,MAAM4E,OACnBm0B,gBAAiBh5B,KAAKC,MAAM+4B,gBAC5B8B,SAAU96B,KAAKC,MAAM66B,SACrBF,gBAAiB56B,KAAKC,MAAM26B,gBAC5Bx2B,YAAapE,KAAKC,MAAMmE,YACxB22B,cAAe/6B,KAAKC,MAAM86B,gBAEnB,YAAToE,EACA,kBAAC,GAAD,CACE5S,cAAevsB,KAAKC,MAAMssB,cAC1BD,cAAetsB,KAAKC,MAAMqsB,gBAElB,aAAT6S,GAA+B,WAARA,GAA6B,WAARA,EAC7C,kBAAC,GAAD,CACEt6B,OAAQ7E,KAAKC,MAAM4E,OACnB+N,SAAU5S,KAAKC,MAAM2S,SACrBG,UAAW/S,KAAKC,MAAM8S,UACtBb,cAAelS,KAAKC,MAAMiS,cAC1B3O,QAAiB,WAAR47B,EACTrJ,QAAiB,WAARqJ,EACTtJ,SAAU71B,KAAKC,MAAM41B,SACrBllB,gBAAiB3Q,KAAKC,MAAM0Q,gBAC5BqC,gBAAiBhT,KAAKC,MAAM+S,gBAC5BkjB,cAAel2B,KAAKC,MAAMi2B,gBAEnB,WAATiJ,EACA,kBAAC,GAAD,CACEhC,cAAen9B,KAAKC,MAAMk9B,cAC1Bja,WAAYljB,KAAKC,MAAMijB,WACvB+Y,iBAAkBj8B,KAAKC,MAAMg8B,iBAC7Be,cAAeh9B,KAAKC,MAAM+8B,cAC1Bx2B,QAASxG,KAAKC,MAAMuG,UAEb,SAAT24B,EACA,kBAAC,GAAD,CACEP,SAAU5+B,KAAKC,MAAM2+B,SACrBC,WAAY7+B,KAAKC,MAAM4+B,WACvB5mB,SAAUjY,KAAKC,MAAMy/B,sBACrBpsB,SAAUtT,KAAKC,MAAMqT,SACrB9M,QAASxG,KAAKC,MAAMuG,UAEb,UAAT24B,EACA,kBAAC,GAAD,CACE3B,UAAWx9B,KAAKC,MAAM0/B,uBACtBpC,QAASv9B,KAAKC,MAAM2/B,kBACtB,OAMKx3B,4BAAW22B,ICjPnB,SAASc,KACd,IAAIC,EAAO9+B,EAQX,MAP8B,iBAAnBa,OAAOC,WACgB,SAA5BD,OAAOC,SAASi+B,UAAmD,aAA5Bl+B,OAAOC,SAASk+B,SACzDF,EAAOj/B,EAAYE,MACVc,OAAOC,SAASk+B,WACzBF,EAAOj+B,OAAOC,SAASk+B,UAAYn+B,OAAOC,SAASm+B,KAAO,IAAMp+B,OAAOC,SAASm+B,KAAO,MAGpFH,EAIF,SAASI,KACd,MAA8B,iBAAnBr+B,OAAOC,UACmB,UAA5BD,OAAOC,SAASi+B,SCS3B,MAAMI,GAAY,IAAIC,MAAM,iBAEtB39B,GAAWC,yBAAe,CAC9B29B,oBAAqB,CAAF,oFAKnBC,cAAe,CAAF,6CAKbC,iBAAkB,CAAF,gFAKhBC,uBAAwB,CAAF,qEAKtBC,cAAe,CAAF,mDAKbC,mBAAoB,CAAF,gEAOpB,MAAMC,WAAkBnhC,IAAMiE,UAC5BC,YAAYzD,GACV0D,MAAM1D,GAEND,KAAK0O,MAAQ1O,KAAK4gC,gBAElB5gC,KAAK6gC,aAAe7gC,KAAK6gC,aAAa7hC,KAAKgB,MAC3CA,KAAK8gC,gBAAkB9gC,KAAK8gC,gBAAgB9hC,KAAKgB,MACjDA,KAAK+gC,aAAe/gC,KAAK+gC,aAAa/hC,KAAKgB,MAC3CA,KAAKghC,kBAAoBhhC,KAAKghC,kBAAkBhiC,KAAKgB,MACrDA,KAAKihC,sBAAwBjhC,KAAKihC,sBAAsBjiC,KAAKgB,MAC7DA,KAAKkhC,YAAclhC,KAAKkhC,YAAYliC,KAAKgB,MACzCA,KAAKmhC,mBAAqBnhC,KAAKmhC,mBAAmBniC,KAAKgB,MACvDA,KAAKohC,gBAAkBphC,KAAKohC,gBAAgBpiC,KAAKgB,MACjDA,KAAKqhC,6BAA+BrhC,KAAKqhC,6BAA6BriC,KAAKgB,MAC3EA,KAAKshC,QAAUthC,KAAKshC,QAAQtiC,KAAKgB,MACjCA,KAAKuhC,yBAA2BvhC,KAAKuhC,yBAAyBviC,KAAKgB,MACnEA,KAAKwhC,sBAAwBxhC,KAAKwhC,sBAAsBxiC,KAAKgB,MAC7DA,KAAKyhC,iBAAmBzhC,KAAKyhC,iBAAiBziC,KAAKgB,MACnDA,KAAK0hC,aAAe1hC,KAAK0hC,aAAa1iC,KAAKgB,MAC3CA,KAAK2hC,kBAAoB3hC,KAAK2hC,kBAAkB3iC,KAAKgB,MACrDA,KAAK4hC,gBAAkB5hC,KAAK4hC,gBAAgB5iC,KAAKgB,MACjDA,KAAK6hC,iBAAmB7hC,KAAK6hC,iBAAiB7iC,KAAKgB,MACnDA,KAAK8hC,OAAS9hC,KAAK8hC,OAAO9iC,KAAKgB,MAC/BA,KAAK+hC,WAAa/hC,KAAK+hC,WAAW/iC,KAAKgB,MACvCA,KAAKgiC,iBAAmBhiC,KAAKgiC,iBAAiBhjC,KAAKgB,MACnDA,KAAKy8B,qBAAuBz8B,KAAKy8B,qBAAqBz9B,KAAKgB,MAC3DA,KAAKiiC,oBAAsBjiC,KAAKiiC,oBAAoBjjC,KAAKgB,MACzDA,KAAKkiC,uBAAyBliC,KAAKkiC,uBAAuBljC,KAAKgB,MAC/DA,KAAKmiC,kBAAoBniC,KAAKmiC,kBAAkBnjC,KAAKgB,MACrDA,KAAKoiC,wBAA0BpiC,KAAKoiC,wBAAwBpjC,KAAKgB,MACjEA,KAAKqiC,iBAAmBriC,KAAKqiC,iBAAiBrjC,KAAKgB,MACnDA,KAAKsiC,wBAA0BtiC,KAAKsiC,wBAAwBtjC,KAAKgB,MACjEA,KAAKuiC,2BAA6BviC,KAAKuiC,2BAA2BvjC,KAAKgB,MACvEA,KAAKwiC,+BAAiCxiC,KAAKwiC,+BAA+BxjC,KAAKgB,MAC/EA,KAAKyiC,0BAA4BziC,KAAKyiC,0BAA0BzjC,KAAKgB,MACrEA,KAAK0iC,eAAiB1iC,KAAK0iC,eAAe1jC,KAAKgB,MAC/CA,KAAK2iC,qBAAuB3iC,KAAK2iC,qBAAqB3jC,KAAKgB,MAC3DA,KAAK4iC,kBAAoB5iC,KAAK4iC,kBAAkB5jC,KAAKgB,MACrDA,KAAK6iC,kBAAoB7iC,KAAK6iC,kBAAkB7jC,KAAKgB,MACrDA,KAAK8iC,0BAA4B9iC,KAAK8iC,0BAA0B9jC,KAAKgB,MACrEA,KAAK+iC,cAAgB/iC,KAAK+iC,cAAc/jC,KAAKgB,MAC7CA,KAAKgjC,iBAAmBhjC,KAAKgjC,iBAAiBhkC,KAAKgB,MACnDA,KAAKijC,kBAAoBjjC,KAAKijC,kBAAkBjkC,KAAKgB,MACrDA,KAAKkjC,kBAAoBljC,KAAKkjC,kBAAkBlkC,KAAKgB,MACrDA,KAAKmjC,gBAAkBnjC,KAAKmjC,gBAAgBnkC,KAAKgB,MACjDA,KAAKojC,iBAAmBpjC,KAAKojC,iBAAiBpkC,KAAKgB,MACnDA,KAAKqjC,sBAAwBrjC,KAAKqjC,sBAAsBrkC,KAAKgB,MAC7DA,KAAKsjC,sBAAwBtjC,KAAKsjC,sBAAsBtkC,KAAKgB,MAC7DA,KAAKujC,sBAAwBvjC,KAAKujC,sBAAsBvkC,KAAKgB,MAC7DA,KAAKwjC,yBAA2BxjC,KAAKwjC,yBAAyBxkC,KAAKgB,MACnEA,KAAKyjC,wBAA0BzjC,KAAKyjC,wBAAwBzkC,KAAKgB,MACjEA,KAAKsgB,kBAAoBtgB,KAAKsgB,kBAAkBthB,KAAKgB,MACrDA,KAAK0jC,aAAe1jC,KAAK0jC,aAAa1kC,KAAKgB,MAC3CA,KAAK06B,oBAAsB16B,KAAK06B,oBAAoB17B,KAAKgB,MACzDA,KAAK2jC,4BAA8B3jC,KAAK2jC,4BAA4B3kC,KAAKgB,MACzEA,KAAK4jC,wBAA0B5jC,KAAK4jC,wBAAwB5kC,KAAKgB,MACjEA,KAAK6jC,wBAA0B7jC,KAAK6jC,wBAAwB7kC,KAAKgB,MACjEA,KAAK8jC,kBAAoB9jC,KAAK8jC,kBAAkB9kC,KAAKgB,MACrDA,KAAK+jC,sBAAwB/jC,KAAK+jC,sBAAsB/kC,KAAKgB,MAC7DA,KAAKgkC,wBAA0BhkC,KAAKgkC,wBAAwBhlC,KAAKgB,MACjEA,KAAKikC,sBAAwBjkC,KAAKikC,sBAAsBjlC,KAAKgB,MAC7DA,KAAKkkC,gBAAkBlkC,KAAKkkC,gBAAgBllC,KAAKgB,MACjDA,KAAKmkC,mBAAqBnkC,KAAKmkC,mBAAmBnlC,KAAKgB,MACvDA,KAAKokC,mBAAqBpkC,KAAKokC,mBAAmBplC,KAAKgB,MACvDA,KAAK+f,0BAA4B/f,KAAK+f,0BAA0B/gB,KAAKgB,MACrEA,KAAKqkC,iCAAmCrkC,KAAKqkC,iCAAiCrlC,KAAKgB,MACnFA,KAAKskC,2BAA6BtkC,KAAKskC,2BAA2BtlC,KAAKgB,MACvEA,KAAKukC,oBAAsBvkC,KAAKukC,oBAAoBvlC,KAAKgB,MACzDA,KAAKwkC,wBAA0BxkC,KAAKwkC,wBAAwBxlC,KAAKgB,MAGnE4gC,gBACE,MAAM6D,EAAWtO,GAAiBO,UAAU,aAAe,GAE3D,MAAO,CACL3jB,WAAW,EAEXoc,OAAO,EAEPuV,WAAW,EACXzG,UAAWwG,EAASxG,WAAa,KACjC1R,cAAekY,EAASlY,eAAiBsT,KACzCvT,cAAe,gBAEf2N,eAAgBwK,EAASE,iBACzBvK,eAAe,EACfD,cAAesK,EAAStK,cACxBD,sBAAuBgG,MD9HG,iBAAnBr+B,OAAOC,UACmB,aAA5BD,OAAOC,SAASk+B,gBC8HC,IAAZpgC,GAAiD,oBAAb2I,WACnB,oBAAjBq8B,cACVC,cAAe1O,GAAiBO,UAAU,kBAE1CzH,oBAAqBzpB,SAASs/B,OAE9BlhB,UAAW,GACXD,WAAY,KACZyb,iBAAarpB,EACbspB,gBAAiB,KAEjB0F,kBAAmB,QACnBC,eAAgB,KAChBC,gBAAiB,KACjB3F,oBAAoB,EAEpBxI,MAAO,GACPC,SAAU,GACVnkB,SAAU,KACVsyB,eAAgB38B,UAAU48B,OAC1BjzB,cAAe,GACfkzB,qBAAqB,EACrBC,iBAAkB,KAClBhW,eAAgB,KAChBkQ,eAAe,EACf7b,cAAgB7hB,OAAOyjC,Y5DzIG,I4D0I1BC,eAAe,EACfC,YAAa,YAEbC,oBAAoB,EACpBC,kBAAmB,KACnBC,mBAAoB,KACpBC,kBAAmB,KACnBC,iBAAkB,GAGlBC,cAAc,EACdC,YAAa,GAGblQ,SAAU,GAEVsH,cAAe,GAEftZ,mBAAoB,GACpBgb,gBAAY9oB,EACZ6oB,cAAU7oB,GAIdxQ,oBACE1D,OAAO4D,iBAAiB,SAAUzF,KAAK6gC,cACvCh/B,OAAO4D,iBAAiB,SAAWG,IAAQ5F,KAAK+gC,cAAa,KAC7Dl/B,OAAO4D,iBAAiB,UAAYG,IAAQ5F,KAAK+gC,cAAa,KAC9Dl/B,OAAO4D,iBAAiB,aAAczF,KAAK8gC,iBAE3Ct7B,SAASC,iBAAiB,mBAAoBzF,KAAKihC,uBAEnDjhC,KAAK+O,SAAS,CACZkZ,cAAeziB,SAASwgC,gBAAgBC,YACxCtX,eAAgBnpB,SAASwgC,gBAAgBE,eAG3C,MAAM,cAACtiC,EAAD,OAAgB0E,GAAUtI,KAAKC,MAAM4D,KAO3C,GANA7D,KAAK6E,OAAS87B,GAAUwF,QAAQnmC,KAAK0O,MAAM6d,cAAevsB,KAAK0O,MAAMuvB,UAAW31B,GAChFtI,KAAK6E,OAAOuhC,UAAYpmC,KAAKohC,gBAC7BphC,KAAK6E,OAAOwhC,aAAermC,KAAKyhC,iBAChCzhC,KAAK6E,OAAOyhC,yBAA2BtmC,KAAKqhC,6BAGxCrhC,KAAK0O,MAAMwrB,qBACb,IACEl6B,KAAKumC,OAAS3mC,gBAAuBglC,cAAehkC,GAAU4lC,YAC9DxmC,KAAKumC,OAAOE,kBAAkB7B,cAAc8B,mBAC5Cn+B,UAAUo+B,cAAcC,SAAS,sBAAsBniC,KAAMoiC,IAC3D7mC,KAAKghC,kBAAkB6F,GACvB7mC,KAAKumC,OAAOO,iBAAiBD,GAC7BA,EAAI/tB,OAAOiuB,YAAYzQ,KAAKC,UAAU,CAACjuB,OAAQA,KAC/CtI,KAAKkjC,oBACDljC,KAAK0O,MAAMyrB,gBACRn6B,KAAK0O,MAAMm2B,cAGd7kC,KAAK6E,OAAOmiC,eAAehnC,KAAK0O,MAAMm2B,eAAe,GAFrD7kC,KAAKmjC,iBAAgB,MAKxBn+B,MAAOC,IAERE,QAAQC,IAAI,qCAAsCH,KAEpD,MAAOA,GACPjF,KAAKkhC,YAAYt9B,EAAc,CAACnD,GAAI,qBAAsB,OAC1D0E,QAAQC,IAAI,0CAA2CH,GACvDjF,KAAK+O,SAAS,CAACmrB,sBAAsB,IAIzC,MAAMmD,EAAQlH,GAAiBO,UAAU,kBACvCP,GAAiBO,UAAU,mBAAgB3gB,EAEvCkxB,EAAYhmC,EAAemB,aAAaP,OAAOC,SAASZ,MAC1Dm8B,GACFr9B,KAAK+O,SAAS,CAAC21B,WAAW,IAG1BrH,EAAM6J,QAAU,IAAIv+B,KAAK00B,EAAM6J,SAC/BlnC,KAAK6E,OAAOsiC,aAAa9J,GACzBr9B,KAAK6E,OAAOuiC,UAAUpiC,MAAOC,IAE3BjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,gBAEzB+hC,EAAU5lC,OAAOmB,YACjBykC,EAAU5lC,OAAOgmC,IACxBJ,EAAU3lC,KAAK,GAAK,GACpBL,EAAe27B,WAAW37B,EAAeoB,eAAe4kC,EAAU3lC,KAAM2lC,EAAU5lC,UACxE4lC,EAAU5lC,OAAOg8B,OAC3Bp8B,EAAe27B,WAAW,IAG5B58B,KAAKsnC,UAAY,KACjBtnC,KAAKunC,kBAAoB,KAEzBvnC,KAAK8gC,kBAGPp7B,uBACE7D,OAAO8D,oBAAoB,SAAU3F,KAAK6gC,cAC1Ch/B,OAAO8D,oBAAoB,aAAc3F,KAAK8gC,iBAC9Ct7B,SAASG,oBAAoB,mBAAoB3F,KAAKihC,uBAIxD,eAAe1U,EAAe0R,EAAW31B,GACvC,MAAMzD,EAAS,IAAInF,IAAOkB,EAAU2rB,E5DtRjB,mC4DsRyC0R,EAAWiC,MAGvE,OAFAr7B,EAAO2iC,iBAAiBl/B,GACxBzD,EAAO4iC,e5D/QoB,G4D+QW,GAC/B5iC,EAGTg8B,eACE,MAAM6G,EAASliC,SAASwgC,gBAAgBC,a5D5PZ,I4D6P5BjmC,KAAK+O,SAAS,CACZkZ,cAAeziB,SAASwgC,gBAAgBC,YACxCtX,eAAgBnpB,SAASwgC,gBAAgBE,eAEvClmC,KAAK0O,MAAMgV,eAAiBgkB,GAC9B1nC,KAAK+O,SAAS,CAAC2U,cAAegkB,IAKlC1G,kBAAkB6F,GAChBA,EAAIc,cAAgB,KAClB,MAAMC,EAAmBf,EAAIgB,WAC7BD,EAAiBE,cAAgB,KAC/B,GAA8B,aAA1BF,EAAiBl5B,OAAwBnG,UAAUo+B,cAAcoB,WAAY,CAC/E,MAAM/gC,EAAM,oCACV,kBAAC,mBAAD,CAAkBvG,GAAG,mBACnByX,eAAe,sBAFP,IAGwD,uBAAG3I,KAAK,IACxE,kBAAC,mBAAD,CAAkB9O,GAAG,gBACnByX,eAAe,YALT,KAQZlY,KAAKkhC,YAAYl6B,EAAK,WAO9B85B,kBACE,MAAM5/B,EAAOD,EAAemB,aAAaP,OAAOC,SAASZ,MACrDA,EAAKI,MAAQJ,EAAKI,KAAKY,OAAS,GAE9B,CAAC,WAAW,WAAW,OAAO,QAAQ,WAAW,UAAU,UAC3D,OAAO,QAAQ,SAAS,UAAU,UAAU,WAAW,IAAIuT,SAASvU,EAAKI,KAAK,IAChFtB,KAAK+O,SAAS,CAACg2B,kBAAmB7jC,EAAKI,KAAK,KAE5C6D,QAAQC,IAAI,yBAA0BlE,EAAKI,KAAK,IAI9CJ,EAAKI,KAAKY,OAAS,GAAKhB,EAAKI,KAAK,IAAMtB,KAAK0O,MAAMwD,eACrDlS,KAAK+O,SAAS,CACZmD,cAAexS,IAAO2K,UAAUnJ,EAAKI,KAAK,IAAMJ,EAAKI,KAAK,GAAK,QAKnEtB,KAAK+O,SAAS,CAACg2B,kBAAmB,KAIhC7jC,EAAKG,OAAO43B,QACdj5B,KAAK+O,SAAS,CAAE8vB,WAAY39B,EAAKG,OAAO43B,SAEtC/3B,EAAKG,OAAOs9B,MACd3+B,KAAK+O,SAAS,CAAE6vB,SAAU19B,EAAKG,OAAOs9B,OAIxC3+B,KAAK+O,SAAS,CACZw2B,cAAerkC,EAAKG,OAAOmB,KAC3BwlC,oBAAqB9mC,EAAKG,OAAOgmC,MAIrCtG,aAAajwB,GACPA,EACF9Q,KAAKkhC,cAELlhC,KAAKkhC,YAAYlhC,KAAKC,MAAM4D,KAAKD,cAAc,CAACnD,GAAI,kBAAmB,QAEzET,KAAK+O,SAAS,CAACm2B,eAAgBp0B,IAGjCmwB,wBACEjhC,KAAK+O,SAAS,CAACkgB,oBAAqBzpB,SAASs/B,SAG/C5D,YAAYj8B,EAAKwO,EAAO9B,EAAQmC,GAC9B9T,KAAK+O,SAAS,CAAC6U,UAAW3e,EAAK0e,WAAYlQ,EAAO2rB,YAAaztB,EAAQ0tB,gBAAiBvrB,IAI1FqtB,mBAAmBrK,EAAOC,GACxB/2B,KAAK+O,SAAS,CACZwwB,eAAe,EACfzI,MAAOA,EACPC,SAAUA,EACVuI,oBAAoB,EACpBoF,WAAW,IAEb1kC,KAAKkhC,YAAY,GAAI,MAEjBlhC,KAAK6E,OAAOojC,cACdjoC,KAAKshC,QAAQxK,EAAOC,EAAU,CAACwC,KAAMv5B,KAAK0O,MAAMmwB,WAAY1Y,KAAMnmB,KAAK0O,MAAMkwB,WAE7E5+B,KAAK6E,OAAOuiC,UAAUpiC,MAAOC,IAE3BjF,KAAK+O,SAAS,CAACwwB,eAAe,EAAOmF,WAAW,EAAOpF,oBAAoB,IAC3Et/B,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAMpCk8B,kBACE,MAAM//B,EAASrB,KAAK6E,OAAOqjC,gBAC3BloC,KAAK+O,SAAS,CACZud,cAAejrB,EAAO8mC,IAAM,KAAO9mC,EAAOogB,MAAQpgB,EAAOogB,MAAQ,UAG/DzhB,KAAK0O,MAAMg2B,WACb1kC,KAAKshC,QAAQthC,KAAK0O,MAAMooB,MAAO92B,KAAK0O,MAAMqoB,SAAU,CAACwC,KAAMv5B,KAAK0O,MAAMmwB,WAAY1Y,KAAMnmB,KAAK0O,MAAMkwB,WAKvGyC,6BAA6B+G,EAAKC,GAGhC,GAFAzW,cAAc5xB,KAAKsoC,oBAEfF,EAAM,EAGR,YADApoC,KAAKkhC,cAIP,GAAImH,EAQF,YANAA,EAAK5jC,KAAK,KAERzE,KAAKkhC,gBACJl8B,MAAOC,IACRjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAKlC,MAAM,cAACtB,GAAiB5D,KAAKC,MAAM4D,KACnC,IAAIwD,EAAQ+gC,EAAM,IAClB/gC,GAAgBA,EAChBrH,KAAKsoC,mBAAqB3W,YAAY,KACpC,MAAM4W,EAAYlhC,EAAQ,GzDlazB,SAAuBmhC,GAC5B,MAAM5+B,EAAMD,KAAKE,MAAM2+B,EAAU,IACjC,IAAIJ,EAAMI,EAAU,GAEpB,OADAJ,EAAMA,EAAM,GAAN,WAAeA,GAAQA,EAC7B,UAAUx+B,EAAV,YAAiBw+B,GyD8ZmBK,CAAcphC,GAASA,EACvDrH,KAAKkhC,YACHt9B,EAAcnB,GAAS49B,oBAAqB,CAACmI,QAASD,IACtD,OACA,KACE3W,cAAc5xB,KAAKsoC,oBACnBtoC,KAAK6E,OAAO6jC,aAEd9kC,EAAcnB,GAAS69B,gBAEzBj5B,GAAS,GACR,KAILo6B,iBAAiBx8B,GACfjF,KAAK+O,SAAS,CACZgE,WAAW,EACXoc,OAAO,EACPiW,qBAAqB,EACrBxhB,UAAW3e,GAAOA,EAAIC,QAAUD,EAAIC,QAAU,eAC9Cye,WAAY1e,GAAOA,EAAIC,QAAU,MAAQ,OACzCq6B,eAAe,EACfkG,oBAAoB,EACpBnZ,cAAe,kBAInBgV,QAAQxK,EAAOC,EAAUuC,GACvB,GAAIt5B,KAAK6E,OAAO8jC,kBAGd,YADA1nC,EAAe27B,WAAW,IAI5BtD,EAAO55B,IAAOkpC,WAAWtP,GAEzB,IAAIuP,EAAU,KACd,MAAMxL,EAAQr9B,KAAK6E,OAAOikC,eACtBhS,GAASC,GACX/2B,KAAK+O,SAAS,CAACgoB,SAAU,OACzB8R,EAAU7oC,KAAK6E,OAAOkkC,WAAWjS,EAAOC,EAAUuC,IACzC+D,IACTwL,EAAU7oC,KAAK6E,OAAOmkC,WAAW3L,EAAMA,MAAO/D,IAG5CuP,EACFA,EAAQpkC,KAAMC,IACRA,EAAKi6B,MAAQ,KAAqB,yBAAdj6B,EAAKkP,MAC3B5T,KAAK+O,SAAS,CAACuwB,oBAAoB,IAC/BhG,GACFt5B,KAAKkhC,YAAYlhC,KAAKC,MAAM4D,KAAKD,cAAc,CAACnD,GAAI,uBAAwB,QAE9ET,KAAKuhC,yBAAyB78B,EAAKrD,SAEnCrB,KAAKwhC,0BAENx8B,MAAOC,IAERjF,KAAK+O,SAAS,CACZwwB,eAAe,EACfV,gBAAY9oB,EACZ6oB,cAAU7oB,EACVupB,oBAAoB,EACpBoF,WAAW,IAEb1kC,KAAKkhC,YAAYj8B,EAAIC,QAAS,OAC9BkxB,aAAaQ,WAAW,cACxB31B,EAAe27B,WAAW,OAK5B37B,EAAe27B,WAAW,IAC1B58B,KAAK+O,SAAS,CAACwwB,eAAe,KAIlCgC,yBAAyBlgC,GACvB,MAAMc,EAASlB,EAAemB,aAAaP,OAAOC,SAASZ,MAC3DiB,EAAOb,KAAK,GAAK,OACjBa,EAAOd,OAAP,OAA0BA,EAAOi4B,KAAK,GACtCr4B,EAAe27B,WAAW37B,EAAeoB,eAAeF,EAAOb,KAAMa,EAAOd,SAG9EmgC,wBACExhC,KAAKkhC,cAGD/K,GAAiBO,UAAU,mBAC7BP,GAAiBQ,UAAU,aAAc32B,KAAK6E,OAAOikC,gBAGvD,MAAM9Q,EAAKh4B,KAAK6E,OAAO8vB,aACvBqD,EAAG7Y,WAAanf,KAAK0hC,aACrB1J,EAAGiR,gBAAkBjpC,KAAK2hC,kBAC1B3J,EAAG5Y,cAAgBpf,KAAK4hC,gBACxB5hC,KAAK+O,SAAS,CACZgE,WAAW,EACX8rB,gBAAY9oB,EACZ6oB,cAAU7oB,EACVnD,SAAU5S,KAAK6E,OAAOqkC,mBACtBxE,WAAW,IAGb1M,EAAGjI,UACDiI,EAAGzW,iBACDkO,eACA0Z,WACA3nB,WACA4nB,WACA3nB,SACAzc,MAAOC,IACPjF,KAAK6E,OAAOwkC,aACZjT,aAAaQ,WAAW,cACxB52B,KAAKkhC,YAAYj8B,EAAIC,QAAS,OAC9BjE,EAAe27B,WAAW,MACzBpL,QAAQ,KACTxxB,KAAK+O,SAAS,CAACuwB,oBAAoB,MAEvCr+B,EAAe27B,WAAW37B,EAAeqoC,gBAAgBznC,OAAOC,SAASZ,KAAM,aAGjFwgC,aAAahgB,GACPA,IACEA,EAAKhP,QACP1S,KAAK+O,SAAS,CACZi2B,eAAgBtjB,EAAKhP,OAAOC,GAC5BsyB,gBAAiB/5B,EAAawW,EAAKhP,OAAOvH,SAG1CuW,EAAKhR,KACP1Q,KAAK+O,SAAS,CACZqrB,eAAgB1Y,EAAKhR,IAAI64B,iBAOjC5H,kBAAkBtP,EAAMqC,GACtB,GAAY,MAARrC,GAAwB,OAARA,EAClBryB,KAAK6hC,mBACD7hC,KAAK0O,MAAMwD,eAAiBwiB,EAAKnyB,OACnCvC,KAAK+O,SAAS,CAACq2B,oBAA8B,MAAR/S,SAElC,GAAY,QAARA,EACTryB,KAAK6hC,wBACA,GAAY,OAARxP,EAAe,CAExB,MAAM9vB,EAAQvC,KAAK6E,OAAOC,SAAS4vB,EAAKnyB,OAClCinC,EAAWjnC,GAASA,EAAMknC,aAG5B/U,EAAKljB,OAAS,GAAKxR,KAAK0O,MAAMurB,gBAAkBuP,IAE9ChkC,SAASs/B,QAAU9kC,KAAK0O,MAAMwD,eAAiBwiB,EAAKnyB,QACtD49B,GAAUuJ,OAId1pC,KAAK6hC,uBACY,QAARxP,IAEQ,QAARA,GAA0B,SAARA,GAGvBryB,KAAK0O,MAAMwD,eAAiBwiB,EAAKnyB,OACnCvC,KAAKiiC,oBAAoB,MAG3BjiC,KAAK6hC,oBACY,OAARxP,EAGLryB,KAAK0O,MAAMwD,eAAiBwiB,EAAKnyB,OACnCvC,KAAK+O,SAAS,CAACs2B,iBAAkB3Q,EAAKhkB,MAEvB,OAAR2hB,GAEQ,OAARA,GAKTltB,QAAQC,IAAI,qCAAuCitB,EAAO,QAAUqC,EAAKnyB,QAI7Eq/B,gBAAgB+H,GACd3pC,KAAK6hC,mBAKP,iCAAiChM,EAAU+T,GACzC,MAAMC,EAAS,GAIf,IAAK,MAAMhsC,KAAKg4B,EACmB,OAA7Bn2B,IAAO2K,UAAUxM,EAAE0E,SACnBsnC,EAAOhsC,EAAE0E,OAAS,CAChB8C,KAAMxH,EAAE0E,MACRunC,QAASjsC,EAAEisC,QACXp3B,OAAQ7U,EAAE6U,OACVF,QAAS3U,EAAE2U,QACX9B,IAAK7S,EAAE6S,MAMf,IAAK,MAAM7S,KAAK+rC,EACTC,EAAOhsC,EAAEwH,QACZwkC,EAAOhsC,EAAEwH,MAAQxH,GAIrB,OAAOK,OAAO0T,OAAOi4B,GAGvBhI,mBACE,MAAMphB,EAAW,CACfoV,SAAU,IAGP71B,KAAK0O,MAAMygB,QACd1O,EAAS0O,OAAQ,GAGnBnvB,KAAK6E,OAAO8vB,aAAatiB,SAAUxU,IACjC4iB,EAASoV,SAAS5zB,KAAKpE,GACnBmC,KAAK0O,MAAMwD,eAAiBrU,EAAE0E,QAChCke,EAAS2kB,oBAAsBvnC,EAAEiT,OACjC2P,EAAS4kB,iBAAmBxnC,EAAE6S,OAIlC+P,EAASoD,mBAAqB8c,GAAUoJ,0BAA0BtpB,EAASoV,SAAU71B,KAAK0O,MAAMyuB,eAChGn9B,KAAK+O,SAAS0R,GAIhBqhB,OAAOz2B,GACL,MAAM9I,EAAQvC,KAAK6E,OAAOC,SAASuG,EAAK9I,OACpCA,EAAM4vB,UAAU9mB,IAAS3L,IAAO+lB,qBAAuBpa,EAAK+mB,MAAQpyB,KAAK0O,MAAMkE,WACjF0f,aAAatyB,KAAKgqC,eAClBhqC,KAAKgqC,cAAgBvxB,WAAW,KAC9BzY,KAAKgqC,mBAAgBj0B,EACrBxT,EAAM0nC,SAAS5+B,EAAKzE,M5DzpBE,M4D+pB5Bm7B,aACE,MAAMmI,EAAMlqC,KAAK6E,OAAOslC,cACxBD,EAAI9qB,cAAgBpf,KAAKgiC,iBACrBkI,EAAIhb,eACNlvB,KAAKgiC,mBAELkI,EAAIna,UAAUma,EAAI3oB,iBAAiB6oB,UAAU3oB,SAASzc,MAAOC,IAC3DjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAKpC88B,mBACE,MAAM4H,EAAgB,GAEtB5pC,KAAK6E,OAAOslC,cAAc93B,SAAU9S,IAClCqqC,EAAc3nC,KAAK1C,KAErBS,KAAK+O,SAAS,CACZouB,cAAeyM,EACf/lB,mBAAoB8c,GAAUoJ,0BAA0B/pC,KAAK0O,MAAMmnB,SAAU+T,KAOjFnN,qBAAqB7kB,GACnB,MAAMsyB,EAAMlqC,KAAK6E,OAAOslC,cACxBD,EAAIG,QAAQ,CAAC3oB,KAAM,CAAChP,OAAQkF,KAASnT,KAAMC,GAClCwlC,EAAI5oB,QAAQ4oB,EAAI3oB,iBAAiB6oB,UAAU3oB,UACjDzc,MAAOC,IACRjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAKlC+8B,oBAAoBr9B,EAAW0lC,EAAcx5B,EAAQJ,GAE/C1Q,KAAK0O,MAAM2gB,gBAAkBrvB,KAAK0O,MAAM2gB,eAAeC,YAAc1qB,GACvE5E,KAAK+O,SAAS,CACZsgB,eAAgB,OAIhBzqB,GACF5E,KAAK+O,SAAS,CACZ6U,UAAW,GACXD,WAAY,KACZ6hB,YAAa,aACbD,eAAe,IAGbvlC,KAAK0O,MAAMwD,eAAiBtN,IAC9B5E,KAAK+O,SAAS,CACZq2B,oBAAqBt0B,EACrBu0B,iBAAkB30B,IAEpBzP,EAAe27B,WAAW37B,EAAespC,YAAY,GAAI3lC,OAI3D5E,KAAK+O,SAAS,CACZ6U,UAAW,GACXD,WAAY,KACZ6hB,YAAa,YACbJ,qBAAqB,EACrBC,iBAAkB,KAClBE,eAAe,IAGjBtkC,EAAe27B,WAAW37B,EAAespC,YAAY,GAAI,QAK7DrI,yBACEliC,KAAK+O,SAAS,CACZy2B,YAAa,cAEfvkC,EAAe27B,WAAW37B,EAAespC,YAAY1oC,OAAOC,SAASZ,KAAM,OAK7EihC,kBAAkBn7B,EAAK6hC,EAASlkB,GAC9B,MAAMpiB,EAAQvC,KAAK6E,OAAOC,SAAS9E,KAAK0O,MAAMwD,gBAE9ClL,EAAMzE,EAAM0E,cAAcD,GAAK,IAE3BwtB,UAAY7P,EAEXpiB,EAAM2sB,iBACJ2Z,IACHA,EAAU2B,QAAQC,WAEpB5B,EAAUA,EAAQpkC,KAAK,IAAelC,EAAMwtB,cAG1C8Y,IACFA,EAAUA,EAAQ7jC,MAAOC,IACvBjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,UAIlC3C,EAAM2E,aAAaF,EAAK6hC,GACrBpkC,KAAMC,IACL,GAAInC,EAAMknC,aACR,OAAOlnC,EAAMgB,SAAQ,KAGxByB,MAAOC,IACNjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIpCk9B,wBAAwBx9B,EAAW+M,GACjC,MAAMpP,EAAQvC,KAAK6E,OAAOC,SAASF,GACnC,IAAI+hB,EAAW,KACf,OAAQhV,GACN,IAAK,SAEH,MAAMhT,EAAO4D,EAAM2e,gBAAgBN,WACnC+F,EAAWpkB,EAAM8nC,QAAQ,CAACtpB,IAAK,CAACpiB,KAAMA,KACf,OAAnB4D,EAAMie,YAGRmG,EAAWA,EAASliB,KAAMC,IACxBnC,EAAM8nC,QAAQ,CAACtpB,IAAK,CAAC1b,KAAMT,EAAWjG,KAAMA,QAGhD,MACF,IAAK,SAEHgoB,EAAWpkB,EAAMwC,UAAS,GAC1B,MACF,IAAK,QAGH,MAAM2lC,EAAKnoC,EAAM2e,gBAAgBypB,WAAW,OAAO9pB,UACnD8F,EAAWpkB,EAAM8nC,QAAQ,CAACtpB,IAAK,CAACpiB,KAAM+rC,KAAMjmC,KAAMC,GACzC1E,KAAKiiC,oBAAoB,OAElC,MACF,QACE98B,QAAQC,IAAI,4BAA6B,IAAMuM,EAAS,MAG5C,MAAZgV,GACFA,EAAS3hB,MAAOC,IACdjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAMpCm9B,mBACEriC,KAAKkhC,cAELjgC,EAAe27B,WAAW37B,EAAeqoC,gBAAgBznC,OAAOC,SAASZ,KAAM,aAIjFohC,wBAAwBsI,EAAQC,EAAWC,EAASC,EAAOC,GAEzDhrC,KAAKkhC,cAELlhC,KAAK6E,OAAOuiC,QAAQpnC,KAAK0O,MAAM6d,eAC5B9nB,KAAK,IACGzE,KAAK6E,OAAOomC,mBAAmBL,EAAQC,EAC5C,CAACn4B,OAAQo4B,EAAS3uB,KAAM6uB,EAAO1R,KAAM55B,IAAOkpC,WAAWmC,MACxDtmC,KAAMC,IACHA,EAAKi6B,MAAQ,KAAoB,wBAAbj6B,EAAKkP,KAC3B5T,KAAKuhC,yBAAyB78B,EAAKrD,QAEnCrB,KAAKwhC,sBAAsBxhC,QAE5BgF,MAAOC,IACRjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIpCq9B,2BAA2BxL,EAAU7G,EAAKlP,GAGxC,GAFAhhB,KAAKkhC,cAEDhR,GAAOlP,EAAQ,CACjB,MAAM3f,EAAS,GACX6uB,IACF7uB,EAAOqR,OAASwd,GAEdlP,IACF3f,EAAO2f,OAASA,GAElBhhB,KAAK6E,OAAO8vB,aAAa0V,QAAQ,CAAC3oB,KAAMrgB,IAAS2D,MAAOC,IACtDjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAG9B6xB,GACF/2B,KAAK6E,OAAOqmC,mBAAmB,KAAMlrC,KAAK6E,OAAOsmC,kBAAmBpU,GAAU/xB,MAAOC,IACnFjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAKpCu9B,0BAA0B2I,GACxB,MAAMpT,EAAKh4B,KAAK6E,OAAO8vB,aACjB+V,EAAK1S,EAAG9W,gBAAgBypB,WAAWS,EAAK,KAAO,MAAMvqB,UAC3DmX,EAAGqS,QAAQ,CAACtpB,IAAK,CAACpiB,KAAM+rC,KAAM1lC,MAAOC,IACnCjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIlCs9B,+BAA+BrmB,GAC7Bnc,KAAK6E,OAAO8vB,aAAa0V,QAAQ,CAACluB,KAAMA,IACrCnX,MAAOC,IACNjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAKpCw9B,iBACE1iC,KAAKkhC,cAELjgC,EAAe27B,WAAW37B,EAAeqoC,gBAAgBznC,OAAOC,SAASZ,KACvElB,KAAK0O,MAAMkE,SAAW,OAAS,aAInC+vB,qBAAqB8B,GACnB,MAAMlY,EAAgBkY,EAASlY,eAAiBvsB,KAAK0O,MAAM6d,cACrD0R,EAAYwG,EAASxG,WAAaj+B,KAAK0O,MAAMuvB,UAC/Cj+B,KAAK6E,SACP7E,KAAK6E,OAAOwhC,kBAAetwB,EAC3B/V,KAAK6E,OAAOwkC,cAEdrpC,KAAK6E,OAAS87B,GAAUwF,QAAQ5Z,EAAe0R,EAAWj+B,KAAKC,MAAM4D,KAAKyE,QAC1EtI,KAAK6E,OAAOuhC,UAAYpmC,KAAKohC,gBAC7BphC,KAAK6E,OAAOwhC,aAAermC,KAAKyhC,iBAEhCzhC,KAAK+O,SAAS,CACZwd,cAAeA,EACf0R,UAAWA,IAEb9H,GAAiBQ,UAAU,WAAY,CACrCpK,cAAeA,EACf0R,UAAWA,IAGbh9B,EAAe27B,WAAW37B,EAAeqoC,gBAAgBznC,OAAOC,SAASZ,KAAM,KAIjF0hC,oBACE3hC,EAAe27B,WAAW37B,EAAeqoC,gBAAgBznC,OAAOC,SAASZ,KACvElB,KAAK0O,MAAMkE,SAAW,UAAY,KAItCiwB,oBACE5hC,EAAe27B,WAAW37B,EAAeqoC,gBAAgBznC,OAAOC,SAASZ,KACvElB,KAAK0O,MAAMkE,SAAW,UAAY,KAItCswB,oBAGEljC,KAAKumC,OAAO8E,eAAe,KACzBrrC,KAAKojC,qBAGPpjC,KAAKumC,OAAO+E,UAAWC,OAMzBpI,gBAAgBqI,GACVA,EACGxrC,KAAK0O,MAAMm2B,eAUd7kC,KAAK+O,SAAS,CAACorB,eAAe,IAC9BhE,GAAiBsV,aAAa,WAAY,CAACtR,eAAe,KAV1Dn6B,KAAKumC,OAAOmF,oBAAoBjnC,KAAK,KACnCzE,KAAKojC,qBACJp+B,MAAOC,IACRjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,OAC9BlF,KAAK+O,SAAS,CAACorB,eAAe,EAAO0K,cAAe,OACpD1O,GAAiBsV,aAAa,WAAY,CAACtR,eAAe,IAC1Dh1B,QAAQC,IAAI,sCAAuCH,KAM9CjF,KAAK0O,MAAMm2B,cACpB7kC,KAAKumC,OAAOoF,YAAY3rC,KAAK0O,MAAMm2B,eAAe7/B,MAAOC,IACvDE,QAAQC,IAAI,0BAA2BH,KACtCusB,QAAQ,KACT2E,GAAiBsV,aAAa,WAAY,CAACtR,eAAe,IAC1D/D,aAAaQ,WAAW,kBACxB52B,KAAK+O,SAAS,CAACorB,eAAe,EAAO0K,cAAe,UAGtD7kC,KAAK+O,SAAS,CAACorB,eAAe,EAAO0K,cAAe,OACpD1O,GAAiBsV,aAAa,WAAY,CAACtR,eAAe,KAI9DiJ,mBACEpjC,KAAKumC,OAAOqF,WAAWnnC,KAAMonC,IACvBA,GAAkB7rC,KAAK0O,MAAMm2B,gBAC/B7kC,KAAK6E,OAAOmiC,eAAe6E,GAAgB,GAC3C1V,GAAiBQ,UAAU,iBAAkBkV,IAE/C7rC,KAAK+O,SAAS,CAAC81B,cAAegH,EAAgB1R,eAAe,IAC7DhE,GAAiBsV,aAAa,WAAY,CAACtR,eAAe,MACzDn1B,MAAOC,IACRjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,OAC9BC,QAAQC,IAAI,oCAAqCH,KAIrD69B,0BAA0B0I,GACxBxrC,KAAK+O,SAAS,CAACkrB,cAAeuR,IAC9BrV,GAAiBsV,aAAa,WAAY,CACxC9G,kBAAmB6G,IAIvBzI,cAAc9J,EAAQx6B,GACTuB,KAAK6E,OAAO8vB,aACpB0V,QAAQ,CAAC/Q,KAAM,CAACC,KAAMN,EAAQthB,IAAKlZ,KAASuG,MAAOC,IACpDjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIlC89B,iBAAiB/J,EAAQx6B,GACZuB,KAAK6E,OAAO8vB,aACpBmX,cAAc7S,EAAQx6B,GAAOuG,MAAOC,IACrCjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIlC+9B,kBAAkBhK,EAAQtS,GACxB3mB,KAAKuhC,yBAAyB,CAACjI,KAAM,CAACL,KAIxCoK,wBACE,MAAMlhC,EAASlB,EAAemB,aAAaP,OAAOC,SAASZ,MAC3D,IAAII,EAAO,GACP,CAAC,WAAW,UAAU,UAAU,SAASmU,SAAStT,EAAOb,KAAK,IAChEA,EAAO,OACE,WAAaa,EAAOb,KAAK,GAClCA,EAAO,WACEtB,KAAK0O,MAAMkE,WACpBtR,EAAO,YAETa,EAAOb,KAAK,GAAKA,EACba,EAAOd,gBACFc,EAAOd,OAAOs9B,YACdx8B,EAAOd,OAAO43B,cACd92B,EAAOd,OAAOgmC,KAEvBpmC,EAAe27B,WAAW37B,EAAeoB,eAAeF,EAAOb,KAAMa,EAAOd,SAC5ErB,KAAK+O,SAAS,CAAC6U,UAAW,GAAID,WAAY,OAI5CooB,eAAe7qC,GACbD,EAAe27B,WAAW37B,EAAeqoC,gBAAgBznC,OAAOC,SAASZ,KAAMA,IAIjFoiC,sBAAsB0I,EAAU9b,EAAK+M,EAAM9gB,GAEzC,MAAMvX,EAAYonC,GAAYhsC,KAAK6E,OAAOonC,oBACpC5qC,EAAS,CACbiuB,WAAY1qB,GAEVonC,GAEF3qC,EAAO0f,IAAM,CAACpiB,K5DjhCmB,S4DmhCjC0C,EAAOqgB,KAAO,CAACV,OAAQ,CAACxC,K5DnhCS,Y4DqhCjCnd,EAAOqgB,KAAO,CAAChP,OAAQwd,EAAK1d,QAAS,CAACf,QAASwrB,IAC/C57B,EAAO8a,KAAOA,GAEhBnc,KAAK+O,SAAS,CAACsgB,eAAgBhuB,GAAS,KAAOrB,KAAKiiC,oBAAoBr9B,KAI1E2+B,sBAAsB2I,EAASC,GACzBnsC,KAAK0O,MAAMwD,eAAiBg6B,GAAWA,GAAWC,GAIpDnsC,KAAK+O,SAAS,CAACmD,cAAei6B,GAAU,KACtClrC,EAAe27B,WAAW37B,EAAespC,YAAY,GAAI4B,MAK/D3I,yBAAyB5+B,EAAWsrB,EAAK+M,EAAMmP,GAC7C,MAAM7pC,EAAQvC,KAAK6E,OAAOC,SAASF,GACnC,GAAIrC,EAAO,CACT,MAAMlB,EAAS,GACX6uB,IACF7uB,EAAOqR,OAASwd,GAEd+M,IACF57B,EAAOmR,QAAWyqB,IAASv9B,IAAOmiB,SAChCniB,IAAOmiB,SAAW,CAACpQ,QAASwrB,IAE5BmP,IACF/qC,EAAO2f,OAASorB,GAElB7pC,EAAM8nC,QAAQ,CAAC3oB,KAAMrgB,IAAS2D,MAAOC,IACnCjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,UAKpCu+B,wBAAwB7+B,EAAWjG,EAAM6Q,GACvC,MAAMjN,EAAQvC,KAAK6E,OAAOC,SAASF,GACnC,GAAIrC,EAAO,CACT,MAAMmoC,EAAKnoC,EAAM2e,gBACb1R,GACFk7B,EAAG2B,YAAY1tC,GACfA,EAAO+rC,EAAG9pB,aAEV8pB,EAAGC,WAAWhsC,GACdA,EAAO+rC,EAAG7pB,WAEZte,EAAM8nC,QAAQ,CAACtpB,IAAK,CAAC1b,KAAMmK,EAAK7Q,KAAMA,KAAQqG,MAAOC,IACnDjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,UAKpCob,kBAAkB1b,EAAWuX,GAC3B,MAAM5Z,EAAQvC,KAAK6E,OAAOC,SAASF,GAC/BrC,GACFA,EAAM8nC,QAAQ,CAACluB,KAAMA,IAAOnX,MAAOC,IACjCjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAKpCw+B,eACE5oB,GAAc,GAGdsb,aAAaQ,WAAW,cACxBR,aAAaQ,WAAW,kBACxBR,aAAaQ,WAAW,YACpB52B,KAAK0O,MAAMm2B,eACb7kC,KAAKumC,OAAOoF,YAAY3rC,KAAK0O,MAAMm2B,eAGjC7kC,KAAK6E,SACP7E,KAAK6E,OAAOwhC,kBAAetwB,EAC3B/V,KAAK6E,OAAOwkC,cAEdrpC,KAAK+O,SAAS/O,KAAK4gC,iBACnB5gC,KAAK6E,OAAS87B,GAAUwF,QAAQnmC,KAAK0O,MAAM6d,cAAevsB,KAAK0O,MAAMuvB,UAAWj+B,KAAKC,MAAM4D,KAAKyE,QAChGtI,KAAK6E,OAAOuhC,UAAYpmC,KAAKohC,gBAC7BphC,KAAK6E,OAAOwhC,aAAermC,KAAKyhC,iBAChCxgC,EAAe27B,WAAW,IAG5BlC,sBACE16B,KAAK6E,OAAOynC,gBAAe,GAAM7nC,KAAMC,IACrC1E,KAAK0jC,iBAITC,4BAA4B/+B,GAC1B,MAAMrC,EAAQvC,KAAK6E,OAAOC,SAASF,GAC9BrC,GAKLA,EAAMsE,gBAAe,GAAM7B,MAAOC,IAChCjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIlC0+B,wBAAwBh/B,GACtB,MAAMrC,EAAQvC,KAAK6E,OAAOC,SAASF,GAC9BrC,GAILA,EAAM2qB,OAAM,GAAMzoB,KAAMC,IAEtBzD,EAAe27B,WAAW37B,EAAespC,YAAY1oC,OAAOC,SAASZ,KAAM,OAC1E8D,MAAOC,IACRjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIlC2+B,wBAAwBj/B,GACtB,MAAMrC,EAAQvC,KAAK6E,OAAOC,SAASF,GAC9BrC,GAILA,EAAM6E,WAAW,KAAM,OAAO3C,KAAMC,IAElCzD,EAAe27B,WAAW37B,EAAespC,YAAY1oC,OAAOC,SAASZ,KAAM,OAC1E8D,MAAOC,IACRjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIlC4+B,kBAAkBl/B,GAChB,MAAMrC,EAAQvC,KAAK6E,OAAOC,SAASF,GAC9BrC,IAKLvC,KAAK6E,OAAO0nC,QAAQ7sC,IAAO8sC,UAAW9sC,IAAOunB,OAAO0L,WAAW,KAAM,CACnE,OAAU,SACV,OAAU/tB,KAIZrC,EAAM6E,WAAW,KAAM,OAAO3C,KAAMC,IAElCzD,EAAe27B,WAAW37B,EAAespC,YAAY1oC,OAAOC,SAASZ,KAAM,OAC1E8D,MAAOC,IACRjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,UAIlC6+B,sBAAsB1iC,EAAQoiB,GAC5BzjB,KAAK+O,SAAS,CACZ02B,oBAAoB,EACpBE,mBAAoB,CAAC59B,EAAG1G,EAAO0G,EAAGG,EAAG7G,EAAO6G,GAC5C09B,kBAAmBvkC,EACnBwkC,iBAAkBpiB,GAAazjB,KAAKgkC,wBAAwB3iC,EAAOuD,WACnE8gC,kBAAmB/lC,IAASkG,YAAY7F,MAAM4rB,0BAIlDoY,wBAAwBp/B,GACtB,MAAMrC,EAAQvC,KAAK6E,OAAOC,SAASF,GAEnC,IAAIkZ,GAAQ,EAAOgY,GAAU,EAAO2W,GAAe,EAAOC,GAAa,EAAOpyB,GAAU,EAAOkvB,GAAW,EAC1G,GAAIjnC,EAAO,CACTmqC,EAAanqC,EAAM2sB,eACnBsa,EAAWjnC,EAAMknC,aAEjB,MAAM/4B,EAAMnO,EAAM2e,gBACdxQ,IACFoN,EAAQpN,EAAIS,UACZ2kB,GAAWplB,EAAIU,WACfq7B,GAAgB/7B,EAAIU,SAAS,QAC7BkJ,EAAU5J,EAAI2Q,aAIlB,MAAO,CACLqrB,EAAa,CACXtsC,MAAOJ,KAAKC,MAAM4D,KAAKD,cAAc,CAACnD,GAAI,mBAC1CyD,QAASlE,KAAKmkC,oBACZ,KACJuI,EAAa,iBAAmB,KAChCA,GAAcpyB,EAAU,sBAAwB,KAChDwD,EAASgY,EAAU,KAAO,eAAkB,aAC5C2W,EAAe,gBAAkB,cAChCjD,EAA6B,KAAlB,gBACZ,gBAIJvF,wBACEjkC,KAAK+O,SAAS,CACZ02B,oBAAoB,EACpBE,mBAAoB,KACpBC,kBAAmB,KACnBF,kBAAmB,OAIvBlB,wBAAwB7yB,EAAQk3B,EAASxnC,GACzB,iBAAVsQ,GACEk3B,GAAWxnC,EAAOuD,WAAavD,EAAOuD,WAAa5E,KAAK0O,MAAMwD,eAChE22B,EAAQpkC,KAAK,KACXzE,KAAKiiC,oBAAoB,QAMjCiC,gBAAgB9jC,EAAOC,EAASK,EAAWisC,EAAarsC,EAAUssC,GAChE5sC,KAAK+O,SAAS,CACZ+2B,cAAc,EACdC,YAAa,CACX3lC,MAAOA,EACPC,QAASA,EACTK,UAAWA,EACXC,QAASgsC,EACTrsC,SAAUA,EACVE,OAAQosC,KAKdzI,qBACEljC,EAAe27B,WAAW37B,EAAe47B,YAAYh7B,OAAOC,SAASZ,KAAM,QAAQ,IACnFlB,KAAK+O,SAAS,CAACw2B,eAAe,IAGhCnB,qBACEnjC,EAAe27B,WAAW37B,EAAe87B,eAAel7B,OAAOC,SAASZ,KAAM,SAC9ElB,KAAK+O,SAAS,CAACw2B,eAAe,IAGhCxlB,0BAA0Bnb,EAAWkT,EAAOnC,GAC1C,IAAK/Q,EACH,OAGF,MAAMrC,EAAQvC,KAAK6E,OAAOC,SAASF,GAC9BrC,IAIDuV,GAASA,EAAM5V,OAAS,GAC1B4V,EAAMvQ,IAAKiI,IACTjN,EAAMsqC,OAAOr9B,EAAK,MAAMxK,MAAOC,IAC7BjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,WAKhCyQ,GAAWA,EAAQzT,OAAS,GAC9ByT,EAAQpO,IAAKiI,IACXjN,EAAM+C,gBAAgBkK,GAAKxK,MAAOC,IAChCjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,YAMtCm/B,iCAAiC/K,EAAMqF,GACrC,GAAI3+B,KAAK6E,OAAO8jC,kBAAmB,CACtB3oC,KAAK6E,OAAO8vB,aACpB0V,QAAQ,CAAC/Q,KAAM,CAACC,KAAMD,EAAMnT,KAAMwY,KAClCl6B,KAAK,KACJxD,EAAe27B,WAAW,MAE3B53B,MAAOC,IACNjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,cAGlClF,KAAK+O,SAAS,CAAC8vB,WAAYvF,EAAMsF,SAAUD,IAC3C3+B,KAAKshC,QAAQ,KAAM,KAAM,CAAC/H,KAAMD,EAAMnT,KAAMwY,IAIhD2F,2BAA2BrL,EAAQx6B,GAEjCuB,KAAK6E,OAAOuiC,UACT3iC,KAAK,IACGzE,KAAK6E,OAAOioC,uBAAuB,QAAS7T,EAAQx6B,IAE5DuG,MAAOC,IAENjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SAIpCq/B,oBAAoBjH,EAAQyP,EAAa1P,IACvCA,EvDzoCG,SAAwB2P,GAC7B,GAAIA,EAAK,CACPA,EAAMA,EAAIlzB,QAAQ,KAAM,KAAKA,QAAQ,KAAM,KAC3C,IACEkzB,EAAMC,KAAKC,KAAKF,IAChB,MAAM/nC,GACNE,QAAQC,IAAI,qCAAsCH,GAClD+nC,EAAM,MAGV,OAAOA,EuD+nCGG,CAAe9P,IAIrBr9B,KAAK6E,OAAOuiC,UACT3iC,KAAK,IACGzE,KAAK6E,OAAOqmC,mBAAmB,KAAM,KAAM6B,EAAa,CAAC1P,MAAOA,KAExEr4B,MAAOC,IAENjF,KAAKkhC,YAAYj8B,EAAIC,QAAS,SARlClF,KAAKkhC,YAAYlhC,KAAKC,MAAM4D,KAAKD,cAAc,CAACnD,GAAI,2BAA4B,OAapFV,SACE,OACE,yBAAKU,GAAG,iBACLT,KAAK0O,MAAM+2B,mBACV,kBAAC,EAAD,CACE5gC,OAAQ7E,KAAK6E,OACb+C,OAAQ5H,KAAK0O,MAAMg3B,kBACnB59B,QAAS9H,KAAK0O,MAAMi3B,mBACpBtkC,OAAQrB,KAAK0O,MAAMk3B,kBACnBx/B,MAAOpG,KAAK0O,MAAMm3B,iBAClB3/B,KAAMlG,KAAKikC,sBACX7/B,YAAapE,KAAKkkC,gBAClB39B,SAAUvG,KAAKwkC,wBACf7/B,eAAiBC,IACXA,GAAa5E,KAAK0O,MAAMwD,eAC1BlS,KAAKiiC,oBAAoB,OAG7Bz7B,QAASxG,KAAKkhC,cAEhB,KAEF,kBAAC,EAAD,CACEhhC,QAASF,KAAK0O,MAAMo3B,aACpB1lC,MAAOJ,KAAK0O,MAAMq3B,YAAY3lC,MAC9BC,QAASL,KAAK0O,MAAMq3B,YAAY1lC,QAChCC,SAAUN,KAAK0O,MAAMq3B,YAAYzlC,SAAY,KAAQN,KAAK+O,SAAS,CAAC+2B,cAAc,KAAc,KAChGtlC,OAAQR,KAAK0O,MAAMq3B,YAAYvlC,OAC/BE,UAAW,KAAQV,KAAK+O,SAAS,CAAC+2B,cAAc,IAAS9lC,KAAK0O,MAAMq3B,YAAYrlC,aAChFC,QAASX,KAAK0O,MAAMq3B,YAAYplC,UAElC,kBAAC,GAAD,CACEkE,OAAQ7E,KAAK6E,OACbkO,UAAW/S,KAAK0O,MAAMqE,UACtB2Q,cAAe1jB,KAAK0O,MAAMgV,cAC1BmQ,SAAU7zB,KAAK0O,MAAMgV,eAA4C,cAA3B1jB,KAAK0O,MAAM82B,YACjD92B,MAAO1O,KAAK0O,MAAMq2B,kBAClB3kC,MAAOJ,KAAK0O,MAAMs2B,eAClB76B,OAAQnK,KAAK0O,MAAMu2B,gBACnBnO,MAAO92B,KAAK0O,MAAMooB,MAClBlkB,SAAU5S,KAAK0O,MAAMkE,SACrB2sB,cAAev/B,KAAK0O,MAAM6wB,cAC1BD,mBAAoBt/B,KAAK0O,MAAM4wB,mBAE/B1b,UAAW5jB,KAAK0O,MAAMkV,UACtBD,WAAY3jB,KAAK0O,MAAMiV,WACvByb,YAAap/B,KAAK0O,MAAM0wB,YACxBC,gBAAiBr/B,KAAK0O,MAAM2wB,gBAE5BntB,cAAelS,KAAK0O,MAAMwD,cAC1B2jB,SAAU71B,KAAK0O,MAAMmnB,SACrBgJ,WAAY7+B,KAAK0O,MAAMmwB,WACvBD,SAAU5+B,KAAK0O,MAAMkwB,SAErBX,UAAWj+B,KAAK0O,MAAMuvB,UACtBhE,cAAej6B,KAAK0O,MAAMurB,cAC1BE,cAAen6B,KAAK0O,MAAMyrB,cAC1BD,qBAAsBl6B,KAAK0O,MAAMwrB,qBACjCE,cAAep6B,KAAK0O,MAAM0rB,cAC1B7N,cAAevsB,KAAK0O,MAAM6d,cAC1BD,cAAetsB,KAAK0O,MAAM4d,cAE1BkT,iBAAkBx/B,KAAK2iC,qBACvBrN,SAAUt1B,KAAKqiC,iBACfjN,WAAYp1B,KAAK0iC,eACjBzK,gBAAiBj4B,KAAK+rC,eACtB7M,eAAgBl/B,KAAKmhC,mBACrB3J,gBAAiBx3B,KAAKsiC,wBACtBtJ,gBAAiBh5B,KAAKuiC,2BACtB9C,oBAAqBz/B,KAAKwiC,+BAC1BzI,0BAA2B/5B,KAAKmjC,gBAChCrJ,sBAAuB95B,KAAK8iC,0BAC5B9I,sBAAuBh6B,KAAKyiC,0BAC5BrJ,UAAWp5B,KAAK+iC,cAChBrJ,aAAc15B,KAAKgjC,iBACnBvJ,cAAez5B,KAAKijC,kBACpBjwB,gBAAiBhT,KAAKiiC,oBACtBjF,cAAeh9B,KAAKsjC,sBACpBxI,SAAU96B,KAAK0jC,aACf9I,gBAAiB56B,KAAK06B,oBACtBt2B,YAAapE,KAAKkkC,gBAClB5wB,SAAUtT,KAAKqjC,sBACf78B,QAASxG,KAAKkhC,YACdxB,sBAAuB1/B,KAAKqkC,iCAC5B1E,uBAAwB3/B,KAAKskC,2BAC7B1E,gBAAiB5/B,KAAKukC,oBACtBrO,cAAel2B,KAAK4iC,kBACpB7H,cAAe/6B,KAAK6iC,kBAEpB3f,WAAYljB,KAAK+hC,WACjB5E,cAAen9B,KAAK0O,MAAMyuB,cAC1BlB,iBAAkBj8B,KAAKy8B,qBAEvB9rB,gBAAiB3Q,KAAK+jC,wBAExB,kBAAC,GAAD,CACEl/B,OAAQ7E,KAAK6E,OACbkO,UAAW/S,KAAK0O,MAAMqE,UACtBoc,MAAOnvB,KAAK0O,MAAMygB,MAClBre,OAAQ9Q,KAAK0O,MAAM02B,oBACnB10B,IAAK1Q,KAAK0O,MAAM22B,iBAChB3hB,cAAe1jB,KAAK0O,MAAMgV,cAC1BuE,cAAejoB,KAAK0O,MAAMuZ,cAC1B0G,eAAgB3uB,KAAK0O,MAAMigB,eAC3BkF,SAAU7zB,KAAK0O,MAAMgV,gBACS,eAA3B1jB,KAAK0O,MAAM82B,aAAgCxlC,KAAK0O,MAAM62B,eACzDhjC,MAAOvC,KAAK0O,MAAMwD,cAClBU,SAAU5S,KAAK0O,MAAMkE,SACrB0Z,cAAetsB,KAAK0O,MAAM4d,cAC1BC,cAAevsB,KAAK0O,MAAM6d,cAC1B0C,mBAAoBjvB,KAAK0O,MAAMugB,mBAE/BrL,UAAW5jB,KAAK0O,MAAMkV,UACtBD,WAAY3jB,KAAK0O,MAAMiV,WACvByb,YAAap/B,KAAK0O,MAAM0wB,YACxBC,gBAAiBr/B,KAAK0O,MAAM2wB,gBAE5BhQ,eAAgBrvB,KAAK0O,MAAM2gB,eAE3B0F,mBAAoB/0B,KAAKkiC,uBACzBrT,OAAQ7uB,KAAK8hC,OACbt7B,QAASxG,KAAKkhC,YACdlR,kBAAmBhwB,KAAKujC,sBACxB5yB,gBAAiB3Q,KAAK+jC,sBACtBhiB,oBAAqB/hB,KAAKyjC,wBAC1BxQ,UAAWjzB,KAAKoiC,wBAChB1P,YAAa1yB,KAAKmiC,oBAEnBniC,KAAK0O,MAAM62B,cACV,kBAAC,GAAD,CACE1gC,OAAQ7E,KAAK6E,OACbkO,UAAW/S,KAAK0O,MAAMqE,UACtB2Q,cAAe1jB,KAAK0O,MAAMgV,cAC1BnhB,MAAOvC,KAAK0O,MAAMwD,cAClB2R,mBAAoB7jB,KAAK0O,MAAMmV,mBAC/BjR,SAAU5S,KAAK0O,MAAMkE,SAErBgR,UAAW5jB,KAAK0O,MAAMkV,UACtBD,WAAY3jB,KAAK0O,MAAMiV,WACvByb,YAAap/B,KAAK0O,MAAM0wB,YACxBC,gBAAiBr/B,KAAK0O,MAAM2wB,gBAE5Bzd,kBAAmB5hB,KAAKwjC,yBACxBlwB,SAAUtT,KAAKokC,mBACfhgC,YAAapE,KAAKkkC,gBAClBniB,oBAAqB/hB,KAAKyjC,wBAC1BtgB,sBAAuBnjB,KAAK+f,0BAC5BqD,iBAAkBpjB,KAAK2jC,4BACvBtgB,aAAcrjB,KAAK4jC,wBACnBtgB,aAActjB,KAAK6jC,wBACnBtgB,cAAevjB,KAAK8jC,kBACpBsJ,YAAaptC,KAAKqtC,yBAClB7pB,kBAAmBxjB,KAAKsgB,kBACxB4C,WAAYljB,KAAK+hC,WACjBv7B,QAASxG,KAAKkhC,YAEdvwB,gBAAiB3Q,KAAK+jC,wBAGxB,OAOK37B,4BAAWu4B,IC//C1B,MAAQt/B,OAAMA,IAAKJ,EAAemB,aAAaP,OAAOC,SAASZ,MACzDuH,GAAYpH,IAAUA,GAAOisC,IAChC/kC,UAAUglC,WAAahlC,UAAUglC,UAAU,IAC5ChlC,UAAUE,UACVF,UAAUC,cACV,KAGIglC,GAAe/kC,GAASgK,cAAcrR,MAAM,QAAQ,GAGpDqB,GACJgrC,EAAYhlC,KACZglC,EAAYD,KACZC,EAAYC,GAEd/tC,IAASI,OACP,kBAAC,eAAD,CAAcuI,OAAQG,GAAUhG,SAAUA,GAAUkrC,cAAenuC,IAAMgoB,UACvE,kBAAC,GAAD,OAEFhiB,SAASwV,eAAe", "file": "index.prod.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/umd/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 7);\n", "module.exports = React;", "module.exports = ReactIntl;", "module.exports = Tinode;", "module.exports = ReactDOM;", "module.exports = firebase;", "module.exports = firebase[\"messaging\"];", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nexport default class Alert extends React.PureComponent {\n  render() {\n    return this.props.visible ?\n      <div className=\"alert-container\">\n        <div className=\"alert\">\n          <div className=\"title\">{this.props.title}</div>\n          <div className=\"content\">{this.props.content}</div>\n          <div className=\"dialog-buttons\">\n            {this.props.onReject ?\n              <button className=\"outline\" onClick={this.props.onReject}>\n                {this.props.reject || <FormattedMessage id=\"button_cancel\" />}\n              </button>\n              :\n              null\n            }\n            <button className=\"blue\" onClick={this.props.onConfirm}>\n              {this.props.confirm || <FormattedMessage id=\"button_ok\" />}\n            </button>\n          </div>\n        </div>\n      </div>\n    :\n    null;\n  }\n};\n", "// This is a generated file. Don't edit.\n\nexport const PACKAGE_VERSION = \"0.16.7\";\n", "import { PACKAGE_VERSION } from './version.js';\n\n// Name of this application, used in the User-Agent.\nexport const APP_NAME = 'TinodeWeb/' + (PACKAGE_VERSION || '0.16');\n\n// API key. Use https://github.com/tinode/chat/tree/master/keygen to generate your own\nexport const API_KEY = 'AQEAAAABAAD_rAp4DJh05a1HAwFT3A6K';\n\n// The array of possible hosts to connect to.\nexport const KNOWN_HOSTS = {hosted: 'web.tinode.co', local: 'localhost:6060'};\n\n// Default host name and port to connect to.\nexport const DEFAULT_HOST = KNOWN_HOSTS.hosted;\n\n// Enable console logging.\nexport const LOGGING_ENABLED = true;\n\n// Minimum time between two keypress notifications, milliseconds.\nexport const KEYPRESS_DELAY = 3*1000;\n// Delay before sending a {note} for reciving a message, milliseconds.\nexport const RECEIVED_DELAY = 500;\n// Delay before sending a read notification, milliseconds.\nexport const READ_DELAY = 1000;\n\n// The shortest allowed tag length. Matches the value on the server.\nexport const MIN_TAG_LENGTH = 4;\n// The maximum number of tags allowed. Matches the value on the server.\nexport const MAX_TAG_COUNT = 16;\n\n// Access mode for P2P subscriptions initiated by the current user.\nexport const DEFAULT_P2P_ACCESS_MODE = 'JRWPS';\n// Access mode for new group topics created by the current user.\nexport const NEW_GRP_ACCESS_MODE = 'JRWPSAO';\n\n// Access mode for no access.\nexport const NO_ACCESS_MODE = 'N';\n\n// Mediaquery breakpoint between desktop and mobile, in px. Should match the value\n// in @media (max-size: 640px) in base.css\nexport const MEDIA_BREAKPOINT = 640;\n// Size of css 'rem' unit in pixels. Default 1rem = 10pt = 13px.\nexport const REM_SIZE = 13;\n\n// Size of the avatar image: When an avatar image is uploaded, it's resized to\n// a square of this size.\nexport const AVATAR_SIZE = 128;\n\n// Size of the broken_image shown in MessagesView\nexport const BROKEN_IMAGE_SIZE = 32;\n\n// Number of chat messages to fetch in one call.\nexport const MESSAGES_PAGE = 24;\n\n// Maximum in-band (included directly into the message) attachment size which fits into\n// a message of 256K in size, assuming base64 encoding and 1024 bytes of overhead.\n// This is size of an object *before* base64 encoding is applied.\n// Increase this limit to a greater value in production, if desired. Also increase\n// max_message_size in server config.\n//  MAX_INBAND_ATTACHMENT_SIZE = base64DecodedLen(max_message_size) - 1024;\nexport const MAX_INBAND_ATTACHMENT_SIZE = 195584;\n\n// Absolute maximum attachment size to be used with the server = 8MB. Increase to\n// something like 100MB in production.\nexport const MAX_EXTERN_ATTACHMENT_SIZE = 1 << 23;\n\n// Maximum allowed linear dimension of an inline image in pixels. You may want\n// to adjust it to 1600 or 2400 for production.\nexport const MAX_IMAGE_DIM = 768;\n\n// Maximum number of online users to be shown in a topic title bar. Others will be\n// hidden under \"+X more\"\nexport const MAX_ONLINE_IN_TOPIC = 4;\n\n// Maximum length of user name, topic title, and private comment.\nexport const MAX_TITLE_LENGTH = 60;\n\n// Link for \"Contact Us\".\nexport const LINK_CONTACT_US = 'email:<EMAIL>';\n\n// Link to Privacy Policy.\nexport const LINK_PRIVACY_POLICY = 'https://tinode.co/privacy.html';\n\n// Link to Terms of Service.\nexport const LINK_TERMS_OF_SERVICE = 'https://tinode.co/terms.html';\n", "// Utility class for hash navigation.\n\n// Parse hash as in http://www.example.com/path#hash as if it were\n// path and arguments.\nexport default class HashNavigation {\n  static parseUrlHash(hash) {\n    // Split path from args, path -> parts[0], args->path[1]\n    let parts = hash.split('?', 2);\n    let params = {};\n    let path = [];\n    if (parts[0]) {\n      path = parts[0].substr(1).split('/');\n    }\n    if (parts[1]) {\n      parts[1].split('&').forEach(function(part) {\n        let item = part.split('=');\n        if (item[0]) {\n          params[decodeURIComponent(item[0])] = decodeURIComponent(item[1]);\n        }\n      });\n    }\n    return {path: path, params: params};\n  }\n\n  static navigateTo(url) {\n    window.location.hash = url;\n  }\n\n  static composeUrlHash(path, params) {\n    var url = path.join('/');\n    var args = [];\n    for (var key in params) {\n      if (params.hasOwnProperty(key)) {\n        args.push(key + '=' + params[key]);\n      }\n    }\n    if (args.length > 0) {\n      url += '?' + args.join('&');\n    }\n    return url;\n  }\n\n  static addUrlParam(hash, key, value) {\n    var parsed = this.parseUrlHash(hash);\n    parsed.params[key] = value;\n    return this.composeUrlHash(parsed.path, parsed.params);\n  }\n\n  static removeUrlParam(hash, key) {\n    var parsed = this.parseUrlHash(hash);\n    delete parsed.params[key];\n    return this.composeUrlHash(parsed.path, parsed.params);\n  }\n\n  static setUrlSidePanel(hash, sidepanel) {\n    var parsed = this.parseUrlHash(hash);\n    parsed.path[0] = sidepanel;\n    return this.composeUrlHash(parsed.path, parsed.params);\n  }\n\n  static setUrlTopic(hash, topic) {\n    var parsed = this.parseUrlHash(hash);\n    parsed.path[1] = topic;\n    // Close InfoView on topic change.\n    delete parsed.params.info;\n    return this.composeUrlHash(parsed.path, parsed.params);\n  }\n}\n", "// Context Menu: popup/dropdown menu.\nimport React from 'react';\nimport <PERSON>act<PERSON><PERSON> from 'react-dom';\nimport { injectIntl, defineMessages } from 'react-intl';\n\nimport { REM_SIZE } from '../config.js';\nimport HashNavigation from '../lib/navigation.js';\n\nconst messages = defineMessages({\n  info: {\n    id: 'menu_item_info',\n    defaultMessage: 'Info',\n    description: 'Show extended topic information'\n  },\n  clear_messages: {\n    id: 'menu_item_clear_messages',\n    defaultMessage: 'Clear messages',\n    description: 'Delete all messages'\n  },\n  clear_for_all: {\n    id: 'menu_item_clear_messages_for_all',\n    defaultMessage: 'Clear for All',\n    description: 'Delete all message(s) for all members'\n  },\n  'delete': {\n    id: 'menu_item_delete',\n    defaultMessage: 'Delete',\n    description: 'Delete selected messages'\n  },\n  delete_for_all: {\n    id: 'menu_item_delete_for_all',\n    defaultMessage: 'Delete for All',\n    description: 'Delete selected message(s) for all members'\n  },\n  send_retry: {\n    id: 'menu_item_send_retry',\n    defaultMessage: 'Retry',\n    description: 'Retry sending message'\n  },\n  mute: {\n    id: 'menu_item_mute',\n    defaultMessage: 'Mute',\n    description: 'Turn off notifications'\n  },\n  unmute: {\n    id: 'menu_item_unmute',\n    defaultMessage: 'Unmute',\n    description: 'Turn notifications on'\n  },\n  topic_delete: {\n    id: 'menu_item_delete_topic',\n    defaultMessage: 'Delete',\n    description: 'Delete entire topic'\n  },\n  topic_delete_warning: {\n    id: 'topic_delete_warning',\n    defaultMessage: 'Are you sure you want to delete this conversation?',\n    description: 'Alert warning when deleting entire topic'\n  },\n  unblock: {\n    id: 'menu_item_unblock',\n    defaultMessage: 'Unblock',\n    description: 'Unblock topic or user'\n  },\n  // Generic string suitable for either topic or user.\n  block: {\n    id: 'menu_item_block',\n    defaultMessage: 'Block',\n    description: 'Block topic or user'\n  },\n  topic_block_warning: {\n    id: 'topic_block_warning',\n    defaultMessage: 'Are you sure you want to block this conversation?',\n    description: 'Alert warning when blocking a topic.'\n  },\n  member_delete: {\n    id: 'menu_item_member_delete',\n    defaultMessage: 'Remove',\n    description: 'Remove user from topic'\n  },\n  archive: {\n    id: 'menu_item_archive_topic',\n    defaultMessage: 'Archive',\n    description: 'Move topic from the list of active chats to archive'\n  }\n});\n\nclass ContextMenu extends React.Component {\n  constructor(props) {\n    super(props);\n\n    const {formatMessage} = props.intl;\n\n    this.handlePageClick = this.handlePageClick.bind(this);\n    this.handleEscapeKey = this.handleEscapeKey.bind(this);\n    this.handleClick = this.handleClick.bind(this);\n\n    // Preconfigured menu items.\n    this.MenuItems = {\n      'topic_info': {\n        id: 'topic_info',\n        title: formatMessage(messages.info),\n        handler: null\n      },\n      'messages_clear': {\n        id: 'messages_clear',\n        title: formatMessage(messages.clear_messages),\n        handler: (params, errorHandler) => {\n          return props.onShowAlert(\n            formatMessage({id: 'menu_item_clear_messages'}), // title\n            formatMessage({id: 'clear_messages_warning'}), // content\n            (() => { this.deleteMessages(true, false, params, errorHandler); }), // onConfirm\n            null, // \"OK\"\n            true, // Show Reject button\n            null  // \"Cancel\"\n          );\n        }\n      },\n      'messages_clear_hard': {\n        id: 'messages_clear_hard',\n        title: formatMessage(messages.clear_for_all),\n        handler: (params, errorHandler) => {\n          return props.onShowAlert(\n            formatMessage({id: 'menu_item_clear_messages_for_all'}), // title\n            formatMessage({id: 'delete_messages_warning'}), // content\n            (() => { return this.deleteMessages(true, true, params, errorHandler); }),\n            null, // \"OK\"\n            true, // Show Reject button\n            null  // \"Cancel\"\n          );\n        }\n      },\n      'message_delete': {\n        id: 'message_delete',\n        title: formatMessage(messages.delete),\n        handler: (params, errorHandler) => {\n          return this.deleteMessages(false, false, params, errorHandler);\n        }\n      },\n      'message_delete_hard': {\n        id: 'message_delete_hard',\n        title: formatMessage(messages.delete_for_all),\n        handler: (params, errorHandler) => {\n          return this.deleteMessages(false, true, params, errorHandler);\n        }\n      },\n      'menu_item_send_retry': {\n        id: 'menu_item_send_retry',\n        title: formatMessage(messages.send_retry),\n        handler: (params, errorHandler) => {\n          return this.retryMessage(params, errorHandler);\n        }\n      },\n      'topic_unmute': {\n        id: 'topic_unmute',\n        title: formatMessage(messages.unmute),\n        handler: this.topicPermissionSetter.bind(this, '+P')\n      },\n      'topic_mute': {\n        id: 'topic_mute',\n        title: formatMessage(messages.mute),\n        handler: this.topicPermissionSetter.bind(this, '-P')\n      },\n      'topic_unblock': {\n        id: 'topic_unblock',\n        title: formatMessage(messages.unblock),\n        handler: this.topicPermissionSetter.bind(this, '+JP')\n      },\n      'topic_block': {\n        id: 'topic_block',\n        title: formatMessage(messages.block),\n        handler: (params, errorHandler) => {\n          return props.onShowAlert(\n            formatMessage({id: 'menu_item_block'}), // title\n            formatMessage(messages.topic_block_warning), // content\n            (() => {\n              return this.topicPermissionSetter('-JP', params, errorHandler).then((ctrl) => {\n                this.props.onTopicRemoved(params.topicName);\n                return ctrl;\n              });\n            }),\n            null, // \"OK\"\n            true, // Show Reject button\n            null  // \"Cancel\"\n          );\n        }\n      },\n      'topic_delete': {\n        id: 'topic_delete',\n        title: formatMessage(messages.topic_delete),\n        handler: (params, errorHandler) => {\n          return props.onShowAlert(\n            formatMessage({id: 'menu_item_delete_topic'}), // title\n            formatMessage(messages.topic_delete_warning), // content\n            (() => {\n              const topic = this.props.tinode.getTopic(params.topicName);\n              if (!topic) {\n                console.log(\"Topic not found: \", params.topicName);\n                return;\n              }\n              return topic.delTopic(true).catch((err) => {\n                if (errorHandler) {\n                  errorHandler(err.message, 'err');\n                }\n              });\n            }),\n            null, // \"OK\"\n            true, // Show Reject button\n            null  // \"Cancel\"\n          );\n        }\n      },\n      'topic_archive': {\n        id: 'topic_archive',\n        title: formatMessage(messages.archive),\n        handler: (params, errorHandler) => {\n          const topic = this.props.tinode.getTopic(params.topicName);\n          if (!topic) {\n            console.log(\"Topic not found: \", params.topicName);\n            return;\n          }\n          return topic.archive(true).catch((err) => {\n            if (errorHandler) {\n              errorHandler(err.message, 'err');\n            }\n          });\n        }\n      },\n      // menu_item_edit_permissions is defined elsewhere.\n      'permissions': {\n        id: 'permissions',\n        title: formatMessage({id: 'menu_item_edit_permissions'}),\n        handler: null\n      },\n      'member_delete': {\n        id: 'member_delete',\n        title: formatMessage(messages.member_delete),\n        handler: (params, errorHandler) => {\n          const topic = this.props.tinode.getTopic(params.topicName);\n          if (!topic || !params.user) {\n            console.log(\"Topic or user not found: '\" + params.topicName + \"', '\" + params.user + \"'\");\n            return;\n          }\n          return topic.delSubscription(params.user).catch((err) => {\n            if (errorHandler) {\n              errorHandler(err.message, 'err');\n            }\n          });\n        }\n      },\n      'member_mute': {\n        id: 'member_mute',\n        title: formatMessage(messages.mute),\n        handler: this.topicPermissionSetter.bind(this, '-P')\n      },\n      'member_unmute': {\n        id: 'member_unmute',\n        title: formatMessage(messages.unmute),\n        handler: this.topicPermissionSetter.bind(this, '+P')\n      },\n      'member_block': {\n        id: 'member_block',\n        title: formatMessage(messages.block),\n        handler: this.topicPermissionSetter.bind(this, '-JP')\n      },\n      'member_unblock': {\n        id: 'member_unblock',\n        title: formatMessage(messages.unblock),\n        handler: this.topicPermissionSetter.bind(this, '+JP')\n      },\n    };\n  }\n\n  componentDidMount() {\n    document.addEventListener('mousedown', this.handlePageClick, false);\n    document.addEventListener('keyup', this.handleEscapeKey, false);\n  }\n\n  componentWillUnmount() {\n    document.removeEventListener('mousedown', this.handlePageClick, false);\n    document.removeEventListener('keyup', this.handleEscapeKey, false);\n  }\n\n  handlePageClick(e) {\n    if (ReactDOM.findDOMNode(this).contains(e.target)) {\n      return;\n    }\n    e.preventDefault();\n    e.stopPropagation();\n    this.props.hide();\n  }\n\n  handleEscapeKey(e) {\n    if (e.keyCode === 27) {\n      this.props.hide();\n    }\n  }\n\n  handleClick(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.props.hide();\n    let item = this.props.items[e.currentTarget.dataset.id];\n    if (typeof item == 'string') {\n      item = this.MenuItems[item];\n    }\n\n    if (!item) {\n      console.log(\"Invalid menu item ID\", e.currentTarget.dataset.id);\n    } else {\n      this.props.onAction(\n        item.id,\n        item.handler(this.props.params, this.props.onError),\n        this.props.params);\n    }\n  }\n\n  // Menu Actions\n\n  deleteMessages(all, hard, params, errorHandler) {\n    const topic = this.props.tinode.getTopic(params.topicName);\n    if (!topic) {\n      console.log(\"Topic not found: \", params.topicName);\n      return;\n    }\n\n    // We don't know if the message is still pending (e.g. attachment is being uploaded),\n    // so try cancelling first. No harm if we can't cancel.\n    // The message can be cancelled if transmission to the server has not\n    // started yet or if the message send has failed.\n    if (!all && topic.cancelSend(params.seq)) {\n      return;\n    }\n    // Can't cancel. Delete instead.\n    const promise = all ?\n      topic.delMessagesAll(hard) :\n      topic.delMessagesList([params.seq], hard);\n\n    return promise.catch((err) => {\n      if (errorHandler) {\n        errorHandler(err.message, 'err');\n      }\n    });\n  }\n\n  // Retries sending failed message.\n  retryMessage(params, errorHandler) {\n    const topic = this.props.tinode.getTopic(params.topicName);\n    // Remove the existing message entry.\n    if (!topic || !topic.flushMessage(params.seq)) {\n      return;\n    }\n    const msg = topic.createMessage(params.content, false);\n    return topic.publishDraft(msg).catch((err) => {\n      if (errorHandler) {\n        errorHandler(err.message, 'err');\n      }\n    });\n  }\n\n  // Function is used by context menu to set permissions.\n  topicPermissionSetter(mode, params, errorHandler) {\n    const topic = this.props.tinode.getTopic(params.topicName);\n    if (!topic) {\n      console.log(\"Topic not found\", params.topicName);\n      return;\n    }\n\n    let result = topic.updateMode(params.user, mode);\n    if (errorHandler) {\n      result = result.catch((err) => {\n        errorHandler(err.message, 'err');\n      });\n    }\n    return result;\n  }\n\n  render() {\n    let count = 0;\n    let menu = [];\n    this.props.items.map((item) => {\n      if (typeof item == 'string') {\n        item = this.MenuItems[item];\n      }\n      if (item && item.title) {\n        menu.push(\n          item.title == '-' ?\n            <li className=\"separator\" key={count} />\n            :\n            <li onClick={this.handleClick} data-id={count} key={count}>{item.title}</li>\n        );\n      }\n      count++;\n    });\n\n    // Ensure that menu is inside the app-container.\n    const hSize = 12 * REM_SIZE;\n    const vSize = REM_SIZE * (0.7 + menu.length * 2.5);\n    const left = (this.props.bounds.right - this.props.clickAt.x < hSize) ?\n        (this.props.clickAt.x - this.props.bounds.left - hSize) :\n        (this.props.clickAt.x - this.props.bounds.left);\n    const top = (this.props.bounds.bottom - this.props.clickAt.y < vSize) ?\n        (this.props.clickAt.y - this.props.bounds.top - vSize) :\n        (this.props.clickAt.y - this.props.bounds.top);\n\n    const position = {\n      left: left + 'px',\n      top: top + 'px'\n    };\n\n    return (\n      <ul className=\"menu\" style={position}>\n        {menu}\n      </ul>\n    );\n  }\n}\n\nexport default injectIntl(ContextMenu);\n", "// Short representation of time in the past.\nexport function shortDateFormat(then, locale) {\n  locale = locale || window.navigator.userLanguage || window.navigator.language;\n  const now = new Date();\n  if (then.getFullYear() == now.getFullYear()) {\n    if (then.getMonth() == now.getMonth() && then.getDate() == now.getDate()) {\n\t    return then.toLocaleTimeString(locale, {hour12: false, hour: '2-digit', minute: '2-digit'});\n    } else {\n\t    return then.toLocaleDateString(locale,\n        {hour12: false, month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'});\n    }\n  }\n  return then.toLocaleDateString(locale,\n    {hour12: false, year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'});\n}\n\n// Convert seconds to minutes:seconds, i.e. 156 sec -> 2:36.\nexport function secondsToTime(seconds) {\n  const min = Math.floor(seconds / 60);\n  let sec = seconds % 60;\n  sec = sec < 10 ? `0${sec}` : sec;\n  return `${min}:${sec}`;\n}\n\n// Convert a number of bytes to human-readable format.\nexport function bytesToHumanSize(bytes) {\n  if (!bytes || bytes == 0) {\n    return '0 Bytes';\n  }\n\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];\n  const bucket = Math.min(Math.floor(Math.log2(bytes) / 10) | 0, sizes.length-1);\n  const count = bytes / Math.pow(1024, bucket);\n  const round = bucket > 0 ? (count < 3 ? 2 : (count < 30 ? 1 : 0)) : 0;\n  return count.toFixed(round) + ' ' + sizes[bucket];\n}\n\n// Get 32 bit integer hash value for a string. Ideally it should produce the same value\n// as Java's String#hash().\nexport function stringHash(value) {\n  let hash = 0;\n  value = '' + value;\n  for (let i = 0; i < value.length; i++) {\n    hash = ((hash << 5) - hash) + value.charCodeAt(i);\n    hash = hash & hash; // Convert to 32bit integer\n  }\n  return hash;\n}\n", "import React from 'react';\n\nimport Tinode from 'tinode-sdk';\n\nimport { stringHash } from '../lib/strformat.js';\n\nexport default class LetterTile extends React.PureComponent {\n  render() {\n    let avatar;\n    if (this.props.avatar === true) {\n      const isGroup = (Tinode.topicType(this.props.topic) == 'grp');\n      const iconColor = (isGroup ? 'light-color' : 'dark-color')\n        + (Math.abs(stringHash(this.props.topic)) % 16);\n      if (this.props.topic && this.props.title && this.props.title.trim()) {\n        const letter = this.props.title.trim().charAt(0);\n        const className = 'lettertile ' + iconColor;\n        avatar = (<div className={className}><div>{letter}</div></div>)\n      } else {\n        const className = 'material-icons ' + iconColor;\n        avatar = isGroup ?\n          <i className={className}>group</i> : <i className={className}>person</i>;\n      }\n    } else if (this.props.avatar) {\n      // If avatar image is invalid, show a placeholder.\n      avatar = <img className=\"avatar\" alt=\"avatar\" src={this.props.avatar}\n        onError={(e)=>{e.target.onerror = null; e.target.src=\"/img/broken_image.png\"}} />;\n    } else {\n      avatar = null;\n    }\n    return avatar;\n  }\n}\n", "// File and image helper functions.\nimport { MAX_INBAND_ATTACHMENT_SIZE, MAX_IMAGE_DIM } from '../config.js';\n\n// Supported image MIME types and corresponding file extensions.\nexport const SUPPORTED_IMAGE_FORMATS = ['image/jpeg', 'image/gif', 'image/png', 'image/svg', 'image/svg+xml'];\nexport const MIME_EXTENSIONS         = ['jpg',        'gif',       'png',       'svg',       'svg'];\n\n// Make a data URL from public.photo\nexport function makeImageUrl(photo) {\n  return (photo && photo.type && photo.data) ?\n    'data:image/' + photo.type + ';base64,' + photo.data : null;\n}\n\n// Calculate linear dimensions for scaling image down to fit under a certain size.\n// Returns an object which contains destination sizes, source sizes, and offsets\n// into source (when making square images).\nexport function fitImageSize(width, height, maxWidth, maxHeight, forceSquare) {\n  // Sanitize input\n  width = width | 0;\n  height = height | 0;\n  maxWidth = maxWidth | 0;\n  maxHeight = maxHeight | 0;\n\n  if (width <= 0 || height <= 0 || maxWidth <= 0 || maxHeight <= 0) {\n    return null;\n  }\n\n  if (forceSquare) {\n    maxWidth = maxHeight = Math.min(maxWidth, maxHeight);\n  }\n\n  let scale = Math.min(\n    Math.min(width, maxWidth) / width,\n    Math.min(height, maxHeight) / height\n  );\n\n  let size = {\n    dstWidth: (width * scale) | 0,\n    dstHeight: (height * scale) | 0,\n  };\n\n  if (forceSquare) {\n    // Also calculate parameters for making the image square.\n    size.dstWidth = size.dstHeight = Math.min(size.dstWidth, size.dstHeight);\n    size.srcWidth = size.srcHeight = Math.min(width, height);\n    size.xoffset = ((width - size.srcWidth) / 2) | 0;\n    size.yoffset = ((height - size.srcWidth) / 2) | 0;\n  } else {\n    size.xoffset = size.yoffset = 0;\n    size.srcWidth = width;\n    size.srcHeight = height;\n  }\n  return size;\n}\n\n// Ensure file's extension matches mime content type\nexport function fileNameForMime(fname, mime) {\n  var idx = SUPPORTED_IMAGE_FORMATS.indexOf(mime);\n  var ext = MIME_EXTENSIONS[idx];\n\n  var at = fname.lastIndexOf('.');\n  if (at >= 0) {\n    fname = fname.substring(0, at);\n  }\n  return fname + '.' + ext;\n}\n\n// Convert uploaded image into a base64-encoded string possibly scaling\n// linear dimensions or constraining to a square.\nexport function imageFileScaledToBase64(file, width, height, forceSquare, onSuccess, onError) {\n  var img = new Image();\n  img.crossOrigin = 'Anonymous';\n  img.onerror = function(err) {\n    onError(\"Image format unrecognized\");\n  }\n  img.onload = function() {\n    var dim = fitImageSize(this.width, this.height, width, height, forceSquare);\n    if (!dim) {\n      onError(\"Invalid image\");\n      return;\n    }\n    var canvas = document.createElement('canvas');\n    canvas.width = dim.dstWidth;\n    canvas.height = dim.dstHeight;\n    var ctx = canvas.getContext('2d');\n    ctx.imageSmoothingEnabled = true;\n    ctx.drawImage(this, dim.xoffset, dim.yoffset, dim.srcWidth, dim.srcHeight,\n      0, 0, dim.dstWidth, dim.dstHeight);\n\n    var mime = (this.width != dim.dstWidth ||\n      this.height != dim.dstHeight ||\n      SUPPORTED_IMAGE_FORMATS.indexOf(file.type) < 0) ? 'image/jpeg' : file.type;\n    var imageBits = canvas.toDataURL(mime);\n    var parts = imageBits.split(',');\n    // Get actual image type: 'data:image/png;base64,'\n    mime = getMimeType(parts[0]);\n    if (!mime) {\n      onError(\"Unsupported image format\");\n      return;\n    }\n    // Ensure the image is not too large\n    var quality = 0.78;\n    if (base64DecodedLen(imageBits.length) > MAX_INBAND_ATTACHMENT_SIZE) {\n      mime = 'image/jpeg';\n    }\n    if (mime == 'image/jpeg') {\n      // Reduce size of the jpeg by reducing image quality\n      while (base64DecodedLen(imageBits.length) > MAX_INBAND_ATTACHMENT_SIZE && quality > 0.45) {\n        imageBits = canvas.toDataURL(mime, quality);\n        quality *= 0.84;\n      }\n    }\n    if (base64DecodedLen(imageBits.length) > MAX_INBAND_ATTACHMENT_SIZE) {\n      onError(\"The image size \" + bytesToHumanSize(base64DecodedLen(imageBits.length)) +\n        \" exceeds the \"  + bytesToHumanSize(MAX_INBAND_ATTACHMENT_SIZE) + \" limit.\", \"err\");\n      return;\n    }\n    canvas = null;\n    onSuccess(imageBits.split(',')[1], mime, dim.dstWidth, dim.dstHeight, fileNameForMime(file.name, mime));\n  };\n  img.src = URL.createObjectURL(file);\n}\n\n// Convert uploaded image file to base64-encoded string without scaling/converting the image\nexport function imageFileToBase64(file, onSuccess, onError) {\n  var reader = new FileReader();\n  reader.addEventListener('load', function() {\n    var parts = reader.result.split(',');\n    var mime = getMimeType(parts[0]);\n    if (!mime) {\n      onError(\"Failed to process image file\");\n      return;\n    }\n\n    // Get image size.\n    var img = new Image();\n    img.crossOrigin = 'Anonymous';\n    img.onload = function() {\n      onSuccess(parts[1], mime, this.width, this.height, fileNameForMime(file.name, mime));\n    }\n    img.onerror = function(err) {\n      onError(\"Image format not recognized\");\n    }\n    img.src = URL.createObjectURL(file);\n  }, false);\n  reader.readAsDataURL(file);\n}\n\nexport function fileToBase64(file, onSuccess, onError) {\n  var reader = new FileReader();\n  reader.addEventListener('load', function() {\n    onSuccess(file.type, reader.result.split(',')[1], file.name);\n  });\n  reader.readAsDataURL(file);\n}\n\n// File pasted from the clipboard. It's either an inline image or a file attachment.\n// FIXME: handle large files out of band.\nexport function filePasted(event, onImageSuccess, onAttachmentSuccess, onError) {\n  var items = (event.clipboardData || event.originalEvent.clipboardData || {}).items;\n  for (var i in items) {\n    var item = items[i];\n    if (item.kind === 'file') {\n      var file = item.getAsFile();\n      if (!file) {\n        console.log(\"Failed to get file object from pasted file item\", item.kind, item.type);\n        continue;\n      }\n      if (file.type && file.type.split('/')[0] == 'image') {\n        // Handle inline image\n        if (file.size > MAX_INBAND_ATTACHMENT_SIZE || SUPPORTED_IMAGE_FORMATS.indexOf(file.type) < 0) {\n          imageFileScaledToBase64(file, MAX_IMAGE_DIM, MAX_IMAGE_DIM, false, onImageSuccess, onError);\n        } else {\n          imageFileToBase64(file, onImageSuccess, onError);\n        }\n      } else {\n        // Handle file attachment\n        fileToBase64(file, onAttachmentSuccess, onError)\n      }\n      // Indicate that the pasted data contains a file.\n      return true;\n    }\n  }\n  // No file found.\n  return false;\n}\n\n// Get mime type from data URL header.\nexport function getMimeType(header) {\n  var mime = /^data:(image\\/[-+a-z0-9.]+);base64/.exec(header);\n  return (mime && mime.length > 1) ? mime[1] : null;\n}\n\n// Given length of a binary object in bytes, calculate the length after\n// base64 encoding.\nexport function base64EncodedLen(n) {\n  return Math.floor((n + 2) / 3) * 4;\n}\n\n// Given length of a base64-encoded object, calculate decoded size of the\n// pbject in bytes.\nexport function base64DecodedLen(n) {\n  return Math.floor(n / 4) * 3;\n}\n\n// Re-encode string to standard base64 encoding with padding.\n// The string may be base64-URL encoded without the padding.\nexport function base64ReEncode(str) {\n  if (str) {\n    str = str.replace(/-/g, '+').replace(/_/g, '/');\n    try {\n      str = btoa(atob(str));\n    } catch(err) {\n      console.log(\"Failed to base64 re-encode string.\", err);\n      str = null;\n    }\n  }\n  return str;\n}\n", "import React from 'react';\n\nimport LetterTile from './letter-tile.jsx';\n\nimport { AVATAR_SIZE } from '../config.js';\nimport { imageFileScaledToBase64, makeImageUrl } from '../lib/blob-helpers.js';\n\nexport default class AvatarUpload extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      dataUrl: props.avatar\n    };\n\n    this.handleFileUpload = this.handleFileUpload.bind(this);\n  }\n\n  componentDidUpdate(prevProps) {\n    if (this.props.avatar != prevProps.avatar) {\n      this.setState({dataUrl: this.props.avatar})\n    }\n  }\n\n  handleFileUpload(e) {\n    imageFileScaledToBase64(e.target.files[0], AVATAR_SIZE, AVATAR_SIZE, true,\n      // Success\n      (base64bits, mime) => {\n        var du = makeImageUrl({data: base64bits, type: mime});\n        this.setState({dataUrl: du});\n        this.props.onImageChanged(du);\n      },\n      // Failure\n      (err) => {\n        this.props.onError(err, 'err');\n      });\n    // Clear the value so the same file can be uploaded again.\n    e.target.value = '';\n  }\n\n  render() {\n    // Randomize id value in case more than one AvatarUpload is shown\n    // at the same time.\n    const randId = 'file-input-avatar-' + (Math.random() + '').substr(2);\n    const className = 'avatar-upload' + (this.props.readOnly ? ' read-only' : '');\n    return (\n      <div className={className}>\n        {this.props.readOnly || !this.state.dataUrl ?\n          null :\n          <a href=\"#\" className=\"clear-avatar\" onClick={(e) => {e.preventDefault(); this.props.onImageChanged(null);}}>\n            <i className=\"material-icons\">clear</i>\n          </a>}\n        {this.state.dataUrl ?\n          <img src={this.state.dataUrl} className=\"preview\" /> :\n          this.props.readOnly && this.props.uid ?\n            <div className=\"avatar-box\">\n              <LetterTile\n                avatar={true}\n                topic={this.props.uid}\n                title={this.props.title} />\n            </div>\n            :\n            <div className=\"blank\">{AVATAR_SIZE}&times;{AVATAR_SIZE}</div>}\n        {this.props.readOnly ? null :\n          <input type=\"file\" id={randId} className=\"inputfile hidden\"\n            accept=\"image/*\" onChange={this.handleFileUpload} />}\n        {this.props.readOnly ? null :\n        <label htmlFor={randId} className=\"round\">\n          <i className=\"material-icons\">file_upload</i>\n        </label>}\n      </div>\n    );\n  }\n};\n", "import React from 'react';\n\n/* BEGIN CheckBox: styled checkbox */\nexport default class CheckBox extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.handleChange = this.handleChange.bind(this);\n  }\n\n  handleChange() {\n    this.props.onChange(this.props.name, !this.props.checked);\n  }\n\n  render() {\n    return (\n      this.props.onChange ? (\n        this.props.checked ?\n          <i className=\"material-icons blue clickable\" onClick={this.handleChange}>check_box</i> :\n          <i className=\"material-icons blue clickable\" onClick={this.handleChange}>check_box_outline_blank</i>\n        ) : (\n          this.props.checked ?\n            <i className=\"material-icons\">check_box</i> :\n            <i className=\"material-icons\">check_box_outline_blank</i>\n        )\n    );\n  }\n}\n/* END CheckBox */\n", "import React from 'react';\n\nconst icon_mapping = {'muted': 'notifications_off', 'banned': 'block'};\n\n/* Contact's labels: [you], or icons [muted], [blocked] */\nexport default class ContactBadges extends React.PureComponent {\n    render() {\n      let badges = null;\n      if (this.props.badges && this.props.badges.length > 0) {\n        badges = [];\n        this.props.badges.map(function(b) {\n          if (b.icon) {\n            badges.push(<i className=\"material-icons as-badge\" key={b.key || b.icon}>{icon_mapping[b.icon]}</i>);\n          } else {\n            const style = 'badge' + (b.color ? ' ' + b.color : '');\n            badges.push(<span className={style} key={b.key || b.name}>{b.name}</span>);\n          }\n        });\n        return <>{badges}</>;\n      }\n      return null;\n    }\n};\n", "// The counter of unread messages in the topic.\nimport React from 'react';\n\nexport default class UnreadBadge extends React.PureComponent {\n  render() {\n    return (this.props.count > 0 ?\n      <span className=\"unread\">{this.props.count > 9 ? \"9+\" : this.props.count}</span>\n      : null);\n  }\n};\n", "// A single topic or user.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport LetterTile from './letter-tile.jsx';\nimport ContactBadges from './contact-badges.jsx';\nimport UnreadBadge from './unread-badge.jsx';\n\nexport default class Contact extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.handleClick = this.handleClick.bind(this);\n    this.handleContextClick = this.handleContextClick.bind(this);\n  }\n\n  handleClick(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    if (this.props.onSelected) {\n      this.props.onSelected(this.props.item, this.props.index, this.props.now, this.props.acs);\n    }\n  }\n\n  handleContextClick(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.props.showContextMenu({ topicName: this.props.item, y: e.pageY, x: e.pageX });\n  }\n\n  render() {\n    let title = this.props.title;\n    if (!title) {\n      title = <i><FormattedMessage id=\"unnamed_topic\" /></i>;\n    } else if (title.length > 30) {\n      // FIXME: this is probably wrong for RTL languages.\n      title = title.substring(0, 28) + '...';\n    }\n    const online = this.props.now ? 'online' : 'offline';\n    const avatar = this.props.avatar ? this.props.avatar : true;\n    const badges = this.props.badges ? this.props.badges.slice() : [];\n    const icon_badges = [];\n    if (this.props.acs) {\n      if (this.props.showMode) {\n        badges.push({name: this.props.acs.getMode(), key: 'mode'});\n      }\n      if (this.props.acs.isMuted()) {\n        icon_badges.push({icon: 'muted'});\n      }\n      if (!this.props.acs.isJoiner()) {\n        icon_badges.push({icon: 'banned'});\n      }\n    }\n\n    return (\n      <li className={!this.props.showCheckmark && this.props.selected ? \"selected\" : null}\n        onClick={this.handleClick}>\n        <div className=\"avatar-box\">\n          <LetterTile\n            avatar={avatar}\n            title={this.props.title}\n            topic={this.props.item} />\n          {this.props.showOnline ? <span className={online} /> :\n            (this.props.showCheckmark && this.props.selected ?\n            <i className=\"checkmark material-icons\">check_circle</i>\n            : null)}\n        </div>\n        <div className=\"text-box\">\n          <div><span className=\"contact-title\">{title}</span>\n            <UnreadBadge count={this.props.unread} /><ContactBadges badges={icon_badges} />\n          </div>\n          {this.props.comment ? <div className=\"contact-comment\">{this.props.comment}</div> : null}\n          <span><ContactBadges badges={badges} /></span>\n        </div>\n        {this.props.showContextMenu ?\n          <span className=\"menuTrigger\">\n            <a href=\"#\" onClick={this.handleContextClick}>\n              <i className=\"material-icons\">expand_more</i>\n            </a>\n          </span> : null}\n      </li>\n    );\n  }\n};\n", "// A panel included into a list of contacts with an action text.\nimport React from 'react';\nimport { injectIntl } from 'react-intl';\n\nclass ContactAction extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.handleClick = this.handleClick.bind(this);\n  }\n\n  handleClick(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.props.onAction(this.props.action);\n  }\n\n  render() {\n    const {formatMessage} = this.props.intl;\n    return (\n      <li onClick={this.handleClick} className=\"action\">\n        <div className=\"action-text\">{formatMessage(this.props.title, this.props.values)}</div>\n      </li>\n    );\n  }\n};\n\nexport default injectIntl(ContactAction);\n", "// ContactList: component for showing a list of contacts,\n// such as a list of group members in a group chat.\nimport React from 'react';\nimport { defineMessages, injectIntl } from 'react-intl';\n\nimport Contact from './contact.jsx';\nimport ContactAction from './contact-action.jsx';\n\nimport { makeImageUrl } from '../lib/blob-helpers.js';\n\nconst messages = defineMessages({\n  badge_you: {\n    id: 'badge_you',\n    defaultMessage: 'you',\n    description: 'Badge for indicating the current user'\n  },\n  badge_owner: {\n    id: 'badge_owner',\n    defaultMessage: 'owner',\n    description: 'Badge for indicating the owner'\n  }\n});\n\nclass ContactList extends React.Component {\n  render() {\n    const {formatMessage} = this.props.intl;\n    const showCheckmark = Array.isArray(this.props.topicSelected);\n    const contactNodes = [];\n    let contactsCount = 0;\n    if (this.props.contacts && this.props.contacts.length > 0) {\n      this.props.contacts.map((c) => {\n        if (c.action) {\n          // Action item\n          contactNodes.push(\n            <ContactAction\n              title={c.title} action={c.action} values={c.values}\n              key={c.action}\n              onAction={this.props.onAction}\n              />);\n        } else {\n          // Normal contact\n          const key = this.props.showMode ? c.user : (c.topic || c.user);\n          // If filter function is provided, filter out the items\n          // which don't satisfy the condition.\n          if (this.props.filterFunc && this.props.filter) {\n            let content = [key];\n            if (c.private && c.private.comment) {\n              content.push(('' + c.private.comment).toLowerCase());\n            }\n            if (c.public && c.public.fn) {\n              content.push(('' + c.public.fn).toLowerCase());\n            }\n            if (!this.props.filterFunc(this.props.filter, content)) {\n              return;\n            }\n          }\n\n          const selected = showCheckmark ?\n            (this.props.topicSelected.indexOf(key) > -1) :\n            (this.props.topicSelected === key);\n          const badges = [];\n          if (this.props.showMode) {\n            if (key == this.props.myUserId) {\n              badges.push({name: formatMessage(messages.badge_you), color: 'green'});\n            }\n            if (c.acs && c.acs.isOwner()) {\n              badges.push({name: formatMessage(messages.badge_owner), color: 'blue'});\n            }\n          }\n          const comment = Array.isArray(c.private) ?\n            c.private.join(',') : (c.private ? c.private.comment : null);\n\n          contactNodes.push(\n            <Contact\n              title={c.public ? c.public.fn : null}\n              avatar={makeImageUrl(c.public ? c.public.photo : null)}\n              comment={comment}\n              unread={this.props.showUnread ? c.unread : 0}\n              now={c.online && this.props.connected}\n              acs={c.acs}\n              showMode={this.props.showMode}\n              badges={badges}\n              showCheckmark={showCheckmark}\n              selected={selected}\n              showOnline={this.props.showOnline}\n              onSelected={this.props.onTopicSelected}\n              showContextMenu={this.props.showContextMenu}\n              item={key}\n              index={contactNodes.length}\n              key={key} />\n          );\n          contactsCount ++;\n        }\n      }, this);\n    }\n\n    return (\n      <div className={this.props.noScroll ? null : \"scrollable-panel\"}>\n        {contactsCount == 0 ?\n          <div className=\"center-medium-text\"\n            dangerouslySetInnerHTML={{__html: this.props.emptyListMessage}} />\n          :\n          null\n        }\n        {contactNodes.length > 0 ?\n          <ul className=\"contact-box\">\n            {contactNodes}\n          </ul>\n          :\n          null\n        }\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(ContactList);\n", "// The X menu to be displayed in title bars.\nimport React from 'react';\n\nexport default class MenuCancel extends React.PureComponent {\n  render() {\n    return (\n      <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onCancel();}}>\n        <i className=\"material-icons\">close</i>\n      </a>\n    );\n  }\n}\n", "import React from 'react';\n\nimport MenuCancel from './menu-cancel.jsx';\n\nexport default class ErrorPanel extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      show: false,\n    };\n\n    this.hide = this.hide.bind(this);\n  }\n\n  componentDidUpdate(prevProps) {\n    if (prevProps.level !== this.props.level) {\n      this.setState({\n        show: !(!this.props.level)\n      });\n    }\n  }\n\n  hide() {\n    this.setState({show: false});\n    if (this.props.onClearError) {\n      this.props.onClearError();\n    }\n  }\n\n  render() {\n    const icons = {err: 'error', warn: 'warning', info: 'info'}\n    const level = icons[this.props.level] || '';\n    const className = 'info-box ' + level;\n    return (\n      <div className={className}>\n        <div className=\"icon\"><i className=\"material-icons\">{level}</i></div>\n        <span>\n          {this.props.text}\n          {this.props.action ?\n            <>\n              &#32;<a href=\"#\"\n                style={{ whiteSpace: 'nowrap' }}\n                onClick={(e) => {e.preventDefault(); this.props.action();}}>\n                {this.props.actionText}\n              </a>\n            </>\n          : null}\n        </span>\n        <div className=\"cancel\"><MenuCancel onCancel={this.hide} /></div>\n      </div>\n    );\n  }\n};\n", "import React from 'react';\n\nimport LetterTile from './letter-tile.jsx';\n\nexport default class Chip extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.handleCancel = this.handleCancel.bind(this);\n  }\n\n  handleCancel(e) {\n    e.preventDefault();\n    this.props.onCancel(this.props.topic, this.props.index);\n  }\n\n  render() {\n    const title = this.props.title || this.props.topic;\n    const className = this.props.invalid ? 'chip invalid' : 'chip';\n    return (\n      <div className={className}>\n        {this.props.noAvatar ?\n          <span className=\"spacer\" /> :\n          <div className=\"avatar-box\">\n            <LetterTile\n              avatar={this.props.avatar || true}\n              topic={this.props.topic}\n              title={this.props.title} />\n          </div>\n        }\n        <span>{title}</span>\n        {this.props.onCancel && !this.props.required ?\n          <a href=\"#\" onClick={this.handleCancel} >&times;</a>\n          : <span className=\"spacer\" />}\n      </div>\n    );\n  }\n};\n", "import React from 'react';\n\nimport Chip from './chip.jsx';\n\nimport { makeImageUrl } from '../lib/blob-helpers.js';\n\n/* BEGIN ChipInput: group membership widget */\nexport default class ChipInput extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = ChipInput.deriveStateFromProps(props);\n    this.state.input = '';\n    this.state.focused = false;\n\n    this.handleTextInput = this.handleTextInput.bind(this);\n    this.removeChipAt = this.removeChipAt.bind(this);\n    this.handleChipCancel = this.handleChipCancel.bind(this);\n    this.handleFocusGained = this.handleFocusGained.bind(this);\n    this.handleFocusLost = this.handleFocusLost.bind(this);\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n  }\n\n  static deriveStateFromProps(props) {\n    return {\n      placeholder: props.chips ? '' : props.prompt,\n      sortedChips: ChipInput.sortChips(props.chips, props.staticMembers),\n      chipIndex: ChipInput.indexChips(props.chips)\n    };\n  }\n\n  componentDidUpdate(prevProps, prevState) {\n    if (prevProps.chips != this.props.chips ||\n      prevProps.staticMembers != this.props.staticMembers ||\n      prevProps.prompt != this.props.prompt) {\n      this.setState(ChipInput.deriveStateFromProps(this.props));\n    }\n    if (!prevState || this.props.chips.length > prevState.sortedChips.length) {\n      this.setState({input: ''});\n    }\n  }\n\n  // Map chip index to user name\n  static indexChips(chips) {\n    const index = {};\n    let count = 0;\n    chips.map((item) => {\n      index[item.user] = count;\n      count ++;\n    });\n    return index;\n  }\n\n  // Have non-removable chips appear before all other chips.\n  static sortChips(chips, keep) {\n    const required = [];\n    const normal = [];\n    chips.map((item) => {\n      if (keep && keep.includes(item.user)) {\n        required.push(item);\n      } else {\n        normal.push(item);\n      }\n    });\n    return required.concat(normal);\n  }\n\n  handleTextInput(e) {\n    this.setState({input: e.target.value});\n    if (this.props.filterFunc) {\n      this.props.filterFunc(e.target.value);\n    }\n  }\n\n  removeChipAt(idx) {\n    const removed = this.state.sortedChips[idx];\n    this.props.onChipRemoved(removed.user, this.state.chipIndex[removed.user]);\n  }\n\n  handleChipCancel(item, idx) {\n    this.removeChipAt(idx);\n  }\n\n  handleFocusGained() {\n    this.setState({focused: true});\n  }\n\n  handleFocusLost() {\n    this.setState({focused: false});\n    if (this.props.onFocusLost) {\n      this.props.onFocusLost(this.state.input);\n    }\n  }\n\n  handleKeyDown(e) {\n    if (e.key === 'Backspace') {\n      if (this.state.input.length == 0 && this.state.sortedChips.length > 0) {\n        const at = this.state.sortedChips.length - 1;\n        if (this.state.sortedChips[at].user !== this.props.staticMembers) {\n          this.removeChipAt(at);\n        }\n      }\n    } else if (e.key === 'Enter') {\n      if (this.props.onEnter) {\n        this.props.onEnter(this.state.input);\n      }\n    } else if (e.key === 'Escape') {\n      if (this.props.onCancel) {\n        this.props.onCancel();\n      }\n    }\n  }\n\n  render() {\n    const chips = [];\n    let count = 0;\n    const staticMembers = this.props.staticMembers || [];\n    this.state.sortedChips.map((item) => {\n      chips.push(\n        <Chip\n          onCancel={this.handleChipCancel}\n          avatar={makeImageUrl(item.public ? item.public.photo : null)}\n          title={item.public ? item.public.fn : undefined}\n          noAvatar={this.props.avatarDisabled}\n          topic={item.user}\n          required={staticMembers.includes(item.user)}\n          invalid={item.invalid}\n          index={count}\n          key={item.user} />\n      );\n      count++;\n    });\n    const className = \"chip-input\" + (this.state.focused ? ' focused' : '');\n    return (\n      <div className={className}>\n        {chips}\n        <input type=\"text\"\n          placeholder={this.state.placeholder}\n          onChange={this.handleTextInput}\n          onFocus={this.handleFocusGained}\n          onBlur={this.handleFocusLost}\n          onKeyDown={this.handleKeyDown}\n          value={this.state.input}\n          autoFocus />\n      </div>\n    );\n  }\n};\n", "// GroupMembers: control for managing a list of group members.\nimport React from 'react';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nimport ChipInput from './chip-input.jsx';\nimport ContactList from './contact-list.jsx';\n\nconst messages = defineMessages({\n  no_contacts: {\n    id: 'no_contacts',\n    defaultMessage: 'You have no contacts :-(',\n    description: 'Shown in ContactsView when the user has no contacts'\n  },\n  contacts_not_found_short: {\n    id: 'contacts_not_found_short',\n    defaultMessage: \"No contacts match ''{query}''\",\n    description: 'Shown in ContactsView when search returned no results'\n  }\n});\n\nclass GroupManager extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      // Array of topic members\n      members: props.members,\n      index: GroupManager.indexMembers(props.members),\n      staticMembers: GroupManager.staticMembers(props.members, props.keepInitialMembers, props.requiredMember),\n      contactFilter: '',\n      noContactsMessage: props.intl.formatMessage(messages.no_contacts),\n      selectedContacts: GroupManager.selectedContacts(props.members)\n    };\n\n    this.handleContactSelected = this.handleContactSelected.bind(this);\n    this.handleMemberRemoved = this.handleMemberRemoved.bind(this);\n    this.handleContactFilter = this.handleContactFilter.bind(this);\n    this.handleSubmit = this.handleSubmit.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n  }\n\n  static indexMembers(members) {\n    let index = {};\n    members.map((m) => {\n      index[m.user] = {delta: 0, present: true}; // Delta: 0 unchanged, +1 added, -1 removed\n    });\n    return index;\n  }\n\n  static staticMembers(members, keepInitial, requiredMember) {\n    let stat = [];\n    members.map((m) => {\n      if (keepInitial || m.user == requiredMember) {\n        stat.push(m.user);\n      }\n    });\n    return stat;\n  }\n\n  static selectedContacts(members) {\n    let sel = [];\n    members.map((m) => {\n      sel.push(m.user);\n    });\n    return sel;\n  }\n\n  handleContactSelected(userId, index) {\n    let status = this.state.index[userId];\n    if (status) {\n      if (status.present) {\n        // Prevent duplicate members\n        return;\n      }\n      status.delta += 1;\n      status.present = true;\n    } else {\n      status = {delta: 1, present: true};\n    }\n\n    let m = this.state.members.slice();\n    m.push(this.props.contacts[index]);\n\n    const sel = GroupManager.selectedContacts(m);\n\n    const i = this.state.index;\n    i[userId] = status;\n\n    this.setState({members: m, index: i, selectedContacts: sel});\n  }\n\n  handleMemberRemoved(userId, index) {\n    const status = this.state.index[userId];\n    if (!status || !status.present) {\n      return;\n    }\n    status.present = false;\n    status.delta -= 1;\n\n    let m = this.state.members.slice();\n    m.splice(index, 1);\n\n    const sel = GroupManager.selectedContacts(m);\n\n    const i = this.state.index;\n    i[userId] = status;\n\n    this.setState({members: m, index: i, selectedContacts: sel});\n  }\n\n  handleContactFilter(val) {\n    const {formatMessage} = this.props.intl;\n    const msg = !val ?\n      formatMessage(messages.no_contacts) :\n      formatMessage(messages.contacts_not_found_short, {query: val});\n\n    this.setState({contactFilter: val, noContactsMessage: msg});\n  }\n\n  static doContactFiltering(filter, values) {\n    if (filter) {\n      for (let i=0; i<values.length; i++) {\n        if (values[i].indexOf(filter) >= 0) {\n          return true;\n        }\n      }\n      return false;\n    }\n    return true;\n  }\n\n  handleSubmit() {\n    var instance = this;\n    var members = [];\n    var added = [];\n    var removed = [];\n\n    var keys = Object.keys(this.state.index);\n    keys.map(function(k) {\n      if (instance.state.index[k].present) {\n        members.push(k);\n      }\n\n      if (instance.state.index[k].delta > 0) {\n        added.push(k);\n      } else if (instance.state.index[k].delta < 0) {\n        removed.push(k);\n      }\n    });\n    this.props.onSubmit(members, added, removed);\n  }\n\n  handleCancel() {\n    this.props.onCancel();\n  }\n\n  render() {\n    const {formatMessage} = this.props.intl;\n    return (\n      <div id=\"group-manager\">\n        <div className=\"panel-form-row\">\n          <label className=\"small\">\n            <FormattedMessage id=\"title_group_members\" defaultMessage=\"Group Members\" description=\"Section title\" />\n          </label>\n        </div>\n        <div className=\"panel-form-row\">\n          <ChipInput\n            chips={this.state.members}\n            staticMembers={this.state.staticMembers}\n            prompt=\"add members\"\n            filterFunc={this.handleContactFilter}\n            onChipRemoved={this.handleMemberRemoved} />\n        </div>\n        <div className=\"panel-form-row\">\n          <label className=\"small\">\n            <FormattedMessage id=\"title_all_contacts\" defaultMessage=\"All Contacts\"\n              description=\"Section title [All Contacts]\" />\n          </label>\n        </div>\n        <ContactList\n          contacts={this.props.contacts}\n          myUserId={this.props.myUserId}\n          topicSelected={this.state.selectedContacts}\n          filter={this.state.contactFilter}\n          filterFunc={GroupManager.doContactFiltering}\n          emptyListMessage={this.state.noContactsMessage}\n          showOnline={false}\n          showUnread={false}\n          onTopicSelected={this.handleContactSelected} />\n        <div id=\"group-manager-buttons\" className=\"panel-form-row\">\n          <button className=\"blue\" onClick={this.handleSubmit}>\n            <FormattedMessage id=\"button_ok\" defaultMessage=\"OK\" description=\"Button [OK]\" />\n          </button>\n          <button className=\"white\" onClick={this.handleCancel}>\n            <FormattedMessage id=\"button_cancel\" defaultMessage=\"Cancel\" description=\"Button [Cancel]\" />\n          </button>\n        </div>\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(GroupManager);\n", "// Password with a visiblity toggle.\nimport React from 'react';\n\nexport default class VisiblePassword extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      value: this.props.value,\n      visible: false\n    };\n\n    this.handleVisibility = this.handleVisibility.bind(this);\n    this.handeTextChange = this.handeTextChange.bind(this);\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleEditingFinished = this.handleEditingFinished.bind(this);\n  }\n\n  handeTextChange(e) {\n    this.setState({value: e.target.value});\n    if (this.props.onChange) {\n      this.props.onChange(e);\n    }\n  }\n\n  handleVisibility(e) {\n    e.preventDefault();\n    this.setState({visible: !this.state.visible});\n  }\n\n  handleKeyDown(e) {\n    if (e.keyCode == 27) {\n      // Escape pressed\n      this.setState({value: this.props.value, visible: false});\n      if (this.props.onFinished) {\n        this.props.onFinished();\n      }\n    } else if (e.keyCode == 13) {\n      // Enter pressed\n      this.handleEditingFinished();\n    }\n  }\n\n  handleEditingFinished(e) {\n    if (e) {\n      let currentTarget = e.currentTarget;\n      setTimeout(() => {\n        if (!currentTarget.contains(document.activeElement)) {\n          if (this.props.onFinished) {\n            this.props.onFinished(this.state.value);\n          }\n        }\n      }, 0);\n    } else if (this.props.onFinished) {\n      this.props.onFinished(this.state.value.trim());\n    }\n  }\n\n  render() {\n    return (\n      <div tabIndex=\"-1\" className=\"group-focus\"\n        onBlur={this.handleEditingFinished}>\n        <input className=\"with-visibility\"\n          type={this.state.visible ? \"text\" : \"password\"}\n          value={this.state.value}\n          placeholder={this.props.placeholder}\n          required={this.props.required ? 'required' : ''}\n          autoFocus={this.props.autoFocus ? 'autoFocus' : ''}\n          autoComplete={this.props.autoComplete}\n          onChange={this.handeTextChange}\n          onKeyDown={this.handleKeyDown} />\n        <span onClick={this.handleVisibility}>\n          <i className=\"material-icons clickable light-gray\">\n            {this.state.visible ? 'visibility' : 'visibility_off'}\n          </i>\n        </span>\n      </div>\n    );\n  }\n}\n", "// In-place text editor. Shows text with an icon which toggles it into an input field.\nimport React from 'react';\nimport ReactDOM from 'react-dom';\n\nimport VisiblePassword from './visible-password.jsx';\n\nexport default class InPlaceEdit extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      active: props.active,\n      initialValue: props.value || '',\n      value: props.value || ''\n    };\n\n    this.handeTextChange = this.handeTextChange.bind(this);\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleStartEditing = this.handleStartEditing.bind(this);\n    this.handleEditingFinished = this.handleEditingFinished.bind(this);\n    this.handlePasswordFinished = this.handlePasswordFinished.bind(this);\n  }\n\n  componentDidUpdate(prevProps, prevState) {\n    // If text has changed while in read mode, update text and discard changes.\n    // Ignore update if in edit mode.\n    const newValue = this.props.value || '';\n    if (prevState.initialValue != newValue && !prevState.active) {\n      this.setState({\n        initialValue: newValue,\n        value: newValue\n      });\n    }\n  }\n\n  handeTextChange(e) {\n    this.setState({value: e.target.value});\n  }\n\n  handleKeyDown(e) {\n    if (e.keyCode === 27) {\n      // Escape pressed\n      this.setState({value: this.props.value, active: false});\n    } else if (e.keyCode === 13) {\n      // Enter pressed\n      this.handleEditingFinished(e);\n    }\n  }\n\n  handleStartEditing() {\n    if (!this.props.readOnly) {\n      ReactDOM.findDOMNode(this).focus();\n      this.setState({active: true});\n    }\n  }\n\n  handleEditingFinished(event) {\n    if (this.props.required && !event.target.checkValidity()) {\n      // Empty input\n      this.setState({value: this.props.value, active: false});\n      return;\n    }\n    this.setState({active: false});\n    let value = this.state.value.trim();\n    if ((value || this.props.value) && (value !== this.props.value)) {\n      this.props.onFinished(value);\n    }\n  }\n\n  handlePasswordFinished(value) {\n    this.setState({active: false});\n    if (value && (value !== this.props.value)) {\n      this.props.onFinished(value);\n    }\n  }\n\n  render() {\n    if (this.state.active) {\n      var fieldType = this.props.type || 'text';\n    } else {\n      var spanText = this.props.type == 'password' ? '••••••••' : this.state.value;\n      var spanClass = 'in-place-edit' +\n        (this.props.readOnly ? ' disabled' : '');\n      if (!spanText) {\n        spanText = this.props.placeholder;\n        spanClass += ' placeholder';\n      }\n      if (spanText.length > 20) {\n        // FIXME: this is wrong for RTL languages.\n        spanText = spanText.substring(0, 19) + '...';\n      }\n    }\n    return (\n      this.state.active ?\n        (fieldType == 'password' ?\n          <VisiblePassword\n            value={this.state.value}\n            placeholder={this.props.placeholder}\n            required={this.props.required ? 'required' : ''}\n            autoComplete={this.props.autoComplete}\n            autoFocus={true}\n            onFinished={this.handlePasswordFinished}/>\n          :\n          <input type={fieldType}\n            value={this.state.value}\n            placeholder={this.props.placeholder}\n            required={this.props.required ? 'required' : ''}\n            autoComplete={this.props.autoComplete}\n            autoFocus\n            onChange={this.handeTextChange}\n            onKeyDown={this.handleKeyDown}\n            onBlur={this.handleEditingFinished} />\n        )\n        :\n        <span className={spanClass} onClick={this.handleStartEditing}>\n          <span className=\"content\">{spanText}</span>\n        </span>\n    );\n  }\n};\n", "// Toggle [Title text >] -> [Title text v]\n\nimport React from 'react';\n\nexport default class MoreButton extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      open: props.open\n    };\n    this.handleToggle = this.handleToggle.bind(this);\n  }\n\n  handleToggle() {\n    const open = !this.state.open;\n    this.setState({open: open});\n    if (this.props.onToggle) {\n      this.props.onToggle(open);\n    }\n  }\n\n  render() {\n    return (<label className=\"small clean-clickable\" onClick={this.handleToggle}>{this.props.title}...\n      {this.state.open ? <i className=\"material-icons\">expand_more</i> :\n        <i className=\"material-icons\">chevron_right</i>}\n      </label>);\n  }\n}\n", "// PermissionsEditor: Component for editing permissions\n// <PermissionsEditor mode=\"JWROD\" skip=\"O\" onChange={this.handleCheckboxTest} />\nimport React from 'react';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nimport CheckBox from './checkbox.jsx';\nimport Contact from './contact.jsx';\n\nimport { makeImageUrl } from '../lib/blob-helpers.js';\n\n// Translatable permission names.\nconst messages = defineMessages({\n  'joiner': {\n    id: 'permission_join',\n    defaultMessage: \"Join ({val})\",\n    description: 'Name of J permission'\n  },\n  'reader': {\n    id: 'permission_read',\n    defaultMessage: \"Read ({val})\",\n    description: 'Name of R permission'\n  },\n  'writer': {\n    id: 'permission_write',\n    defaultMessage: \"Write ({val})\",\n    description: 'Name of W permission'\n  },\n  'preser': {\n    id: 'permission_pres',\n    defaultMessage: \"Get notified ({val})\",\n    description: 'Name of P permission'\n  },\n  'approver': {\n    id: 'permission_admin',\n    defaultMessage: \"Approve ({val})\",\n    description: 'Name of A permission'\n  },\n  'sharer': {\n    id: 'permission_share',\n    defaultMessage: \"Share ({val})\",\n    description: 'Name of S permission'\n  },\n  'deleter': {\n    id: 'permission_delete',\n    defaultMessage: \"Delete ({val})\",\n    description: 'Name of D permission'\n  },\n  'owner': {\n    id: 'permission_owner',\n    defaultMessage: \"Owner ({val})\",\n    description: 'Name of O permission'\n  }\n});\n\nclass PermissionsEditor extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      mode: (props.mode || '').replace('N', '')\n    };\n\n    this.handleChange = this.handleChange.bind(this);\n    this.handleSubmit = this.handleSubmit.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n  }\n\n  handleChange(val) {\n    let mode = this.state.mode;\n    let idx = mode.indexOf(val);\n    if (idx == -1) {\n      mode += val;\n    } else {\n      mode = mode.replace(val, '');\n    }\n    this.setState({mode: mode});\n  }\n\n  handleSubmit() {\n    // Normalize string, otherwise cannot check if mode has changed.\n    var mode = (this.state.mode || 'N').split('').sort().join('');\n    var before = (this.props.mode || 'N').split('').sort().join('')\n    if (mode !== before) {\n      this.props.onSubmit(mode);\n    } else {\n      this.props.onCancel();\n    }\n  }\n\n  handleCancel() {\n    this.props.onCancel();\n  }\n\n  render() {\n    const {formatMessage} = this.props.intl;\n    const all = 'JRWPASDO';\n    const names = {\n      'J': formatMessage(messages.joiner, {val: 'J'}),\n      'R': formatMessage(messages.reader, {val: 'R'}),\n      'W': formatMessage(messages.writer, {val: 'W'}),\n      'P': formatMessage(messages.preser, {val: 'P'}),\n      'A': formatMessage(messages.approver, {val: 'A'}),\n      'S': formatMessage(messages.sharer, {val: 'S'}),\n      'D': formatMessage(messages.deleter, {val: 'D'}),\n      'O': formatMessage(messages.owner, {val: 'O'})\n    };\n\n    let skip = this.props.skip || '';\n    let mode = this.state.mode;\n    let compare = (this.props.compare || '').replace('N', '');\n    let items = [];\n    for (let i=0; i<all.length; i++) {\n      let c = all.charAt(i);\n      if (skip.indexOf(c) >= 0 && mode.indexOf(c) < 0) {\n        // Permission is marked as inactive: hide unchecked permissions, disable checked permissions\n        continue;\n      }\n      items.push(\n        <tr key={c}>\n          <td>{names[c]}</td>\n          <td className=\"checkbox\">{skip.indexOf(c) < 0 ?\n            <CheckBox name={c} checked={(mode.indexOf(c) >= 0)} onChange={this.handleChange}/>\n            :\n            <CheckBox name={c} checked={(mode.indexOf(c) >= 0)} />\n          }</td>{this.props.compare ? <td className=\"checkbox\">\n            <CheckBox name={c} checked={(compare.indexOf(c) >= 0)}/>\n          </td> : null}\n        </tr>\n      );\n    }\n\n    return (\n      <div className=\"panel-form-column\">\n        {this.props.userTitle ?\n          <ul className=\"contact-box\"><Contact\n            item={this.props.item}\n            title={this.props.userTitle}\n            avatar={makeImageUrl(this.props.userAvatar ? this.props.userAvatar : null)} /></ul> : null}\n        <label className=\"small\"><FormattedMessage id=\"title_permissions\"\n          defaultMessage=\"Permissions\" description=\"Section title\"/></label>\n        <table className=\"permission-editor\">\n        {this.props.compare ?\n          <thead><tr>\n            <th></th><th>{this.props.modeTitle}</th>\n            <th>{this.props.compareTitle}</th>\n          </tr></thead> :\n          null}\n        <tbody>\n          {items}\n        </tbody></table>\n        <br />\n        <div className=\"dialog-buttons\">\n          <button className=\"outline\" onClick={this.handleCancel}>\n            <FormattedMessage id=\"button_cancel\" />\n          </button>\n          <button className=\"blue\" onClick={this.handleSubmit}>\n            <FormattedMessage id=\"button_ok\" />\n          </button>\n        </div>\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(PermissionsEditor);\n", "// Odds and ends\n\n// Make shortcut icon appear with a green dot + show unread count in title.\nexport function updateFavicon(count) {\n  const oldIcon = document.getElementById('shortcut-icon');\n  const head = document.head || document.getElementsByTagName('head')[0];\n  const newIcon = document.createElement('link');\n  newIcon.type = 'image/png';\n  newIcon.id = 'shortcut-icon';\n  newIcon.rel = 'shortcut icon';\n  newIcon.href = 'img/logo32x32' + (count > 0 ? 'a' : '') + '.png';\n  if (oldIcon) {\n    head.removeChild(oldIcon);\n  }\n  head.appendChild(newIcon);\n\n  document.title = (count > 0 ? '('+count+') ' : '') + 'Tinode';\n}\n\n// Create VCard which represents topic 'public' info\nexport function vcard(fn, imageDataUrl) {\n  let card = null;\n\n  if ((fn && fn.trim()) || imageDataUrl) {\n    card = {};\n    if (fn) {\n      card.fn = fn.trim();\n    }\n    if (imageDataUrl) {\n      const dataStart = imageDataUrl.indexOf(',');\n      card.photo = dataStart >= 0 ? {\n        data: imageDataUrl.substring(dataStart+1),\n        type: 'jpg'\n      } : imageDataUrl;\n    }\n  }\n  return card;\n}\n\n// Deep-shallow compare two arrays: shallow compare each element.\nexport function arrayEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (!Array.isArray(a) || !Array.isArray(b)) {\n    return false;\n  }\n\n  // Compare lengths first.\n  if (a.length != b.length) {\n    return false;\n  }\n  // Order of elements is ignored.\n  a.sort();\n  b.sort();\n  for (let i = 0, l = a.length; i < l; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// Checks (loosely) if the given string is a phone. If so, returns the phone number in a format\n// as close to E.164 as possible.\nexport function asPhone(val) {\n  val = val.trim();\n  if (/^(?:\\+?(\\d{1,3}))?[- (.]*(\\d{3})[- ).]*(\\d{3})[- .]*(\\d{2})[- .]*(\\d{2})?$/.test(val)) {\n    return val.replace(/[- ().]*/, '');\n  }\n  return null;\n}\n\n// Checks (loosely) if the given string is an email. If so returns the email.\nexport function asEmail(val) {\n  val = val.trim();\n  if (/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$/.test(val)) {\n    return val;\n  }\n  return null;\n}\n\n// Checks if URL is a relative url, i.e. has no 'scheme://', including the case of missing scheme '//'.\n// The scheme is expected to be RFC-compliant, e.g. [a-z][a-z0-9+.-]*\n// example.html - ok\n// https:example.com - not ok.\n// http:/example.com - not ok.\n// ' ↲ https://example.com' - not ok. (↲ means carriage return)\nexport function isUrlRelative(url) {\n  return !/^\\s*([a-z][a-z0-9+.-]*:|\\/\\/)/im.test(url);\n}\n\n// Ensure URL does not present an XSS risk. Optional allowedSchemes may contain an array of\n// strings with permitted URL schemes, such as ['ftp', 'ftps']; otherwise accept http and https only.\nexport function sanitizeUrl(url, allowedSchemes) {\n  if (!url) {\n    return null;\n  }\n\n  // Strip control characters and whitespace. They are not valid URL characters anyway.\n  url = url.replace(/[^\\x20-\\x7E]/gmi, '').trim();\n\n  // Relative URLs are safe.\n  if (!/^([a-z][a-z0-9+.-]*:|\\/\\/)/i.test(url)) {\n    return url;\n  }\n  // Blob URLs are safe.\n  if (/^blob:http/.test(url)) {\n    return url;\n  }\n\n  // Absolute URL. Accept only safe schemes, or no scheme.\n  const schemes = Array.isArray(allowedSchemes) ? allowedSchemes.join('|') : 'http|https';\n  const re = new RegExp('^((' + schemes + '):|//)', 'i');\n  if (!re.test(url)) {\n    return null;\n  }\n\n  return url;\n}\n\n// Ensure URL is suitable for <img src=\"url\"> field: the URL must be a relative URL or\n// have http:, https:, or data: scheme. In case of data: scheme, the URL must start with\n// a 'data:image/XXXX;base64,'.\nexport function sanitizeImageUrl(url) {\n  if (!url) {\n    return null;\n  }\n\n  const sanitizedUrl = sanitizeUrl(url);\n  if (sanitizedUrl) {\n    return sanitizedUrl;\n  }\n\n  // Is this a data: URL of an image?\n  if (/data:image\\/[a-z0-9.-]+;base64,/i.test(url.trim())) {\n    return url;\n  }\n\n  return null;\n}\n", "// TagManager: edit topic or user tags.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport ChipInput from './chip-input.jsx';\n\nimport { MAX_TAG_COUNT, MIN_TAG_LENGTH } from '../config.js';\nimport { arrayEqual } from '../lib/utils.js';\n\nexport default class TagManager extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      tags: this.props.tags,\n      tagInput: '',\n      activated: this.props.activated\n    };\n\n    this.handleTagInput = this.handleTagInput.bind(this);\n    this.handleAddTag = this.handleAddTag.bind(this);\n    this.handleRemoveTag = this.handleRemoveTag.bind(this);\n    this.handleSubmit = this.handleSubmit.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n  }\n\n  static getDerivedStateFromProps(nextProps, prevState) {\n    if (!arrayEqual(nextProps.tags, prevState.tags) && !prevState.activated) {\n      return {tags: nextProps.tags};\n    }\n    return null;\n  }\n\n  handleTagInput(text) {\n    this.setState({tagInput: text});\n    if (text.length > 0) {\n      const last = text[text.length-1];\n      if (text[0] == '\"') {\n        // This is a quoted string.\n        if (text.length > 1 && last == '\"') {\n          this.handleAddTag(text.substring(1, text.length-1));\n        }\n      } else if (last == ',' || last == ' ' || last == ';' || last == '\"') {\n        // User entered ',', ' ' or ';'\n        this.handleAddTag(text.substring(0, text.length-1).trim());\n      }\n    }\n  }\n\n  handleAddTag(tag) {\n    if (tag.length > 0 && this.state.tags.length < MAX_TAG_COUNT) {\n      const tags = this.state.tags.slice(0);\n      tags.push(tag);\n\n      this.setState({tags: tags, tagInput: ''});\n      if (this.props.onTagsChanged) {\n        this.props.onTagsChanged(tags);\n      }\n      return tags;\n    }\n    return this.state.tags;\n  }\n\n  handleRemoveTag(tag, index) {\n    const tags = this.state.tags.slice(0);\n    tags.splice(index, 1);\n    this.setState({tags: tags});\n    if (this.props.onTagsChanged) {\n      this.props.onTagsChanged(tags);\n    }\n  }\n\n  handleSubmit() {\n    // Add unprocessed input to tags, submit the list.\n    this.props.onSubmit(this.handleAddTag(this.state.tagInput.trim()));\n    this.setState({activated: false, tags: this.props.tags});\n  }\n\n  handleCancel() {\n    this.setState({activated: false, tagInput: '', tags: this.props.tags});\n    if (this.props.onCancel) {\n      this.props.onCancel();\n    }\n  }\n\n  render() {\n    let tags = [];\n    if (this.state.activated) {\n      this.state.tags.map((tag) => {\n        tags.push({user: tag, invalid: (tag.length < MIN_TAG_LENGTH)});\n      });\n    } else {\n      this.state.tags.map((tag) => {\n        tags.push(<span className=\"badge\" key={tags.length}>{tag}</span>);\n      });\n      if (tags.length == 0) {\n        tags = (\n          <i>\n            <FormattedMessage id=\"tags_not_found\" defaultMessage=\"No tags defined. Add some.\" description=\"\" />\n          </i>\n        );\n      }\n    }\n    return (\n      <div className=\"panel-form-column\">\n        <div className=\"panel-form-row\">\n          <label className=\"small\">{this.props.title}</label>\n        </div>\n        {this.state.activated ?\n        <div>\n          <FormattedMessage id=\"tags_editor_no_tags\" defaultMessage=\"Add some tags\"\n            description=\"Tag editor prompt when no tags are found.\">{\n            (add_tags_prompt) => <ChipInput\n              chips={tags}\n              avatarDisabled={true}\n              prompt={add_tags_prompt}\n              onEnter={this.handleAddTag}\n              onFocusLost={this.handleAddTag}\n              onCancel={this.handleCancel}\n              onChipRemoved={this.handleRemoveTag}\n              filterFunc={this.handleTagInput} />\n          }</FormattedMessage>\n          {this.props.onSubmit || this.props.onCancel ?\n            <div id=\"tag-manager-buttons\" className=\"dialog-buttons panel-form-row\">\n              <button className=\"outline\" onClick={this.handleCancel}>\n                <FormattedMessage id=\"button_cancel\" defautMessage=\"Cancel\" description=\"Rejection button [Cancel]\" />\n              </button>\n              <button className=\"blue\" onClick={this.handleSubmit}>\n                <FormattedMessage id=\"button_ok\" defautMessage=\"OK\" description=\"Confirmation button [OK]\" />\n              </button>\n            </div>\n          : null}\n        </div>\n        :\n        <div className=\"quoted\">\n          <a href=\"#\" className=\"flat-button\" onClick={(e) => {e.preventDefault(); this.setState({activated: true});}}>\n            <i className=\"material-icons\">edit</i> &nbsp;<FormattedMessage id=\"title_manage_tags\" defaultMessage=\"Manage\"\n              description=\"Section title for the list of tags\" />\n          </a>\n          <>{tags}</>\n        </div>\n      }\n      </div>\n    );\n  }\n};\n", "// InfoView: panel with topic/user info.\nimport React from 'react';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nimport Tinode from 'tinode-sdk';\n\nimport AvatarUpload from '../widgets/avatar-upload.jsx';\nimport CheckBox from '../widgets/checkbox.jsx';\nimport ContactList from '../widgets/contact-list.jsx';\nimport ErrorPanel from '../widgets/error-panel.jsx';\nimport GroupManager from '../widgets/group-manager.jsx';\nimport InPlaceEdit from '../widgets/in-place-edit.jsx';\nimport MenuCancel from '../widgets/menu-cancel.jsx';\nimport MoreButton from '../widgets/more-button.jsx';\nimport PermissionsEditor from '../widgets/permissions-editor.jsx';\nimport TagManager from '../widgets/tag-manager.jsx';\n\nimport { MAX_TITLE_LENGTH, NO_ACCESS_MODE } from '../config.js';\n\nimport { makeImageUrl } from '../lib/blob-helpers.js';\nimport { arrayEqual, vcard } from '../lib/utils.js';\n\nconst messages = defineMessages({\n  requested: {\n    id: 'requested_permissions',\n    defaultMessage: 'Requested',\n    description: 'Title for permissions'\n  },\n  granted: {\n    id: 'granted_permissions',\n    defaultMessage: 'Granted',\n    description: 'Title for permissions'\n  },\n  edit_permissions: {\n    id: 'menu_item_edit_permissions',\n    defaultMessage: 'Edit permissions',\n    description: 'Menu item [Edit permissions]'\n  },\n  other_user: {\n    id: 'label_other_user',\n    defaultMessage: 'Other',\n    description: 'Label for the other user when the user is unnamed'\n  },\n  clear_messages: {\n    id: 'action_clear_messages',\n    defaultMessage: 'Clear Messages',\n    description: 'Flat button [Clear Messages] (soft-delete messages)'\n  },\n  clear_messages_warning: {\n    id: 'clear_messages_warning',\n    defaultMessage: 'Are you sure you want to clear all messages? It cannot be undone.',\n    description: 'Alert dialog warning when deleting all messages.'\n  },\n  delete_messages: {\n    id: 'action_delete_messages',\n    defaultMessage: 'Clear Messages for All',\n    description: 'Flat button [Clear for All] (hard-delete all messages)'\n  },\n  delete_messages_warning: {\n    id: 'delete_messages_warning',\n    defaultMessage: 'Are you sure you want to delete all messages for everyone? It cannot be undone.',\n    description: 'Alert dialog warning when hard-deleting all messages.'\n  },\n  leave_chat: {\n    id: 'action_leave_chat',\n    defaultMessage: 'Leave Conversation',\n    description: 'Flat button [Leave Conversation]'\n  },\n  leave_chat_warning: {\n    id: 'leave_chat_warning',\n    defaultMessage: 'Are you sure you want to leave this conversation?',\n    description: 'Alert dialog warning when unsubscribing from a chat.'\n  },\n  block_contact: {\n    id: 'action_block_contact',\n    defaultMessage: \"Block Contact\",\n    description: \"Flat button [Block Contact]\"\n  },\n  block_contact_warning: {\n    id: 'block_contact_warning',\n    defaultMessage: 'Are you sure you want to block this contact?',\n    description: 'Alert dialog warning when blocking a contact.'\n  },\n  report_chat: {\n    id: 'action_report_chat',\n    defaultMessage: 'Report Conversation',\n    description: 'Flat button [Report Group]'\n  },\n  report_chat_warning: {\n    id: 'report_chat_warning',\n    defaultMessage: 'Are you sure you want to block and report this conversation?',\n    description: 'Alert dialog warning when reporting a conversation for abuse'\n  },\n});\n\nclass InfoView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      topic: null,\n      owner: false,\n      admin: false,\n      sharer: false,\n      deleter: false,\n      muted: false,\n      address: null,\n      groupTopic: undefined,\n      fullName: undefined,\n      avatar: null,\n      private: null,\n      selectedContact: null,\n      access: null,\n      modeGiven: null,\n      modeWant: null,\n      modeGiven2: null, // P2P topic, the other user mode given\n      modeWant2: null,  // P2P topic, the other user mode want\n      auth: null,\n      anon: null,\n      contactList: [],\n      tags: [],\n      showMemberPanel: false,\n      showPermissionEditorFor: undefined,\n      moreInfoExpanded: false,\n      previousMetaDesc: undefined,\n      previousSubsUpdated: undefined,\n      previousTagsUpdated: undefined\n    };\n\n    this.resetSubs = this.resetSubs.bind(this);\n    this.resetDesc = this.resetDesc.bind(this);\n    this.onMetaDesc = this.onMetaDesc.bind(this);\n    this.onSubsUpdated = this.onSubsUpdated.bind(this);\n    this.onTagsUpdated = this.onTagsUpdated.bind(this);\n    this.handleFullNameUpdate = this.handleFullNameUpdate.bind(this);\n    this.handlePrivateUpdate = this.handlePrivateUpdate.bind(this);\n    this.handleImageChanged = this.handleImageChanged.bind(this);\n    this.handleMuted = this.handleMuted.bind(this);\n    this.handlePermissionsChanged = this.handlePermissionsChanged.bind(this);\n    this.handleLaunchPermissionsEditor = this.handleLaunchPermissionsEditor.bind(this);\n    this.handleHidePermissionsEditor = this.handleHidePermissionsEditor.bind(this);\n    this.handleShowAddMembers = this.handleShowAddMembers.bind(this);\n    this.handleHideAddMembers = this.handleHideAddMembers.bind(this);\n    this.handleMemberUpdateRequest = this.handleMemberUpdateRequest.bind(this);\n    this.handleDeleteMessages = this.handleDeleteMessages.bind(this);\n    this.handleLeave = this.handleLeave.bind(this);\n    this.handleBlock = this.handleBlock.bind(this);\n    this.handleReport = this.handleReport.bind(this);\n    this.handleMemberSelected = this.handleMemberSelected.bind(this);\n    this.handleMoreInfo = this.handleMoreInfo.bind(this);\n    this.handleTagsUpdated = this.handleTagsUpdated.bind(this);\n    this.handleContextMenu = this.handleContextMenu.bind(this);\n  }\n\n  // No need to separately handle component mount.\n  componentDidUpdate(props) {\n    const topic = this.props.tinode.getTopic(props.topic);\n    if (!topic) {\n      return;\n    }\n\n    if (this.onMetaDesc != topic.onMetaDesc) {\n      this.previousMetaDesc = topic.onMetaDesc;\n      topic.onMetaDesc = this.onMetaDesc;\n\n      this.previousSubsUpdated = topic.onSubsUpdated;\n      topic.onSubsUpdated = this.onSubsUpdated;\n\n      if (topic.getType() == 'grp') {\n        this.previousTagsUpdated = topic.onTagsUpdated;\n        topic.onTagsUpdated = this.onTagsUpdated;\n      } else {\n        this.previousTagsUpdated = undefined;\n      }\n    }\n\n    if (this.state.topic != props.topic) {\n      this.setState({topic: props.topic});\n      this.resetDesc(topic, props);\n      this.resetSubs(topic, props);\n    }\n  }\n\n  componentWillUnmount() {\n    const topic = this.props.tinode.getTopic(this.props.topic);\n    if (!topic) {\n      return;\n    }\n    this.setState({topic: null});\n    topic.onMetaDesc = this.previousMetaDesc;\n    topic.onSubsUpdated = this.previousSubsUpdated;\n    topic.onTagsUpdated = this.previousTagsUpdated;\n  }\n\n  resetSubs(topic, props) {\n    const newState = {contactList: []};\n    if (topic.getType() == 'p2p') {\n      // Fetch the other party in the p2p conversation.\n      // Topic may not be ready yet, so check if user is found.\n      const user2 = topic.subscriber(props.topic);\n      if (user2) {\n        newState.modeGiven2 = user2.acs.getGiven();\n        newState.modeWant2 = user2.acs.getWant();\n      } else {\n        newState.modeGiven2 = NO_ACCESS_MODE;\n        newState.modeWant2 = NO_ACCESS_MODE;\n      }\n    } else {\n      topic.subscribers((sub) => {\n        newState.contactList.push(sub);\n      }, this);\n    }\n\n    this.setState(newState);\n  }\n\n  resetDesc(topic, props) {\n    const defacs = topic.getDefaultAccess() || {};\n    const acs = topic.getAccessMode();\n\n    this.setState({\n      owner: acs && acs.isOwner(),\n      admin: acs && acs.isAdmin(),\n      sharer: acs && acs.isSharer(),\n      deleter: acs && acs.isDeleter(),\n      muted: acs && acs.isMuted(),\n\n      fullName: topic.public ? topic.public.fn : undefined,\n      avatar: makeImageUrl(topic.public ? topic.public.photo : null),\n      private: topic.private ? topic.private.comment : null,\n      address: topic.name,\n      groupTopic: (topic.getType() == 'grp'),\n      showMemberPanel: false,\n      access: acs ? acs.getMode() : undefined,\n      modeGiven: acs ? acs.getGiven() : undefined,\n      modeWant: acs ? acs.getWant() : undefined,\n      auth: defacs.auth,\n      anon: defacs.anon\n    });\n\n    if (topic.getType() == 'grp' && acs && acs.isOwner()) {\n      // Requesting tags: owner is editing the topic.\n      topic.getMeta(topic.startMetaQuery().withTags().build());\n    }\n  }\n\n  onMetaDesc(desc) {\n    const topic = this.props.tinode.getTopic(this.props.topic);\n    if (!topic) {\n      return;\n    }\n    this.resetDesc(topic, this.props);\n\n    if (this.previousMetaDesc && this.previousMetaDesc != this.onMetaDesc) {\n      this.previousMetaDesc(desc);\n    }\n  }\n\n  onSubsUpdated(subs) {\n    const topic = this.props.tinode.getTopic(this.props.topic);\n    if (!topic) {\n      return;\n    }\n    this.resetSubs(topic, this.props);\n\n    if (this.previousSubsUpdated && this.previousSubsUpdated != this.onSubsUpdated) {\n      this.previousSubsUpdated(subs);\n    }\n  }\n\n  onTagsUpdated(tags) {\n    this.setState({tags: tags});\n\n    if (this.previousTagsUpdated && this.previousTagsUpdated != this.onTagsUpdated) {\n      this.previousTagsUpdated();\n    }\n  }\n\n  handleFullNameUpdate(fn) {\n    fn = fn.trim().substring(0, MAX_TITLE_LENGTH);\n    if (this.state.fullName !== fn) {\n      this.setState({fullName: fn});\n      this.props.onTopicDescUpdate(this.props.topic, vcard(fn, null), null);\n    }\n  }\n\n  handlePrivateUpdate(comment) {\n    comment = comment.trim().substring(0, MAX_TITLE_LENGTH);\n    if (this.state.private !== comment) {\n      this.setState({private: comment});\n      this.props.onTopicDescUpdate(this.props.topic, null, comment || Tinode.DEL_CHAR);\n    }\n  }\n\n  handleImageChanged(img) {\n    this.setState({avatar: img});\n    this.props.onTopicDescUpdate(this.props.topic, vcard(null, img || Tinode.DEL_CHAR), null);\n  }\n\n  handleMuted(ignored, checked) {\n    this.setState({muted: checked});\n    this.props.onChangePermissions(this.props.topic, checked ? '-P' : '+P');\n  }\n\n  handlePermissionsChanged(perm) {\n    switch (this.state.showPermissionEditorFor) {\n      case 'auth':\n        this.props.onTopicDescUpdate(this.props.topic, null, null, {auth: perm});\n        break;\n      case 'anon':\n        this.props.onTopicDescUpdate(this.props.topic, null, null, {anon: perm});\n        break;\n      case 'mode':\n      case 'want':\n        this.props.onChangePermissions(this.props.topic, perm);\n        break;\n      case 'given':\n        this.props.onChangePermissions(this.props.topic, perm, this.props.topic);\n        break;\n      case 'user':\n        this.props.onChangePermissions(this.props.topic, perm, this.state.userPermissionsEdited);\n        break;\n    }\n\n    this.setState({showPermissionEditorFor: undefined});\n  }\n\n  handleLaunchPermissionsEditor(which, uid) {\n    const {formatMessage} = this.props.intl;\n    let toEdit, toCompare, toSkip, titleEdit, titleCompare, userTitle, userAvatar;\n    switch (which) {\n      case 'mode':\n        toEdit = this.state.access;\n        break;\n      case 'want':\n        toEdit = this.state.modeWant;\n        toCompare = this.state.modeGiven;\n        toSkip = this.state.owner ? 'O' :  // Don't allow owner to unset 'O' permission.\n          // Allow accepting any of 'ASDO' permissions but don't allow asking for them.\n          Tinode.AccessMode.encode(Tinode.AccessMode.diff('ASDO', this.state.modeGiven));\n        titleEdit = formatMessage(messages.requested);\n        titleCompare = formatMessage(messages.granted);\n        break;\n      case 'given':\n        toEdit = this.state.modeGiven2;\n        toCompare = this.state.modeWant2;\n        toSkip = this.state.groupTopic ? (this.state.owner ? '' : 'O') : 'ASDO';\n        titleEdit = formatMessage(messages.granted);\n        titleCompare = formatMessage(messages.requested);\n        break;\n      case 'auth':\n        toEdit = this.state.auth;\n        toSkip = 'O';\n        break;\n      case 'anon':\n        toEdit = this.state.anon;\n        toSkip = 'O';\n        break;\n      case 'user': {\n        let topic = this.props.tinode.getTopic(this.props.topic);\n        if (!topic) {\n          return;\n        }\n        var user = topic.subscriber(uid);\n        if (!user || !user.acs) {\n          return;\n        }\n        toEdit = user.acs.getGiven();\n        toCompare = user.acs.getWant();\n        toSkip = this.state.owner ? '' : 'O';\n        titleEdit = formatMessage(messages.granted);\n        titleCompare = formatMessage(messages.requested);\n        if (user.public) {\n          userTitle = user.public.fn;\n          userAvatar = user.public.photo;\n        }\n        break;\n      }\n      default:\n        console.log(\"Unknown permission editing mode '\" + which + \"'\");\n        break;\n    }\n    this.setState({\n      showPermissionEditorFor: which,\n      userPermissionsEdited: uid,\n      userPermissionsTitle: userTitle,\n      userPermissionsAvatar: userAvatar,\n      editedPermissions: toEdit,\n      immutablePermissions: toCompare,\n      editedPermissionsTitle: titleEdit,\n      immutablePermissionsTitle: titleCompare,\n      editedPermissionsSkipped: toSkip,\n    });\n  }\n\n  handleHidePermissionsEditor() {\n    this.setState({showPermissionEditorFor: undefined});\n  }\n\n  handleShowAddMembers(e) {\n    e.preventDefault();\n    this.props.onInitFind();\n    this.setState({showMemberPanel: true});\n  }\n\n  handleHideAddMembers() {\n    this.setState({showMemberPanel: false});\n  }\n\n  handleMemberUpdateRequest(members, added, removed) {\n    this.props.onMemberUpdateRequest(this.props.topic, added, removed);\n    this.setState({showMemberPanel: false});\n  }\n\n  handleDeleteMessages(e) {\n    e.preventDefault();\n    const {formatMessage} = this.props.intl;\n    this.props.onShowAlert(\n      formatMessage(this.state.deleter ? messages.delete_messages : messages.clear_messages), // title\n      formatMessage(this.state.deleter ? messages.delete_messages_warning : messages.clear_messages_warning), // content\n      (() => { this.props.onDeleteMessages(this.props.topic); }), // onConfirm\n      null, // \"OK\"\n      true, // Show Reject button\n      null  // \"Cancel\"\n    );\n  }\n\n  handleLeave(e) {\n    e.preventDefault();\n    const {formatMessage} = this.props.intl;\n    this.props.onShowAlert(\n      formatMessage(messages.leave_chat), // title\n      formatMessage(messages.leave_chat_warning), // content\n      (() => { this.props.onLeaveTopic(this.props.topic); }), // onConfirm\n      null, // \"OK\"\n      true, // Show Reject button\n      null  // \"Cancel\"\n    );\n  }\n\n  handleBlock(e) {\n    e.preventDefault();\n    const {formatMessage} = this.props.intl;\n    this.props.onShowAlert(\n      formatMessage(messages.block_contact), // title\n      formatMessage(messages.block_contact_warning), // content\n      (() => { this.props.onBlockTopic(this.props.topic); }), // onConfirm\n      null, // \"OK\"\n      true, // Show Reject button\n      null  // \"Cancel\"\n    );\n  }\n\n  handleReport(e) {\n    e.preventDefault();\n    const {formatMessage} = this.props.intl;\n    this.props.onShowAlert(\n      formatMessage(messages.report_chat), // title\n      formatMessage(messages.report_chat_warning), // content\n      (() => { this.props.onReportTopic(this.props.topic); }), // onConfirm\n      null, // \"OK\"\n      true, // Show Reject button\n      null  // \"Cancel\"\n    );\n  }\n\n  handleMemberSelected(uid) {\n    this.setState({selectedContact: uid});\n  }\n\n  handleMoreInfo(open) {\n    this.setState({moreInfoExpanded: open});\n  }\n\n  handleTagsUpdated(tags) {\n    if (!arrayEqual(this.state.tags.slice(0), tags.slice(0))) {\n      this.props.onTopicTagsUpdate(this.props.topic, tags);\n    }\n  }\n\n  handleContextMenu(params) {\n    const {formatMessage} = this.props.intl;\n    const instance = this;\n    const topic = this.props.tinode.getTopic(this.props.topic);\n    if (!topic) {\n      return;\n    }\n    const user = topic.subscriber(params.topicName);\n    if (!user || !user.acs) {\n      return;\n    }\n\n    const menuItems = [\n      {title: formatMessage(messages.edit_permissions), handler: function() {\n        instance.handleLaunchPermissionsEditor('user', params.topicName);\n      }},\n      'member_delete',\n      user.acs.isMuted() ? 'member_unmute' : 'member_mute',\n      user.acs.isJoiner() ? 'member_block' : 'member_unblock'\n    ];\n    this.props.showContextMenu({\n      topicName: this.props.topic,\n      x: params.x,\n      y: params.y,\n      user: params.topicName}, menuItems);\n  }\n\n  render() {\n    const {formatMessage} = this.props.intl;\n\n    return (\n      <div id=\"info-view\">\n        <div className=\"caption-panel\" id=\"info-caption-panel\">\n          <div className=\"panel-title\" id=\"info-title\">\n            <FormattedMessage id=\"title_info\" defaultMessage=\"Info\" description=\"Title for InfoView\" />\n          </div>\n          <div>\n            <MenuCancel onCancel={this.props.onCancel} />\n          </div>\n        </div>\n        {this.props.displayMobile ?\n          <ErrorPanel\n            level={this.props.errorLevel}\n            text={this.props.errorText}\n            onClearError={this.props.onError} /> : null}\n        {this.state.showMemberPanel ?\n          <GroupManager\n            members={this.state.contactList}\n            requiredMember={this.props.myUserId}\n            keepInitialMembers={!this.state.admin && !this.state.owner}\n            myUserId={this.props.myUserId}\n            contacts={this.props.searchableContacts}\n            onCancel={this.handleHideAddMembers}\n            onSubmit={this.handleMemberUpdateRequest} />\n          :\n        this.state.showPermissionEditorFor ?\n          <PermissionsEditor\n            mode={this.state.editedPermissions}\n            compare={this.state.immutablePermissions}\n            skip={this.state.editedPermissionsSkipped}\n            modeTitle={this.state.editedPermissionsTitle}\n            compareTitle={this.state.immutablePermissionsTitle}\n            userTitle={this.state.userPermissionsTitle}\n            item={this.state.userPermissionsEdited}\n            userAvatar={this.state.userPermissionsAvatar}\n            onSubmit={this.handlePermissionsChanged}\n            onCancel={this.handleHidePermissionsEditor}\n            />\n          :\n          <div id=\"info-view-content\" className=\"scrollable-panel\">\n            <div className=\"panel-form-row\">\n              <div className=\"panel-form-column\">\n                <div><label className=\"small\">\n                  <FormattedMessage id=\"label_topic_name\" defaultMessage=\"Name\"\n                    description=\"Label for editing topic name\" />\n                </label></div>\n                <div><InPlaceEdit\n                    placeholder={this.state.groupTopic ? \"Group name\" : <i>Unknown</i>}\n                    readOnly={!this.state.owner}\n                    value={this.state.fullName}\n                    required={true}\n                    onFinished={this.handleFullNameUpdate} /></div>\n                <div>\n                  <label className=\"small\">\n                    <FormattedMessage id=\"label_private\" defaultMessage=\"Private comment\"\n                      description=\"Label for editing 'private'\" />\n                  </label>\n                </div>\n                <div>\n                  <FormattedMessage id=\"private_editing_placeholder\"\n                    defaultMessage=\"Visible to you only\"\n                    description=\"Placeholder for editing 'private'\">{\n                    (private_placeholder) => <InPlaceEdit\n                      placeholder={private_placeholder}\n                      value={this.state.private}\n                      onFinished={this.handlePrivateUpdate} />\n                  }</FormattedMessage>\n                </div>\n              </div>\n              <AvatarUpload\n                avatar={this.state.avatar}\n                readOnly={!this.state.owner}\n                uid={this.props.topic}\n                title={this.state.fullName}\n                onImageChanged={this.handleImageChanged}\n                onError={this.props.onError} />\n            </div>\n            <div className=\"hr\" />\n            <div className=\"panel-form-column\">\n              <div className=\"panel-form-row\">\n                <label>\n                  <FormattedMessage id=\"label_muting_topic\" defaultMessage=\"Muted:\"\n                    description=\"Label for Muting/unmuting the topic\" />\n                </label>\n                <CheckBox name=\"P\" checked={this.state.muted}\n                  onChange={this.handleMuted} />\n              </div>\n              <FormattedMessage id=\"action_more\" defaultMessage=\"More\"\n                description=\"Action for showing more content\">{\n                (more) => <MoreButton\n                  title={more}\n                  open={this.state.moreInfoExpanded}\n                  onToggle={this.handleMoreInfo} />\n              }</FormattedMessage>\n              {this.state.moreInfoExpanded ?\n                <div className=\"panel-form-column\">\n                  <div className=\"panel-form-row\">\n                    <label><FormattedMessage id=\"label_user_id\" /></label>\n                    <tt>{this.state.address}</tt>\n                  </div>\n                  {this.state.groupTopic ?\n                    <div className=\"panel-form-row\">\n                      <label>\n                        <FormattedMessage id=\"label_your_permissions\" defaultMessage=\"Your permissions:\"\n                          description=\"Label for current user permissions\" />\n                      </label>\n                      <tt className=\"clickable\"\n                        onClick={this.handleLaunchPermissionsEditor.bind(this, 'want')}>\n                        {this.state.access}\n                      </tt>\n                    </div>\n                    :\n                    <div>\n                      <div>\n                        <label className=\"small\">\n                          <FormattedMessage id=\"label_permissions\" defaultMessage=\"Permissions:\"\n                            description=\"Section title\" />\n                        </label>\n                      </div>\n                      <div className=\"quoted\">\n                        <div>\n                          <FormattedMessage id=\"label_you\" defaultMessage=\"You:\"\n                            description=\"Label for the current user\" /> &nbsp;<tt className=\"clickable\"\n                          onClick={this.handleLaunchPermissionsEditor.bind(this, 'want')}>\n                          {this.state.access}\n                        </tt></div>\n                        <div>{this.state.fullName ? this.state.fullName : formatMessage(messages.other_user)}:\n                          &nbsp;<tt className=\"clickable\" onClick={this.handleLaunchPermissionsEditor.bind(this, 'given')}>\n                          {this.state.modeGiven2}\n                          </tt>\n                        </div>\n                      </div>\n                    </div>\n                  }\n                  {this.state.sharer && (this.state.auth || this.state.anon) ?\n                  <div>\n                    <div>\n                      <label className=\"small\">\n                        <FormattedMessage id=\"label_default_access\" defaultMessage=\"Default access mode:\"\n                          description=\"Section title\" />\n                      </label>\n                    </div>\n                    <div className=\"quoted\">\n                      <div>Auth: {this.state.admin ?\n                        <tt className=\"clickable\"\n                          onClick={this.handleLaunchPermissionsEditor.bind(this, 'auth')}>\n                          {this.state.auth}\n                        </tt>\n                        :\n                        <tt>{this.state.auth}</tt>\n                      }\n                      </div>\n                      <div>Anon: {this.state.admin ?\n                        <tt className=\"clickable\"\n                          onClick={this.handleLaunchPermissionsEditor.bind(this, 'anon')}>\n                          {this.state.anon}\n                        </tt>\n                        :\n                        <tt>{this.state.anon}</tt>\n                      }\n                      </div>\n                    </div>\n                  </div>\n                  :\n                  null\n                }\n                </div>\n              :\n              null\n              }\n            </div>\n            <div className=\"hr\" />\n            {this.state.owner ?\n              <>\n                <FormattedMessage id=\"title_tag_manager\">{\n                  (tags) => <TagManager\n                    title={tags}\n                    tags={this.state.tags}\n                    activated={false}\n                    onSubmit={this.handleTagsUpdated} />\n                }</FormattedMessage>\n                <div className=\"hr\" />\n              </>\n              :\n              null\n            }\n            <div className=\"panel-form-column\">\n              <a href=\"#\" className=\"flat-button\" onClick={this.handleDeleteMessages}>\n                <i className=\"material-icons\">delete_outline</i> &nbsp;{\n                  formatMessage(this.state.deleter ? messages.delete_messages : messages.clear_messages)\n                }\n              </a>\n              <a href=\"#\" className=\"red flat-button\" onClick={this.handleLeave}>\n                <i className=\"material-icons\">exit_to_app</i> &nbsp;{formatMessage(messages.leave_chat)}\n              </a>\n              {!this.state.groupTopic ?\n                <a href=\"#\" className=\"red flat-button\" onClick={this.handleBlock}>\n                  <i className=\"material-icons\">block</i> &nbsp;{formatMessage(messages.block_contact)}\n                </a>\n                :\n                null\n              }\n              {!this.state.owner ?\n                <a href=\"#\" className=\"red flat-button\" onClick={this.handleReport}>\n                  <i className=\"material-icons\">report</i> &nbsp;{formatMessage(messages.report_chat)}\n                </a>\n                :\n                null\n              }\n            </div>\n            {this.state.groupTopic ?\n              <>\n                <div className=\"hr\" />\n                <div className=\"panel-form-column\">\n                  <div className=\"panel-form-row\">\n                    <label className=\"small\">\n                      <FormattedMessage id=\"label_group_members\" defaultMessage=\"Group members:\"\n                        description=\"Section title or label\" />\n                    </label>\n                  </div>\n                  <div className=\"panel-form-row\">\n                    {this.state.sharer ?\n                      <a href=\"#\" className=\"flat-button\" onClick={this.handleShowAddMembers}>\n                        <i className=\"material-icons\">person_add</i> &nbsp;<FormattedMessage id=\"button_add_members\"\n                          defaultMessage=\"Add members\" description=\"Flat button [Add members] (to topic)\" />\n                      </a>\n                      : null}\n                  </div>\n                  <FormattedMessage id=\"group_has_no_members\" defaultMessage=\"No members\"\n                    description=\"Shown in place of group members\">{\n                    (no_members) => <ContactList\n                      contacts={this.state.contactList}\n                      myUserId={this.props.myUserId}\n                      emptyListMessage={no_members}\n                      topicSelected={this.state.selectedContact}\n                      showOnline={false}\n                      showUnread={false}\n                      showMode={true}\n                      noScroll={true}\n                      onTopicSelected={this.handleMemberSelected}\n                      showContextMenu={this.state.admin ? this.handleContextMenu : false}\n                    />\n                  }</FormattedMessage>\n                </div>\n              </>\n              :\n              null\n            }\n          </div>\n        }\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(InfoView);\n", "// File uload/download progress indicator with a cancel inside.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nexport default class FileProgress extends React.PureComponent {\n  render() {\n    return (\n      <div className=\"uploader\">\n        <div><span style={{width: (this.props.progress * 100) + \"%\"}}></span></div>\n        {this.props.progress < 0.999 ?\n          <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onCancel();}}>\n            <i className=\"material-icons\">close</i> <FormattedMessage id=\"action_cancel\"\n              defaultMessage=\"cancel\" description=\"Call to action [cancel]\" />\n          </a>\n          :\n          <FormattedMessage id=\"upload_finishing\" defaultMessage=\"finishing...\"\n            description=\"Notification that upload is finishing\" />\n        }\n      </div>\n    );\n  }\n}\n", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport FileProgress from './file-progress.jsx';\nimport { bytesToHumanSize } from '../lib/strformat.js';\nimport { isUrlRelative, sanitizeUrl } from '../lib/utils.js';\n\nexport default class Attachment extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      downloader: null,\n      progress: 0\n    };\n\n    this.downloadFile = this.downloadFile.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n  }\n\n  downloadFile(url, filename, mimetype) {\n    var downloader = this.props.tinode.getLargeFileHelper();\n    this.setState({downloader: downloader});\n    downloader.download(url, filename, mimetype, (loaded) => {\n      this.setState({progress: loaded / this.props.size});\n    }).then(() => {\n      this.setState({downloader: null, progress: 0});\n    }).catch((err) => {\n      if (err) {\n        this.props.onError(\"Error downloading file: \" + err.message, 'err');\n      }\n      this.setState({downloader: null, progress: 0});\n    });\n  }\n\n  handleCancel() {\n    if (this.props.uploader) {\n      this.props.onCancelUpload();\n    } else if (this.state.downloader) {\n      this.state.downloader.cancel();\n    }\n  }\n\n  render() {\n    let filename = this.props.filename || 'file_attachment';\n    if (filename.length > 36) {\n      filename = filename.substr(0, 16) + '...' + filename.substr(-16);\n    }\n    let size = this.props.size > 0 ?\n      <span className=\"small gray\">({bytesToHumanSize(this.props.size)})</span> :\n      null;\n\n    // Detect if the download URL is relative or absolute.\n    // If the URL is relative use LargeFileHelper to attach authentication\n    // credentials to the request.\n    let url, helperFunc;\n    if (!this.props.uploader && !this.state.downloader && isUrlRelative(this.props.downloadUrl)) {\n      // Relative URL. Use download helper.\n      url = '#';\n      helperFunc = (e) => {\n        e.preventDefault();\n        this.downloadFile(this.props.downloadUrl, this.props.filename, this.props.mimetype);\n      };\n    } else {\n      url = sanitizeUrl(this.props.downloadUrl) || 'about:blank';\n      helperFunc = null;\n    }\n    return (\n      <div className=\"attachment\">\n        <div><i className=\"material-icons big gray\">insert_drive_file</i></div>\n        <div className=\"flex-column\">\n          <div>{filename} {size}</div>\n          {this.props.uploader || this.state.downloader ?\n            <FileProgress progress={this.props.uploader ? this.props.progress : this.state.progress}\n              onCancel={this.handleCancel} />\n            :\n            <div><a href={url} download={this.props.filename} onClick={helperFunc} >\n              <i className=\"material-icons\">file_download</i> <FormattedMessage id=\"save_attachment\"\n                defaultMessage=\"save\" description=\"Call to save an attachment\" />\n            </a></div>\n          }\n        </div>\n      </div>\n    );\n  }\n};\n", "// Received/read indicator.\nimport React from 'react';\nimport { defineMessages, injectIntl } from 'react-intl';\n\nimport Tinode from 'tinode-sdk';\n\nimport { shortDateFormat } from '../lib/strformat.js';\n\nconst messages = defineMessages({\n  'sending': {\n    'id': 'message_sending',\n    'defaultMessage': 'sending...',\n    'description': 'Message being sent, in place of time stamp'\n  },\n  'failed': {\n    'id': 'message_sending_failed',\n    'defaultMessage': 'failed',\n    'description': 'Failed to send message, in place of time stamp'\n  }\n});\n\nclass ReceivedMarker extends React.PureComponent {\n  render() {\n    const {formatMessage} = this.props.intl;\n    let timestamp;\n    if (this.props.received <= Tinode.MESSAGE_STATUS_SENDING) {\n      timestamp = formatMessage(messages.sending);\n    } else if (this.props.received == Tinode.MESSAGE_STATUS_FAILED) {\n      timestamp = formatMessage(messages.failed);\n    } else {\n      timestamp = shortDateFormat(this.props.timestamp, this.props.intl.locale);\n    }\n\n    let marker = null;\n    if (this.props.received <= Tinode.MESSAGE_STATUS_SENDING) {\n      marker = (<i className=\"material-icons small\">access_time</i>); // watch face\n    } else if (this.props.received == Tinode.MESSAGE_STATUS_FAILED) {\n      marker = (<i className=\"material-icons small amber\">warning</i>); // yellow icon /!\\\n    } else if (this.props.received == Tinode.MESSAGE_STATUS_SENT) {\n      marker = (<i className=\"material-icons small\">done</i>); // checkmark\n    } else if (this.props.received == Tinode.MESSAGE_STATUS_RECEIVED) {\n      marker = (<i className=\"material-icons small\">done_all</i>); // double checkmark\n    } else if (this.props.received == Tinode.MESSAGE_STATUS_READ) {\n      marker = (<i className=\"material-icons small blue\">done_all</i>); // blue double checkmark\n    }\n\n    return (\n      <span className=\"timestamp\">\n        {timestamp}{'\\u00a0'}{marker}\n      </span>\n    );\n  }\n};\n\nexport default injectIntl(ReceivedMarker);\n", "// Single message, sent or received.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { Drafty } from 'tinode-sdk'\n\nimport Attachment from './attachment.jsx';\nimport LetterTile from './letter-tile.jsx';\nimport ReceivedMarker from './received-marker.jsx'\nimport { sanitizeImageUrl, sanitizeUrl } from '../lib/utils.js';\n\nexport default class ChatMessage extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      progress: 0\n    };\n\n    if (props.uploader) {\n      props.uploader.onProgress = this.handleProgress.bind(this);\n    }\n\n    this.handleImagePreview = this.handleImagePreview.bind(this);\n    this.handleFormButtonClick = this.handleFormButtonClick.bind(this);\n    this.handleContextClick = this.handleContextClick.bind(this);\n    this.handleCancelUpload = this.handleCancelUpload.bind(this);\n  }\n\n  handleImagePreview(e) {\n    e.preventDefault();\n    this.props.onImagePreview({\n      url: e.target.src,\n      filename: e.target.title,\n      width: e.target.dataset.width,\n      height: e.target.dataset.height,\n      size: e.target.dataset.size,\n      type: e.target.dataset.mime\n    });\n  }\n\n  handleFormButtonClick(e) {\n    e.preventDefault();\n    const data = {\n      seq: this.props.seq\n    };\n    data.resp = {};\n    if (e.target.dataset.name) {\n      data.resp[e.target.dataset.name] = e.target.dataset.val ? e.target.dataset.val :\n        e.target.dataset.val === undefined ? 1 : '' + e.target.dataset.val;\n    }\n    if (e.target.dataset.act == 'url') {\n      data.ref = sanitizeUrl(e.target.dataset.ref) || 'about:blank';\n    }\n    const text = e.target.dataset.title || 'unknown';\n    this.props.onFormResponse(e.target.dataset.act, text, data);\n  }\n\n  handleContextClick(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    const menuItems = this.props.received == Tinode.MESSAGE_STATUS_FAILED ? ['menu_item_send_retry'] : [];\n    this.props.showContextMenu({ seq: this.props.seq, content: this.props.content,\n                                 y: e.pageY, x: e.pageX }, menuItems);\n  }\n\n  handleProgress(ratio) {\n    this.setState({progress: ratio});\n  }\n\n  handleCancelUpload() {\n    this.props.uploader.cancel();\n  }\n\n  render() {\n    const sideClass = this.props.deleted ? 'center' :\n      (this.props.sequence + ' ' + (this.props.response ? 'left' : 'right'));\n    const bubbleClass = (this.props.sequence == 'single' || this.props.sequence == 'last') ? 'bubble tip' : 'bubble';\n    const avatar = this.props.deleted ? null : (this.props.userAvatar || true);\n    const fullDisplay = (this.props.userFrom && this.props.response &&\n      (this.props.sequence == 'single' || this.props.sequence == 'last'));\n\n    let content = this.props.content;\n    const attachments = [];\n    if (this.props.mimeType == Drafty.getContentType() && Drafty.isValid(content)) {\n      Drafty.attachments(content, function(att, i) {\n        if (att.mime == 'application/json') {\n          // Don't show json objects as attachments.\n          // They are not meant for users.\n          return;\n        }\n        attachments.push(<Attachment\n          tinode={this.props.tinode}\n          downloadUrl={Drafty.getDownloadUrl(att)}\n          filename={att.name} uploader={Drafty.isUploading(att)}\n          mimetype={att.mime} size={Drafty.getEntitySize(att)}\n          progress={this.state.progress}\n          onCancelUpload={this.handleCancelUpload}\n          onError={this.props.onError}\n          key={i} />);\n      }, this);\n      content = React.createElement(React.Fragment, null, Drafty.format(content, draftyFormatter, this));\n    } else if (this.props.deleted) {\n      // Message represents a range of deleted messages.\n      content = <><i className=\"material-icons gray\">block</i> <i className=\"gray\">\n        <FormattedMessage id=\"deleted_content\"\n          defaultMessage=\"content deleted\" description=\"Shown when messages are deleted\" />\n      </i></>\n    } else if (typeof content != 'string') {\n      content = <>\n          <i className=\"material-icons gray\">error_outline</i> <i className=\"gray\">\n            <FormattedMessage id=\"invalid_content\"\n              defaultMessage=\"invalid content\" description=\"Shown when message is unreadable\" /></i>\n        </>\n    }\n\n    return (\n      <li className={sideClass}>\n        {this.props.userFrom && this.props.response ?\n          <div className=\"avatar-box\">\n            {fullDisplay ?\n              <LetterTile\n                topic={this.props.userFrom}\n                title={this.props.userName}\n                avatar={avatar} /> :\n              null}\n          </div> :\n          null}\n        <div>\n          <div className={bubbleClass}>\n            <div className=\"message-content\">\n              {content}\n              {attachments}\n              {this.props.timestamp ?\n                <ReceivedMarker\n                  timestamp={this.props.timestamp}\n                  received={this.props.received} />\n                : null}\n            </div>\n            {this.props.deleted ?\n              null :\n              <span className=\"menuTrigger\">\n                <a href=\"#\" onClick={this.handleContextClick}>\n                  <i className=\"material-icons\">expand_more</i>\n                </a>\n              </span>\n            }\n          </div>\n          {fullDisplay ?\n            <div className=\"author\">\n              <FormattedMessage id=\"user_not_found\" defaultMessage=\"Not found\"\n                description=\"In place of a user's full name when the user is not found.\">{\n                    (notFound) => {return this.props.userName || <i>{notFound}</i>}\n              }</FormattedMessage>\n            </div>\n            : null\n          }\n        </div>\n      </li>\n    );\n  }\n};\n\n// Convert Drafty object to a tree of React elements.\nimport { BROKEN_IMAGE_SIZE, REM_SIZE } from '../config.js';\nimport { fitImageSize } from '../lib/blob-helpers.js';\n\nfunction draftyFormatter(style, data, values, key) {\n  let el = Drafty.tagName(style);\n  if (el) {\n    let attr = Drafty.attrValue(style, data) || {};\n    attr.key = key;\n    switch (style) {\n      case 'IM':\n        // Additional processing for images\n        if (data) {\n          attr.className = 'inline-image';\n          let dim = fitImageSize(data.width, data.height,\n            Math.min(this.props.viewportWidth - REM_SIZE * 4, REM_SIZE * 36), REM_SIZE * 24, false);\n          dim = dim || {dstWidth: BROKEN_IMAGE_SIZE, dstHeight: BROKEN_IMAGE_SIZE};\n          attr.style = { width: dim.dstWidth + 'px', height: dim.dstHeight + 'px' };\n          attr.src = sanitizeImageUrl(attr.src);\n          if (attr.src) {\n            attr.onClick = this.handleImagePreview;\n            attr.className += ' image-clickable';\n          } else {\n            attr.src = 'img/broken_image.png';\n          }\n        }\n        break;\n      case 'BN':\n        // Button\n        attr.onClick = this.handleFormButtonClick;\n        let inner = React.Children.map(values, (child) => {\n          return typeof child == 'string' ? child : undefined;\n        });\n        if (!inner || inner.length == 0) {\n          inner = [attr.name]\n        }\n        // Get text which will be sent back when the button is clicked.\n        attr['data-title'] = inner.join('');\n        break;\n      case 'FM':\n        // Form\n        attr.className = 'bot-form';\n        break;\n      case 'FE':\n        // Form element formatting is dependent on element content.\n        break;\n    }\n    return React.createElement(el, attr, values);\n  } else {\n    return values;\n  }\n};\n", "// Send message form.\nimport React from 'react';\nimport { defineMessages, injectIntl } from 'react-intl';\nimport { Drafty } from 'tinode-sdk';\n\nimport { KEYPRESS_DELAY } from '../config.js';\nimport { filePasted } from '../lib/blob-helpers.js';\n\nconst messages = defineMessages({\n  'messaging_disabled': {\n    id: 'messaging_disabled_prompt',\n    defaultMessage: 'Messaging disabled',\n    description: 'Prompt in SendMessage in read-only topic'\n  },\n  'type_new_message': {\n    id: 'new_message_prompt',\n    defaultMessage: 'New message',\n    description: 'Prompt in send message field'\n  },\n  'add_image_caption': {\n    id: 'image_caption_prompt',\n    defaultMessage: 'Image caption',\n    description: 'Prompt in SendMessage for attached image'\n  },\n  'file_attachment_too_large': {\n    id: 'file_attachment_too_large',\n    defaultMessage: 'The file size {size} exceeds the {limit} limit.',\n    description: 'Error message when attachment is too large'\n  },\n  'cannot_initiate_upload': {\n    id: 'cannot_initiate_file_upload',\n    defaultMessage: 'Cannot initiate file upload.',\n    description: 'Generic error messagewhen attachment fails'\n  },\n});\n\nclass SendMessage extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      message: '',\n      // Make initial keypress time as if it happened 5001 milliseconds in the past.\n      keypressTimestamp: new Date().getTime() - KEYPRESS_DELAY - 1\n    };\n\n    this.handlePasteEvent = this.handlePasteEvent.bind(this);\n    this.handleAttachImage = this.handleAttachImage.bind(this);\n    this.handleAttachFile = this.handleAttachFile.bind(this);\n    this.handleSend = this.handleSend.bind(this);\n    this.handleKeyPress = this.handleKeyPress.bind(this);\n    this.handleMessageTyping = this.handleMessageTyping.bind(this);\n  }\n\n  componentDidMount() {\n    if (this.messageEditArea) {\n      this.messageEditArea.addEventListener('paste', this.handlePasteEvent, false);\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.messageEditArea) {\n      this.messageEditArea.removeEventListener('paste', this.handlePasteEvent, false);\n    }\n  }\n\n  componentDidUpdate() {\n    if (this.messageEditArea) {\n      this.messageEditArea.focus();\n    }\n  }\n\n  handlePasteEvent(e) {\n    if (this.props.disabled) {\n      return;\n    }\n    // FIXME: handle large files too.\n    if (filePasted(e,\n      (bits, mime, width, height, fname) => {\n        this.props.onAttachImage(mime, bits, width, height, fname);\n      },\n      (mime, bits, fname) => {\n        this.props.onAttachFile(mime, bits, fname);\n      },\n      this.props.onError)) {\n\n      // If a file was pasted, don't paste base64 data into input field.\n      e.preventDefault();\n    }\n  }\n\n  handleAttachImage(e) {\n    if (e.target.files && e.target.files.length > 0) {\n      this.props.onAttachImage(e.target.files[0]);\n    }\n    // Clear the value so the same file can be uploaded again.\n    e.target.value = '';\n  }\n\n  handleAttachFile(e) {\n    const {formatMessage} = this.props.intl;\n    if (e.target.files && e.target.files.length > 0) {\n      this.props.onAttachFile(e.target.files[0]);\n    }\n    // Clear the value so the same file can be uploaded again.\n    e.target.value = '';\n  }\n\n  handleSend(e) {\n    e.preventDefault();\n    const message = this.state.message.trim();\n    if (message || this.props.acceptBlank || this.props.noInput) {\n      this.props.onSendMessage(message);\n      this.setState({message: ''});\n    }\n  }\n\n  /* Send on Enter key */\n  handleKeyPress(e) {\n    // Remove this if you don't want Enter to trigger send\n    if (e.key === 'Enter') {\n      // Have Shift-Enter insert a line break instead\n      if (!e.shiftKey) {\n        e.preventDefault();\n        e.stopPropagation();\n\n        this.handleSend(e);\n      }\n    }\n  }\n\n  handleMessageTyping(e) {\n    const newState = {message: e.target.value};\n    if (this.props.onKeyPress) {\n      const now = new Date().getTime();\n      if (now - this.state.keypressTimestamp > KEYPRESS_DELAY) {\n        this.props.onKeyPress();\n        newState.keypressTimestamp = now;\n      }\n    }\n    this.setState(newState);\n  }\n\n  render() {\n    const {formatMessage} = this.props.intl;\n    const prompt = this.props.disabled ?\n      formatMessage(messages.messaging_disabled) :\n      (this.props.messagePrompt ?\n        formatMessage(messages[this.props.messagePrompt]) :\n        formatMessage(messages.type_new_message));\n    return (\n      <div id=\"send-message-panel\">\n        {!this.props.disabled ?\n          <>\n            {this.props.onAttachFile ?\n              <>\n                <a href=\"#\" onClick={(e) => {e.preventDefault(); this.attachImage.click();}} title=\"Add image\">\n                  <i className=\"material-icons secondary\">photo</i>\n                </a>\n                <a href=\"#\" onClick={(e) => {e.preventDefault(); this.attachFile.click();}} title=\"Attach file\">\n                  <i className=\"material-icons secondary\">attach_file</i>\n                </a>\n              </>\n              :\n              null}\n            {this.props.noInput ?\n              <div className=\"hr thin\" /> :\n              <textarea id=\"sendMessage\" placeholder={prompt}\n                value={this.state.message} onChange={this.handleMessageTyping}\n                onKeyPress={this.handleKeyPress}\n                ref={(ref) => {this.messageEditArea = ref;}}\n                autoFocus />}\n            <a href=\"#\" onClick={this.handleSend} title=\"Send\">\n              <i className=\"material-icons\">send</i>\n            </a>\n            <input type=\"file\" ref={(ref) => {this.attachFile = ref;}}\n              onChange={this.handleAttachFile} style={{display: 'none'}} />\n            <input type=\"file\" ref={(ref) => {this.attachImage = ref;}} accept=\"image/*\"\n              onChange={this.handleAttachImage} style={{display: 'none'}} />\n          </>\n          :\n          <div id=\"writing-disabled\">{prompt}</div>\n        }\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(SendMessage);\n", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport SendMessage from '../widgets/send-message.jsx';\n\nimport { bytesToHumanSize } from '../lib/strformat.js';\n\n// Get material icon name from mime type.\nfunction iconFromMime(mime) {\n  // If more icons become available in material icons, add them to this mime-to-icon mapping.\n  const mimeToIcon = {default: 'insert_drive_file', image: 'image', text: 'description', video: 'theaters'};\n\n  return mimeToIcon[mime] || mimeToIcon[(mime || '').split('/')[0]] || mimeToIcon['default'];\n}\n\nexport default class DocPreview extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.handleSendDoc = this.handleSendDoc.bind(this);\n  }\n\n  handleSendDoc(caption) {\n    this.props.onClose();\n    this.props.onSendMessage(this.props.content.file);\n  }\n\n  render() {\n    if (!this.props.content) {\n      return null;\n    }\n    return (\n      <div id=\"image-preview\">\n        <div id=\"image-preview-caption-panel\">\n          <span>{this.props.content.filename}</span>\n          <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onClose();}}><i className=\"material-icons gray\">close</i></a>\n        </div>\n        <div id=\"image-preview-container\">\n          <div className=\"flex-column narrow\">\n            <i className=\"material-icons gray\">{iconFromMime(this.props.content.type)}</i>\n            <div><b><FormattedMessage id=\"label_content_type\" /></b> {this.props.content.type || 'application/octet-stream'}</div>\n            <div><b><FormattedMessage id=\"label_size\" /></b> {bytesToHumanSize(this.props.content.size)}</div>\n          </div>\n        </div>\n        <SendMessage\n          noInput={true}\n          onSendMessage={this.handleSendDoc}\n          onError={this.props.onError} />\n      </div>\n    );\n  }\n};\n", "// GroupSubs: a list of group subscribers currently online.\n// Show in the topic title bar\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport { MAX_ONLINE_IN_TOPIC } from '../config.js'\n\nimport LetterTile from './letter-tile.jsx';\nimport { makeImageUrl } from '../lib/blob-helpers.js';\n\nexport default class GroupSubs extends React.Component {\n  constructor(props) {\n    super(props);\n  }\n\n  render() {\n    const usersOnline = [];\n    const totalCount = (this.props.subscribers || []).length;\n    const countToShow = Math.min(MAX_ONLINE_IN_TOPIC, totalCount);\n\n    (this.props.subscribers || []).some((sub) => {\n      usersOnline.push(\n        <div className=\"avatar-box\" key={sub.user}>\n          <LetterTile\n            topic={sub.user}\n            avatar={makeImageUrl(sub.public ? sub.public.photo : null) || true}\n            title={sub.public ? sub.public.fn : null} />\n        </div>\n      );\n      return usersOnline.length == countToShow;\n    });\n\n    return (\n      <div id=\"topic-users\">{usersOnline} {totalCount > countToShow ?\n        <span>\n          <FormattedMessage id=\"more_online_members\" defaultMessage=\"+{overflow} more\"\n              description=\"Shown in MessagesView title bar when the number of online subscribers exceeds MAX_ONLINE_IN_TOPIC\"\n              values={{ overflow: (totalCount - countToShow) }} />\n        </span> : null}\n      </div>\n    );\n  }\n};\n", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport SendMessage from '../widgets/send-message.jsx';\n\nimport { REM_SIZE } from '../config.js';\nimport { fitImageSize } from '../lib/blob-helpers.js';\nimport { bytesToHumanSize } from '../lib/strformat.js';\n\nexport default class ImagePreview extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      width: 0,\n      height: 0\n    };\n\n    this.handleSendImage = this.handleSendImage.bind(this);\n  }\n\n  assignWidth(node) {\n    if (node && !this.state.width) {\n      const bounds = node.getBoundingClientRect();\n      this.setState({\n        width: bounds.width | 0,\n        height: bounds.height | 0\n      });\n    }\n  }\n\n  handleSendImage(caption) {\n    this.props.onClose();\n    this.props.onSendMessage(caption, this.props.content.type, this.props.content.bits,\n      this.props.content.width, this.props.content.height, this.props.content.filename);\n  }\n\n  render() {\n    if (!this.props.content) {\n      return null;\n    }\n\n    const dim = fitImageSize(this.props.content.width, this.props.content.height,\n      this.state.width, this.state.height, false);\n    const size = dim ? { width: dim.dstWidth + 'px', height: dim.dstHeight + 'px' } :\n      ((this.props.content.width > this.props.content.height) ? {width: '100%'} : {height: '100%'});\n    size.maxWidth = '100%';\n    size.maxHeight = '100%';\n\n    let filename = this.props.content.filename;\n    // Averate font aspect ratio is ~0.5; File name takes 1/3 of the viewport width.\n    const maxlength = Math.max(((this.state.width / REM_SIZE / 1.5) | 0) - 2, 12);\n    if (filename.length > maxlength) {\n      filename = filename.slice(0, maxlength/2 - 1) + '…' + filename.slice(1 - maxlength/2);\n    }\n    const width = this.props.content.width || '-';\n    const height = this.props.content.height || '-';\n    return (\n      <div id=\"image-preview\">\n        <div id=\"image-preview-caption-panel\">\n          {!this.props.onSendMessage ?\n            <a href={this.props.content.url} download={this.props.content.filename}>\n              <i className=\"material-icons\">file_download</i> <FormattedMessage\n                id=\"download_action\" defaultMessage=\"download\" description=\"Call to action [download]\" />\n            </a>\n            :\n            <span>{this.props.content.filename}</span>\n          }\n          <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onClose();}}><i className=\"material-icons gray\">close</i></a>\n        </div>\n        <div id=\"image-preview-container\" ref={(node) => this.assignWidth(node)}>\n          <img src={this.props.content.url} style={size} />\n        </div>\n        {this.props.onSendMessage ?\n          <SendMessage\n            messagePrompt=\"add_image_caption\"\n            acceptBlank={true}\n            onSendMessage={this.handleSendImage}\n            onError={this.props.onError} />\n          :\n          <div id=\"image-preview-footer\">\n            <div>\n              <div><b><FormattedMessage id=\"label_file_name\" defaultMessage=\"File name:\"\n                description=\"Label for a file name\" /></b></div>\n              <div><span title={this.props.content.filename}>{filename ? filename : '-'}</span></div>\n            </div>\n            <div>\n              <div><b><FormattedMessage id=\"label_content_type\" defaultMessage=\"Content type:\"\n                description=\"Label for file content type (mime)\" /></b></div>\n              <div>{this.props.content.type}</div>\n            </div>\n            <div>\n              <div><b><FormattedMessage id=\"label_size\" defaultMessage=\"Size:\"\n                description=\"Label for file size\" /></b></div>\n              <div>{width} &times; {height} px; {bytesToHumanSize(this.props.content.size)}</div>\n            </div>\n          </div>}\n      </div>\n    );\n  }\n};\n", "// A single topic or user.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nexport default class Invitation extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.handleButtonAction = this.handleButtonAction.bind(this);\n  }\n\n  handleButtonAction(evt, data) {\n    evt.preventDefault();\n    this.props.onAction(data);\n  }\n\n  render() {\n    return (\n      <div className=\"accept-invite-panel\">\n        <div className=\"title\">\n          <FormattedMessage id=\"chat_invitation\"\n            defaultMessage=\"You are invited to start a new chat. What would you like to do?\"\n            description=\"New chat invitation message: [Accept] [Ignore] [Block].\" />\n        </div>\n        <div className=\"footer\">\n          <button className=\"blue\" onClick={event => { this.handleButtonAction(event, \"accept\"); }}>\n            <FormattedMessage id=\"chat_invitation_accept\"\n              defaultMessage=\"Accept\" description=\"Action [Accept] for chat invitation.\" />\n          </button>\n          <button className=\"white\" onClick={event => { this.handleButtonAction(event, \"delete\"); }}>\n            <FormattedMessage id=\"chat_invitation_ignore\"\n              defaultMessage=\"Ignore\" description=\"Action [Ignore] for chat invitation.\" />\n          </button>\n          <button className=\"white\" onClick={event => { this.handleButtonAction(event, \"block\"); }}>\n            <FormattedMessage id=\"chat_invitation_block\"\n              defaultMessage=\"Block\" description=\"Action [Block] for chat invitation.\" />\n          </button>\n        </div>\n      </div>\n    );\n  }\n};\n", "import React from 'react';\n\nexport default class LoadSpinner extends React.PureComponent {\n  render() {\n    return (this.props.show ?\n      <div className=\"load-spinner-box\"><div className=\"loader-spinner\"></div></div> : null);\n  }\n}\n", "/* This is just a static page to display when no conversation is selected. */\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport Tinode from 'tinode-sdk';\n\nimport { APP_NAME } from '../config.js';\n\nexport default class LogoView extends React.PureComponent {\n  render() {\n    const version = APP_NAME + ' (' + Tinode.getLibrary() + ')';\n    return (\n      <div id=\"dummy-view\">\n        <div>\n          <a href=\"https://github.com/tinode/chat/\">\n            <img id=\"logo\" alt=\"logo\" src=\"img/logo.svg\" />\n            <h2>Tinode Web</h2>\n          </a>\n          <p><FormattedMessage id=\"label_client\" defaultMessage=\"Client:\" /> {version}</p>\n          <p><FormattedMessage id=\"label_server\" defaultMessage=\"Server:\" /> {this.props.serverVersion} ({this.props.serverAddress})</p>\n        </div>\n      </div>\n    );\n  }\n};\n", "import React from 'react';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nimport Tinode from 'tinode-sdk';\nconst Drafty = Tinode.Drafty;\n\nimport ChatMessage from '../widgets/chat-message.jsx';\nimport DocPreview from '../widgets/doc-preview.jsx';\nimport ErrorPanel from '../widgets/error-panel.jsx';\nimport GroupSubs from '../widgets/group-subs.jsx';\nimport ImagePreview from '../widgets/image-preview.jsx';\nimport Invitation from '../widgets/invitation.jsx';\nimport LetterTile from '../widgets/letter-tile.jsx';\nimport LoadSpinner from '../widgets/load-spinner.jsx';\nimport LogoView from './logo-view.jsx';\nimport SendMessage from '../widgets/send-message.jsx';\n\nimport { DEFAULT_P2P_ACCESS_MODE, KEYPRESS_DELAY, MESSAGES_PAGE, MAX_EXTERN_ATTACHMENT_SIZE,\n  MAX_IMAGE_DIM, MAX_INBAND_ATTACHMENT_SIZE, READ_DELAY } from '../config.js';\nimport { SUPPORTED_IMAGE_FORMATS, filePasted, fileToBase64, imageFileToBase64,\n  imageFileScaledToBase64, makeImageUrl } from '../lib/blob-helpers.js';\nimport { bytesToHumanSize, shortDateFormat } from '../lib/strformat.js';\n\n// Run timer with this frequency (ms) for checking notification queue.\nconst NOTIFICATION_EXEC_INTERVAL = 300;\n\nconst messages = defineMessages({\n  online_now: {\n    id: 'online_now',\n    defaultMessage: 'online now',\n    description: 'Indicator that the user or topic is currently online',\n  },\n  last_seen: {\n    id: 'last_seen_timestamp',\n    defaultMessage: 'Last seen',\n    description: 'Label for the timestamp of when the user or topic was last online'\n  },\n  'not_found': {\n    id: 'title_not_found',\n    defaultMessage: 'Not found',\n    description: 'Title shown when topic is not found'\n  }\n});\n\n// Checks if the access permissions are granted but not yet accepted.\nfunction isUnconfirmed(acs) {\n  if (acs) {\n    const ex = acs.getExcessive() || '';\n    return acs.isJoiner('given') && (ex.includes('R') || ex.includes('W'));\n  }\n  return false;\n}\n\nfunction isPeerRestricted(acs) {\n  if (acs) {\n    const ms = acs.getMissing() || '';\n    return acs.isJoiner('want') && (ms.includes('R') || ms.includes('W'));\n  }\n  return false;\n}\n\nclass MessagesView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = MessagesView.getDerivedStateFromProps(props, {});\n\n    this.leave = this.leave.bind(this);\n    this.sendImageAttachment = this.sendImageAttachment.bind(this);\n    this.sendFileAttachment = this.sendFileAttachment.bind(this);\n    this.sendKeyPress = this.sendKeyPress.bind(this);\n    this.handleScrollReference = this.handleScrollReference.bind(this);\n    this.handleScrollEvent = this.handleScrollEvent.bind(this);\n    this.handleDescChange = this.handleDescChange.bind(this);\n    this.handleSubsUpdated = this.handleSubsUpdated.bind(this);\n    this.handleNewMessage = this.handleNewMessage.bind(this);\n    this.handleAllMessagesReceived = this.handleAllMessagesReceived.bind(this);\n    this.handleInfoReceipt = this.handleInfoReceipt.bind(this);\n    this.handleImagePostview = this.handleImagePostview.bind(this);\n    this.handleClosePreview = this.handleClosePreview.bind(this);\n    this.handleFormResponse = this.handleFormResponse.bind(this);\n    this.handleContextClick = this.handleContextClick.bind(this);\n    this.handleShowContextMenuMessage = this.handleShowContextMenuMessage.bind(this);\n    this.handleNewChatAcceptance = this.handleNewChatAcceptance.bind(this);\n    this.handleEnablePeer = this.handleEnablePeer.bind(this);\n    this.handleAttachFile = this.handleAttachFile.bind(this);\n    this.handleAttachImage = this.handleAttachImage.bind(this);\n    this.postReadNotification = this.postReadNotification.bind(this);\n    this.clearNotificationQueue = this.clearNotificationQueue.bind(this);\n\n    this.readNotificationQueue = [];\n    this.readNotificationTimer = null;\n  }\n\n  componentDidMount() {\n    // this.propsChange(this.props, this.state);\n    if (this.messagesScroller) {\n      this.messagesScroller.addEventListener('scroll', this.handleScrollEvent);\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.messagesScroller) {\n      this.messagesScroller.removeEventListener('scroll', this.handleScrollEvent);\n    }\n\n    // Flush all notifications.\n    this.clearNotificationQueue();\n  }\n\n  // Scroll last message into view on component update e.g. on message received\n  // or vertical shrinking.\n  componentDidUpdate(prevProps, prevState) {\n    if (this.messagesScroller) {\n      if (prevState.topic != this.state.topic || prevState.messages.length != this.state.messages.length) {\n        // New message\n        this.messagesScroller.scrollTop = this.messagesScroller.scrollHeight - this.state.scrollPosition;\n      } else if (prevProps.viewportHeight > this.props.viewportHeight) {\n        // Componet changed height.\n        this.messagesScroller.scrollTop += prevProps.viewportHeight - this.props.viewportHeight;\n      }\n    }\n\n    const topic = this.props.tinode.getTopic(this.state.topic);\n    if (this.state.topic != prevState.topic) {\n      if (prevState.topic && !Tinode.isNewGroupTopicName(prevState.topic)) {\n        this.leave(prevState.topic);\n      }\n\n      if (topic) {\n        topic.onData = this.handleNewMessage;\n        topic.onAllMessagesReceived = this.handleAllMessagesReceived;\n        topic.onInfo = this.handleInfoReceipt;\n        topic.onMetaDesc = this.handleDescChange;\n        topic.onSubsUpdated = this.handleSubsUpdated;\n        topic.onPres = this.handleSubsUpdated;\n      }\n    }\n\n    if (!this.props.applicationVisible) {\n      // If application is not visible, flush all unsent 'read' notifications.\n      this.clearNotificationQueue();\n    } else {\n      // Otherwise assume there are unread messages.\n      this.postReadNotification(0);\n    }\n\n    if (topic && !topic.isSubscribed() && this.props.ready &&\n        ((this.state.topic != prevState.topic) || !prevProps.ready)) {\n      // Is this a new topic?\n      const newTopic = (this.props.newTopicParams && this.props.newTopicParams._topicName == this.props.topic);\n\n      // Don't request the tags. They are useless unless the user\n      // is the owner and is editing the topic.\n      let getQuery = topic.startMetaQuery().withLaterDesc().withLaterSub();\n      if (this.state.isReader || newTopic) {\n        // If reading is either permitted or we don't know because it's a new topic. Ask for messages.\n        getQuery = getQuery.withLaterData(MESSAGES_PAGE);\n        if (this.state.isReader) {\n          getQuery = getQuery.withLaterDel();\n        }\n        // And show \"loading\" spinner.\n        this.setState({ fetchingMessages: true });\n      }\n      const setQuery = newTopic ? this.props.newTopicParams : undefined;\n      topic.subscribe(getQuery.build(), setQuery)\n        .then((ctrl) => {\n          if (this.state.topic != ctrl.topic) {\n            this.setState({topic: ctrl.topic});\n          }\n          this.props.onNewTopicCreated(this.props.topic, ctrl.topic);\n          // If there are unsent messages, try sending them now.\n          topic.queuedMessages((pub) => {\n            if (!pub._sending && topic.isSubscribed()) {\n              topic.publishMessage(pub);\n            }\n          });\n        })\n        .catch((err) => {\n          console.log(\"Failed subscription to\", this.state.topic);\n          this.props.onError(err.message, 'err');\n          const blankState = MessagesView.getDerivedStateFromProps({}, {});\n          blankState.title = this.props.intl.formatMessage(messages.not_found);\n          this.setState(blankState);\n        });\n    }\n  }\n\n  static getDerivedStateFromProps(nextProps, prevState) {\n    let nextState = {};\n    if (!nextProps.topic) {\n      // Default state: no topic.\n      nextState = {\n        messages: [],\n        onlineSubs: [],\n        topic: null,\n        title: '',\n        avatar: null,\n        docPreview: null,\n        imagePreview: null,\n        imagePostview: null,\n        typingIndicator: false,\n        scrollPosition: 0,\n        fetchingMessages: false,\n        peerMessagingDisabled: false\n      };\n    } else if (nextProps.topic != prevState.topic) {\n      const topic = nextProps.tinode.getTopic(nextProps.topic);\n      nextState = {\n        topic: nextProps.topic,\n        docPreview: null,\n        imagePreview: null,\n        imagePostview: null,\n        typingIndicator: false,\n        scrollPosition: 0,\n        fetchingMessages: false\n      };\n\n      if (topic) {\n        // Topic exists.\n        const msgs = [];\n        const subs = [];\n\n        if (nextProps.connected) {\n          topic.subscribers((sub) => {\n            if (sub.online && sub.user != nextProps.myUserId) {\n              subs.push(sub);\n            }\n          });\n        }\n\n        topic.messages(function(msg) {\n          if (!msg.deleted) {\n            msgs.push(msg);\n          }\n        });\n\n        Object.assign(nextState, {\n          messages: msgs,\n          onlineSubs: subs\n        });\n\n        if (topic.public) {\n          Object.assign(nextState, {\n            title: topic.public.fn,\n            avatar: makeImageUrl(topic.public.photo)\n          });\n        } else {\n          Object.assign(nextState, {\n            title: '',\n            avatar: null\n          });\n        }\n\n        const peer = topic.p2pPeerDesc();\n        if (peer) {\n          Object.assign(nextState, {\n            peerMessagingDisabled: isPeerRestricted(peer.acs)\n          });\n        } else if (prevState.peerMessagingDisabled) {\n          Object.assign(nextState, {\n            peerMessagingDisabled: false\n          });\n        }\n      } else {\n        // Invalid topic.\n        Object.assign(nextState, {\n          messages: [],\n          onlineSubs: [],\n          title: '',\n          avatar: null,\n          peerMessagingDisabled: false\n        });\n      }\n    }\n\n    if (nextProps.acs) {\n      if (nextProps.acs.isWriter() != prevState.isWriter) {\n        nextState.isWriter = !prevState.isWriter;\n      }\n      if (nextProps.acs.isReader() != prevState.isReader) {\n        nextState.isReader = !prevState.isReader;\n      }\n      if (!nextProps.acs.isReader('given') != prevState.readingBlocked) {\n        nextState.readingBlocked = !prevState.readingBlocked;\n      }\n    } else {\n      if (prevState.isWriter) {\n        nextState.isWriter = false;\n      }\n      if (prevState.isReader) {\n        nextState.isReader = false;\n      }\n      if (!prevState.readingBlocked) {\n        prevState.readingBlocked = true;\n      }\n    }\n\n    if (isUnconfirmed(nextProps.acs) == !prevState.unconformed) {\n      nextState.unconfirmed = !prevState.unconformed;\n    }\n\n    // Clear subscribers online when there is no connection.\n    if (!nextProps.connected && prevState.onlineSubs && prevState.onlineSubs.length > 0) {\n      nextState.onlineSubs = [];\n    }\n\n    return nextState;\n  }\n\n  leave(oldTopicName) {\n    if (!oldTopicName) {\n      return;\n    }\n    let oldTopic = this.props.tinode.getTopic(oldTopicName);\n    if (oldTopic && oldTopic.isSubscribed()) {\n      oldTopic.leave(false)\n        .catch(() => { /* do nothing here */ })\n        .finally(() => {\n          // We don't care if the request succeeded or failed.\n          // The topic is dead regardless.\n          this.setState({fetchingMessages: false});\n          oldTopic.onData = undefined;\n          oldTopic.onAllMessagesReceived = undefined;\n          oldTopic.onInfo = undefined;\n          oldTopic.onMetaDesc = undefined;\n          oldTopic.onSubsUpdated = undefined;\n          oldTopic.onPres = undefined;\n        });\n    }\n  }\n\n  handleScrollReference(node) {\n    if (node) {\n      node.addEventListener('scroll', this.handleScrollEvent);\n      this.messagesScroller = node;\n      this.messagesScroller.scrollTop = this.messagesScroller.scrollHeight - this.state.scrollPosition;\n    }\n  }\n\n  // Get older messages\n  handleScrollEvent(event) {\n    this.setState({scrollPosition: event.target.scrollHeight - event.target.scrollTop});\n    if (event.target.scrollTop <= 0) {\n      this.setState((prevState, props) => {\n        const newState = {};\n        if (!prevState.fetchingMessages) {\n          const topic = this.props.tinode.getTopic(this.state.topic);\n          if (topic && topic.isSubscribed() && topic.msgHasMoreMessages()) {\n            newState.fetchingMessages = true;\n            topic.getMessagesPage(MESSAGES_PAGE).catch((err) => {\n              this.setState({fetchingMessages: false});\n              this.props.onError(err.message, 'err');\n            });\n          }\n        }\n        return newState;\n      });\n    }\n  }\n\n  handleDescChange(desc) {\n    if (desc.public) {\n      this.setState({\n        title: desc.public.fn,\n        avatar: makeImageUrl(desc.public.photo)\n      });\n    } else {\n      this.setState({\n        title: '',\n        avatar: null\n      });\n    }\n\n    if (desc.acs) {\n      this.setState({\n        isWriter: desc.acs.isWriter(),\n        isReader: desc.acs.isReader(),\n        readingBlocked: !desc.acs.isReader('given'),\n        unconfirmed: isUnconfirmed(desc.acs),\n      });\n    }\n  }\n\n  postReadNotification(seq) {\n    // Ignore notifications if the app is invisible.\n    if (!this.props.applicationVisible) {\n      return;\n    }\n\n    // Set up the timer if it's not running already.\n    if (!this.readNotificationTimer) {\n      this.readNotificationTimer = setInterval(() => {\n        if (this.readNotificationQueue.length == 0) {\n          // Shut down the timer if the queue is empty.\n          clearInterval(this.readNotificationTimer);\n          this.readNotificationTimer = null;\n          return;\n        }\n\n        let seq = -1;\n        while (this.readNotificationQueue.length > 0) {\n          const n = this.readNotificationQueue[0];\n          if (n.topicName != this.state.topic) {\n            // Topic has changed. Drop the notification.\n            this.readNotificationQueue.shift();\n            continue;\n          }\n\n          const now = new Date();\n          if (n.sendAt <= now) {\n            // Remove expired notification from queue.\n            this.readNotificationQueue.shift();\n            seq = Math.max(seq, n.seq);\n          } else {\n            break;\n          }\n        }\n\n        // Send only one notification for the whole batch of messages.\n        if (seq >= 0) {\n          const topic = this.props.tinode.getTopic(this.state.topic);\n          if (topic) {\n            topic.noteRead(seq);\n          }\n        }\n      }, NOTIFICATION_EXEC_INTERVAL);\n    }\n\n    const now = new Date();\n    this.readNotificationQueue.push({\n      topicName: this.state.topic,\n      seq: seq,\n      sendAt: now.setMilliseconds(now.getMilliseconds() + READ_DELAY)\n    });\n  }\n\n  // Clear notification queue and timer.\n  clearNotificationQueue() {\n    this.readNotificationQueue = [];\n    if (this.readNotificationTimer) {\n      clearInterval(this.readNotificationTimer);\n      this.readNotificationTimer = null;\n    }\n  }\n\n  handleSubsUpdated() {\n    if (this.state.topic) {\n      const subs = [];\n      const topic = this.props.tinode.getTopic(this.state.topic);\n      topic.subscribers((sub) => {\n        if (sub.online && sub.user != this.props.myUserId) {\n          subs.push(sub);\n        }\n      });\n      const newState = {onlineSubs: subs};\n      const peer = topic.p2pPeerDesc();\n      if (peer) {\n        Object.assign(newState, {\n          peerMessagingDisabled: isPeerRestricted(peer.acs)\n        });\n      } else if (this.state.peerMessagingDisabled) {\n        Object.assign(newState, {\n          peerMessagingDisabled: false\n        });\n      }\n      this.setState(newState);\n    }\n  }\n\n  handleNewMessage(msg) {\n    // Regenerate messages list\n    const topic = this.props.tinode.getTopic(this.state.topic);\n    const newState = {messages: []};\n    topic.messages((m) => {\n      if (!m.deleted) {\n        newState.messages.push(m);\n      }\n    });\n\n    // msg could be null if one or more messages were deleted.\n    if (msg && !msg.deleted) {\n      // If the message is added to the end of the message list,\n      // scroll to the bottom.\n      if (topic.isNewMessage(msg.seq)) {\n        newState.scrollPosition = 0;\n      }\n\n      // Aknowledge messages except own messages. They are\n      // automatically assumed to be read and recived.\n      const status = topic.msgStatus(msg);\n      if (status >= Tinode.MESSAGE_STATUS_SENT && msg.from != this.props.myUserId) {\n        this.postReadNotification(msg.seq);\n      }\n      this.props.onData(msg);\n    }\n    this.setState(newState);\n  }\n\n  handleAllMessagesReceived(count) {\n    this.setState({fetchingMessages: false});\n    if (count > 0) {\n      // 0 means \"latest\".\n      this.postReadNotification(0);\n    }\n  }\n\n  handleInfoReceipt(info) {\n    switch (info.what) {\n      case 'kp': {\n        clearTimeout(this.keyPressTimer);\n        var instance = this;\n        this.keyPressTimer = setTimeout(function() {\n          instance.setState({typingIndicator: false});\n        }, KEYPRESS_DELAY + 1000);\n        if (!this.state.typingIndicator) {\n          this.setState({typingIndicator: true});\n        }\n        break;\n      }\n      case 'read':\n      case 'recv':\n        // Redraw due to changed recv/read status.\n        this.forceUpdate();\n        break;\n      default:\n        console.log(\"Other change in topic: \", info.what);\n    }\n  }\n\n  handleImagePostview(content) {\n    this.setState({ imagePostview: content });\n  }\n\n  handleClosePreview() {\n    this.setState({ imagePostview: null, imagePreview: null, docPreview: null });\n  }\n\n  handleFormResponse(action, text, data) {\n    if (action == 'pub') {\n      this.props.sendMessage(Drafty.attachJSON(Drafty.parse(text), data));\n    } else if (action == 'url') {\n      const url = new URL(data.ref);\n      const params = url.searchParams;\n      for (let key in data.resp) {\n        if (data.resp.hasOwnProperty(key)) {\n          params.set(key, data.resp[key]);\n        }\n      }\n      ['name', 'seq'].map(function(key) {\n        if (data[key]) {\n          params.set(key, data[key]);\n        }\n      });\n      params.set('uid', this.props.myUserId);\n      url.search = params;\n      window.open(url, '_blank');\n    } else {\n      console.log(\"Unknown action in form\", action);\n    }\n  }\n\n  handleContextClick(e) {\n    e.preventDefault();\n    e.stopPropagation();\n    this.props.showContextMenu({ topicName: this.state.topic, y: e.pageY, x: e.pageX });\n  }\n\n  handleShowContextMenuMessage(params, messageSpecificMenuItems) {\n    params.topicName = this.state.topic;\n    const menuItems = messageSpecificMenuItems || [];\n    menuItems.push('message_delete');\n    const topic = this.props.tinode.getTopic(params.topicName);\n    if (topic) {\n      const acs = topic.getAccessMode();\n      if (acs && acs.isDeleter()) {\n        menuItems.push('message_delete_hard');\n      }\n    }\n    this.props.showContextMenu(params, menuItems);\n  }\n\n  handleNewChatAcceptance(action) {\n    this.props.onNewChat(this.state.topic, action);\n  }\n\n  handleEnablePeer(e) {\n    e.preventDefault();\n    this.props.onChangePermissions(this.state.topic, DEFAULT_P2P_ACCESS_MODE, this.state.topic);\n  }\n\n  sendKeyPress() {\n    const topic = this.props.tinode.getTopic(this.state.topic);\n    if (topic.isSubscribed()) {\n      topic.noteKeyPress();\n    }\n  }\n\n  sendFileAttachment(file) {\n    if (file.size > MAX_INBAND_ATTACHMENT_SIZE) {\n      // Too large to send inband - uploading out of band and sending as a link.\n      const uploader = this.props.tinode.getLargeFileHelper();\n      if (!uploader) {\n        this.props.onError(this.props.intl.formatMessage(messages.cannot_initiate_upload));\n        return;\n      }\n      const uploadCompletionPromise = uploader.upload(file);\n      const msg = Drafty.attachFile(null, file.type, null, file.name, file.size, uploadCompletionPromise);\n      // Pass data and the uploader to the TinodeWeb.\n      this.props.sendMessage(msg, uploadCompletionPromise, uploader);\n    } else {\n      // Small enough to send inband.\n      fileToBase64(file,\n        (mime, bits, fname) => {\n          this.props.sendMessage(Drafty.attachFile(null, mime, bits, fname));\n        },\n        this.props.onError\n      );\n    }\n  }\n\n  handleAttachFile(file) {\n    if (file.size > MAX_EXTERN_ATTACHMENT_SIZE) {\n      // Too large.\n      this.props.onError(this.props.intl.formatMessage(messages.file_attachment_too_large,\n          {size: bytesToHumanSize(file.size), limit: bytesToHumanSize(MAX_EXTERN_ATTACHMENT_SIZE)}), 'err');\n    } else {\n      this.setState({ docPreview: {\n        file: file,\n        filename: file.name,\n        size: file.size,\n        type: file.type\n      }});\n    }\n  }\n\n  sendImageAttachment(caption, mime, bits, width, height, fname) {\n    let msg = Drafty.insertImage(null, 0, mime, bits, width, height, fname);\n    if (caption) {\n      msg = Drafty.appendLineBreak(msg);\n      msg = Drafty.append(msg, Drafty.init(caption));\n    }\n    this.props.sendMessage(msg);\n  }\n\n  handleAttachImage(file) {\n    // Check if the uploaded file is indeed an image and if it isn't too large.\n    if (file.size > MAX_INBAND_ATTACHMENT_SIZE || SUPPORTED_IMAGE_FORMATS.indexOf(file.type) < 0) {\n      // Convert image for size or format.\n      imageFileScaledToBase64(file, MAX_IMAGE_DIM, MAX_IMAGE_DIM, false,\n        // Success\n        (bits, mime, width, height, fname) => {\n          this.setState({imagePreview: {\n            url: URL.createObjectURL(file),\n            bits: bits,\n            filename: fname,\n            width: width,\n            height: height,\n            size: bits.length,\n            type: mime\n          }});\n        },\n        // Failure\n        (err) => {\n          this.props.onError(err, 'err');\n        });\n    } else {\n      // Image can be uploaded as is. No conversion is needed.\n      imageFileToBase64(file,\n        // Success\n        (bits, mime, width, height, fname) => {\n          this.setState({imagePreview: {\n            url: URL.createObjectURL(file),\n            bits: bits,\n            filename: fname,\n            width: width,\n            height: height,\n            size: bits.length,\n            type: mime\n          }});\n        },\n        // Failure\n        (err) => {\n          this.props.onError(err, 'err');\n        }\n      );\n    }\n  }\n\n  render() {\n    const {formatMessage} = this.props.intl;\n\n    let component;\n    if (this.props.hideSelf) {\n      component = null;\n    } else if (!this.state.topic) {\n      component = (\n        <LogoView\n          serverVersion={this.props.serverVersion}\n          serverAddress={this.props.serverAddress} />\n      );\n    } else {\n      let component2;\n      if (this.state.imagePreview) {\n        // Preview image before sending.\n        component2 = (\n          <ImagePreview\n            content={this.state.imagePreview}\n            onClose={this.handleClosePreview}\n            onSendMessage={this.sendImageAttachment} />\n        );\n      } else if (this.state.imagePostview) {\n        // Expand received image.\n        component2 = (\n          <ImagePreview\n            content={this.state.imagePostview}\n            onClose={this.handleClosePreview} />\n        );\n      } else if (this.state.docPreview) {\n        // Preview attachment before sending.\n        component2 = (\n          <DocPreview\n            content={this.state.docPreview}\n            onClose={this.handleClosePreview}\n            onSendMessage={this.sendFileAttachment} />\n        );\n      } else {\n        const topic = this.props.tinode.getTopic(this.state.topic);\n        const groupTopic = topic.getType() == 'grp';\n        let messageNodes = [];\n        let previousFrom = null;\n        let chatBoxClass = null;\n        for (let i=0; i<this.state.messages.length; i++) {\n          let msg = this.state.messages[i];\n          let nextFrom = null;\n\n          if (i + 1 < this.state.messages.length) {\n            nextFrom = this.state.messages[i+1].from\n          }\n\n          let sequence = 'single';\n          if (msg.from == previousFrom) {\n            if (msg.from == nextFrom) {\n              sequence = 'middle';\n            } else {\n              sequence = 'last';\n            }\n          } else if (msg.from == nextFrom) {\n            sequence = 'first';\n          }\n          previousFrom = msg.from;\n\n          const isReply = !(msg.from == this.props.myUserId);\n          const deliveryStatus = topic.msgStatus(msg);\n\n          let userName, userAvatar, userFrom;\n          if (groupTopic) {\n            const user = topic.userDesc(msg.from);\n            if (user && user.public) {\n              userName = user.public.fn;\n              userAvatar = makeImageUrl(user.public.photo);\n            }\n            userFrom = msg.from;\n            chatBoxClass='chat-box group';\n          } else {\n            chatBoxClass='chat-box';\n          }\n\n          messageNodes.push(\n            <ChatMessage\n              tinode={this.props.tinode}\n              content={msg.content} deleted={msg.hi}\n              mimeType={msg.head ? msg.head.mime : null}\n              timestamp={msg.ts} response={isReply} seq={msg.seq}\n              userFrom={userFrom} userName={userName} userAvatar={userAvatar}\n              sequence={sequence} received={deliveryStatus} uploader={msg._uploader}\n              viewportWidth={this.props.viewportWidth}\n              showContextMenu={this.handleShowContextMenuMessage}\n              onImagePreview={this.handleImagePostview}\n              onFormResponse={this.handleFormResponse}\n              onError={this.props.onError}\n              key={msg.seq} />\n          );\n        }\n\n        let lastSeen = null;\n        const cont = this.props.tinode.getMeTopic().getContact(this.state.topic);\n        if (cont && Tinode.topicType(cont.topic) == 'p2p') {\n          if (cont.online) {\n            lastSeen = formatMessage(messages.online_now);\n          } else if (cont.seen) {\n            lastSeen = formatMessage(messages.last_seen) + \": \" +\n              shortDateFormat(cont.seen.when, this.props.intl.locale);\n            // TODO: also handle user agent in c.seen.ua\n          }\n        }\n        const avatar = this.state.avatar || true;\n        const online = this.props.online ? 'online' + (this.state.typingIndicator ? ' typing' : '') : 'offline';\n\n        component2 = (\n          <>\n            <div id=\"topic-caption-panel\" className=\"caption-panel\">\n              {this.props.displayMobile ?\n                <a href=\"#\" id=\"hide-message-view\" onClick={(e) => {e.preventDefault(); this.props.onHideMessagesView();}}>\n                  <i className=\"material-icons\">arrow_back</i>\n                </a>\n                :\n                null}\n              <div className=\"avatar-box\">\n                <LetterTile\n                  avatar={avatar}\n                  topic={this.state.topic}\n                  title={this.state.title} />\n                <span className={online} />\n              </div>\n              <div id=\"topic-title-group\">\n                <div id=\"topic-title\" className=\"panel-title\">{\n                  this.state.title ||\n                  <i><FormattedMessage id=\"unnamed_topic\" defaultMessage=\"Unnamed\"\n                    description=\"Title shown when the topic has no name\" /></i>\n                }</div>\n                <div id=\"topic-last-seen\">{lastSeen}</div>\n              </div>\n              {groupTopic ?\n                <GroupSubs\n                  subscribers={this.state.onlineSubs} /> :\n                <div id=\"topic-users\" />\n              }\n              <div>\n                <a href=\"#\" onClick={this.handleContextClick}>\n                  <i className=\"material-icons\">more_vert</i>\n                </a>\n              </div>\n            </div>\n            {this.props.displayMobile ?\n              <ErrorPanel\n                level={this.props.errorLevel}\n                text={this.props.errorText}\n                onClearError={this.props.onError} />\n              : null}\n            <LoadSpinner show={this.state.fetchingMessages} />\n            <div id=\"messages-container\">\n              <div id=\"messages-panel\" ref={this.handleScrollReference}>\n                <ul id=\"scroller\" className={chatBoxClass}>\n                  {messageNodes}\n                </ul>\n              </div>\n              {!this.state.isReader ?\n              <div id=\"write-only-background\">\n                {this.state.readingBlocked ?\n                <div id=\"write-only-note\">\n                  <FormattedMessage id=\"messages_not_readable\" defaultMessage=\"no access to messages\"\n                    description=\"Message shown in topic without the read access\" />\n                </div>\n                : null }\n              </div>\n              : null }\n            </div>\n            {this.state.peerMessagingDisabled && !this.state.unconfirmed ?\n              <div id=\"peer-messaging-disabled-note\">\n                <i className=\"material-icons secondary\">block</i> <FormattedMessage\n                  id=\"peers_messaging_disabled\" defaultMessage=\"Peer's messaging is disabled.\"\n                  description=\"Shown when the p2p peer's messaging is disabled\" /> <a href=\"#\"\n                    onClick={this.handleEnablePeer}><FormattedMessage id=\"enable_peers_messaging\"\n                    defaultMessage=\"Enable\" description=\"Call to action to enable peer's messaging\" /></a>.\n              </div> : null}\n            {this.state.unconfirmed ?\n              <Invitation onAction={this.handleNewChatAcceptance} />\n              :\n              <SendMessage\n                disabled={!this.state.isWriter}\n                onSendMessage={this.props.sendMessage}\n                onKeyPress={this.sendKeyPress}\n                onAttachFile={this.handleAttachFile}\n                onAttachImage={this.handleAttachImage}\n                onError={this.props.onError} />}\n          </>\n        );\n      }\n\n      component = <div id=\"topic-view\">{component2}</div>\n    }\n    return component;\n  }\n};\n\nexport default injectIntl(MessagesView);\n", "// The <- button to be displayed in title bars.\nimport React from 'react';\n\nexport default class ButtonBack extends React.PureComponent {\n  render() {\n    return (\n      <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onBack();}}>\n        <i className=\"material-icons\">arrow_back</i>\n      </a>\n    );\n  }\n}\n", "import React from 'react';\n\nexport default class MenuContacts extends React.PureComponent {\n  render() {\n    return (\n      <div>\n        <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onNewTopic();}}><i className=\"material-icons\">chat</i></a>\n        &nbsp;\n        <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onSettings();}}><i className=\"material-icons\">settings</i></a>\n      </div>\n    );\n  }\n};\n", "import React from 'react';\n\nexport default class MenuStart extends React.PureComponent {\n  render() {\n    return (\n        <div>\n          <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onSignUp();}}><i className=\"material-icons\">person_add</i></a>\n          &nbsp;\n          <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onSettings();}}><i className=\"material-icons\">settings</i></a>\n        </div>\n    );\n  }\n};\n", "import React from 'react';\n\nimport LetterTile from './letter-tile.jsx';\nimport ButtonBack from './button-back.jsx';\nimport MenuContacts from './menu-contacts.jsx';\nimport MenuStart from './menu-start.jsx';\n\nexport default class SideNavbar extends React.PureComponent {\n  render() {\n    return (\n        <div id=\"side-caption-panel\" className=\"caption-panel\">\n          {this.props.onCancel ? <ButtonBack onBack={this.props.onCancel} /> : null}\n          {this.props.avatar ?\n            <div id=\"self-avatar\" className=\"avatar-box\">\n              <LetterTile\n                avatar={this.props.avatar}\n                topic={this.props.myUserId}\n                title={this.props.title} />\n            </div>\n            :\n            null}\n          <div id=\"sidepanel-title\" className=\"panel-title\">{this.props.title}</div>\n          {this.props.state === 'login' ?\n              <MenuStart onSignUp={this.props.onSignUp} onSettings={this.props.onSettings} /> :\n            this.props.state === 'contacts' ?\n              <MenuContacts onNewTopic={this.props.onNewTopic} onSettings={this.props.onSettings} /> :\n            null}\n        </div>\n    );\n  }\n};\n", "/* ContactsView holds all contacts-related stuff */\nimport React from 'react';\nimport { FormattedMessage, defineMessages } from 'react-intl';\n\nimport ContactList from '../widgets/contact-list.jsx';\n\nimport { updateFavicon } from '../lib/utils.js';\n\nconst messages = defineMessages({\n  archived_contacts_title: {\n    id: \"archived_contacts\",\n    defaultMessage: \"Archived contacts ({count})\",\n    description: \"Label for archived chats\"\n  }\n});\n\nexport default class ContactsView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.handleAction = this.handleAction.bind(this);\n\n    this.state = ContactsView.deriveStateFromProps(props);\n  }\n\n  static deriveStateFromProps(props) {\n    const contacts = [];\n    let unreadThreads = 0;\n    let archivedCount = 0;\n    props.chatList.map((c) => {\n      const blocked = c.acs && !c.acs.isJoiner();\n      // Show only blocked contacts only when props.blocked == true.\n      if (blocked && props.blocked) {\n        contacts.push(c);\n      }\n      if (blocked || props.blocked) {\n        return;\n      }\n\n      if (c.private && c.private.arch) {\n        if (props.archive) {\n          contacts.push(c);\n        } else {\n          archivedCount ++;\n        }\n      } else if (!props.archive) {\n        contacts.push(c);\n        unreadThreads += c.unread > 0 ? 1 : 0;\n      }\n    });\n\n    contacts.sort((a, b) => {\n      return (b.touched || 0) - (a.touched || 0);\n    });\n\n    if (archivedCount > 0) {\n      contacts.push({\n        action: 'archive',\n        title: messages.archived_contacts_title,\n        values: {count: archivedCount}\n      });\n    }\n\n    return {\n      contactList: contacts,\n      unreadThreads: unreadThreads\n    };\n  }\n\n  componentDidUpdate(prevProps, prevState) {\n    if (prevProps.chatList != this.props.chatList ||\n        prevProps.archive != this.props.archive ||\n        prevProps.blocked != this.props.blocked) {\n      const newState = ContactsView.deriveStateFromProps(this.props);\n      this.setState(newState);\n      if (newState.unreadThreads != prevState.unreadThreads) {\n        updateFavicon(newState.unreadThreads);\n      }\n    }\n  }\n\n  handleAction(action_ignored) {\n    this.props.onShowArchive();\n  }\n\n  render() {\n    return (\n      <FormattedMessage id=\"contacts_not_found\"\n        defaultMessage=\"You have no chats<br />¯∖_(ツ)_/¯\"\n        description=\"HTML message shown in ContactList when no contacts are found\">{\n        (no_contacts) => <ContactList\n          connected={this.props.connected}\n          contacts={this.state.contactList}\n          emptyListMessage={no_contacts}\n          topicSelected={this.props.topicSelected}\n          myUserId={this.props.myUserId}\n          showOnline={true}\n          showUnread={true}\n          onTopicSelected={this.props.onTopicSelected}\n          showContextMenu={this.props.showContextMenu}\n          onAction={this.handleAction} />\n      }</FormattedMessage>\n    );\n  }\n};\n", "// Helper functions for storing values in localStorage.\n// By default localStorage can store only strings, not objects or other types.\n\nexport default class LocalStorageUtil {\n  // Replace old object with the new one.\n  static setObject(key, value) {\n    localStorage.setItem(key, JSON.stringify(value));\n  }\n\n  // Get stored object.\n  static getObject(key) {\n    const value = localStorage.getItem(key);\n    return value && JSON.parse(value);\n  }\n\n  // Partially or wholly update stored object.\n  static updateObject(key, value) {\n    const oldVal = this.getObject(key);\n    this.setObject(key, Object.assign(oldVal || {}, value));\n  }\n\n  // Just a wrapper.\n  static removeItem(key) {\n    localStorage.removeItem(key);\n  }\n}\n", "// Account registration.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport AvatarUpload from '../widgets/avatar-upload.jsx';\nimport CheckBox from '../widgets/checkbox.jsx';\nimport VisiblePassword from '../widgets/visible-password.jsx';\n\nimport LocalStorageUtil from '../lib/local-storage.js';\nimport { vcard } from '../lib/utils.js';\n\nimport { MAX_TITLE_LENGTH } from '../config.js';\n\nexport default class CreateAccountView extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      login: '',\n      password: '',\n      email: '',\n      fn: '', // full/formatted name\n      imageDataUrl: null,\n      errorCleared: false,\n      saveToken: LocalStorageUtil.getObject('keep-logged-in')\n    };\n\n    this.handleLoginChange = this.handleLoginChange.bind(this);\n    this.handlePasswordChange = this.handlePasswordChange.bind(this);\n    this.handleEmailChange = this.handleEmailChange.bind(this);\n    this.handleFnChange = this.handleFnChange.bind(this);\n    this.handleImageChanged = this.handleImageChanged.bind(this);\n    this.handleToggleSaveToken = this.handleToggleSaveToken.bind(this);\n    this.handleSubmit = this.handleSubmit.bind(this);\n  }\n\n  handleLoginChange(e) {\n    this.setState({login: e.target.value});\n  }\n\n  handlePasswordChange(password) {\n    this.setState({password: password});\n  }\n\n  handleEmailChange(e) {\n    this.setState({email: e.target.value})\n  }\n\n  handleFnChange(e) {\n    this.setState({fn: e.target.value});\n  }\n\n  handleImageChanged(img) {\n    this.setState({imageDataUrl: img});\n  }\n\n  handleToggleSaveToken() {\n    LocalStorageUtil.setObject('keep-logged-in', !this.state.saveToken);\n    this.setState({saveToken: !this.state.saveToken});\n  }\n\n  handleSubmit(e) {\n    e.preventDefault();\n    this.setState({errorCleared: false});\n    this.props.onCreateAccount(\n      this.state.login.trim(),\n      this.state.password.trim(),\n      vcard(this.state.fn.trim().substring(0, MAX_TITLE_LENGTH), this.state.imageDataUrl),\n      {'meth': 'email', 'val': this.state.email});\n  }\n\n  render() {\n    let submitClasses = 'blue';\n    if (this.props.disabled) {\n      submitClasses += ' disabled';\n    }\n\n    return (\n      <form className=\"panel-form-column\" onSubmit={this.handleSubmit}>\n        <div className=\"panel-form-row\">\n          <div className=\"panel-form-column\">\n            <FormattedMessage id=\"login_prompt\">{\n              (login_prompt) => <input type=\"text\" placeholder={login_prompt} autoComplete=\"user-name\"\n                value={this.state.login} onChange={this.handleLoginChange} required autoFocus />\n            }</FormattedMessage>\n            <FormattedMessage id=\"password_prompt\">{\n              (password_prompt) => <VisiblePassword placeholder={password_prompt} autoComplete=\"new-password\"\n                value={this.state.password} onFinished={this.handlePasswordChange}\n                required={true} />\n            }</FormattedMessage>\n          </div>\n          <AvatarUpload\n            onImageChanged={this.handleImageChanged}\n            onError={this.props.onError} />\n        </div>\n        <div  className=\"panel-form-row\">\n          <FormattedMessage id=\"full_name_prompt\" defaultMessage=\"Full name, e.g. John Doe\"\n            description=\"Input placeholder for person's full name\">{\n            (full_name_prompt) => <input type=\"text\" placeholder={full_name_prompt} autoComplete=\"name\"\n              value={this.state.fn} onChange={this.handleFnChange} required/>\n          }</FormattedMessage>\n        </div>\n        <div className=\"panel-form-row\">\n          <FormattedMessage id=\"email_prompt\" defaultMessage=\"Email, e.g. <EMAIL>\"\n            description=\"Input placeholder for email entry\">{\n            (email_prompt) => <input type=\"email\" placeholder={email_prompt} autoComplete=\"email\"\n              value={this.state.email} onChange={this.handleEmailChange} required/>\n          }</FormattedMessage>\n        </div>\n        <div className=\"panel-form-row\">\n          <CheckBox id=\"save-token\" name=\"save-token\" checked={this.state.saveToken}\n            onChange={this.handleToggleSaveToken} />\n          <FormattedMessage id=\"stay_logged_in\">{\n            (stay_logged_in) => <label htmlFor=\"save-token\">&nbsp;{stay_logged_in}</label>\n          }</FormattedMessage>\n        </div>\n        <div className=\"dialog-buttons\">\n          <button className={submitClasses} type=\"submit\">\n            <FormattedMessage id=\"button_sign_up\" defaultMessage=\"Sign up\"\n              description=\"Create account button [Sign Up]\" />\n          </button>\n        </div>\n      </form>\n    );\n  }\n};\n", "// Edit account parameters.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport AvatarUpload from '../widgets/avatar-upload.jsx';\n\nimport { makeImageUrl } from '../lib/blob-helpers.js';\n\nexport default class EditAccountView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    const me = this.props.tinode.getMeTopic();\n    this.state = {\n      fullName: me.public ? me.public.fn : undefined,\n      avatar: makeImageUrl(me.public ? me.public.photo : null)\n    };\n  }\n\n  render() {\n    return (\n      <div className=\"scrollable-panel\">\n        <div className=\"panel-form-row\">\n          <div className=\"panel-form-column\">\n            <label className=\"small\">\n              <FormattedMessage id=\"label_your_name\" defaultMessage=\"Your name\"\n                description=\"Label for full name editing\" />\n            </label>\n            <div className=\"large\">{this.state.fullName}</div>\n            <div>\n              <label className=\"small\"><FormattedMessage id=\"label_user_id\" defaultMessage=\"ID:\"\n                description=\"Label for user address (ID)\" /></label>&nbsp;\n              <tt>{this.props.myUserId}</tt>\n            </div>\n          </div>\n          <AvatarUpload\n            avatar={this.state.avatar}\n            readOnly={!this.state.owner}\n            uid={this.props.myUserId}\n            title={this.state.fullName} />\n        </div>\n        <div className=\"panel-form-row\">\n          <a href=\"#\" className=\"flat-button\" onClick={(e) => {e.preventDefault(); this.props.onBasicNavigate('general');}}>\n            <i className=\"material-icons\">edit</i>&nbsp;\n            <FormattedMessage id=\"button_edit\" defaultMessage=\"Edit\" description=\"Call to action [Edit]\" />\n          </a>\n        </div>\n        <div className=\"hr\" />\n        <div className=\"panel-form-column\">\n          <a href=\"#\" className=\"flat-button\" onClick={(e) => {e.preventDefault(); this.props.onBasicNavigate('notif');}}>\n            <i className=\"material-icons\">notifications</i>&nbsp;<FormattedMessage id=\"sidepanel_title_acc_notifications\" />\n          </a>\n          <a href=\"#\" className=\"flat-button\" onClick={(e) => {e.preventDefault(); this.props.onBasicNavigate('security');}}>\n            <i className=\"material-icons\">security</i>&nbsp;<FormattedMessage id=\"sidepanel_title_acc_security\" />\n          </a>\n          <a href=\"#\" className=\"flat-button\" onClick={(e) => {e.preventDefault(); this.props.onBasicNavigate('support');}}>\n            <i className=\"material-icons\">contact_support</i>&nbsp;<FormattedMessage id=\"sidepanel_title_acc_support\" />\n          </a>\n        </div>\n      </div>\n    );\n  }\n};\n", "// Edit account parameters.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport Tinode from 'tinode-sdk';\n\nimport AvatarUpload from '../widgets/avatar-upload.jsx';\nimport InPlaceEdit from '../widgets/in-place-edit.jsx';\nimport TagManager from '../widgets/tag-manager.jsx';\n\nimport { MAX_TITLE_LENGTH } from '../config.js';\nimport { makeImageUrl } from '../lib/blob-helpers.js';\nimport { arrayEqual, asEmail, asPhone, vcard } from '../lib/utils.js';\n\nexport default class AccGeneralView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    const me = this.props.tinode.getMeTopic();\n    this.state = {\n      fullName: me.public ? me.public.fn : undefined,\n      avatar: makeImageUrl(me.public ? me.public.photo : null),\n      tags: me.tags(),\n      credentials: me.getCredentials() || [],\n      addCredActive: false,\n      addCredInvalid: false,\n      newCred: '',\n      previousOnTags: me.onTagsUpdated\n    };\n\n    this.tnNewTags = this.tnNewTags.bind(this);\n    this.tnCredsUpdated = this.tnCredsUpdated.bind(this);\n    this.handleFullNameUpdate = this.handleFullNameUpdate.bind(this);\n    this.handleImageChanged = this.handleImageChanged.bind(this);\n    this.handleCredChange = this.handleCredChange.bind(this);\n    this.handleCredKeyDown = this.handleCredKeyDown.bind(this);\n    this.handleCredEntered = this.handleCredEntered.bind(this);\n    this.handleTagsUpdated = this.handleTagsUpdated.bind(this);\n  }\n\n  componentDidMount() {\n    const me = this.props.tinode.getMeTopic();\n    me.onCredsUpdated = this.tnCredsUpdated;\n    me.onTagsUpdated = this.tnNewTags;\n  }\n\n  componentWillUnmount() {\n    const me = this.props.tinode.getMeTopic();\n    me.onTagsUpdated = this.state.previousOnTags;\n    me.onCredsUpdated = undefined;\n  }\n\n  tnNewTags(tags) {\n    this.setState({tags: tags});\n  }\n\n  tnCredsUpdated(creds) {\n    this.setState({credentials: creds || []});\n  }\n\n  handleFullNameUpdate(fn) {\n    fn = fn.trim().substring(0, MAX_TITLE_LENGTH);\n    if (fn) {\n      this.setState({fullName: fn});\n      this.props.onUpdateAccount(undefined, vcard(fn, null));\n    }\n  }\n\n  handleImageChanged(img) {\n    this.setState({avatar: img});\n    this.props.onUpdateAccount(undefined, vcard(null, img || Tinode.DEL_CHAR));\n  }\n\n  handleCredChange(e) {\n    this.setState({newCred: e.target.value, addCredInvalid: false});\n  }\n\n  handleCredKeyDown(e) {\n    if (e.keyCode === 27) {\n      // Escape pressed\n      this.setState({newCred: '', addCredActive: false});\n    } else if (e.keyCode === 13) {\n      // Enter pressed\n      this.handleCredEntered(e);\n    }\n  }\n\n  handleCredEntered(e) {\n    let value = this.state.newCred.trim();\n    if (!value) {\n      this.setState({addCredActive: false, addCredInvalid: false});\n      return;\n    }\n\n    let val = asPhone(value);\n    let method;\n    if (val) {\n      method = 'tel';\n    } else {\n      val = asEmail(value);\n      if (val) {\n        method = 'email';\n      }\n    }\n    if (method) {\n      this.props.onCredAdd(method, val);\n      this.setState({addCredActive: false, newCred: ''});\n    } else {\n      this.setState({addCredInvalid: true});\n    }\n  }\n\n  handleTagsUpdated(tags) {\n    // Check if tags have actually changed.\n    if (arrayEqual(this.state.tags.slice(0), tags.slice(0))) {\n      return;\n    }\n    this.props.onUpdateTags(tags);\n  }\n\n  render() {\n    const credentials = [];\n    this.state.credentials.map((cred) => {\n      credentials.push(<div key={cred.meth + \":\" + cred.val + \":\" + cred.done}>{cred.meth}: <tt>{cred.val}</tt>\n        <span > {!cred.done ?\n          <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onCredConfirm(cred.meth, cred.val);}}>\n              <FormattedMessage id=\"validate_credential_action\" defaultMessage=\"confirm\"\n                description=\"Validate credentail call to action\" />\n            </a>\n          : null} <a href=\"#\" onClick={(e) => {e.preventDefault(); this.props.onCredDelete(cred.meth, cred.val);}}><i\n            className=\"material-icons gray\">delete_outline</i></a></span></div>);\n    });\n\n    return (\n      <div className=\"scrollable-panel\">\n        <div className=\"panel-form-row\">\n          <div className=\"panel-form-column\">\n            <label className=\"small\"><FormattedMessage id=\"label_your_name\" /></label>\n            <div><FormattedMessage id=\"full_name_prompt\">{\n              (full_name_placeholder) => <InPlaceEdit\n                placeholder={full_name_placeholder}\n                value={this.state.fullName}\n                onFinished={this.handleFullNameUpdate} />\n            }</FormattedMessage></div>\n          </div>\n          <AvatarUpload\n            avatar={this.state.avatar}\n            uid={this.props.myUserId}\n            title={this.state.fullName}\n            onImageChanged={this.handleImageChanged}\n            onError={this.props.onError} />\n        </div>\n        <div className=\"hr\" />\n        <FormattedMessage id=\"title_tag_manager\" defaultMessage=\"Tags (user discovery)\"\n          description=\"Section title for TagManager\">{\n          (title_tag_manager) => <TagManager\n            title={title_tag_manager}\n            activated={false}\n            tags={this.state.tags}\n            onSubmit={this.handleTagsUpdated} />\n        }</FormattedMessage>\n        <div className=\"hr\" />\n        <div className=\"panel-form-column\">\n          <label className=\"small\">\n            <FormattedMessage id=\"label_user_contacts\" defaultMessage=\"Contacts:\"\n            description=\"Label for user contacts\" />\n          </label>\n          <div className=\"quoted\">\n            {credentials}\n            {this.state.addCredActive ?\n              <input type=\"text\" value={this.state.value}\n                className={this.state.addCredInvalid ? 'invalid' : null}\n                placeholder=\"Phone number or email\" required=\"required\" autoFocus\n                onChange={this.handleCredChange} onKeyDown={this.handleCredKeyDown} onBlur={this.handleCredEntered} />\n              : null}\n            <div>\n              <a href=\"#\" onClick={(e) => {e.preventDefault(); this.setState({addCredActive: true});}}>\n                <i className=\"material-icons\">add</i>\n                <FormattedMessage id=\"button_add_another\" defaultMessage=\"Add another\" description=\"Call to action [+ add another]\" />\n              </a></div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n};\n", "// Edit account parameters.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport CheckBox from '../widgets/checkbox.jsx';\n\nexport default class EditAccountView extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.handleCheckboxClick = this.handleCheckboxClick.bind(this);\n  }\n\n  handleCheckboxClick(what, checked) {\n    if (what == 'sound') {\n      this.props.onToggleMessageSounds(checked);\n    } else if (what == 'alert') {\n      this.props.onTogglePushNotifications(checked);\n    } else if (what == 'incognito') {\n      this.props.onToggleIncognitoMode(checked);\n    }\n  }\n\n  render() {\n    return (\n        <div className=\"scrollable-panel\">\n          <div className=\"panel-form-row\">\n            <label htmlFor=\"message-sound\">\n              <FormattedMessage id=\"label_message_sound\" defaultMessage=\"Message sound:\"\n                description=\"Label for message sounds toggle\" />\n            </label>\n            <CheckBox name=\"sound\" id=\"message-sound\"\n              checked={this.props.messageSounds} onChange={this.handleCheckboxClick} />\n          </div>\n          <div className=\"panel-form-row\">\n            <label htmlFor=\"desktop-alerts\">\n            {this.props.desktopAlertsEnabled ?\n              <FormattedMessage id=\"label_push_notifications\" defaultMessage=\"Notification alerts:\"\n                description=\"Label for push notifications switch\" />\n              :\n              <FormattedMessage id=\"label_push_notifications_disabled\"\n                defaultMessage=\"Notification alerts (requires HTTPS):\"\n                description=\"Label for push notifications switch\" />\n            }\n            </label>\n            <CheckBox name=\"alert\" id=\"desktop-alerts\"\n              checked={this.props.desktopAlerts}\n              onChange={this.props.desktopAlertsEnabled ? this.handleCheckboxClick : null} />\n          </div>\n          <div className=\"panel-form-row\">\n            <label htmlFor=\"incognito-mode\">\n              <FormattedMessage id=\"label_incognito_mode\" defaultMessage=\"Incognito mode:\"\n                description=\"Label for incognito mode toggle\" />\n            </label>\n            <CheckBox name=\"incognito\" id=\"incognito-mode\"\n              checked={this.props.incognitoMode} onChange={this.handleCheckboxClick} />\n          </div>\n        </div>\n    );\n  }\n};\n", "// Edit account parameters.\nimport React from 'react';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nimport InPlaceEdit from '../widgets/in-place-edit.jsx';\nimport PermissionsEditor from '../widgets/permissions-editor.jsx';\n\nconst messages = defineMessages({\n  delete_account: {\n    id: 'delete_account',\n    defaultMessage: 'Delete account',\n    description: 'Title for delete account warning'\n  },\n  delete_account_warning: {\n    id: 'delete_account_arning',\n    defaultMessage: 'Are you sure you want to delete your account? It cannot be undone.',\n    description: 'Warning message when deleting an account'\n  }\n});\n\nclass AccSecurityView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    const me = this.props.tinode.getMeTopic();\n    let blockedCount = 0;\n    me.contacts((c) => {\n      if (c.acs && !c.acs.isJoiner()) {\n        blockedCount ++;\n      }\n    });\n    const defacs = me.getDefaultAccess();\n    this.state = {\n      auth: defacs ? defacs.auth : null,\n      anon: defacs ? defacs.anon : null,\n      showPermissionEditorFor: undefined,\n      blockedCount: blockedCount,\n    };\n\n    this.handlePasswordUpdate = this.handlePasswordUpdate.bind(this);\n    this.handleLaunchPermissionsEditor = this.handleLaunchPermissionsEditor.bind(this);\n    this.handleHidePermissionsEditor = this.handleHidePermissionsEditor.bind(this);\n    this.handlePermissionsChanged = this.handlePermissionsChanged.bind(this);\n    this.handleDeleteAccount = this.handleDeleteAccount.bind(this);\n  }\n\n  handlePasswordUpdate(pwd) {\n    this.setState({password: pwd});\n    this.props.onUpdateAccount(pwd);\n  }\n\n  handleLaunchPermissionsEditor(which) {\n    this.setState({\n      showPermissionEditorFor: which,\n      editedPermissions: this.state[which]\n    });\n  }\n\n  handleHidePermissionsEditor() {\n    this.setState({showPermissionEditorFor: undefined});\n  }\n\n  handlePermissionsChanged(perm) {\n    let defacs = {};\n    defacs[this.state.showPermissionEditorFor] = perm;\n    this.props.onUpdateAccount(undefined, undefined, defacs);\n\n    let newState = {showPermissionEditorFor: undefined};\n    newState[this.state.showPermissionEditorFor] = perm;\n    this.setState(newState);\n  }\n\n  handleDeleteAccount(e) {\n    e.preventDefault();\n    const {formatMessage} = this.props.intl;\n    this.props.onShowAlert(\n      formatMessage(messages.delete_account), // title\n      formatMessage(messages.delete_account_warning), // content\n      (() => { this.props.onDeleteAccount(); }), // onConfirm\n      null, // use default text \"OK\"\n      true, // Show Reject button\n      null  // use default text \"Cancel\"\n    );\n  }\n\n  render() {\n    return (\n      <React.Fragment>{this.state.showPermissionEditorFor ?\n        <PermissionsEditor\n          mode={this.state.editedPermissions}\n          skip=\"O\"\n          onSubmit={this.handlePermissionsChanged}\n          onCancel={this.handleHidePermissionsEditor} />\n        :\n        <div className=\"scrollable-panel\">\n          <div className=\"panel-form-column\">\n            <label className=\"small\">\n              <FormattedMessage id=\"label_password\" defaultMessage=\"Password\"\n                  description=\"Label for password editing\" />\n            </label>\n            <div>\n              <FormattedMessage id=\"password_unchanged_prompt\" defaultMessage=\"Unchanged\"\n                description=\"Message in editor while password is unchanged\">{\n                (password_unchanged) => <InPlaceEdit\n                  placeholder={password_unchanged}\n                  type=\"password\"\n                  onFinished={this.handlePasswordUpdate} />\n              }</FormattedMessage>\n            </div>\n          </div>\n          <div className=\"hr\" />\n          <div className=\"panel-form-column\">\n            <a href=\"#\" className=\"red flat-button\" onClick={(e) => {e.preventDefault(); this.props.onLogout();}}>\n              <i className=\"material-icons\">exit_to_app</i> &nbsp;<FormattedMessage id=\"button_logout\"\n                defaultMessage=\"Logout\" description=\"Button [Logout]\" />\n            </a>\n            <a href=\"#\" className=\"red flat-button\" onClick={(e) => {this.handleDeleteAccount(e);}}>\n              <i className=\"material-icons\">delete</i> &nbsp;<FormattedMessage id=\"button_delete_account\"\n                defaultMessage=\"Delete account\" description=\"Button [Delete account]\" />\n            </a>\n          </div>\n          <div className=\"hr\" />\n          <div className=\"panel-form-column\">\n            <div>\n              <label className=\"small\">\n                <FormattedMessage id=\"label_default_access_mode\" defaultMessage=\"Default access mode:\"\n                description=\"Label for default access mode\" />\n              </label>\n            </div>\n            <div className=\"quoted\">\n              <div>Auth: <tt className=\"clickable\"\n                onClick={this.handleLaunchPermissionsEditor.bind(this, 'auth')}>{this.state.auth}</tt></div>\n              <div>Anon: <tt className=\"clickable\"\n                onClick={this.handleLaunchPermissionsEditor.bind(this, 'anon')}>{this.state.anon}</tt></div>\n            </div>\n          </div>\n          {this.state.blockedCount > 0 ?\n            <>\n              <div className=\"hr\" />\n              <div className=\"panel-form-row\">\n                <i className=\"material-icons\">block</i>&nbsp;\n                <a href=\"#\" className=\"gray\" onClick={(e) => {e.preventDefault(); this.props.onShowBlocked();}}>\n                  <FormattedMessage id=\"blocked_contacts_link\" defaultMessage=\"Blocked contacts ({count})\"\n                    values={{ count: this.state.blockedCount }} description=\"Blocked contacts link\" />\n                </a>\n              </div>\n            </>\n            : null }\n        </div>\n      }</React.Fragment>\n    );\n  }\n};\n\nexport default injectIntl(AccSecurityView);\n", "// Edit account parameters.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport Tinode from 'tinode-sdk';\n\nimport { APP_NAME, LINK_CONTACT_US, LINK_PRIVACY_POLICY, LINK_TERMS_OF_SERVICE } from '../config.js';\n\nexport default class AccSupportView extends React.PureComponent {\n  render() {\n    return (\n      <div className=\"scrollable-panel\">\n        <div className=\"panel-form-column\">\n          <a href={LINK_CONTACT_US} className=\"flat-button\" target=\"_blank\">\n            <i className=\"material-icons\">email</i> &nbsp;<FormattedMessage id=\"link_contact_us\"\n              defaultMessage=\"Contact Us\" description=\"Ancor text for contacting us by email\" />\n          </a>\n          <a href={LINK_TERMS_OF_SERVICE} className=\"flat-button\" target=\"_blank\">\n            <i className=\"material-icons\">description</i> &nbsp;<FormattedMessage id=\"link_terms_of_service\"\n              defaultMessage=\"Terms of Service\" description=\"Ancor text for terms of service link\" />\n          </a>\n          <a href={LINK_PRIVACY_POLICY} className=\"flat-button\" target=\"_blank\">\n            <i className=\"material-icons\">policy</i> &nbsp;<FormattedMessage id=\"link_privacy_policy\"\n              defaultMessage=\"Privacy Policy\" description=\"Ancor text for privacy policy link\" />\n          </a>\n        </div>\n        <div className=\"hr\" />\n        <div className=\"panel-form-column\">\n          <div className=\"panel-form-row\">\n            <label className=\"small\"><FormattedMessage id=\"label_client\" /></label>\n            {APP_NAME}\n          </div>\n          <div className=\"panel-form-row\">\n            <label className=\"small\"><FormattedMessage id=\"label_sdk\" defaultMessage=\"SDK:\" /></label>\n            {Tinode.getLibrary()}\n          </div>\n          <div className=\"panel-form-row\">\n            <label className=\"small\"><FormattedMessage id=\"label_server\" /></label>\n            {this.props.serverVersion}\n          </div>\n          <div className=\"panel-form-row\">\n            <label className=\"small\"><FormattedMessage id=\"label_server_address\" defaultMessage=\"Server address:\" /></label>\n            {this.props.serverAddress}\n          </div>\n        </div>\n      </div>\n    );\n  }\n};\n", "// Login form.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport CheckBox from '../widgets/checkbox.jsx';\nimport VisiblePassword from '../widgets/visible-password.jsx';\n\nimport LocalStorageUtil from '../lib/local-storage.js';\n\nexport default class LoginView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      login: props.login,\n      password: '',\n      hostName: props.serverAddress,\n      saveToken: LocalStorageUtil.getObject('keep-logged-in')\n    };\n    this.handleLoginChange = this.handleLoginChange.bind(this);\n    this.handlePasswordChange = this.handlePasswordChange.bind(this);\n    this.handleToggleSaveToken = this.handleToggleSaveToken.bind(this);\n    this.handleSubmit = this.handleSubmit.bind(this);\n  }\n\n  handleLoginChange(e) {\n    this.setState({login: e.target.value});\n  }\n\n  handlePasswordChange(e) {\n    this.setState({password: e.target.value});\n  }\n\n  handleToggleSaveToken() {\n    LocalStorageUtil.setObject('keep-logged-in', !this.state.saveToken);\n    this.setState({saveToken: !this.state.saveToken});\n  }\n\n  handleSubmit(e) {\n    e.preventDefault();\n    this.props.onLogin(this.state.login.trim(), this.state.password.trim());\n  }\n\n  render() {\n    var submitClasses = 'blue';\n    if (this.props.disabled) {\n      submitClasses += ' disabled';\n    }\n\n    return (\n      <form id=\"login-form\" onSubmit={this.handleSubmit}>\n        <FormattedMessage id=\"login_prompt\" defaultMessage=\"Login\"\n          description=\"Login placeholder in LoginView\">\n        {\n          (login_prompt) => <input type=\"text\" id=\"inputLogin\"\n            placeholder={login_prompt}\n            autoComplete=\"username\"\n            autoCorrect=\"off\"\n            autoCapitalize=\"none\"\n            value={this.state.login}\n            onChange={this.handleLoginChange}\n            required autoFocus />\n        }\n        </FormattedMessage>\n        <FormattedMessage id=\"password_prompt\" defaultMessage=\"Password\"\n          description=\"Password placeholder in LoginView\">\n        {\n          (password_prompt) => <VisiblePassword type=\"password\" id=\"inputPassword\"\n            placeholder={password_prompt}\n            autoComplete=\"current-password\"\n            value={this.state.password}\n            onChange={this.handlePasswordChange}\n            required={true} />\n        }\n        </FormattedMessage>\n        <div className=\"panel-form-row\">\n          <CheckBox id=\"save-token\" name=\"save-token\" checked={this.state.saveToken}\n            onChange={this.handleToggleSaveToken} />\n          <label htmlFor=\"save-token\">&nbsp;\n            <FormattedMessage id=\"stay_logged_in\" defaultMessage=\"Stay logged in\"\n              description=\"Label for a checkbox\" />\n          </label>\n          <a href=\"#reset\">\n            <FormattedMessage id=\"forgot_password_link\" defaultMessage=\"Forgot password?\"\n              description=\"Link to Reset password form\" />\n          </a>\n        </div>\n        <div className=\"dialog-buttons\">\n          <button className={submitClasses} type=\"submit\">\n            <FormattedMessage id=\"button_sign_in\" defaultMessage=\"Sign in\"\n              description=\"Button [Sign In]\" />\n          </button>\n        </div>\n      </form>\n    );\n  }\n};\n/* END Login */\n", "import React from 'react';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nconst messages = defineMessages({\n  invalid_id: {\n    id: 'error_invalid_id',\n    defaultMessage: 'Invalid ID',\n    description: 'Error message'\n  }\n});\n\nclass NewTopicById extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      groupId: '',\n    };\n\n    this.handleChange = this.handleChange.bind(this);\n    this.handleKeyPress = this.handleKeyPress.bind(this);\n    this.handleSubmit = this.handleSubmit.bind(this);\n  }\n\n  handleChange(e) {\n    this.setState({groupId: e.target.value});\n  }\n\n  handleKeyPress(e) {\n    if (e.key === 'Enter') {\n      this.handleSubmit(e);\n    }\n  }\n\n  handleSubmit(e) {\n    e.preventDefault();\n    if (this.state.groupId) {\n      var name = this.state.groupId.trim();\n      if (name.length > 3 && (name.substr(0, 3) == 'usr' || name.substr(0, 3) == 'grp')) {\n        this.props.onSubmit(name);\n      } else {\n        this.props.onError(this.props.intl.formatMessage(messages.invalid_id), 'err');\n      }\n    }\n  }\n\n  render() {\n    return (\n      <div className=\"panel-form\">\n        <div className=\"panel-form-row\">\n        <FormattedMessage id=\"group_user_id_prompt\" defaultMessage=\"Group or User ID\"\n          description=\"Prompt for entering user or group ID\">{\n          (prompt) => <input type=\"text\" placeholder={prompt}\n            value={this.state.groupId} onChange={this.handleChange}\n            onKeyPress={this.handleKeyPress} required />\n        }</FormattedMessage>\n        </div>\n        <div className=\"dialog-buttons\">\n          <button className=\"blue\" onClick={this.handleSubmit}>\n            <FormattedMessage id=\"button_subscribe\" defaultMessage=\"Subscribe\"\n              description=\"Button [Subscribe]\" />\n          </button>\n        </div>\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(NewTopicById);\n", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport AvatarUpload from './avatar-upload.jsx';\nimport TagManager from './tag-manager.jsx';\n\nimport { MAX_TITLE_LENGTH } from '../config.js';\n\nexport default class NewTopicGroup extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      fn: '', // full/formatted name\n      private: '',\n      imageDataUrl: null,\n      tags: []\n    };\n\n    this.handleFnChange = this.handleFnChange.bind(this);\n    this.handlePrivateChange = this.handlePrivateChange.bind(this);\n    this.handleImageChanged = this.handleImageChanged.bind(this);\n    this.handleTagsChanged = this.handleTagsChanged.bind(this);\n    this.handleTagsChanged = this.handleTagsChanged.bind(this);\n    this.handleSubmit = this.handleSubmit.bind(this);\n  }\n\n  handleFnChange(e) {\n    this.setState({fn: e.target.value});\n  }\n\n  handlePrivateChange(e) {\n    this.setState({private: e.target.value});\n  }\n\n  handleImageChanged(img) {\n    this.setState({imageDataUrl: img});\n  }\n\n  handleTagsChanged(tags) {\n    this.setState({tags: tags});\n  }\n\n  handleSubmit(e) {\n    e.preventDefault();\n\n    const fn = this.state.fn.trim().substring(0, MAX_TITLE_LENGTH);\n    const comment = this.state.private.trim().substring(0, MAX_TITLE_LENGTH);\n    if (fn) {\n      this.props.onSubmit(fn, this.state.imageDataUrl, comment, this.state.tags);\n    }\n  }\n\n  render() {\n    var submitClasses = 'blue';\n    if (this.props.disabled) {\n      submitClasses += ' disabled';\n    }\n    return (\n      <form className=\"panel-form\" onSubmit={this.handleSubmit}>\n        <div className=\"panel-form-row\">\n          <div className=\"panel-form-column\">\n            <label className=\"small\" htmlFor=\"new-topic-fn\">\n              <FormattedMessage id=\"label_topic_name\" />\n            </label>\n            <FormattedMessage id=\"topic_name_editing_placeholder\" defaultMessage=\"Freeform name of the group\"\n              description=\"Prompt for entering topic name\">{\n              (placeholder) => <input type=\"text\" id=\"new-topic-fn\" placeholder={placeholder}\n                value={this.state.fn} onChange={this.handleFnChange} autoFocus required />\n            }</FormattedMessage>\n            <br />\n            <label className=\"small\" htmlFor=\"new-topic-priv\">\n              <FormattedMessage id=\"label_private\" />\n            </label>\n            <FormattedMessage id=\"private_editing_placeholder\">{\n              (placeholder) => <input type=\"text\" id=\"new-topic-priv\" placeholder={placeholder}\n                value={this.state.private} onChange={this.handlePrivateChange} />\n            }</FormattedMessage>\n          </div>\n          <AvatarUpload\n            onError={this.props.onError}\n            onImageChanged={this.handleImageChanged} />\n        </div>\n        <FormattedMessage id=\"title_tag_manager\">{\n          (title) => <TagManager\n            tags={this.state.tags}\n            activated={true}\n            onTagsChanged={this.handleTagsChanged}\n            title={title} />\n        }</FormattedMessage>\n        <div className=\"dialog-buttons\">\n          <button className={submitClasses}>\n            <FormattedMessage id=\"button_create\" defaultMessage=\"Create\"\n              description=\"Button [Create]\" />\n          </button>\n        </div>\n      </form>\n    );\n  }\n};\n", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport Tinode from 'tinode-sdk';\n\nexport default class SearchContacts extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      edited: false,\n      search: ''\n    };\n\n    this.handleSearchChange = this.handleSearchChange.bind(this);\n    this.handleSearch = this.handleSearch.bind(this);\n    this.handleClear = this.handleClear.bind(this);\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n  }\n\n  componentWillUnmount() {\n    if (this.state.edited) {\n      this.setState({search: '', edited: false});\n      this.props.onSearchContacts(Tinode.DEL_CHAR);\n    }\n  }\n\n  handleSearchChange(e) {\n    this.setState({search: e.target.value});\n  }\n\n  handleSearch(e) {\n    e.preventDefault();\n    var query = this.state.search.trim();\n    this.setState({edited: (query.length > 0)});\n    this.props.onSearchContacts(query.length > 0 ? query : Tinode.DEL_CHAR);\n  }\n\n  handleClear(e) {\n    e.preventDefault();\n    if (this.state.edited) {\n      this.props.onSearchContacts(Tinode.DEL_CHAR);\n    }\n    this.setState({search: '', edited: false});\n  }\n\n  handleKeyDown(e) {\n    if (e.key === 'Enter') {\n      this.handleSearch(e);\n    } else if (e.key === 'Escape') {\n      this.handleClear();\n    }\n  }\n\n  render() {\n    return (\n      <div className=\"panel-form\">\n        <div className=\"panel-form-row\">\n          <i className=\"material-icons search\">search</i>\n          <FormattedMessage id=\"search_placeholder\" defaultMessage=\"List like email:<EMAIL>, tel:17025550003...\"\n            description=\"Placeholder in contacts search field\">{\n            (search_placeholder) => <input className=\"search\" type=\"text\"\n              placeholder={search_placeholder}\n              value={this.state.search} onChange={this.handleSearchChange}\n              onKeyDown={this.handleKeyDown} required autoFocus />\n          }</FormattedMessage>\n          <a href=\"#\" onClick={this.handleClear}>\n            <i className=\"material-icons\">close</i>\n          </a>\n        </div>\n      </div>\n    );\n  }\n};\n", "// Create new topic and invite users or send an invite.\nimport React from 'react';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nimport Tinode from 'tinode-sdk';\n\nimport ContactList from '../widgets/contact-list.jsx';\nimport NewTopicById from '../widgets/new-topic-by-id.jsx';\nimport NewTopicGroup from '../widgets/new-topic-group.jsx';\nimport SearchContacts from '../widgets/search-contacts.jsx';\n\nimport HashNavigation from '../lib/navigation.js';\nimport { vcard } from '../lib/utils.js';\n\nconst messages = defineMessages({\n  search_for_contacts: {\n    id: \"search_for_contacts\",\n    defaultMessage: \"Use search to find contacts\",\n    description: \"Text shown in contacts view when user entered no search query.\"\n  },\n  search_no_results: {\n    id: \"search_no_results\",\n    defaultMessage: \"Search returned no results\",\n    description: \"Text shown in contacts view when query returned no results.\"\n  }\n});\n\nclass NewTopicView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      tabSelected: 'p2p',\n      searchQuery: null\n    };\n\n    this.handleTabClick = this.handleTabClick.bind(this);\n    this.handleSearchContacts = this.handleSearchContacts.bind(this);\n    this.handleContactSelected = this.handleContactSelected.bind(this);\n    this.handleNewGroupSubmit = this.handleNewGroupSubmit.bind(this);\n    this.handleGroupByID = this.handleGroupByID.bind(this);\n  }\n\n  componentDidMount() {\n    this.props.onInitFind();\n  }\n\n  handleTabClick(e) {\n    e.preventDefault();\n    HashNavigation.navigateTo(HashNavigation.addUrlParam(window.location.hash, 'tab', e.currentTarget.dataset.id));\n    this.setState({tabSelected: e.currentTarget.dataset.id});\n  }\n\n  handleSearchContacts(query) {\n    this.props.onSearchContacts(query);\n    this.setState({searchQuery: Tinode.isNullValue(query) ? null : query});\n  }\n\n  handleContactSelected(sel) {\n    if (this.state.tabSelected === 'p2p') {\n      HashNavigation.navigateTo(HashNavigation.removeUrlParam(window.location.hash, 'tab'));\n      this.props.onCreateTopic(sel, undefined);\n    }\n  }\n\n  handleNewGroupSubmit(name, dataUrl, priv, tags) {\n    HashNavigation.navigateTo(HashNavigation.removeUrlParam(window.location.hash, 'tab'));\n    this.props.onCreateTopic(undefined, vcard(name, dataUrl), priv, tags);\n  }\n\n  handleGroupByID(topicName) {\n    HashNavigation.navigateTo(HashNavigation.removeUrlParam(window.location.hash, 'tab'));\n    this.props.onCreateTopic(topicName);\n  }\n\n  render() {\n    const {formatMessage} = this.props.intl;\n    const no_contacts_placeholder = formatMessage(this.state.searchQuery ?\n      messages.search_no_results : messages.search_for_contacts);\n    return (\n      <div className=\"flex-column\">\n        <ul className=\"tabbar\">\n          <li className={this.state.tabSelected === \"p2p\" ? \"active\" : null}>\n            <a href=\"#\" data-id=\"p2p\" onClick={this.handleTabClick}>\n              <FormattedMessage id=\"tabtitle_find_user\" defaultMessage=\"find\"\n                description=\"Tab title Find\" />\n            </a>\n          </li>\n          <li className={this.state.tabSelected === \"grp\" ? \"active\" : null}>\n            <a href=\"#\" data-id=\"grp\" onClick={this.handleTabClick}>\n              <FormattedMessage id=\"tabtitle_new_group\" defaultMessage=\"new group\"\n                description=\"Tab title New Group\" />\n            </a>\n          </li>\n          <li className={this.state.tabSelected === \"byid\" ? \"active\" : null}>\n            <a href=\"#\" data-id=\"byid\" onClick={this.handleTabClick}>\n              <FormattedMessage id=\"tabtitle_group_by_id\" defaultMessage=\"by id\"\n                description=\"Tab title Find topic by ID\" />\n            </a>\n          </li>\n        </ul>\n        {this.state.tabSelected === 'grp' ?\n          <NewTopicGroup onSubmit={this.handleNewGroupSubmit} /> :\n          this.state.tabSelected === 'byid' ?\n          <NewTopicById\n            onSubmit={this.handleGroupByID}\n            onError={this.props.onError} /> :\n          <div className=\"flex-column\">\n            <SearchContacts\n              type=\"p2p\"\n              onSearchContacts={this.handleSearchContacts} />\n            <ContactList\n              contacts={this.props.searchResults}\n              myUserId={this.props.myUserId}\n              emptyListMessage={no_contacts_placeholder}\n              showOnline={false}\n              showUnread={false}\n              showContextMenu={false}\n              onTopicSelected={this.handleContactSelected} />\n          </div>}\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(NewTopicView);\n", "// A password reset form.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport VisiblePassword from '../widgets/visible-password.jsx';\n\nimport HashNavigation from '../lib/navigation.js';\n\nexport default class PasswordResetView extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      email: '',\n      password: ''\n    };\n\n    this.handleSubmit = this.handleSubmit.bind(this);\n    this.handleEmailChange = this.handleEmailChange.bind(this);\n    this.handlePasswordChange = this.handlePasswordChange.bind(this);\n  }\n\n  componentDidMount() {\n    let parsed = HashNavigation.parseUrlHash(window.location.hash);\n    this.setState({token: parsed.params.token, scheme: parsed.params.scheme});\n  }\n\n  handleSubmit(e) {\n    e.preventDefault();\n    if (this.state.token) {\n      this.props.onReset(this.state.scheme, this.state.password.trim(), this.state.token);\n    } else {\n      this.props.onRequest('email', this.state.email.trim());\n    }\n  }\n\n  handleEmailChange(e) {\n    this.setState({email: e.target.value});\n  }\n\n  handlePasswordChange(e) {\n    this.setState({password: e.target.value});\n  }\n\n  render() {\n    let reset = (this.state.token && this.state.scheme);\n    return (\n      <form id=\"password-reset-form\" onSubmit={this.handleSubmit}>\n        {reset ?\n          <FormattedMessage id=\"new_password_placeholder\" defaultMessage=\"Enter new password\"\n            description=\"Placeholder for entering new password\">{\n            (placeholder) => <VisiblePassword\n              placeholder={placeholder}\n              autoComplete=\"new-password\"\n              value={this.state.password}\n              required={true} autoFocus={true}\n              onChange={this.handlePasswordChange} />\n          }</FormattedMessage>\n          :\n          <>\n            <label htmlFor=\"inputEmail\">\n              <FormattedMessage id=\"label_reset_password\"\n                defaultMessage=\"Send a password reset email:\"\n                description=\"Label for password reset field\" />\n            </label>\n            <FormattedMessage id=\"credential_email_prompt\" defaultMessage=\"Your registration email\"\n              description=\"Placeholder for entering email\">{\n              (placeholder) => <input type=\"email\" id=\"inputEmail\"\n                placeholder={placeholder}\n                autoComplete=\"email\"\n                value={this.state.email}\n                onChange={this.handleEmailChange}\n                required autoFocus />\n            }</FormattedMessage>\n          </>\n        }\n        <div className=\"dialog-buttons\">\n          <button className=\"blue\" type=\"submit\">{reset ?\n            <FormattedMessage id=\"button_reset\" defaultMessage=\"Reset\" description=\"Button [Reset]\" /> :\n            <FormattedMessage id=\"button_send_request\" defaultMessage=\"Send request\"\n              description=\"Button [Send request]\" />\n          }</button>\n        </div>\n      </form>\n    );\n  }\n}\n", "import React from 'react';\n\nimport { KNOWN_HOSTS } from '../config.js';\n\n/* Combobox for selecting host name */\n\nexport default class HostSelector extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      hostName: props.serverAddress,\n      changed: false\n    };\n\n    this.handleHostNameChange = this.handleHostNameChange.bind(this);\n    this.handleEditingFinished = this.handleEditingFinished.bind(this);\n  }\n\n  handleHostNameChange(e) {\n    this.setState({hostName: e.target.value, changed: true});\n  }\n\n  handleEditingFinished() {\n    if (this.state.changed) {\n      this.setState({changed: false});\n      this.props.onServerAddressChange(this.state.hostName.trim());\n    }\n  }\n\n  render() {\n    var hostOptions = [];\n    for (let key in KNOWN_HOSTS) {\n      let item = KNOWN_HOSTS[key];\n      hostOptions.push(\n        <option key={item} value={item} />\n      );\n    }\n    return (\n      <div className=\"panel-form-row\">\n        <input type=\"search\" id=\"host-name\" placeholder={this.props.hostName} list=\"known-hosts\"\n          className=\"quoted\" value={this.state.hostName} onChange={this.handleHostNameChange}\n          onBlur={this.handleEditingFinished} required />\n        <datalist id=\"known-hosts\">\n          {hostOptions}\n        </datalist>\n      </div>\n    );\n  }\n}\n", "// Tinode config panel.\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport HostSelector from '../widgets/host-selector.jsx';\n\nexport default class SettingsView extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      transport: props.transport || 'def',\n      serverAddress: props.serverAddress,\n    };\n\n    this.handleSubmit = this.handleSubmit.bind(this);\n    this.handleTransportSelected = this.handleTransportSelected.bind(this);\n    this.handleServerAddressChange = this.handleServerAddressChange.bind(this);\n  }\n\n  handleSubmit(e) {\n    e.preventDefault();\n    this.props.onUpdate({\n      transport: this.state.transport,\n      serverAddress: this.state.serverAddress\n    });\n  }\n\n  handleTransportSelected(e) {\n    this.setState({transport: e.currentTarget.value});\n  }\n\n  handleServerAddressChange(name) {\n    this.setState({serverAddress: name});\n  }\n\n  render() {\n    const names = {def: \"default\", ws: \"websocket\", lp: \"long polling\"};\n    var transportOptions = [];\n    var instance = this;\n    ['def', 'ws', 'lp'].map(function(item) {\n      var id = 'transport-' + item;\n      var name = names[item];\n      transportOptions.push(\n        <li key={item}>\n          <input type=\"radio\" id={id} name=\"transport-select\" value={item}\n            checked={instance.state.transport === item}\n            onChange={instance.handleTransportSelected} />\n          <label htmlFor={id}>{name}</label>\n        </li>\n      );\n    });\n\n    return (\n      <form id=\"settings-form\" className=\"panel-form\" onSubmit={this.handleSubmit}>\n        <div className=\"panel-form-row\">\n          <label className=\"small\">\n            <FormattedMessage id=\"label_server_to_use\" defaultMessage=\"Server to use:\"\n              description=\"Label for server selector in SettingsView\" />\n          </label>\n        </div>\n        <HostSelector serverAddress={this.state.serverAddress}\n          onServerAddressChange={this.handleServerAddressChange} />\n        <div className=\"panel-form-row\">\n          <label className=\"small\">\n            <FormattedMessage id=\"label_wire_transport\" defaultMessage=\"Wire transport:\"\n              description=\"Label for wire transport selection in SettingsView\" />\n          </label>\n        </div>\n        <div className=\"panel-form-row\">\n          <ul className=\"quoted\">\n            {transportOptions}\n          </ul>\n        </div>\n        <div className=\"dialog-buttons\">\n          <button type=\"submit\" className=\"blue\">\n            <FormattedMessage id=\"button_update\" defaultMessage=\"Update\"\n              description=\"Button [Update]\" />\n          </button>\n        </div>\n      </form>\n    );\n  }\n};\n", "// ValidationView: panel for confirming credentials, like email or phone.\nimport React from 'react';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nconst messages = defineMessages({\n  phone: {\n    id: 'phone_dative',\n    defaultMessage: 'phone',\n    description: \"Dative case of 'phone', i.e. 'phone' in 'by phone'\",\n  },\n  email: {\n    id: 'email_dative',\n    defaultMessage: 'email',\n    description: \"Dative case of 'email', i.e. 'email' in 'by email'\",\n  }\n});\n\nclass ValidationView extends React.PureComponent {\n  constructor(props) {\n    super(props);\n\n    this.state = {\n      code: props.credCode || ''\n    };\n\n    this.handleChange = this.handleChange.bind(this);\n    this.handleKeyPress = this.handleKeyPress.bind(this);\n    this.handleSubmit = this.handleSubmit.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n  }\n\n  handleChange(e) {\n    this.setState({code: e.target.value});\n  }\n\n  handleKeyPress(e) {\n    if (e.key === 'Enter') {\n      this.handleSubmit(e);\n    } else if (e.key == 'Escape') {\n      this.handleCancel(e);\n    }\n  }\n\n  handleSubmit(e) {\n    e.preventDefault();\n    if (this.state.code && this.state.code.trim()) {\n      this.props.onSubmit(this.props.credMethod, this.state.code.trim());\n    }\n  }\n\n  handleCancel(e) {\n    e.preventDefault();\n    this.props.onCancel();\n  }\n\n\n  render() {\n    const { formatMessage } = this.props.intl;\n    const methods = {'email': formatMessage(messages.email), 'tel': formatMessage(messages.phone)};\n    let method = methods[this.props.credMethod] || this.props.credMethod;\n    return (\n      <div className=\"panel-form\">\n        <div className=\"panel-form-row\">\n          <label className=\"small\" htmlFor=\"enter-confirmation-code\">\n            <FormattedMessage id=\"enter_confirmation_code_prompt\"\n              defaultMessage=\"Enter confirmation code sent to you by {method}:\"\n              description=\"Request to enter confirmation code\"\n              values={{method: method}} />\n          </label>\n        </div>\n        <div className=\"panel-form-row\">\n        <FormattedMessage id=\"numeric_confirmation_code_prompt\"\n          defaultMessage=\"Numbers only\" description=\"Prompt for numeric conformation code\">{\n          (numbers_only) => <input type=\"text\" id=\"enter-confirmation-code\"\n            placeholder={numbers_only}\n            value={this.state.code} onChange={this.handleChange}\n            onKeyPress={this.handleKeyPress} required />\n        }</FormattedMessage>\n        </div>\n        <div className=\"dialog-buttons\">\n          <button className=\"blue\" onClick={this.handleSubmit}>\n            <FormattedMessage id=\"button_confirm\" defaultMessage=\"Confirm\" description=\"Button [Confirm]\" />\n          </button>\n          <button className=\"white\" onClick={this.handleCancel}>\n            <FormattedMessage id=\"button_cancel\" />\n          </button>\n        </div>\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(ValidationView);\n", "import React from 'react';\nimport { defineMessages, injectIntl } from 'react-intl';\n\nimport ErrorPanel from '../widgets/error-panel.jsx';\nimport LoadSpinner from '../widgets/load-spinner.jsx';\nimport SideNavbar from '../widgets/side-navbar.jsx';\n\nimport ContactsView from './contacts-view.jsx';\nimport CreateAccountView from './create-account-view.jsx';\nimport EditAccountView from './edit-account-view.jsx';\nimport AccGeneralView from './acc-general-view.jsx';\nimport AccNotificationsView from './acc-notifications-view.jsx';\nimport AccSecurityView from './acc-security-view.jsx';\nimport AccSupportView from './acc-support-view.jsx';\nimport LoginView from './login-view.jsx';\nimport NewTopicView from './new-topic-view.jsx';\nimport PasswordResetView from './password-reset-view.jsx';\nimport SettingsView from './settings-view.jsx';\nimport ValidationView from './validation-view.jsx';\n\n// Panel titles for translation.\nconst messages = defineMessages({\n  'login': {\n    id: 'sidepanel_title_login',\n    description: 'Sidepanel title for LoginView.',\n    defaultMessage: 'Sign In'\n  },\n  'register': {\n    id: 'sidepanel_title_register',\n    description: 'Sidepanel title for CreateAccountView.',\n    defaultMessage: 'Create Account'\n  },\n  'settings': {\n    id: 'sidepanel_title_settings',\n    description: 'Sidepanel title for SettingsView.',\n    defaultMessage: 'Settings'\n  },\n  'edit': {\n    id: 'sidepanel_title_account_settings',\n    description: 'Sidepanel title for EditAccountView.',\n    defaultMessage: 'Account Settings'\n  },\n  'general': {\n    id: 'sidepanel_title_acc_general',\n    description: 'Sidepanel title for AccGeneralView.',\n    defaultMessage: 'General'\n  },\n  'security': {\n    id: 'sidepanel_title_acc_security',\n    description: 'Sidepanel title for AccSecurityView.',\n    defaultMessage: 'Security'\n  },\n  'notif': {\n    id: 'sidepanel_title_acc_notifications',\n    description: 'Sidepanel title for AccNotificationsView.',\n    defaultMessage: 'Notifications'\n  },\n  'support': {\n    id: 'sidepanel_title_acc_support',\n    description: 'Sidepanel title for AccSupportView.',\n    defaultMessage: 'Support'\n  },\n  'newtpk': {\n    id: 'sidepanel_title_newtpk',\n    description: 'Sidepanel title for NewTopicView.',\n    defaultMessage: 'Start New Chat'\n  },\n  'cred': {\n    id: 'sidepanel_title_cred',\n    description: 'Sidepanel title for ValidationView.',\n    defaultMessage: 'Confirm Credentials'\n  },\n  'reset': {\n    id: 'sidepanel_title_reset',\n    description: 'Sidepanel title for PasswordResetView.',\n    defaultMessage: 'Reset Password'\n  },\n  'archive': {\n    id: 'sidepanel_title_archive',\n    description: 'Sidepanel title for ContactsView-Archive.',\n    defaultMessage: 'Archived Chats'\n  },\n  'blocked': {\n    id: 'sidepanel_title_blocked',\n    description: 'Sidepanel title for ContactsView-Blocked.',\n    defaultMessage: 'Blocked Chats'\n  }\n});\n\nclass SidepanelView extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.handleLoginRequested = this.handleLoginRequested.bind(this);\n    this.handleNewTopic = this.handleNewTopic.bind(this);\n  }\n\n  handleLoginRequested(login, password) {\n    this.props.onLoginRequest(login, password);\n  }\n\n  handleNewTopic() {\n    this.props.onBasicNavigate('newtpk');\n  }\n\n  render() {\n    const {formatMessage} = this.props.intl;\n    const view = this.props.state || (this.props.myUserId ? 'contacts' : 'login');\n\n    let title, avatar;\n    if (view == 'contacts') {\n      title = this.props.title;\n      avatar = this.props.avatar ? this.props.avatar : true;\n    } else {\n      title = formatMessage(messages[view]);\n      avatar = false;\n    }\n\n    let onCancel;\n    if (['login', 'contacts'].indexOf(view) == -1) {\n      onCancel = this.props.onCancel;\n    }\n\n    return (\n      <div id=\"sidepanel\" className={this.props.hideSelf ? 'nodisplay' : null}>\n        <SideNavbar\n          state={view}\n          title={title}\n          avatar={avatar}\n          myUserId={this.props.myUserId}\n          onSignUp={this.props.onSignUp}\n          onSettings={this.props.onSettings}\n          onNewTopic={this.handleNewTopic}\n          onCancel={onCancel} />\n\n        <ErrorPanel\n          level={this.props.errorLevel}\n          text={this.props.errorText}\n          action={this.props.errorAction}\n          actionText={this.props.errorActionText}\n          onClearError={this.props.onError} />\n\n        <LoadSpinner show={this.props.loadSpinnerVisible} />\n\n        {view === 'login' ?\n          <LoginView\n            login={this.props.login}\n            disabled={this.props.loginDisabled}\n            onLogin={this.handleLoginRequested} /> :\n\n          view === 'register' ?\n          <CreateAccountView\n            onCreateAccount={this.props.onCreateAccount}\n            onCancel={this.props.onCancel}\n            onError={this.props.onError} /> :\n\n          view === 'settings' ?\n          <SettingsView\n            transport={this.props.transport}\n            serverAddress={this.props.serverAddress}\n            onCancel={this.props.onCancel}\n            onUpdate={this.props.onGlobalSettings} /> :\n\n          view === 'edit' ?\n          <EditAccountView\n            tinode={this.props.tinode}\n            myUserId={this.props.myUserId}\n            onBasicNavigate={this.props.onBasicNavigate} /> :\n\n          view === 'general' ?\n          <AccGeneralView\n            tinode={this.props.tinode}\n            myUserId={this.props.myUserId}\n            onUpdateAccount={this.props.onUpdateAccount}\n            onUpdateTags={this.props.onUpdateAccountTags}\n            onCredAdd={this.props.onCredAdd}\n            onCredDelete={this.props.onCredDelete}\n            onCredConfirm={this.props.onCredConfirm}\n            onBasicNavigate={this.props.onBasicNavigate}\n            onError={this.props.onError} /> :\n\n          view === 'notif' ?\n          <AccNotificationsView\n            messageSounds={this.props.messageSounds}\n            desktopAlerts={this.props.desktopAlerts}\n            desktopAlertsEnabled={this.props.desktopAlertsEnabled}\n            incognitoMode={this.props.incognitoMode}\n            onTogglePushNotifications={this.props.onTogglePushNotifications}\n            onToggleMessageSounds={this.props.onToggleMessageSounds}\n            onToggleIncognitoMode={this.props.onToggleIncognitoMode} /> :\n\n          view === 'security' ?\n          <AccSecurityView\n            tinode={this.props.tinode}\n            onUpdateAccount={this.props.onUpdateAccount}\n            onLogout={this.props.onLogout}\n            onDeleteAccount={this.props.onDeleteAccount}\n            onShowAlert={this.props.onShowAlert}\n            onShowBlocked={this.props.onShowBlocked} /> :\n\n          view === 'support' ?\n          <AccSupportView\n            serverAddress={this.props.serverAddress}\n            serverVersion={this.props.serverVersion} /> :\n\n          (view === 'contacts' || view == 'archive' || view == 'blocked') ?\n          <ContactsView\n            tinode={this.props.tinode}\n            myUserId={this.props.myUserId}\n            connected={this.props.connected}\n            topicSelected={this.props.topicSelected}\n            archive={view == 'archive'}\n            blocked={view == 'blocked'}\n            chatList={this.props.chatList}\n            showContextMenu={this.props.showContextMenu}\n            onTopicSelected={this.props.onTopicSelected}\n            onShowArchive={this.props.onShowArchive} /> :\n\n          view === 'newtpk' ?\n          <NewTopicView\n            searchResults={this.props.searchResults}\n            onInitFind={this.props.onInitFind}\n            onSearchContacts={this.props.onSearchContacts}\n            onCreateTopic={this.props.onCreateTopic}\n            onError={this.props.onError} /> :\n\n          view === 'cred' ?\n          <ValidationView\n            credCode={this.props.credCode}\n            credMethod={this.props.credMethod}\n            onSubmit={this.props.onValidateCredentials}\n            onCancel={this.props.onCancel}\n            onError={this.props.onError} /> :\n\n          view === 'reset' ?\n          <PasswordResetView\n            onRequest={this.props.onPasswordResetRequest}\n            onReset={this.props.onResetPassword} /> :\n          null}\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(SidepanelView);\n", "import { KNOWN_HOSTS, DEFAULT_HOST } from '../config.js';\n\n// Detect server address from the URL\nexport function detectServerAddress() {\n  let host = DEFAULT_HOST;\n  if (typeof window.location == 'object') {\n    if (window.location.protocol == 'file:' || window.location.hostname == 'localhost') {\n      host = KNOWN_HOSTS.local;\n    } else if (window.location.hostname) {\n      host = window.location.hostname + (window.location.port ? ':' + window.location.port : '');\n    }\n  }\n  return host;\n}\n\n// Detect if the page is served over HTTPS.\nexport function isSecureConnection() {\n  if (typeof window.location == 'object') {\n    return window.location.protocol == 'https:';\n  }\n  return false;\n}\n\nexport function isLocalHost() {\n  if (typeof window.location == 'object') {\n    return window.location.hostname == 'localhost';\n  }\n  return false;\n}\n", "// The top-level class to hold all functionality together.\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport { FormattedMessage, defineMessages, injectIntl } from 'react-intl';\n\nimport * as firebase from 'firebase/app';\nimport 'firebase/messaging';\n\nimport Tinode from 'tinode-sdk';\n\nimport Alert from '../widgets/alert.jsx';\nimport ContextMenu from '../widgets/context-menu.jsx';\n\nimport InfoView from './info-view.jsx';\nimport MessagesView from './messages-view.jsx';\nimport SidepanelView from './sidepanel-view.jsx';\n\nimport { API_KEY, APP_NAME, DEFAULT_P2P_ACCESS_MODE, LOGGING_ENABLED,\n  MEDIA_BREAKPOINT, RECEIVED_DELAY } from '../config.js';\nimport { base64ReEncode, makeImageUrl } from '../lib/blob-helpers.js';\nimport { detectServerAddress, isLocalHost, isSecureConnection } from '../lib/host-name.js';\nimport LocalStorageUtil from '../lib/local-storage.js';\nimport HashNavigation from '../lib/navigation.js';\nimport { secondsToTime } from '../lib/strformat.js'\nimport { updateFavicon } from '../lib/utils.js';\n\n// Sound to play on message received.\nconst POP_SOUND = new Audio('audio/msg.mp3');\n\nconst messages = defineMessages({\n  reconnect_countdown: {\n    id: 'reconnect_countdown',\n    defaultMessage: 'Disconnected. Reconnecting in {seconds}…',\n    description: 'Message shown when an app update is available.'\n  },\n  reconnect_now: {\n    id: 'reconnect_now',\n    defaultMessage: 'Try now',\n    description: 'Prompt for reconnecting now'\n  },\n  push_init_failed: {\n    id: 'push_init_failed',\n    defaultMessage: 'Failed to initialize push notifications',\n    description: 'Error message when push notifications have failed to initialize.'\n  },\n  invalid_security_token: {\n    id: 'invalid_security_token',\n    defaultMessage: 'Invalid security token',\n    description: 'Error message when resetting password.'\n  },\n  no_connection: {\n    id: 'no_connection',\n    defaultMessage: 'No connection',\n    description: 'Warning that the user is offline.'\n  },\n  code_doesnot_match: {\n    id: 'code_doesnot_match',\n    defaultMessage: 'Code does not match',\n    description: 'Error message when the credential validation code is incorrect.'\n  }\n});\n\nclass TinodeWeb extends React.Component {\n  constructor(props) {\n    super(props);\n\n    this.state = this.getBlankState();\n\n    this.handleResize = this.handleResize.bind(this);\n    this.handleHashRoute = this.handleHashRoute.bind(this);\n    this.handleOnline = this.handleOnline.bind(this);\n    this.checkForAppUpdate = this.checkForAppUpdate.bind(this);\n    this.handleVisibilityEvent = this.handleVisibilityEvent.bind(this);\n    this.handleError = this.handleError.bind(this);\n    this.handleLoginRequest = this.handleLoginRequest.bind(this);\n    this.handleConnected = this.handleConnected.bind(this);\n    this.handleAutoreconnectIteration = this.handleAutoreconnectIteration.bind(this);\n    this.doLogin = this.doLogin.bind(this);\n    this.handleCredentialsRequest = this.handleCredentialsRequest.bind(this);\n    this.handleLoginSuccessful = this.handleLoginSuccessful.bind(this);\n    this.handleDisconnect = this.handleDisconnect.bind(this);\n    this.tnMeMetaDesc = this.tnMeMetaDesc.bind(this);\n    this.tnMeContactUpdate = this.tnMeContactUpdate.bind(this);\n    this.tnMeSubsUpdated = this.tnMeSubsUpdated.bind(this);\n    this.resetContactList = this.resetContactList.bind(this);\n    this.tnData = this.tnData.bind(this);\n    this.tnInitFind = this.tnInitFind.bind(this);\n    this.tnFndSubsUpdated = this.tnFndSubsUpdated.bind(this);\n    this.handleSearchContacts = this.handleSearchContacts.bind(this);\n    this.handleTopicSelected = this.handleTopicSelected.bind(this);\n    this.handleHideMessagesView = this.handleHideMessagesView.bind(this);\n    this.handleSendMessage = this.handleSendMessage.bind(this);\n    this.handleNewChatInvitation = this.handleNewChatInvitation.bind(this);\n    this.handleNewAccount = this.handleNewAccount.bind(this);\n    this.handleNewAccountRequest = this.handleNewAccountRequest.bind(this);\n    this.handleUpdateAccountRequest = this.handleUpdateAccountRequest.bind(this);\n    this.handleUpdateAccountTagsRequest = this.handleUpdateAccountTagsRequest.bind(this);\n    this.handleToggleIncognitoMode = this.handleToggleIncognitoMode.bind(this);\n    this.handleSettings = this.handleSettings.bind(this);\n    this.handleGlobalSettings = this.handleGlobalSettings.bind(this);\n    this.handleShowArchive = this.handleShowArchive.bind(this);\n    this.handleShowBlocked = this.handleShowBlocked.bind(this);\n    this.handleToggleMessageSounds = this.handleToggleMessageSounds.bind(this);\n    this.handleCredAdd = this.handleCredAdd.bind(this);\n    this.handleCredDelete = this.handleCredDelete.bind(this);\n    this.handleCredConfirm = this.handleCredConfirm.bind(this);\n    this.initDesktopAlerts = this.initDesktopAlerts.bind(this);\n    this.togglePushToken = this.togglePushToken.bind(this);\n    this.requestPushToken = this.requestPushToken.bind(this);\n    this.handleSidepanelCancel = this.handleSidepanelCancel.bind(this);\n    this.handleNewTopicRequest = this.handleNewTopicRequest.bind(this);\n    this.handleNewTopicCreated = this.handleNewTopicCreated.bind(this);\n    this.handleTopicUpdateRequest = this.handleTopicUpdateRequest.bind(this);\n    this.handleChangePermissions = this.handleChangePermissions.bind(this);\n    this.handleTagsUpdated = this.handleTagsUpdated.bind(this);\n    this.handleLogout = this.handleLogout.bind(this);\n    this.handleDeleteAccount = this.handleDeleteAccount.bind(this);\n    this.handleDeleteMessagesRequest = this.handleDeleteMessagesRequest.bind(this);\n    this.handleLeaveUnsubRequest = this.handleLeaveUnsubRequest.bind(this);\n    this.handleBlockTopicRequest = this.handleBlockTopicRequest.bind(this);\n    this.handleReportTopic = this.handleReportTopic.bind(this);\n    this.handleShowContextMenu = this.handleShowContextMenu.bind(this);\n    this.defaultTopicContextMenu = this.defaultTopicContextMenu.bind(this);\n    this.handleHideContextMenu = this.handleHideContextMenu.bind(this);\n    this.handleShowAlert = this.handleShowAlert.bind(this);\n    this.handleShowInfoView = this.handleShowInfoView.bind(this);\n    this.handleHideInfoView = this.handleHideInfoView.bind(this);\n    this.handleMemberUpdateRequest = this.handleMemberUpdateRequest.bind(this);\n    this.handleValidateCredentialsRequest = this.handleValidateCredentialsRequest.bind(this);\n    this.handlePasswordResetRequest = this.handlePasswordResetRequest.bind(this);\n    this.handleResetPassword = this.handleResetPassword.bind(this);\n    this.handleContextMenuAction = this.handleContextMenuAction.bind(this);\n  }\n\n  getBlankState() {\n    const settings = LocalStorageUtil.getObject('settings') || {};\n\n    return {\n      connected: false,\n      // Connected and subscribed to 'me'\n      ready: false,\n      // Try to re-login on new connection.\n      autoLogin: false,\n      transport: settings.transport || null,\n      serverAddress: settings.serverAddress || detectServerAddress(),\n      serverVersion: \"no connection\",\n      // \"On\" is the default, so saving the \"off\" state.\n      messageSounds: !settings.messageSoundsOff,\n      incognitoMode: false,\n      desktopAlerts: settings.desktopAlerts,\n      desktopAlertsEnabled: (isSecureConnection() || isLocalHost()) &&\n        (typeof firebase != 'undefined') && (typeof navigator != 'undefined') &&\n        (typeof FIREBASE_INIT != 'undefined'),\n      firebaseToken: LocalStorageUtil.getObject('firebase-token'),\n\n      applicationVisible: !document.hidden,\n\n      errorText: '',\n      errorLevel: null,\n      errorAction: undefined,\n      errorActionText: null,\n\n      sidePanelSelected: 'login',\n      sidePanelTitle: null,\n      sidePanelAvatar: null,\n      loadSpinnerVisible: false,\n\n      login: '',\n      password: '',\n      myUserId: null,\n      liveConnection: navigator.onLine,\n      topicSelected: '',\n      topicSelectedOnline: false,\n      topicSelectedAcs: null,\n      newTopicParams: null,\n      loginDisabled: false,\n      displayMobile: (window.innerWidth <= MEDIA_BREAKPOINT),\n      showInfoPanel: false,\n      mobilePanel: 'sidepanel',\n\n      contextMenuVisible: false,\n      contextMenuBounds: null,\n      contextMenuClickAt: null,\n      contextMenuParams: null,\n      contextMenuItems: [],\n\n      // Modal alert dialog.\n      alertVisible: false,\n      alertParams: {},\n\n      // Chats as shown in the ContactsView\n      chatList: [],\n      // Contacts returned by a search query.\n      searchResults: [],\n      // Merged results of a search query and p2p chats.\n      searchableContacts: [],\n      credMethod: undefined,\n      credCode: undefined\n    };\n  }\n\n  componentDidMount() {\n    window.addEventListener('resize', this.handleResize);\n    window.addEventListener('online', (e) => { this.handleOnline(true); });\n    window.addEventListener('offline', (e) => { this.handleOnline(false); });\n    window.addEventListener('hashchange', this.handleHashRoute);\n    // Window/tab visible or invisible for pausing timers.\n    document.addEventListener('visibilitychange', this.handleVisibilityEvent);\n\n    this.setState({\n      viewportWidth: document.documentElement.clientWidth,\n      viewportHeight: document.documentElement.clientHeight\n    });\n\n    const {formatMessage, locale} = this.props.intl;\n    this.tinode = TinodeWeb.tnSetup(this.state.serverAddress, this.state.transport, locale);\n    this.tinode.onConnect = this.handleConnected;\n    this.tinode.onDisconnect = this.handleDisconnect;\n    this.tinode.onAutoreconnectIteration = this.handleAutoreconnectIteration;\n\n    // Initialize desktop alerts.\n    if (this.state.desktopAlertsEnabled) {\n      try {\n        this.fbPush = firebase.initializeApp(FIREBASE_INIT, APP_NAME).messaging();\n        this.fbPush.usePublicVapidKey(FIREBASE_INIT.messagingVapidKey);\n        navigator.serviceWorker.register('/service-worker.js').then((reg) => {\n          this.checkForAppUpdate(reg);\n          this.fbPush.useServiceWorker(reg);\n          reg.active.postMessage(JSON.stringify({locale: locale}));\n          this.initDesktopAlerts();\n          if (this.state.desktopAlerts) {\n            if (!this.state.firebaseToken) {\n              this.togglePushToken(true);\n            } else {\n              this.tinode.setDeviceToken(this.state.firebaseToken, true);\n            }\n          }\n        }).catch((err) => {\n          // registration failed :(\n          console.log(\"Failed to register service worker:\", err);\n        });\n      } catch (err) {\n        this.handleError(formatMessage({id: 'push_init_failed'}), 'err');\n        console.log(\"Failed to initialize push notifications\", err);\n        this.setState({desktopAlertsEnabled: false});\n      }\n    }\n\n    const token = LocalStorageUtil.getObject('keep-logged-in') ?\n      LocalStorageUtil.getObject('auth-token') : undefined;\n\n    const parsedNav = HashNavigation.parseUrlHash(window.location.hash);\n    if (token) {\n      this.setState({autoLogin: true});\n\n      // When reading from storage, date is returned as string.\n      token.expires = new Date(token.expires);\n      this.tinode.setAuthToken(token);\n      this.tinode.connect().catch((err) => {\n        // Socket error\n        this.handleError(err.message, 'err');\n      });\n      delete parsedNav.params.info;\n      delete parsedNav.params.tab;\n      parsedNav.path[0] = '';\n      HashNavigation.navigateTo(HashNavigation.composeUrlHash(parsedNav.path, parsedNav.params));\n    } else if (!parsedNav.params.token) {\n      HashNavigation.navigateTo('');\n    }\n\n    this.readTimer = null;\n    this.readTimerCallback = null;\n\n    this.handleHashRoute();\n  }\n\n  componentWillUnmount() {\n    window.removeEventListener('resize', this.handleResize);\n    window.removeEventListener('hashchange', this.handleHashRoute);\n    document.removeEventListener('visibilitychange', this.handleVisibilityEvent);\n  }\n\n  // Setup transport (usually websocket) and server address. This will terminate connection with the server.\n  static tnSetup(serverAddress, transport, locale) {\n    const tinode = new Tinode(APP_NAME, serverAddress, API_KEY, transport, isSecureConnection());\n    tinode.setHumanLanguage(locale);\n    tinode.enableLogging(LOGGING_ENABLED, true);\n    return tinode;\n  }\n\n  handleResize() {\n    const mobile = document.documentElement.clientWidth <= MEDIA_BREAKPOINT;\n    this.setState({\n      viewportWidth: document.documentElement.clientWidth,\n      viewportHeight: document.documentElement.clientHeight\n    });\n    if (this.state.displayMobile != mobile) {\n      this.setState({displayMobile: mobile});\n    }\n  }\n\n  // Check if a newer version of TinodeWeb app is available at the server.\n  checkForAppUpdate(reg) {\n    reg.onupdatefound = () => {\n      const installingWorker = reg.installing;\n      installingWorker.onstatechange = () => {\n        if (installingWorker.state == 'installed' && navigator.serviceWorker.controller) {\n          const msg = <>\n            <FormattedMessage id=\"update_available\"\n              defaultMessage=\"Update available.\"\n              description=\"Message shown when an app update is available.\" /> <a href=\"\">\n              <FormattedMessage id=\"reload_update\"\n                defaultMessage=\"Reload\"\n                description=\"Call to action to reload application when update is available.\" />\n            </a>.</>;\n          this.handleError(msg, 'info');\n        }\n      }\n    }\n  }\n\n  // Handle for hashchange event: display appropriate panels.\n  handleHashRoute() {\n    const hash = HashNavigation.parseUrlHash(window.location.hash);\n    if (hash.path && hash.path.length > 0) {\n      // Left-side panel selector.\n      if (['register','settings','edit','notif','security','support','general',\n          'cred','reset','newtpk','archive','blocked','contacts',''].includes(hash.path[0])) {\n        this.setState({sidePanelSelected: hash.path[0]});\n      } else {\n        console.log(\"Unknown sidepanel view\", hash.path[0]);\n      }\n\n      // Topic for MessagesView selector.\n      if (hash.path.length > 1 && hash.path[1] != this.state.topicSelected) {\n        this.setState({\n          topicSelected: Tinode.topicType(hash.path[1]) ? hash.path[1] : null\n        });\n      }\n    } else {\n      // Empty hashpath\n      this.setState({sidePanelSelected: ''});\n    }\n\n    // Save validation credentials, if available.\n    if (hash.params.method) {\n      this.setState({ credMethod: hash.params.method });\n    }\n    if (hash.params.code) {\n      this.setState({ credCode: hash.params.code });\n    }\n\n    // Additional parameters of panels.\n    this.setState({\n      showInfoPanel: hash.params.info,\n      newTopicTabSelected: hash.params.tab\n    });\n  }\n\n  handleOnline(online) {\n    if (online) {\n      this.handleError();\n    } else {\n      this.handleError(this.props.intl.formatMessage({id: 'no_connection'}), 'warn');\n    }\n    this.setState({liveConnection: online});\n  }\n\n  handleVisibilityEvent() {\n    this.setState({applicationVisible: !document.hidden});\n  }\n\n  handleError(err, level, action, actionText) {\n    this.setState({errorText: err, errorLevel: level, errorAction: action, errorActionText: actionText});\n  }\n\n  // User clicked Login button in the side panel.\n  handleLoginRequest(login, password) {\n    this.setState({\n      loginDisabled: true,\n      login: login,\n      password: password,\n      loadSpinnerVisible: true,\n      autoLogin: true\n    });\n    this.handleError('', null);\n\n    if (this.tinode.isConnected()) {\n      this.doLogin(login, password, {meth: this.state.credMethod, resp: this.state.credCode});\n    } else {\n      this.tinode.connect().catch((err) => {\n        // Socket error\n        this.setState({loginDisabled: false, autoLogin: false, loadSpinnerVisible: false});\n        this.handleError(err.message, 'err');\n      });\n    }\n  }\n\n  // Connection succeeded.\n  handleConnected() {\n    const params = this.tinode.getServerInfo();\n    this.setState({\n      serverVersion: params.ver + ' ' + (params.build ? params.build : 'none')\n    });\n\n    if (this.state.autoLogin) {\n      this.doLogin(this.state.login, this.state.password, {meth: this.state.credMethod, resp: this.state.credCode});\n    }\n  }\n\n  // Called for each auto-reconnect iteration.\n  handleAutoreconnectIteration(sec, prom) {\n    clearInterval(this.reconnectCountdown);\n\n    if (sec < 0) {\n      // Clear error\n      this.handleError();\n      return;\n    }\n\n    if (prom) {\n      // Reconnecting now\n      prom.then(() => {\n        // Reconnected: clear error\n        this.handleError();\n      }).catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n      return;\n    }\n\n    const {formatMessage} = this.props.intl;\n    let count = sec / 1000;\n    count = count | count;\n    this.reconnectCountdown = setInterval(() => {\n      const timeLeft = (count > 99) ? secondsToTime(count) : count;\n      this.handleError(\n        formatMessage(messages.reconnect_countdown, {seconds: timeLeft}),\n        'warn',\n        () => {\n          clearInterval(this.reconnectCountdown);\n          this.tinode.reconnect();\n        },\n        formatMessage(messages.reconnect_now)\n      );\n      count -= 1;\n    }, 1000);\n  }\n\n  // Connection lost\n  handleDisconnect(err) {\n    this.setState({\n      connected: false,\n      ready: false,\n      topicSelectedOnline: false,\n      errorText: err && err.message ? err.message : \"Disconnected\",\n      errorLevel: err && err.message ? 'err' : 'warn',\n      loginDisabled: false,\n      contextMenuVisible: false,\n      serverVersion: \"no connection\"\n    });\n  }\n\n  doLogin(login, password, cred) {\n    if (this.tinode.isAuthenticated()) {\n      // Already logged in. Go to default screen.\n      HashNavigation.navigateTo('');\n      return;\n    }\n    // Sanitize and package credentail.\n    cred = Tinode.credential(cred);\n    // Try to login with login/password. If they are not available, try token. If no token, ask for login/password.\n    let promise = null;\n    const token = this.tinode.getAuthToken();\n    if (login && password) {\n      this.setState({password: null});\n      promise = this.tinode.loginBasic(login, password, cred);\n    } else if (token) {\n      promise = this.tinode.loginToken(token.token, cred);\n    }\n\n    if (promise) {\n      promise.then((ctrl) => {\n        if (ctrl.code >= 300 && ctrl.text === 'validate credentials') {\n          this.setState({loadSpinnerVisible: false});\n          if (cred) {\n            this.handleError(this.props.intl.formatMessage({id: 'code_doesnot_match'}), 'warn');\n          }\n          this.handleCredentialsRequest(ctrl.params);\n        } else {\n          this.handleLoginSuccessful();\n        }\n      }).catch((err) => {\n        // Login failed, report error.\n        this.setState({\n          loginDisabled: false,\n          credMethod: undefined,\n          credCode: undefined,\n          loadSpinnerVisible: false,\n          autoLogin: false\n        });\n        this.handleError(err.message, 'err');\n        localStorage.removeItem('auth-token');\n        HashNavigation.navigateTo('');\n      });\n    } else {\n      // No login credentials provided.\n      // Make sure we are on the login page.\n      HashNavigation.navigateTo('');\n      this.setState({loginDisabled: false});\n    }\n  }\n\n  handleCredentialsRequest(params) {\n    const parsed = HashNavigation.parseUrlHash(window.location.hash);\n    parsed.path[0] = 'cred';\n    parsed.params['method'] = params.cred[0];\n    HashNavigation.navigateTo(HashNavigation.composeUrlHash(parsed.path, parsed.params));\n  }\n\n  handleLoginSuccessful() {\n    this.handleError();\n\n    // Refresh authentication token.\n    if (LocalStorageUtil.getObject('keep-logged-in')) {\n      LocalStorageUtil.setObject('auth-token', this.tinode.getAuthToken());\n    }\n    // Logged in fine, subscribe to 'me' attaching callbacks from the contacts view.\n    const me = this.tinode.getMeTopic();\n    me.onMetaDesc = this.tnMeMetaDesc;\n    me.onContactUpdate = this.tnMeContactUpdate;\n    me.onSubsUpdated = this.tnMeSubsUpdated;\n    this.setState({\n      connected: true,\n      credMethod: undefined,\n      credCode: undefined,\n      myUserId: this.tinode.getCurrentUserID(),\n      autoLogin: true,\n    });\n    // Subscribe, fetch topic desc, the list of subscriptions. Messages are not fetched.\n    me.subscribe(\n      me.startMetaQuery().\n        withLaterSub().\n        withDesc().\n        withTags().\n        withCred().\n        build()\n      ).catch((err) => {\n        this.tinode.disconnect();\n        localStorage.removeItem('auth-token');\n        this.handleError(err.message, 'err');\n        HashNavigation.navigateTo('');\n      }).finally(() => {\n        this.setState({loadSpinnerVisible: false});\n      });\n    HashNavigation.navigateTo(HashNavigation.setUrlSidePanel(window.location.hash, 'contacts'));\n  }\n\n  tnMeMetaDesc(desc) {\n    if (desc) {\n      if (desc.public) {\n        this.setState({\n          sidePanelTitle: desc.public.fn,\n          sidePanelAvatar: makeImageUrl(desc.public.photo)\n        });\n      }\n      if (desc.acs) {\n        this.setState({\n          incognitoMode: !desc.acs.isPresencer()\n        });\n      }\n    }\n  }\n\n  // Reactions to updates to the contact list.\n  tnMeContactUpdate(what, cont) {\n    if (what == 'on' || what == 'off') {\n      this.resetContactList();\n      if (this.state.topicSelected == cont.topic) {\n        this.setState({topicSelectedOnline: (what == 'on')});\n      }\n    } else if (what == 'read') {\n      this.resetContactList();\n    } else if (what == 'msg') {\n      // Check if the topic is archived. If so, don't play a sound.\n      const topic = this.tinode.getTopic(cont.topic);\n      const archived = topic && topic.isArchived();\n\n      // New message received. Maybe the message is from the current user, then unread is 0.\n      if (cont.unread > 0 && this.state.messageSounds && !archived) {\n        // Skip update if the topic is currently open, otherwise the badge will annoyingly flash.\n        if (document.hidden || this.state.topicSelected != cont.topic) {\n          POP_SOUND.play();\n        }\n      }\n      // Reorder contact list to use possibly updated 'touched'.\n      this.resetContactList();\n    } else if (what == 'recv') {\n      // Explicitly ignoring \"recv\" -- it causes no visible updates to contact list.\n    } else if (what == 'gone' || what == 'unsub') {\n      // Topic deleted or user unsubscribed. Remove topic from view.\n      // If the currently selected topic is gone, clear the selection.\n      if (this.state.topicSelected == cont.topic) {\n        this.handleTopicSelected(null);\n      }\n      // Redraw without the deleted topic.\n      this.resetContactList();\n    } else if (what == 'acs') {\n      // Permissions changed. If it's for the currently selected topic,\n      // update the views.\n      if (this.state.topicSelected == cont.topic) {\n        this.setState({topicSelectedAcs: cont.acs});\n      }\n    } else if (what == 'del') {\n      // TODO: messages deleted (hard or soft) -- update pill counter.\n    } else if (what == 'upd') {\n      // upd - handled by the SDK. Explicitly ignoring here.\n    } else {\n      // TODO(gene): handle other types of notifications:\n      // * ua -- user agent changes (maybe display a pictogram for mobile/desktop).\n      console.log(\"Unsupported (yet) presence update:\" + what + \" in: \" + cont.topic);\n    }\n  }\n\n  tnMeSubsUpdated(unused) {\n    this.resetContactList();\n  }\n\n  // Merge search results and contact list to create a single flat\n  // list of kown contacts for GroupManager to use.\n  static prepareSearchableContacts(chatList, foundContacts) {\n    const merged = {};\n\n    // For chatList topics merge only p2p topics and convert them to the\n    // same format as foundContacts.\n    for (const c of chatList) {\n      if (Tinode.topicType(c.topic) == 'p2p') {\n          merged[c.topic] = {\n            user: c.topic,\n            updated: c.updated,\n            public: c.public,\n            private: c.private,\n            acs: c.acs\n          };\n      }\n    }\n\n    // Add all foundCountacts if they have not been added already.\n    for (const c of foundContacts) {\n      if (!merged[c.user]) {\n        merged[c.user] = c;\n      }\n    }\n\n    return Object.values(merged);\n  }\n\n  resetContactList() {\n    const newState = {\n      chatList: []\n    };\n\n    if (!this.state.ready) {\n      newState.ready = true;\n    }\n\n    this.tinode.getMeTopic().contacts((c) => {\n      newState.chatList.push(c);\n      if (this.state.topicSelected == c.topic) {\n        newState.topicSelectedOnline = c.online;\n        newState.topicSelectedAcs = c.acs;\n      }\n    });\n    // Merge search results and chat list.\n    newState.searchableContacts = TinodeWeb.prepareSearchableContacts(newState.chatList, this.state.searchResults);\n    this.setState(newState);\n  }\n\n  // Sending \"received\" notifications\n  tnData(data) {\n    const topic = this.tinode.getTopic(data.topic);\n    if (topic.msgStatus(data) >= Tinode.MESSAGE_STATUS_SENT && data.from != this.state.myUserId) {\n      clearTimeout(this.receivedTimer);\n      this.receivedTimer = setTimeout(() => {\n        this.receivedTimer = undefined;\n        topic.noteRecv(data.seq);\n      }, RECEIVED_DELAY);\n    }\n  }\n\n  /* Fnd topic: find contacts by tokens */\n  tnInitFind() {\n    const fnd = this.tinode.getFndTopic();\n    fnd.onSubsUpdated = this.tnFndSubsUpdated;\n    if (fnd.isSubscribed()) {\n      this.tnFndSubsUpdated();\n    } else {\n      fnd.subscribe(fnd.startMetaQuery().withSub().build()).catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n    }\n  }\n\n  tnFndSubsUpdated() {\n    const foundContacts = [];\n    // Don't attempt to create P2P topics which already exist. Server will reject the duplicates.\n    this.tinode.getFndTopic().contacts((s) => {\n      foundContacts.push(s);\n    });\n    this.setState({\n      searchResults: foundContacts,\n      searchableContacts: TinodeWeb.prepareSearchableContacts(this.state.chatList, foundContacts)\n    });\n  }\n\n  /** Called when the user enters a contact into the contact search field in the NewAccount panel\n    @param query {Array} is an array of contacts to search for\n   */\n  handleSearchContacts(query) {\n    const fnd = this.tinode.getFndTopic();\n    fnd.setMeta({desc: {public: query}}).then((ctrl) => {\n      return fnd.getMeta(fnd.startMetaQuery().withSub().build());\n    }).catch((err) => {\n      this.handleError(err.message, 'err');\n    });\n  }\n\n  // User clicked on a contact in the side panel or deleted a contact.\n  handleTopicSelected(topicName, unused_index, online, acs) {\n    // Clear newTopicParams after use.\n    if (this.state.newTopicParams && this.state.newTopicParams._topicName != topicName) {\n      this.setState({\n        newTopicParams: null\n      });\n    }\n\n    if (topicName) {\n      this.setState({\n        errorText: '',\n        errorLevel: null,\n        mobilePanel: 'topic-view',\n        showInfoPanel: false\n      });\n      // Different contact selected.\n      if (this.state.topicSelected != topicName) {\n        this.setState({\n          topicSelectedOnline: online,\n          topicSelectedAcs: acs\n        });\n        HashNavigation.navigateTo(HashNavigation.setUrlTopic('', topicName));\n      }\n    } else {\n      // Currently selected contact deleted\n      this.setState({\n        errorText: '',\n        errorLevel: null,\n        mobilePanel: 'sidepanel',\n        topicSelectedOnline: false,\n        topicSelectedAcs: null,\n        showInfoPanel: false\n      });\n\n      HashNavigation.navigateTo(HashNavigation.setUrlTopic('', null));\n    }\n  }\n\n  // In mobile view user requested to show sidepanel\n  handleHideMessagesView() {\n    this.setState({\n      mobilePanel: 'sidepanel'\n    });\n    HashNavigation.navigateTo(HashNavigation.setUrlTopic(window.location.hash, null));\n  }\n\n  // User is sending a message, either plain text or a drafty object, possibly\n  // with attachments.\n  handleSendMessage(msg, promise, uploader) {\n    const topic = this.tinode.getTopic(this.state.topicSelected);\n\n    msg = topic.createMessage(msg, false);\n    // The uploader is used to show progress.\n    msg._uploader = uploader;\n\n    if (!topic.isSubscribed()) {\n      if (!promise) {\n        promise = Promise.resolve();\n      }\n      promise = promise.then(() => { return topic.subscribe(); });\n    }\n\n    if (promise) {\n      promise = promise.catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n    }\n\n    topic.publishDraft(msg, promise)\n      .then((ctrl) => {\n        if (topic.isArchived()) {\n          return topic.archive(false);\n        }\n      })\n      .catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n  }\n\n  handleNewChatInvitation(topicName, action) {\n    const topic = this.tinode.getTopic(topicName);\n    let response = null;\n    switch (action) {\n      case 'accept':\n        // Accept given permissions.\n        const mode = topic.getAccessMode().getGiven();\n        response = topic.setMeta({sub: {mode: mode}});\n        if (topic.getType() == 'p2p') {\n          // For P2P topics change 'given' permission of the peer too.\n          // In p2p topics the other user has the same name as the topic.\n          response = response.then((ctrl) => {\n            topic.setMeta({sub: {user: topicName, mode: mode}});\n          });\n        }\n        break;\n      case 'delete':\n        // Ignore invitation by deleting it.\n        response = topic.delTopic(true);\n        break;\n      case 'block':\n        // Ban the topic making futher invites impossible.\n        // Just self-ban.\n        const am = topic.getAccessMode().updateWant('-JP').getWant();\n        response = topic.setMeta({sub: {mode: am}}).then((ctrl) => {\n          return this.handleTopicSelected(null);\n        });\n        break;\n      default:\n        console.log(\"Unknown invitation action\", '\"' + action + '\"\"');\n    }\n\n    if (response != null) {\n      response.catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n    }\n  }\n\n  // User chose a Sign Up menu item.\n  handleNewAccount() {\n    this.handleError();\n\n    HashNavigation.navigateTo(HashNavigation.setUrlSidePanel(window.location.hash, 'register'));\n  }\n\n  // Actual registration of a new account.\n  handleNewAccountRequest(login_, password_, public_, cred_, tags_) {\n    // Clear old error, if any.\n    this.handleError();\n\n    this.tinode.connect(this.state.serverAddress)\n      .then(() => {\n        return this.tinode.createAccountBasic(login_, password_,\n          {public: public_, tags: tags_, cred: Tinode.credential(cred_)});\n      }).then((ctrl) => {\n        if (ctrl.code >= 300 && ctrl.text == 'validate credentials') {\n          this.handleCredentialsRequest(ctrl.params);\n        } else {\n          this.handleLoginSuccessful(this);\n        }\n      }).catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n  }\n\n  handleUpdateAccountRequest(password, pub, defacs) {\n    this.handleError();\n\n    if (pub || defacs) {\n      const params = {};\n      if (pub) {\n        params.public = pub;\n      }\n      if (defacs) {\n        params.defacs = defacs;\n      }\n      this.tinode.getMeTopic().setMeta({desc: params}).catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n    }\n    if (password) {\n      this.tinode.updateAccountBasic(null, this.tinode.getCurrentLogin(), password).catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n    }\n  }\n\n  handleToggleIncognitoMode(on) {\n    const me = this.tinode.getMeTopic();\n    const am = me.getAccessMode().updateWant(on ? '-P' : '+P').getWant();\n    me.setMeta({sub: {mode: am}}).catch((err) => {\n      this.handleError(err.message, 'err');\n    });\n  }\n\n  handleUpdateAccountTagsRequest(tags) {\n    this.tinode.getMeTopic().setMeta({tags: tags})\n      .catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n  }\n\n  // User chose Settings menu item.\n  handleSettings() {\n    this.handleError();\n\n    HashNavigation.navigateTo(HashNavigation.setUrlSidePanel(window.location.hash,\n      this.state.myUserId ? 'edit' : 'settings'));\n  }\n\n  // User updated global parameters.\n  handleGlobalSettings(settings) {\n    const serverAddress = settings.serverAddress || this.state.serverAddress;\n    const transport = settings.transport || this.state.transport;\n    if (this.tinode) {\n      this.tinode.onDisconnect = undefined;\n      this.tinode.disconnect();\n    }\n    this.tinode = TinodeWeb.tnSetup(serverAddress, transport, this.props.intl.locale);\n    this.tinode.onConnect = this.handleConnected;\n    this.tinode.onDisconnect = this.handleDisconnect;\n\n    this.setState({\n      serverAddress: serverAddress,\n      transport: transport\n    });\n    LocalStorageUtil.setObject('settings', {\n      serverAddress: serverAddress,\n      transport: transport\n    });\n\n    HashNavigation.navigateTo(HashNavigation.setUrlSidePanel(window.location.hash, ''));\n  }\n\n  // User chose 'Archived chats'.\n  handleShowArchive() {\n    HashNavigation.navigateTo(HashNavigation.setUrlSidePanel(window.location.hash,\n      this.state.myUserId ? 'archive' : ''));\n  }\n\n  // User viewes 'Blocked chats'.\n  handleShowBlocked() {\n    HashNavigation.navigateTo(HashNavigation.setUrlSidePanel(window.location.hash,\n      this.state.myUserId ? 'blocked' : ''));\n  }\n\n  // Initialize desktop alerts = push notifications.\n  initDesktopAlerts() {\n    // Google could not be bothered to mention that\n    // onTokenRefresh is never called.\n    this.fbPush.onTokenRefresh(() => {\n      this.requestPushToken();\n    });\n\n    this.fbPush.onMessage((payload) => {\n      // No need to do anything about it.\n      // All the magic happends in the service worker.\n    });\n  }\n\n  togglePushToken(enabled) {\n    if (enabled) {\n      if (!this.state.firebaseToken) {\n        this.fbPush.requestPermission().then(() => {\n          this.requestPushToken();\n        }).catch((err) => {\n          this.handleError(err.message, 'err');\n          this.setState({desktopAlerts: false, firebaseToken: null});\n          LocalStorageUtil.updateObject('settings', {desktopAlerts: false});\n          console.log(\"Failed to get permission to notify.\", err);\n        });\n      } else {\n        this.setState({desktopAlerts: true});\n        LocalStorageUtil.updateObject('settings', {desktopAlerts: true});\n      }\n    } else if (this.state.firebaseToken) {\n      this.fbPush.deleteToken(this.state.firebaseToken).catch((err) => {\n        console.log(\"Unable to delete token.\", err);\n      }).finally(() => {\n        LocalStorageUtil.updateObject('settings', {desktopAlerts: false});\n        localStorage.removeItem('firebase-token');\n        this.setState({desktopAlerts: false, firebaseToken: null});\n      });\n    } else {\n      this.setState({desktopAlerts: false, firebaseToken: null});\n      LocalStorageUtil.updateObject('settings', {desktopAlerts: false});\n    }\n  }\n\n  requestPushToken() {\n    this.fbPush.getToken().then((refreshedToken) => {\n      if (refreshedToken != this.state.firebaseToken) {\n        this.tinode.setDeviceToken(refreshedToken, true);\n        LocalStorageUtil.setObject('firebase-token', refreshedToken);\n      }\n      this.setState({firebaseToken: refreshedToken, desktopAlerts: true});\n      LocalStorageUtil.updateObject('settings', {desktopAlerts: true});\n    }).catch((err) => {\n      this.handleError(err.message, 'err');\n      console.log(\"Failed to retrieve firebase token\", err);\n    });\n  }\n\n  handleToggleMessageSounds(enabled) {\n    this.setState({messageSounds: enabled});\n    LocalStorageUtil.updateObject('settings', {\n      messageSoundsOff: !enabled\n    });\n  }\n\n  handleCredAdd(method, value) {\n    const me = this.tinode.getMeTopic();\n    me.setMeta({cred: {meth: method, val: value}}).catch((err) => {\n      this.handleError(err.message, 'err');\n    });\n  }\n\n  handleCredDelete(method, value) {\n    const me = this.tinode.getMeTopic();\n    me.delCredential(method, value).catch((err) => {\n      this.handleError(err.message, 'err');\n    });\n  }\n\n  handleCredConfirm(method, response) {\n    this.handleCredentialsRequest({cred: [method]});\n  }\n\n  // User clicked Cancel button in Setting or Sign Up panel.\n  handleSidepanelCancel() {\n    const parsed = HashNavigation.parseUrlHash(window.location.hash);\n    let path = '';\n    if (['security','support','general','notif'].includes(parsed.path[0])) {\n      path = 'edit';\n    } else if ('blocked' == parsed.path[0]) {\n      path = 'security';\n    } else if (this.state.myUserId) {\n      path = 'contacts';\n    }\n    parsed.path[0] = path;\n    if (parsed.params) {\n      delete parsed.params.code;\n      delete parsed.params.method;\n      delete parsed.params.tab;\n    }\n    HashNavigation.navigateTo(HashNavigation.composeUrlHash(parsed.path, parsed.params));\n    this.setState({errorText: '', errorLevel: null});\n  }\n\n  // Basic nagigator by hash value. No need to bind to this.\n  basicNavigator(hash) {\n    HashNavigation.navigateTo(HashNavigation.setUrlSidePanel(window.location.hash, hash));\n  }\n\n  // Request to start a new topic. New P2P topic requires peer's name.\n  handleNewTopicRequest(peerName, pub, priv, tags) {\n\n    const topicName = peerName || this.tinode.newGroupTopicName();\n    const params = {\n      _topicName: topicName,\n    };\n    if (peerName) {\n      // Because we are initialing the subscription, set 'want' to all permissions.\n      params.sub = {mode: DEFAULT_P2P_ACCESS_MODE};\n      // Give the other user all permissions too.\n      params.desc = {defacs: {auth: DEFAULT_P2P_ACCESS_MODE}};\n    } else {\n      params.desc = {public: pub, private: {comment: priv}};\n      params.tags = tags;\n    }\n    this.setState({newTopicParams: params}, () => {this.handleTopicSelected(topicName)});\n  }\n\n  // New topic was creted, here is the new topic name.\n  handleNewTopicCreated(oldName, newName) {\n    if (this.state.topicSelected == oldName && oldName != newName) {\n      // If the current URl contains the old topic name, replace it with new.\n      // Update the name of the selected topic first so the navigator doen't clear\n      // the state.\n      this.setState({topicSelected: newName}, () => {\n        HashNavigation.navigateTo(HashNavigation.setUrlTopic('', newName));\n      });\n    }\n  }\n\n  handleTopicUpdateRequest(topicName, pub, priv, permissions) {\n    const topic = this.tinode.getTopic(topicName);\n    if (topic) {\n      const params = {};\n      if (pub) {\n        params.public = pub;\n      }\n      if (priv) {\n        params.private = (priv === Tinode.DEL_CHAR) ?\n          Tinode.DEL_CHAR : {comment: priv};\n      }\n      if (permissions) {\n        params.defacs = permissions;\n      }\n      topic.setMeta({desc: params}).catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n    }\n  }\n\n  handleChangePermissions(topicName, mode, uid) {\n    const topic = this.tinode.getTopic(topicName);\n    if (topic) {\n      const am = topic.getAccessMode();\n      if (uid) {\n        am.updateGiven(mode);\n        mode = am.getGiven();\n      } else {\n        am.updateWant(mode);\n        mode = am.getWant();\n      }\n      topic.setMeta({sub: {user: uid, mode: mode}}).catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n    }\n  }\n\n  handleTagsUpdated(topicName, tags) {\n    const topic = this.tinode.getTopic(topicName);\n    if (topic) {\n      topic.setMeta({tags: tags}).catch((err) => {\n        this.handleError(err.message, 'err');\n      });\n    }\n  }\n\n  handleLogout() {\n    updateFavicon(0);\n\n    // Remove stored data.\n    localStorage.removeItem('auth-token');\n    localStorage.removeItem('firebase-token');\n    localStorage.removeItem('settings');\n    if (this.state.firebaseToken) {\n      this.fbPush.deleteToken(this.state.firebaseToken)\n    }\n\n    if (this.tinode) {\n      this.tinode.onDisconnect = undefined;\n      this.tinode.disconnect();\n    }\n    this.setState(this.getBlankState());\n    this.tinode = TinodeWeb.tnSetup(this.state.serverAddress, this.state.transport, this.props.intl.locale);\n    this.tinode.onConnect = this.handleConnected;\n    this.tinode.onDisconnect = this.handleDisconnect;\n    HashNavigation.navigateTo('');\n  }\n\n  handleDeleteAccount() {\n    this.tinode.delCurrentUser(true).then((ctrl) => {\n      this.handleLogout();\n    });\n  }\n\n  handleDeleteMessagesRequest(topicName) {\n    const topic = this.tinode.getTopic(topicName);\n    if (!topic) {\n      return;\n    }\n\n    // Request hard-delete all messages.\n    topic.delMessagesAll(true).catch((err) => {\n      this.handleError(err.message, 'err');\n    });\n  }\n\n  handleLeaveUnsubRequest(topicName) {\n    const topic = this.tinode.getTopic(topicName);\n    if (!topic) {\n      return;\n    }\n\n    topic.leave(true).then((ctrl) => {\n      // Hide MessagesView and InfoView panels.\n      HashNavigation.navigateTo(HashNavigation.setUrlTopic(window.location.hash, ''));\n    }).catch((err) => {\n      this.handleError(err.message, 'err');\n    });\n  }\n\n  handleBlockTopicRequest(topicName) {\n    const topic = this.tinode.getTopic(topicName);\n    if (!topic) {\n      return;\n    }\n\n    topic.updateMode(null, '-JP').then((ctrl) => {\n      // Hide MessagesView and InfoView panels.\n      HashNavigation.navigateTo(HashNavigation.setUrlTopic(window.location.hash, ''));\n    }).catch((err) => {\n      this.handleError(err.message, 'err');\n    });\n  }\n\n  handleReportTopic(topicName) {\n    const topic = this.tinode.getTopic(topicName);\n    if (!topic) {\n      return;\n    }\n\n    // Publish spam report.\n    this.tinode.publish(Tinode.TOPIC_SYS, Tinode.Drafty.attachJSON(null, {\n      'action': 'report',\n      'target': topicName\n    }));\n\n    // Remove J and P permissions.\n    topic.updateMode(null, '-JP').then((ctrl) => {\n      // Hide MessagesView and InfoView panels.\n      HashNavigation.navigateTo(HashNavigation.setUrlTopic(window.location.hash, ''));\n    }).catch((err) => {\n      this.handleError(err.message, 'err');\n    });\n  }\n\n  handleShowContextMenu(params, menuItems) {\n    this.setState({\n      contextMenuVisible: true,\n      contextMenuClickAt: {x: params.x, y: params.y},\n      contextMenuParams: params,\n      contextMenuItems: menuItems || this.defaultTopicContextMenu(params.topicName),\n      contextMenuBounds: ReactDOM.findDOMNode(this).getBoundingClientRect()\n    });\n  }\n\n  defaultTopicContextMenu(topicName) {\n    const topic = this.tinode.getTopic(topicName);\n\n    let muted = false, blocked = false, self_blocked = false, subscribed = false, deleter = false, archived = false;\n    if (topic) {\n      subscribed = topic.isSubscribed();\n      archived = topic.isArchived();\n\n      const acs = topic.getAccessMode();\n      if (acs) {\n        muted = acs.isMuted();\n        blocked = !acs.isJoiner();\n        self_blocked = !acs.isJoiner('want');\n        deleter = acs.isDeleter();\n      }\n    }\n\n    return [\n      subscribed ? {\n        title: this.props.intl.formatMessage({id: 'menu_item_info'}),\n        handler: this.handleShowInfoView\n      } : null,\n      subscribed ? 'messages_clear' : null,\n      subscribed && deleter ? 'messages_clear_hard' : null,\n      muted ? (blocked ? null : 'topic_unmute') : 'topic_mute',\n      self_blocked ? 'topic_unblock' : 'topic_block',\n      !archived ? 'topic_archive' : null,\n      'topic_delete'\n    ];\n  }\n\n  handleHideContextMenu() {\n    this.setState({\n      contextMenuVisible: false,\n      contextMenuClickAt: null,\n      contextMenuParams: null,\n      contextMenuBounds: null\n    });\n  }\n\n  handleContextMenuAction(action, promise, params) {\n    if (action == 'topic_archive') {\n      if (promise && params.topicName && params.topicName == this.state.topicSelected) {\n        promise.then(() => {\n          this.handleTopicSelected(null);\n        });\n      }\n    }\n  }\n\n  handleShowAlert(title, content, onConfirm, confirmText, onReject, rejectText) {\n    this.setState({\n      alertVisible: true,\n      alertParams: {\n        title: title,\n        content: content,\n        onConfirm: onConfirm,\n        confirm: confirmText,\n        onReject: onReject,\n        reject: rejectText\n      }\n    });\n  }\n\n  handleShowInfoView() {\n    HashNavigation.navigateTo(HashNavigation.addUrlParam(window.location.hash, 'info', true));\n    this.setState({showInfoPanel: true});\n  }\n\n  handleHideInfoView() {\n    HashNavigation.navigateTo(HashNavigation.removeUrlParam(window.location.hash, 'info'));\n    this.setState({showInfoPanel: false});\n  }\n\n  handleMemberUpdateRequest(topicName, added, removed) {\n    if (!topicName) {\n      return;\n    }\n\n    const topic = this.tinode.getTopic(topicName);\n    if (!topic) {\n      return;\n    }\n\n    if (added && added.length > 0) {\n      added.map((uid) => {\n        topic.invite(uid, null).catch((err) => {\n          this.handleError(err.message, 'err');\n        });\n      });\n    }\n\n    if (removed && removed.length > 0) {\n      removed.map((uid) => {\n        topic.delSubscription(uid).catch((err) => {\n          this.handleError(err.message, 'err');\n        });\n      });\n    }\n  }\n\n  handleValidateCredentialsRequest(cred, code) {\n    if (this.tinode.isAuthenticated()) {\n      const me = this.tinode.getMeTopic();\n      me.setMeta({cred: {meth: cred, resp: code}})\n        .then(() => {\n          HashNavigation.navigateTo('');\n        })\n        .catch((err) => {\n          this.handleError(err.message, 'err');\n        });\n    } else {\n      this.setState({credMethod: cred, credCode: code});\n      this.doLogin(null, null, {meth: cred, resp: code});\n    }\n  }\n\n  handlePasswordResetRequest(method, value) {\n    // If already connected, connnect() will return a resolved promise.\n    this.tinode.connect()\n      .then(() => {\n        return this.tinode.requestResetAuthSecret('basic', method, value);\n      })\n      .catch((err) => {\n        // Socket error\n        this.handleError(err.message, 'err');\n      });\n  }\n\n  handleResetPassword(scheme, newPassword, token) {\n    token = base64ReEncode(token);\n    if (!token) {\n      this.handleError(this.props.intl.formatMessage({id: 'invalid_security_token'}), 'err');\n    } else {\n      this.tinode.connect()\n        .then(() => {\n          return this.tinode.updateAccountBasic(null, null, newPassword, {token: token});\n        })\n        .catch((err) => {\n          // Socket error\n          this.handleError(err.message, 'err');\n        });\n    }\n  }\n\n  render() {\n    return (\n      <div id=\"app-container\">\n        {this.state.contextMenuVisible ?\n          <ContextMenu\n            tinode={this.tinode}\n            bounds={this.state.contextMenuBounds}\n            clickAt={this.state.contextMenuClickAt}\n            params={this.state.contextMenuParams}\n            items={this.state.contextMenuItems}\n            hide={this.handleHideContextMenu}\n            onShowAlert={this.handleShowAlert}\n            onAction={this.handleContextMenuAction}\n            onTopicRemoved={(topicName) => {\n              if (topicName == this.state.topicSelected) {\n                this.handleTopicSelected(null);\n              }\n            }}\n            onError={this.handleError} />\n          :\n          null\n        }\n        <Alert\n          visible={this.state.alertVisible}\n          title={this.state.alertParams.title}\n          content={this.state.alertParams.content}\n          onReject={this.state.alertParams.onReject ? (() => { this.setState({alertVisible: false}); }) : null}\n          reject={this.state.alertParams.reject}\n          onConfirm={() => { this.setState({alertVisible: false}); this.state.alertParams.onConfirm(); }}\n          confirm={this.state.alertParams.confirm}\n          />\n        <SidepanelView\n          tinode={this.tinode}\n          connected={this.state.connected}\n          displayMobile={this.state.displayMobile}\n          hideSelf={this.state.displayMobile && this.state.mobilePanel !== 'sidepanel'}\n          state={this.state.sidePanelSelected}\n          title={this.state.sidePanelTitle}\n          avatar={this.state.sidePanelAvatar}\n          login={this.state.login}\n          myUserId={this.state.myUserId}\n          loginDisabled={this.state.loginDisabled}\n          loadSpinnerVisible={this.state.loadSpinnerVisible}\n\n          errorText={this.state.errorText}\n          errorLevel={this.state.errorLevel}\n          errorAction={this.state.errorAction}\n          errorActionText={this.state.errorActionText}\n\n          topicSelected={this.state.topicSelected}\n          chatList={this.state.chatList}\n          credMethod={this.state.credMethod}\n          credCode={this.state.credCode}\n\n          transport={this.state.transport}\n          messageSounds={this.state.messageSounds}\n          desktopAlerts={this.state.desktopAlerts}\n          desktopAlertsEnabled={this.state.desktopAlertsEnabled}\n          incognitoMode={this.state.incognitoMode}\n          serverAddress={this.state.serverAddress}\n          serverVersion={this.state.serverVersion}\n\n          onGlobalSettings={this.handleGlobalSettings}\n          onSignUp={this.handleNewAccount}\n          onSettings={this.handleSettings}\n          onBasicNavigate={this.basicNavigator}\n          onLoginRequest={this.handleLoginRequest}\n          onCreateAccount={this.handleNewAccountRequest}\n          onUpdateAccount={this.handleUpdateAccountRequest}\n          onUpdateAccountTags={this.handleUpdateAccountTagsRequest}\n          onTogglePushNotifications={this.togglePushToken}\n          onToggleMessageSounds={this.handleToggleMessageSounds}\n          onToggleIncognitoMode={this.handleToggleIncognitoMode}\n          onCredAdd={this.handleCredAdd}\n          onCredDelete={this.handleCredDelete}\n          onCredConfirm={this.handleCredConfirm}\n          onTopicSelected={this.handleTopicSelected}\n          onCreateTopic={this.handleNewTopicRequest}\n          onLogout={this.handleLogout}\n          onDeleteAccount={this.handleDeleteAccount}\n          onShowAlert={this.handleShowAlert}\n          onCancel={this.handleSidepanelCancel}\n          onError={this.handleError}\n          onValidateCredentials={this.handleValidateCredentialsRequest}\n          onPasswordResetRequest={this.handlePasswordResetRequest}\n          onResetPassword={this.handleResetPassword}\n          onShowArchive={this.handleShowArchive}\n          onShowBlocked={this.handleShowBlocked}\n\n          onInitFind={this.tnInitFind}\n          searchResults={this.state.searchResults}\n          onSearchContacts={this.handleSearchContacts}\n\n          showContextMenu={this.handleShowContextMenu} />\n\n        <MessagesView\n          tinode={this.tinode}\n          connected={this.state.connected}\n          ready={this.state.ready}\n          online={this.state.topicSelectedOnline}\n          acs={this.state.topicSelectedAcs}\n          displayMobile={this.state.displayMobile}\n          viewportWidth={this.state.viewportWidth}\n          viewportHeight={this.state.viewportHeight}\n          hideSelf={this.state.displayMobile &&\n            (this.state.mobilePanel !== 'topic-view' || this.state.showInfoPanel)}\n          topic={this.state.topicSelected}\n          myUserId={this.state.myUserId}\n          serverVersion={this.state.serverVersion}\n          serverAddress={this.state.serverAddress}\n          applicationVisible={this.state.applicationVisible}\n\n          errorText={this.state.errorText}\n          errorLevel={this.state.errorLevel}\n          errorAction={this.state.errorAction}\n          errorActionText={this.state.errorActionText}\n\n          newTopicParams={this.state.newTopicParams}\n\n          onHideMessagesView={this.handleHideMessagesView}\n          onData={this.tnData}\n          onError={this.handleError}\n          onNewTopicCreated={this.handleNewTopicCreated}\n          showContextMenu={this.handleShowContextMenu}\n          onChangePermissions={this.handleChangePermissions}\n          onNewChat={this.handleNewChatInvitation}\n          sendMessage={this.handleSendMessage} />\n\n        {this.state.showInfoPanel ?\n          <InfoView\n            tinode={this.tinode}\n            connected={this.state.connected}\n            displayMobile={this.state.displayMobile}\n            topic={this.state.topicSelected}\n            searchableContacts={this.state.searchableContacts}\n            myUserId={this.state.myUserId}\n\n            errorText={this.state.errorText}\n            errorLevel={this.state.errorLevel}\n            errorAction={this.state.errorAction}\n            errorActionText={this.state.errorActionText}\n\n            onTopicDescUpdate={this.handleTopicUpdateRequest}\n            onCancel={this.handleHideInfoView}\n            onShowAlert={this.handleShowAlert}\n            onChangePermissions={this.handleChangePermissions}\n            onMemberUpdateRequest={this.handleMemberUpdateRequest}\n            onDeleteMessages={this.handleDeleteMessagesRequest}\n            onLeaveTopic={this.handleLeaveUnsubRequest}\n            onBlockTopic={this.handleBlockTopicRequest}\n            onReportTopic={this.handleReportTopic}\n            onAddMember={this.handleManageGroupMembers}\n            onTopicTagsUpdate={this.handleTagsUpdated}\n            onInitFind={this.tnInitFind}\n            onError={this.handleError}\n\n            showContextMenu={this.handleShowContextMenu}\n            />\n          :\n          null\n        }\n      </div>\n    );\n  }\n};\n\nexport default injectIntl(TinodeWeb);\n", "// Put all packages together.\n// Used to generate umd/index.prod.js\n\nimport React from 'react';\nimport ReactDOM from 'react-dom';\n\nimport { IntlProvider } from 'react-intl';\n\nimport allMessages from './messages.json';\nimport TinodeWeb from './views/tinode-web.jsx';\nimport HashNavigation from './lib/navigation.js';\n\n// Detect human language to use in the UI:\n//  Check parameters from URL hash #?hl=ru, then browser, then use 'en' as a fallback.\nconst { params } = HashNavigation.parseUrlHash(window.location.hash);\nconst language = (params && params.hl) ||\n  (navigator.languages && navigator.languages[0]) ||\n  navigator.language ||\n  navigator.userLanguage ||\n  'en';\n\n// Get the base language 'en' from a more specific 'en_GB' or 'en-US' as a partial fallback.\nconst baseLanguage = language.toLowerCase().split(/[-_]/)[0];\n\n// Try the full locale first, then the locale without the region code, fallback to 'en'.\nconst messages =\n  allMessages[language] ||\n  allMessages[baseLanguage] ||\n  allMessages.en;\n\nReactDOM.render(\n  <IntlProvider locale={language} messages={messages} textComponent={React.Fragment}>\n    <TinodeWeb />\n  </IntlProvider>,\n  document.getElementById('mountPoint')\n);\n"], "sourceRoot": ""}