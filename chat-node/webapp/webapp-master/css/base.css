* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: <PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  text-rendering: optimizeLegibility;
}

/* Basic elements defined */

html, body {
  height: 100%;
  width: 100%;
  font-size: 10pt; /* 13.33 px/rem */
  color: #666;
  overflow: hidden;
  background-color: silver;
  box-shadow: inset 0 10rem #3949AB; /* the deep-plue stripe accross the top */
}

tt {
  font-family: 'Roboto Mono', Courier, monospace;
}

a {
  color: #2196f3;
  text-decoration: none;
}
a:hover,
a:focus {
  color: #0a6ebd;
  text-decoration: underline;
}

a.red:hover,
a.red:focus {
  color: #F44336;
}

a.gray:hover,
a.gray:focus {
  color: #999;
}

a.flat-button {
  display: flex;
  align-items: center;
  margin: 0.15rem 0.35rem;
  height: 2em;
  line-height: 2em;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  white-space: nowrap;
  text-transform: uppercase;
}
a.flat-button:hover,
a.flat-button:focus {
  text-decoration: none;
}

form {
  font-size: 1.1rem;
  padding: 0.5rem 0.75rem;
  line-height: 1.5;
}

label {
  font-size: 1.1rem;
  margin-right: auto;
}

label.small {
  color: #1e88e5;
  font-size: 0.95rem;
}

img.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

/* Input fields and buttons */

button {
  display: inline-block;
  font-family: inherit;
  font-size: 1.05rem;
  height: 2.2rem;
  line-height: 2.2rem;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  border: 1px solid transparent;
  white-space: nowrap;
  padding-left: 1.2rem;
  padding-right: 1.2rem;
  border-radius: 4px;
  box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.25);
  text-transform: uppercase;
  min-width: 5rem;
  margin: 0 0.35rem 0 0.35rem;
}

button[disabled],
html input[disabled] {
  cursor: default;
  opacity: 0.65;
  box-shadow: none;
}

button.round {
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  padding: 0;
  margin: 0;
  line-height: 3.5rem;
  outline: none; /* otherwise a square outline is shown */
  box-shadow: 0 2px 0.5rem 0 rgba(0, 0, 0, 0.25);
  z-index: 2;
}

button.round.small {
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  box-shadow: 0 2px 0.35rem 0 rgba(0, 0, 0, 0.25);
}

button:focus, button:hover, button:active {
  color: #fff;
}
button:active {
  box-shadow: inset 0 3px 0.5rem rgba(0, 0, 0, 0.125);
}
button:active.round.small {
  box-shadow: inset 0 2px 0.35rem rgba(0, 0, 0, 0.125);
}

button.blue {
  background-color: #1e88e5; /* blue 600 */
  color: white;
}
button.blue:focus, button.blue:hover, button.blue:active {
  background-color: #1976d2;
}

button.blue {
  background-color: #1e88e5; /* blue 600 */
  color: white;
}
button.blue:focus, button.blue:hover, button.blue:active {
  background-color: #2962ff;
}

button.white {
  background-color: #FFF;
  color: #2196f3; /* blue 500 */
}
button.white:focus, button.white:hover, button.blue:active {
  background-color: #e3f2fd;
}

button.outline {
  background-color: transparent;
  color: #2196f3; /* blue 500 */
  border: 1px solid silver;
  box-shadow: none;
}
button.outline:focus, button.outline:hover, button.outline:active {
  background-color: #e8eaf6; /* indigo 50 */
}

textarea, input {
  font-family: inherit;
  font-size: 1.1rem;
  background: transparent;
  border: none;
  margin: 0.25rem 0;
  padding: 0 0.125rem;
  outline: none;
  color: #666;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea {
  height: 2.5rem;
  border-bottom: 1px solid #ccc;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.1s ease-in-out;
}

input[type="text"],
input[type="email"],
input[type="password"],
textarea {
  width: 100%;
}

textarea:focus,
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="search"]:focus {
  border-bottom: 1px solid #2196f3;
  box-shadow: 0 1px 0 rgba(33, 150, 243, 0.25);
}

textarea.invalid,
input[type="text"].invalid,
input[type="email"].invalid,
input[type="password"].invalid,
input[type="search"].invalid {
  border-bottom: 1px solid #e57373;
  box-shadow: 0 1px 0 rgba(229, 115, 115, 0.25);
}

input[type="text"]::placeholder,
input[type="email"]::placeholder,
input[type="password"]::placeholder,
input[type="search"]::placeholder,
textarea::placeholder {
  color: #bbb;
  font-weight: lighter;
}

input[type="text"].with-visibility,
input[type="password"].with-visibility {
  margin-right:-2rem;
  padding-right:2rem;
}

textarea {
  resize: none;
  padding-top: 0.45rem;
}
/* END of input and buttons */

/* BEGIN scrollbar styling.  */

/* Chrome & other webkit browsers */
::-webkit-scrollbar {
	width: 0.6rem;
}

::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.2);
	background-color: rgba(255,255,255,0.1);
}

::-webkit-scrollbar-thumb {
	background-color: rgba(0,0,0,0.3);
}

/* Firefox only */
* {
  scrollbar-width: thin;
  scrollbar-color: silver;
}
/* END of Scrollbar styling.  */

/* BEGIN Popup/dropdown menu */

ul.menu {
  position: absolute;
  list-style: none;
  width: 12rem;
  border-radius: 0.2rem;
  padding-top: 0.35rem;
  padding-bottom: 0.35rem;
  box-shadow: 0 1px 6px rgba(0, 0, 0, .45);
  background-color: white;
  z-index: 4;
}

ul.menu > li {
  padding: 0.75rem 0.75rem 0.75rem 1.25rem;
}

ul.menu > li:hover {
  background-color: #f5f5f5;
}

ul.menu > li.separator {
  padding: 0;
  margin: 2px;
  height: 1px;
  background-color: #ccc;
}

ul.menu > li.separator:hover {
  background-color: #ccc;
}

/* The down arrow head - trigger for context menu */
li .menuTrigger {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 1.5rem;
  height: 1.2rem;
  line-height: 1.2rem;
  border-radius: 0.6rem;
  text-align: center;
  opacity: 0;
}
li:hover .menuTrigger {
  opacity: 1.0;
}

li:hover .menuTrigger a {
  color: #757575;
}

li.left:hover .menuTrigger {
  -text-shadow: #c5e1a5 0 0 15px, #c5e1a5 0 0 5px;
  background-image: radial-gradient(#c5e1a5 40%, transparent 75%);
}
li.right:hover .menuTrigger {
  -text-shadow: #fafafa 0 0 15px, #fafafa 0 0 5px;
  background-image: radial-gradient(#fafafa 40%, transparent 75%);
}

/* END popup/dropdown menu */

/* Begin Alert: modal window with message and buttons. */
div.alert-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  -x-backdrop-filter: blur(4px);
  background: rgba(0,0,0,0.33);
  z-index: 3;
}
div.alert {
  background-color: white;
  box-shadow: 0 1px 6px rgba(0, 0, 0, .45);
  padding: 1rem;
  border-radius: 4px;
  margin: auto 2rem auto 2rem;
  width: content;
  min-width: 16rem;
  max-width: 24rem;
}
div.alert .title {
  color: #2196f3;
  font-size: 1.2rem;
  font-weight: 500;
  width: 100%;
  padding: 0 0 0.35rem 0;
}
div.alert .content {
  padding: 0.5rem 0 0.5rem 0;
}
/* END Alert */

/* Generic text colors */
.blue {
  color: #2196f3; /* blue 500 */
}

.red {
  color: #D32F2F; /* red 700 */
}

.amber {
  color: #FFC107; /* amber 500 */
}

.gray {
  color: #757575;
}

.light-gray {
  color: #bbb;
}

.large {
  font-size: 120%;
}

.small {
  font-size: 90%;
}

.float-right {
  margin-left: auto;
  float: right;
}

.hr {
  display: block;
  width: 100%;
  height: 0.45rem;
  min-height: 0.3rem;
  background-color: #f0f0f0;
  box-shadow: inset 0 0.25rem 0.25rem -0.25rem rgba(0,0,0,0.15),
    inset 0 -0.25rem 0.25rem -0.25rem rgba(0,0,0,0.15);
}

.hr.thin {
  height: 0.015rem;
}

/* Element which can take focus but shows no outline */
.group-focus {
  outline: none;
}

/* END generic text colors */

/* END of basic elements */

#mountPoint {
  height: 100%;
  padding-top: 1rem;
  padding-bottom: 3rem;
}

/* Top-level application layout style: sidepanel + main view */
#noscript-message {
  position: relative;
  display: flex;
  flex-direction: column;
  max-width: 30rem;
  overflow: hidden;
  margin-top: 5rem;
  margin-left: auto;
  margin-right: auto;
  border-radius: 0.25rem;
  box-shadow: 1px 2px 0.3rem gray;
  background-color: white;
}

#noscript-title {
  padding: 1rem;
  font-size: 120%;
  font-weight: bolder;
  background-color: #2196f3; /* blue 500 */
  color: #bbdefb; /* light-blue 100 */
}

#noscript-content {
  padding: 1rem;
}

#app-container {
  position: relative;
  display: flex;
  height: 100%;
  max-width: 80rem;
  overflow: hidden;
  margin-left: auto;
  margin-right: auto;
  border-radius: 0.25rem;
  box-shadow: 1px 2px 0.3rem gray;
  background-color: white;
}

#sidepanel {
  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 0.3;
  width: 24rem;
  min-width: 18rem;
  height: 100%;
}

#topic-view {
  position: relative;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

#info-view {
  display: flex;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 0.3;
  width: 24rem;
  min-width: 18rem;
  height: 100%;
}

@media (max-width: 960px) {
  #mountPoint {
    padding: 0;
  }
}

@media (max-width: 640px) {
  #app-container {
    width: 100%;
    border-radius: 0;
    box-shadow: none;
  }

  #sidepanel {
    width: 100%;
  }

  #info-view {
    width: 100%;
  }
}

/* Generic announcement text, centered vertically and horizontally */
.center-medium-text {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 1.4rem;
}

/* Class to alternatively hide sidepanel/topic-view/info-view */
.nodisplay {
  display: none!important;
}

/* Quoted div */
.quoted {
  margin-left: 1rem;
}

/* BEGIN InPlaceEdit styles */
.in-place-edit {
  display: inline-block;
  font-family: inherit;
  font-size: 1.1rem;
  height: 2.5rem;
  line-height: 2.25;
  margin: 0.25rem 0.125rem;
  width: 100%;
  vertical-align: baseline;
  border-bottom: 1px dashed #ddd;
  cursor: pointer;
}

.in-place-edit.disabled {
  border-bottom: none;
  cursor: default;
}

.in-place-edit.placeholder {
  color: #bbb;
}

.in-place-edit .content {
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden
}
/* END of InPlaceEdit styles */

table.permission-editor {
  width: 100%;
}

table.permission-editor td {
  margin: 0.35rem 0;
  font-size: 1.1rem;
  vertical-align: middle;
}

table.permission-editor td.checkbox {
  text-align: center;
  color: #999;
}

.permission-editor .material-icons {
  font-size: 1.1rem;
  line-height: 1.1rem;
}

.material-icons.clickable {
  cursor: pointer;
  border-bottom: none;
}

.clickable {
  border-bottom: 1px dashed #ddd;
  cursor: pointer;
}

.image-clickable {
  cursor: pointer;
}

.clean-clickable {
  cursor: pointer;
}


/* END of top-level layout */

/* Sidepanel styles */
.panel-title {
  font-size: 1.4rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.caption-panel {
  display: flex;
  align-items: center;
  flex: none;
  padding: 0.5rem;
  height: 4rem;
  white-space: nowrap;
  box-shadow: 0 1px 2px gray;
  overflow-x: hidden;
  z-index: 1; /* otherwise the shadow becomes invisible */
}

.dialog-buttons {
  display: flex;
  padding: 0.5rem;
  justify-content: flex-end;
}

.dialog-buttons button {
  margin: 0 0.5rem;
}

.avatar-box {
  position: relative;
  flex-grow: 0;
  flex-shrink: 0;
}

.bot-form {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 8rem;
  line-height: 2rem;
}
.bot-form div {
  min-height: 2rem;
  vertical-align: middle;
}
.bot-form button {
  display: inline-block;
  margin: 0.15rem;
  background-color: #eef;
  color: #2196f3; /* blue 500 */
  font-size: inherit;
  text-transform: none;
  height: 2rem;
  line-height: 2rem;
}
.bot-form > div > button {
  width: auto;
}
.bot-form button:focus,
.bot-form button:hover,
.bot-form button:active {
  outline: 0;
  background-color: #e3f2fd;
}

#side-caption-panel {
  background-color: #2196f3; /* blue 500 */
  color: #bbdefb; /* light-blue 100 */
}

#side-caption-panel a {
  color: #bbdefb; /* blue 100 */
  padding-left: 0.5rem;
  line-height: 1;
}

#side-caption-panel :hover {
  color: white;
}

#sidepanel-title {
  margin-right: auto;
  margin-left: 0.5rem;
}

#self-avatar {
  width: 3rem;
  min-width: 3rem;
  height: 3rem;
  flex: none;
  border-radius: 50%;
}

#self-avatar.avatar-box {
  font-size: 1.8rem;
}

/* Application settings */
#settings-form ul {
  list-style: none;
}

#settings-form li {
  vertical-align: middle;
}
#settings-form label {
  display: inline-block;
  padding-left: 0.5rem;
}

/* Input with a search icon on the left */
input.search {
  text-indent: 1.4rem;
}
i.search {
  position: absolute;
  left: .6rem;
}

/* Area which displays an error message */
.info-box {
  position: relative;
  display: none;
  overflow: hidden;
  padding: 1rem 1.5rem 1rem 0.5rem;
}
.info-box .cancel {
  position: absolute;
  right: 0.25rem;
  top: 0.5rem;
}

.info-box.error {
  display: flex;
  background-color: #ffcdd2;
  border-bottom: 1px solid #ffbfc8;
  color: #b71c1c;
}
.info-box.warning {
  display: flex;
  background-color: #fff9c4;
  border-bottom: 1px solid #fff59d;
  color: #6d4c41;
}
.info-box.info {
  display: flex;
  background-color: #e1f5fe;
  border-bottom: 1px solid #b3e5fc;
  color: #0288d1;
}
.info-box .cancel .material-icons {
  font-size: 1rem;
  color: #757575;
}
.info-box.error .cancel .material-icons {
  color: #b71c1c;
}
.info-box .icon {
  display: block;
  margin-right: 0.5rem;
}
.info-box .icon .material-icons {
  font-size: 1.5rem;
}
.info-box.warning .icon .material-icons {
  color: #fbc02d;
}
.info-box.error .icon .material-icons {
  color: #ef5350;
}
.info-box.info .icon .material-icons {
  color: #0288d1;
}
/* List of contacts */
.flex-column {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.flex-column.narrow {
  height: auto;
}

.scrollable-panel {
  overflow-y: auto;
  margin-top: 0.25rem;
  margin-bottom: auto;
  height: 100%;
}

.contact-box {
  list-style: none;
  overflow-x: hidden;
}

.contact-box > li {
  display: flex;
  position: relative;
  padding: 0.35rem 0.25rem 0.5rem 0.35rem;
  min-height: 3.5rem;
  flex-grow: 0;
  flex-shrink: 0;
  border-bottom: 1px solid #eee;
  border-collapse: collapse;
  overflow-x: hidden;
}
.contact-box > li:last-child {
  border-bottom: none; /* remove border from the last contact */
}
.contact-box > li:hover {
  background-color: #eceff1; /* blue-gray 50 highlight */
}
.contact-box > li.selected {
  background-color: #cfd8dc; /* blue-gray 100 highlight */
}
.contact-box > li.action {
  min-height: 2.5rem;
  border-bottom: none;
}

.contact-box .text-box {
  overflow-x: hidden;
}

.contact-box .contact-title {
  font-size: 1.05rem;
  text-overflow: ellipsis;
  overflow: hidden
}

.contact-box .contact-comment {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-weight: lighter;
}

/* Avatar and online-offline indicator */
.contact-box .avatar-box {
  margin-right: 0.35rem;
  position: relative;
  width: 3rem;
  min-width: 3rem;
  height: 3rem;
  min-height: 3rem;
  font-size: 1.8rem;
  overflow: hidden;
  padding: 0;
}
.contact-box .avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}
.contact-box .avatar-box > .material-icons {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  font-size: 2.4rem;
  line-height: 3rem;
}
.contact-box .text-box {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.contact-box .action-text {
  text-align: center;
  font-size: 0.95rem;
  line-height: 2.5rem;
  margin: 0 auto 0 auto;
  cursor: pointer;
}

/* Online-offline indicator positioned inside avatar-box */
.avatar-box .online, .offline {
  display: inline-block;
  width: 0.75rem;
  min-width: 0.75rem;
  height: 0.75rem;
  min-height: 0.75rem;
  flex-shrink: 0;
  border-radius: 50%;
  position: absolute;
  right: 0;
  bottom: 0.1rem;
}
.avatar-box .online {
  background-color: #4caf50;
}
.avatar-box .offline {
  background-color: #C0C0C0;
}

/* Typing indicator: animation of the online dot */
.online.typing {
  animation: typing 0.5s infinite;
}
@keyframes typing {
  0%, 100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-0.3rem);
  }
}

.contact-box .checkmark.material-icons {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  font-size: 1.5rem;
  line-height: 1.5rem;
  color: #0097A7;
  position: absolute;
  right: 0.05rem;
  bottom: 0.05rem;
  background-color: white;
  overflow: visible;
  border: 0.0px solid transparent;
}
/* END of avatar box */

#add-topic {
  position: absolute;
  right: 1rem;
  bottom: 1rem;
}

/* Various forms inside a panel (signup form, new topic form) */
.panel-form {
  display: flex;
  flex-direction: column;
  padding: 0;
}

.panel-form-row {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  line-height: 1.5;
  flex-grow: 0;
  flex-shrink: 0;
}

.panel-form-column {
  display: flex;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 0;
  padding: 0.5rem 0.75rem;
  line-height: 1.5;
}

/* Make sure padding is only applied at
   the top level panel */
.panel-form-row > .panel-form-column,
.panel-form-column > .panel-form-column {
  padding: 0!important;
  flex-grow: 1;
}
.panel-form-column > .panel-form-row,
.panel-form-row > .panel-form-row {
  padding: 0!important;
}

/* Styling of avatar upload control */
.avatar-upload {
  display: flex;
  position: relative;
  flex-direction: column;
  flex-shrink: 0;
  width: 7rem;
  height: 9rem;
  margin-left: 0.5rem;
  font-size: 4.5rem;
  padding: 0;
}

.avatar-upload.read-only {
  height: auto;
}

.avatar-upload .clear-avatar {
  position: absolute;
  font-size: 1rem;
  top: 0;
  right: 0;
  opacity: 0;
  background-image: radial-gradient(#fff 40%, transparent 75%);
}

.avatar-upload:hover .clear-avatar {
  opacity: 1;
}

.avatar-upload .avatar-box {
  position: relative;
  width: 7rem;
  height: 7rem;
}

.avatar-upload .avatar-box > .material-icons {
  position: relative;
  font-size: 4rem;
  line-height: 7rem;
  vertical-align: middle;
}

/* Clearfix */
.avatar-upload:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.avatar-upload .blank {
  text-align: center;
  vertical-align: middle;
  border: 1px dashed #999;
  font-size: 1rem;
  flex-shrink: 0;
  width: 7rem;
  height: 7rem;
  overflow: hidden;
  color: #999;
  line-height: 7rem;
  border-radius: 50%;
}

.avatar-upload .preview {
  width: 7rem;
  height: 7rem;
  overflow: hidden;
  border-radius: 50%;
}

.avatar-upload .inputfile.hidden {
	width: 0.1px;
	height: 0.1px;
	opacity: 0;
	overflow: hidden;
	position: absolute;
}

.avatar-upload .inputfile + label.round {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 3rem;
  height: 3rem;
  line-height: 3rem;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(0,0,0,0.4);
  margin-left: auto;
  margin-right: auto;
  margin-top: -1rem;
  bottom: 0;
  background-color: #fafafa;
}

.avatar-upload .inputfile:focus + label.round {
	outline: none;
}

.avatar-upload .inputfile:focus + label,
.avatar-upload .inputfile + label:hover {
  background-color: #e3f2fd;
  color: #42a5f5;
}
/* END of avatar upload */
/* END of new account panel */
/* END of sidepanel styles */

/* Topic/messages view */
#topic-caption-panel {
  background-color: #b0bec5;
}
#topic-caption-panel a {
  color: #757575;
  text-decoration: none;
}

#topic-caption-panel :hover {
  color: black;
}

#topic-caption-panel .avatar-box {
  position: relative;
  width: 3rem;
  min-width: 3rem;
  height: 3rem;
  font-size: 1.8rem;
  margin-right: 0.35rem;
}

#topic-caption-panel .avatar-box > .material-icons {
  font-size: 2.4rem;
  line-height: 3rem;
}

#topic-title-group {
  overflow-x: hidden;
}

#hide-message-view {
  display: block;
  margin-right: 0.35rem;
}

#topic-users {
  margin-left: auto;
  margin-right: 1rem;
}

#topic-users .avatar-box {
  display: inline-block;
  width: 1.8rem;
  min-width: 1.8rem;
  height: 1.8rem;
  min-height: 1.8rem;
  font-size: 1rem;
  margin-right: 0.2rem;
}

#topic-users .avatar-box > .material-icons {
  font-size: 1.4rem;
  line-height: 1.8rem;
}

/* Overflow span "+18 more" */
#topic-users > span {
  line-height: 2rem;
  vertical-align: top;
}

/* Scrollable panel where the messages are shown */
#messages-panel {
  position: relative;
  padding: 0.75rem;
  overflow-y: scroll;
  background-color: #eceff1;
  height: 100%;
  background-image: url("../img/bg_messages.png");
}

#messages-container {
  position: relative;
  overflow-y: hidden;
  height: 100%;
}

#write-only-background {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-image: repeating-linear-gradient(45deg, rgba(255,255,255,0.5), rgba(255,255,255,.5) 0.5rem,
    rgba(255,255,255,.8) 0.5rem, rgba(255,255,255,.8) 1rem);
}

#write-only-note {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  color: #666;
  height: auto;
  width: auto;
  line-height: 2rem;
  text-align: center;
  padding: 0.75rem 1.25rem 0.75rem 1.25rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.12);
}

#peer-messaging-disabled-note {
  position: absolute;
  left: 50%;
  bottom: 4.3rem;
  transform: translate(-50%);
  background-color: white;
  color: #666;
  width: fit-content;
  line-height: 2rem;
  text-align: center;
  padding: 0.75rem 1.25rem 0.75rem 1.25rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.12);
}

/* Footer-panel with the input for the new message */
#send-message-panel {
  display: flex;
  align-items: center;
  flex: none;
  padding: 0.4rem 0.4rem 0.4rem 0.6rem;
  background-color: #eceff1;
  box-shadow: 0px -2px 3px -1px rgba(0,0,0,0.1); /* Faint shadow above panel */
  z-index: 2;
  height: 4rem;
}

#send-message-panel #writing-disabled {
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
}

#sendMessage {
  overflow: hidden;
  overflow-wrap: break-word;
}

#send-message-panel .material-icons {
  min-width: 1.75rem;
}

#send-message-panel .material-icons.secondary, .material-icons.disabled {
  color: #999!important;
}

#send-message-panel .material-icons.secondary:hover {
  color: #2196f3!important;
}

/* New chat invitation panel */

.accept-invite-panel {
  background-color: #eceff1;
  box-shadow: 0px -2px 3px -1px rgba(0,0,0,0.1); /* Faint shadow above panel */
  width: 100%;
  z-index: 1;
}

.accept-invite-panel .title {
  font-size: 1.2rem;
  padding: 0.75rem;
}

.accept-invite-panel .footer {
  background-color: #fafafa;
  white-space: nowrap;
  padding-bottom: 0.35rem;
  text-align: center;
}

.accept-invite-panel button {
  font-size: 1rem;
  height: 2em;
  line-height: 2em;
  border: 1px solid transparent;
  margin: 0.25rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.25);
}

/* END of chat invitation panel */

/* A static panel shown when no topic is selected */
#dummy-view {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0f7fa;
}
#dummy-view a {
  text-decoration: none;
  display: block;
  text-align: center;
  vertical-align: middle;
}

#image-preview {
  position: absolute;
  display: flex;
  flex-direction: column;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  background-color: rgba(255,255,255,0.90);
}

#image-preview-caption-panel {
  display: flex;
  justify-content: space-between;
  background-color: #eceff1;
  box-shadow: 0 1px 2px silver; /* Faint shadow under the panel */
  line-height: 2.5rem;
  height: 4.6rem;
  padding: 1rem 0.75rem 0 0.75rem;
  z-index: 3;
}

#image-preview-caption-panel a {
  color: #757575;
  text-decoration: none;
}

#image-preview-caption-panel a:hover {
  color: black;
}

#image-preview-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

#image-preview-footer {
  display: flex;
  justify-content: space-around;
  height: 3.2rem;
  line-height: 1.5rem;
  background-color: #eceff1;
  box-shadow: 0px -2px 3px -1px rgba(0,0,0,0.1); /* Faint shadow above panel */
}

#image-preview-container .material-icons {
  font-size: 6rem;
}

/* Talk bubbles */
.chat-box {
  list-style: none;
  padding: 0 0 3.5rem 0;
  margin: 0 0.5rem;
}

/* Group chats have avatars on the left. Make left margin smaller */
.chat-box.group {
  margin-left: -0.4rem;
}

.chat-box li {
  display: flex;
  margin: 0;
  padding: 0;
  align-items: flex-end;
}

.chat-box .right {
  justify-content: flex-end;
}

.chat-box .center {
  justify-content: center;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.chat-box .bubble {
  display: flex;
  position: relative;
	max-width: 40rem;
  min-width: 9rem;
	height: auto;
  padding: 0.75rem 0.5rem 0.75rem 1.25rem;
  margin: 0;
  text-align: left;
  border-radius: 0.5rem;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.12);
}

.chat-box li.single {
  margin-top: 0.25rem;
  margin-bottom: 0.75rem;
}

.chat-box li.first {
  margin-top: 0.25rem;
  margin-bottom: 0.15rem;
}

.chat-box li.middle {
  margin-top: 0.15rem;
  margin-bottom: 0.15rem;
}

.chat-box li.last {
  margin-top: 0.15rem;
  margin-bottom: 0.75rem;
}

.chat-box .left .bubble {
  background-color: #c5e1a5;
}

.chat-box .right .bubble {
  background-color: #fafafa;
}

.chat-box .center .bubble {
  background-color: #e3f2fd;
  padding: 0.75rem 0.75rem 0.5rem 0.75rem;
}

/* Left triangle placed bottom left flush. */
.chat-box .left .tip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  right: auto;
  left: -.8rem;
  top: auto;
  bottom: 0;
  border: 0.62rem solid #c5e1a5;
  color: #c5e1a5;
  border-top-color: transparent;
  border-left-color: transparent;
  box-shadow: 0 1.6px 0.5px -0.8px rgba(0, 0, 0, 0.12);
}

/* Right triangle placed bottom right flush. */
.chat-box .right .tip::before {
  content: '';
  position: absolute;
  display: flex;
  justify-content: space-between;
  width: 0;
  height: 0;
  left: auto;
  right: -.8rem;
  top: auto;
  bottom: 0;
  border: 0.62rem solid #fafafa;
  color: #fafafa;
  border-top-color: transparent;
  border-right-color: transparent;
  box-shadow: 0 1.6px 0.5px -0.8px rgba(0, 0, 0, 0.12);
}

.chat-box .avatar-box {
  position: relative;
  width: 1.4rem;
  min-width: 1.4rem;
  height: 1.4rem;
  min-height: 1.4rem;
  font-size: 0.8rem;
  margin-bottom: 0.8rem;
  margin-right: 0.8rem;
}

.chat-box .bubble div.message-content {
  color: #212121;
  margin: 0;
  max-width: 36rem;
  overflow-wrap: break-word;
}
/* The timestamp is a floating element. Need to ensure div.message-content is expanded
 * to accomodate it. It's usually called clearfix. */
.chat-box .bubble div.message-content:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.chat-box .timestamp {
  display: inline-block;
  position: relative;
  font-size: 80%;
  color: #777;
  padding-left: 1rem;
  top: 0.5rem;
  float: right;
  white-space: nowrap;
}

/* Typing notification bubble */
.chat-box .typing {
  display: inline-block;
  border-radius: 0.5rem;
  width: 2rem;
  padding: 0.25rem 0.35rem;
  vertical-align: middle;
  text-align: center;
  color: #aaa;
  margin-top: 0.75rem;
}

/* The name of the person who sent the message
 * (group chat only)
 */
.chat-box .author {
  font-size: 80%;
  color:#777;
}

/* Inline image styles */
.inline-image {
  max-width: 36rem;
  max-height: 24rem;
  overflow: hidden;
  border-radius: 0.1rem;
  display: block;
}

/* Attachment styles */
.attachment {
  display: flex;
}
.attachment:last-of-type {
  margin-bottom: -0.45rem;
}

/* END of talk bubbles */
/* END of topic/mesages view */

/* InfoView styles */
#info-caption-panel {
  background-color: #b0bec5;
}
#info-caption-panel a {
  color: #757575;
  line-height: 1;
  text-decoration: none;
}
#info-caption-panel :hover {
  color: black;
}

#info-title {
  margin-right: auto;
}

#group-manager {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: hidden;
}

#group-manager-buttons {
  display: flex;
  background-color: #eceff1;
  box-shadow: 0 -1px rgba(0,0,0,0.1); /* Faint shadow above panel */
  z-index: 2;
  justify-content: space-around;
  padding: 0.4rem;
  height: 4rem;
  border-left: 1px solid #ccc;
}

/* END of InfoView */

/* BEGIN Tabs */

ul.tabbar {
  flex-shrink: 0;
  flex-grow: 0;
  background: #eeeeee;
  list-style:none;
  margin:0;
  width:100%;
  overflow:hidden;
  padding:0;
  padding-left:1rem;
}

ul.tabbar > li {
  float: left;
}

ul.tabbar > li > a {
  position: relative;
  padding-top: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  display: block;
  text-align: center;
  line-height: 2rem;
  font-weight: 400;
  font-size: 1.3rem;
  font-variant: small-caps;
  text-decoration: none;
  color: rgba(33,150,243,0.6);
  overflow: hidden;
  box-shadow: inset 0 -2px 2.5px -1px rgba(0,0,0,0.25);
}

ul.tabbar li.active > a {
  color: #2196f3;
}

ul.tabbar li.active > a:after {
  height:0.15rem;
  display:block;
  content:"";
  bottom:0;
  left:0;
  position: absolute;
  background: #2196f3;
  animation: tabbar-border-expand 0.2s cubic-bezier(0.4, 0.0, 0.4, 1) 0s alternate forwards;
}

@keyframes tabbar-border-expand {
  0% {
    opacity:0;
    width:0;
  }
  100% {
    opacity:1;
    width:100%;
  }
}
/* END Tabs */

/* Miscellaneous */

/* Badge with the number of unread messages */
.unread {
  display: inline-block;
  font-size: 0.75rem;
  font-weight: bolder;
  background-color: #0097A7;
  color: white;
  border-radius: 50%;
  width: 1.35rem;
  height: 1.35rem;
  margin-left: 0.5rem;
  line-height: 1.35rem;
  text-align: center;
  vertical-align: middle;
}

.material-icons.as-badge {
  padding-left: 0.2rem;
  font-size: 1.25rem;
  color: #bbb;
}

/* Badge with a label, like [you], [muted], etc */
.badge {
  font-family: 'Roboto Mono', Courier, monospace, sans-serif;
  font-size: 0.8rem;
  border: 1px solid silver;
  background-color: #e8e8e8;
  color: #666;
  padding: 0.05rem 0.25rem 0.05rem 0.25rem;
  margin: 0.05rem 0.1rem 0.05rem 0.1rem;
  border-radius: 0.2rem;
  display: inline-block;
  align-self: flex-start;
}

.badge.green {
  border-color: #9c9;
  background-color: #efe;
  color: #383;
}

.badge.yellow {
  border-color: #cc9;
  background-color: #ffe;
  color: #660;
}

.badge.blue {
  border-color: #99c;
  background-color: #eef;
  color: #339;
}

.badge.red {
  border-color: #c99;
  background-color: #fee;
  color: #933;
}

.badge.magenta {
  border-color: #c9c;
  background-color: #fef;
  color: #939;
}

/* Letter tile base style, responsive square box */
.lettertile {
  width: 100%;
  padding-bottom: 100%;
  border-radius: 50%;
}

/* Div with letter tile content */
.lettertile > div {
  display: flex;
  flex-shrink: 0;
  flex-grow: 0;
  position: absolute;
  top: 0; bottom: 0; left: 0; right: 0;
  text-transform: uppercase;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 50%;
  line-height: 1;
  color: rgba(250, 250, 250, 0.8); /* default text color #FAFAFA, gray 50 */
}

.lettertile:hover {
  color: white;
}

/* Colors of letter tiles */
.light-color0 {
  background-color: #ef9a9a;
}
.light-color1 {
  background-color: #90caf9;
}
.light-color2 {
  background-color: #b0bec5;
}
.light-color3 {
  background-color: #b39ddb;
}
.light-color4 {
  background-color: #ffab91;
}
.light-color5 {
  background-color: #a5d6a7;
}
.light-color6 {
  background-color: #eeeeee;
}
.light-color7 {
  background-color: #e6ee9c;
}
.light-color8 {
  background-color: #c5e1a5;
}
.light-color9 {
  background-color: #f0e57d;
}
.light-color10 {
  background-color: #f48fb1;
}
.light-color11 {
  background-color: #9fa8da;
}
.light-color12 {
  background-color: #ffe082;
}
.light-color13 {
  background-color: #bcaaa4;
}
.light-color14 {
  background-color: #80deea;
}
.light-color15 {
  background-color: #ce93d8;
}

/* Darker version of letter tile colors */
.dark-color0 {
  background-color: #C62828;
}
.dark-color1 {
  background-color: #AD1457;
}
.dark-color2 {
  background-color: #6A1B9A;
}
.dark-color3 {
  background-color: #4527A0;
}
.dark-color4 {
  background-color: #283593;
}
.dark-color5 {
  background-color: #1565C0;
}
.dark-color6 {
  background-color: #0277BD;
}
.dark-color7 {
  background-color: #00838F;
}
.dark-color8 {
  background-color: #00695C;
}
.dark-color9 {
  background-color: #2E7D32;
}
.dark-color10 {
  background-color: #558B2F;
}
.dark-color11 {
  background-color: #9E9D24;
}
.dark-color12 {
  background-color: #F9A825;
}
.dark-color13 {
  background-color: #FF8F00;
}
.dark-color14 {
  background-color: #EF6C00;
}
.dark-color15 {
  background-color: #D84315;
}

/* Material icons resized from the default 24pt */
.material-icons {
  font-size: 1.4rem;
  line-height: 1;
  vertical-align: middle;
  text-align: center;
}
.material-icons.small {
  font-size: 1rem;
}
.material-icons.big {
  font-size: 2.4rem;
}

#self-avatar .material-icons {
  font-size: 2.4rem;
  line-height: 3rem;
}
.avatar-box .material-icons {
  color: rgba(250, 250, 250, 0.8); /* #fafafa, gray 50 */
  border-radius: 50%;
  width: inherit;
  height: inherit;
}

.chip-input {
  display: flex;
  flex-wrap: wrap;
  flex-grow: 1;
  cursor: text;
  font-size: 1.1rem;
  font-weight: normal;
  background: transparent;
  border-bottom: 1px solid #ccc;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.1s ease-in-out;
  max-height: 10rem;
  min-height: 4rem;
  overflow-y: scroll;
  margin: 0.25rem 0 0.25rem 0;
  outline: none;
  color: #666;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
}

.chip-input.focused {
  border-bottom: 1px solid #2196f3;
  box-shadow: 0 1px 0 rgba(33, 150, 243, 0.25);
}

.chip-input > input[type="text"] {
  display: inline-block;
  width: auto;
  border: none;
  line-height: 1.8;
  box-shadow: none;
  min-width: 4rem;
  flex: 1 0 auto;
  height: 1.8rem;
  margin: 0.125rem;
}

.chip {
  display: flex;
  flex: 0 0 auto;
  align-items: center;
  height: 1.8rem;
  line-height: 1.8;
  border-radius: 0.9rem;
  margin: 0.125rem;
  background-color: #eee;
  white-space: nowrap;
  font-size: 90%;
}

.chip.invalid {
  color: #c66;
  background-color: #fee;
}

.chip > a {
  display: inline-block;
  color: #ccc;
  background-color: #aaa;
  border-radius: 50%;
  margin: 0.3rem;
  width: 1.175rem;
  height: 1.175rem;
  line-height: 1.15;
  text-decoration: none;
}

.chip > a:focus,
.chip > a:hover {
  text-decoration: none;
  color: #ddd;
  background-color: #bbb;
}

.chip .avatar-box {
  position: relative;
  width: 1.8rem;
  min-width: 1.8rem;
  height: 1.8rem;
  min-height: 1.8rem;
  font-size: 1rem;
  margin-right: 0.2rem;
}

.chip .avatar-box .material-icons {
  font-size: 1.4rem;
  line-height: 1.8rem;
}

.chip .spacer {
  display: inline-block;
  margin: 0.3rem;
  width: 0.25rem;
  height: 1.175rem;
}

.load-spinner-box {
  position: absolute;
  box-shadow: 0.1rem 0.1rem 0.15rem 1px rgba(0,0,0,0.25);
  border-radius: 50%;
  padding: 0.25rem;
  width: 2.5rem;
  height: 2.5rem;
  left: 0;
  right: 0;
  top: 5rem;
  margin-left: auto;
  margin-right: auto;
  background-color: white;
  z-index: 2;
}

.loader-spinner {
  border: 0.35rem solid #f3f3f3;
  border-radius: 50%;
  border-top: 0.35rem solid #1e88e5;
  width: 2rem;
  height: 2rem;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* File upload styles */
.uploader {
  display: flex;
}
/* Gray uploader bar of fixed length (background) */
.uploader > div {
  position: relative;
  height: 0.6rem;
  padding: 0.15rem;
  margin: 0.35rem;
  width: 6rem;
  line-height: 0;
  border-radius: 0.1rem;
  background-color: #cfd8dc;
}
/* Colored uploader progress bar of variable length */
.uploader > div > span {
  display: inline-block;
  height: 0.3rem;
  background-color: #1e88e5;
  border-radius: 0.08rem;
}
