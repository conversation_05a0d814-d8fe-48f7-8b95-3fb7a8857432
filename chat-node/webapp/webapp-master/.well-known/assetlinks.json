[{"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "co.tinode.tindroidx", "sha256_cert_fingerprints": ["5B:BE:6A:C4:A7:66:A8:BB:4B:C1:52:66:E5:A4:BD:66:3C:33:50:04:11:CB:17:44:45:89:5E:3C:8D:55:77:7A"]}}, {"relation": ["delegate_permission/common.get_login_creds"], "target": {"namespace": "web", "site": "https://web.tinode.co"}}, {"relation": ["delegate_permission/common.get_login_creds"], "target": {"namespace": "android_app", "package_name": "co.tinode.tindroidx", "sha256_cert_fingerprints": ["5B:BE:6A:C4:A7:66:A8:BB:4B:C1:52:66:E5:A4:BD:66:3C:33:50:04:11:CB:17:44:45:89:5E:3C:8D:55:77:7A"]}}]