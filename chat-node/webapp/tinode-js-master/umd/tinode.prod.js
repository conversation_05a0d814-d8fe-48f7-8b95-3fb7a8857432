!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Tinode=e()}}(function(){var e=function(e){var t;return function(n){return t||e(t={exports:{},parent:n},t.exports),t.exports}},t=e(function(e,t){e.exports={version:"0.16.7"}}),n=e(function(e,t){"use strict";const n=[{name:"ST",start:/(?:^|[\W_])(\*)[^\s*]/,end:/[^\s*](\*)(?=$|[\W_])/},{name:"EM",start:/(?:^|\W)(_)[^\s_]/,end:/[^\s_](_)(?=$|\W)/},{name:"DL",start:/(?:^|[\W_])(~)[^\s~]/,end:/[^\s~](~)(?=$|[\W_])/},{name:"CO",start:/(?:^|\W)(`)[^`]/,end:/[^`](`)(?=$|\W)/}],s=[{name:"LN",dataName:"url",pack:function(e){return/^[a-z]+:\/\//i.test(e)||(e="http://"+e),{url:e}},re:/(?:(?:https?|ftp):\/\/|www\.|ftp\.)[-A-Z0-9+&@#\/%=~_|$?!:,.]*[A-Z0-9+&@#\/%=~_|$]/gi},{name:"MN",dataName:"val",pack:function(e){return{val:e.slice(1)}},re:/\B@(\w\w+)/g},{name:"HT",dataName:"val",pack:function(e){return{val:e.slice(1)}},re:/\B#(\w\w+)/g}],i={ST:{name:"b",isVoid:!1},EM:{name:"i",isVoid:!1},DL:{name:"del",isVoid:!1},CO:{name:"tt",isVoid:!1},BR:{name:"br",isVoid:!0},LN:{name:"a",isVoid:!1},MN:{name:"a",isVoid:!1},HT:{name:"a",isVoid:!1},IM:{name:"img",isVoid:!0},FM:{name:"div",isVoid:!1},RW:{name:"div",isVoid:!1},BN:{name:"button",isVoid:!1},HD:{name:"",isVoid:!1}};function o(e,t){let n;try{let i=(n=atob(e)).length,o=new ArrayBuffer(i),r=new Uint8Array(o);for(let e=0;e<i;e++)r[e]=n.charCodeAt(e);return URL.createObjectURL(new Blob([o],{type:t}))}catch(s){console.log("Drafty: failed to convert object.",s.message)}return null}const r={ST:{open:function(){return"<b>"},close:function(){return"</b>"}},EM:{open:function(){return"<i>"},close:function(){return"</i>"}},DL:{open:function(){return"<del>"},close:function(){return"</del>"}},CO:{open:function(){return"<tt>"},close:function(){return"</tt>"}},BR:{open:function(){return"<br/>"},close:function(){return""}},HD:{open:function(){return""},close:function(){return""}},LN:{open:function(e){return'<a href="'+e.url+'">'},close:function(e){return"</a>"},props:function(e){return e?{href:e.url,target:"_blank"}:null}},MN:{open:function(e){return'<a href="#'+e.val+'">'},close:function(e){return"</a>"},props:function(e){return e?{name:e.val}:null}},HT:{open:function(e){return'<a href="#'+e.val+'">'},close:function(e){return"</a>"},props:function(e){return e?{name:e.val}:null}},BN:{open:function(e){return"<button>"},close:function(e){return"</button>"},props:function(e){return e?{"data-act":e.act,"data-val":e.val,"data-name":e.name,"data-ref":e.ref}:null}},IM:{open:function(e){const t=o(e.val,e.mime),n=e.ref?e.ref:t;return(e.name?'<a href="'+n+'" download="'+e.name+'">':"")+'<img src="'+t+'"'+(e.width?' width="'+e.width+'"':"")+(e.height?' height="'+e.height+'"':"")+' border="0" />'},close:function(e){return e.name?"</a>":""},props:function(e){return e?{src:o(e.val,e.mime),title:e.name,"data-width":e.width,"data-height":e.height,"data-name":e.name,"data-size":e.val?.75*e.val.length|0:0,"data-mime":e.mime}:null}},FM:{open:function(e){return"<div>"},close:function(e){return"</div>"}},RW:{open:function(e){return"<div>"},close:function(e){return"</div>"}}};var a=function(){};a.parse=function(e){if("string"!=typeof e)return null;const t=e.split(/\r?\n/),i=[],o={},r=[];t.map(function(e){let t,a,c=[];if(n.map(function(t){c=c.concat(function(e,t,n,s){const i=[];let o=0,r=e.slice(0);for(;r.length>0;){const a=t.exec(r);if(null==a)break;let c=a.index+a[0].lastIndexOf(a[1]);r=r.slice(c+1),o=(c+=o)+1;const u=n?n.exec(r):null;if(null==u)break;let h=u.index+u[0].indexOf(u[1]);r=r.slice(h+1),o=(h+=o)+1,i.push({text:e.slice(c+1,h),children:[],start:c,end:h,type:s})}return i}(e,t.start,t.end,t.name))}),0==c.length)a={txt:e};else{c.sort(function(e,t){return e.start-t.start}),c=function e(t){if(0==t.length)return[];const n=[t[0]];let s=t[0];for(let i=1;i<t.length;i++)t[i].start>s.end?(n.push(t[i]),s=t[i]):t[i].end<s.end&&s.children.push(t[i]);for(let i in n)n[i].children=e(n[i].children);return n}(c);const t=function e(t,n){let s="";const i=[];for(var o in t){const a=t[o];if(!a.text){var r=e(a.children,s.length+n);a.text=r.txt,i=i.concat(r.fmt)}a.type&&i.push({at:s.length+n,len:a.text.length,tp:a.type}),s+=a.text}return{txt:s,fmt:i}}(function e(t,n,s,i){const o=[];if(0==i.length)return[];for(let r in i){const s=i[r];s.start>n&&o.push({text:t.slice(n,s.start)});const a={type:s.type},c=e(t,s.start+1,s.end,s.children);c.length>0?a.children=c:a.text=s.text,o.push(a),n=s.end+1}return n<s&&o.push({text:t.slice(n,s)}),o}(e,0,e.length,c),0);a={txt:t.txt,fmt:t.fmt}}if((t=function(e){let t,n=[];if(s.map(function(s){for(;null!==(t=s.re.exec(e));)n.push({offset:t.index,len:t[0].length,unique:t[0],data:s.pack(t[0]),type:s.name})}),0==n.length)return n;n.sort(function(e,t){return e.offset-t.offset});let i=-1;return n=n.filter(function(e){const t=e.offset>i;return i=e.offset+e.len,t})}(a.txt)).length>0){const e=[];for(let n in t){const s=t[n];let r=o[s.unique];r||(r=i.length,o[s.unique]=r,i.push({tp:s.type,data:s.data})),e.push({at:s.offset,len:s.len,key:r})}a.ent=e}r.push(a)});const a={txt:""};if(r.length>0){a.txt=r[0].txt,a.fmt=(r[0].fmt||[]).concat(r[0].ent||[]);for(let e=1;e<r.length;e++){const t=r[e],n=a.txt.length+1;a.fmt.push({tp:"BR",len:1,at:n-1}),a.txt+=" "+t.txt,t.fmt&&(a.fmt=a.fmt.concat(t.fmt.map(function(e){return e.at+=n,e}))),t.ent&&(a.fmt=a.fmt.concat(t.ent.map(function(e){return e.at+=n,e})))}0==a.fmt.length&&delete a.fmt,i.length>0&&(a.ent=i)}return a},a.init=function(e){return"string"!=typeof e?null:{txt:e}},a.append=function(e,t){if(null==e)return t;if(null==t)return e;e.txt=e.txt||"",t.txt=t.txt||"";const n=e.txt.length;return e.txt+=t.txt,Array.isArray(t.fmt)&&(e.fmt=e.fmt||[],Array.isArray(t.ent)&&(e.ent=e.ent||[]),t.fmt.forEach(s=>{const i={at:s.at+n,len:s.len};s.tp?i.tp=s.tp:(i.key=e.ent.length,e.ent.push(t.ent[s.key||0])),e.fmt.push(i)})),e},a.insertImage=function(e,t,n,s,i,o,r,a,c){return(e=e||{txt:" "}).ent=e.ent||[],e.fmt=e.fmt||[],e.fmt.push({at:t,len:1,key:e.ent.length}),e.ent.push({tp:"IM",data:{mime:n,val:s,width:i,height:o,name:r,ref:c,size:0|a}}),e},a.appendImage=function(e,t,n,s,i,o,r,c){return(e=e||{txt:""}).txt+=" ",a.insertImage(e,e.txt.length-1,t,n,s,i,o,r,c)},a.attachFile=function(e,t,n,s,i,o){(e=e||{txt:""}).ent=e.ent||[],e.fmt=e.fmt||[],e.fmt.push({at:-1,len:0,key:e.ent.length});const r={tp:"EX",data:{mime:t,val:n,name:s,ref:o,size:0|i}};return o instanceof Promise&&(r.data.ref=o.then(e=>{r.data.ref=e},e=>{})),e.ent.push(r),e},a.wrapAsForm=function(e,t,n){return"string"==typeof e&&(e={txt:e}),e.fmt=e.fmt||[],e.fmt.push({at:t,len:n,tp:"FM"}),e},a.insertButton=function(e,t,n,s,i,o,r){return"string"==typeof e&&(e={txt:e}),!e||!e.txt||e.txt.length<t+n?null:n<=0||-1==["url","pub"].indexOf(i)?null:"url"!=i||r?(r=""+r,e.ent=e.ent||[],e.fmt=e.fmt||[],e.fmt.push({at:t,len:n,key:e.ent.length}),e.ent.push({tp:"BN",data:{act:i,val:o,ref:r,name:s}}),e):null},a.appendButton=function(e,t,n,s,i,o){const r=(e=e||{txt:""}).txt.length;return e.txt+=t,a.insertButton(e,r,t.length,n,s,i,o)},a.attachJSON=function(e,t){return(e=e||{txt:""}).ent=e.ent||[],e.fmt=e.fmt||[],e.fmt.push({at:-1,len:0,key:e.ent.length}),e.ent.push({tp:"EX",data:{mime:"application/json",val:t}}),e},a.appendLineBreak=function(e){return(e=e||{txt:""}).fmt=e.fmt||[],e.fmt.push({at:e.txt.length,len:1,tp:"BR"}),e.txt+=" ",e},a.UNSAFE_toHTML=function(e){let{txt:t,fmt:n,ent:s}=e;const i=[];if(n)for(let u in n){const e=n[u];let t,o=e.tp;if(!o){const n=s[0|e.key];n&&(o=n.tp,t=n.data)}r[o]&&(i.push({idx:e.at+e.len,len:-e.len,what:r[o].close(t)}),i.push({idx:e.at,len:e.len,what:r[o].open(t)}))}i.sort(function(e,t){return t.idx==e.idx?t.len-e.len:t.idx-e.idx});for(let r in i)i[r].what&&(o=t,a=i[r].idx,c=i[r].what,t=o.slice(0,a)+c+o.slice(a));var o,a,c;return t},a.format=function(e,t,n){let{txt:s,fmt:o,ent:r}=e;if(s=s||"",Array.isArray(r)||(r=[]),!Array.isArray(o)){if(1!=r.length)return[s];o=[{at:0,len:0,key:0}]}let a=[].concat(o);return a.map(function(e){e.at=e.at||0,e.len=e.len||0,e.len<0&&(e.len=0),e.at<-1&&(e.at=-1)}),a.sort(function(e,t){return e.at-t.at==0?t.len-e.len:e.at-t.at}),a=a.map(e=>{let t,n=e.tp;return n||(e.key=e.key||0,r[e.key]&&(t=r[e.key].data,n=r[e.key].tp)),{tp:n=n||"HD",data:t,at:e.at,len:e.len}}),function e(t,n,s,o,r,a){const c=[];for(let u=0;u<o.length;u++){const s=o[u];if(s.at<0)continue;n<s.at&&(c.push(r.call(a,null,void 0,t.slice(n,s.at),c.length)),n=s.at);const h=[];for(let e=u+1;e<o.length&&o[e].at<s.at+s.len;e++)h.push(o[e]),u=e;const l=i[s.tp]||{};c.push(r.call(a,s.tp,s.data,l.isVoid?null:e(t,n,s.at+s.len,h,r,a),c.length)),n=s.at+s.len}return n<s&&c.push(r.call(a,null,void 0,t.slice(n,s),c.length)),c}(s,0,s.length,a,t,n)},a.toPlainText=function(e){return"string"==typeof e?e:e.txt},a.isPlainText=function(e){return"string"==typeof e||!(e.fmt||e.ent)},a.isValid=function(e){if(!e)return!1;const{txt:t,fmt:n,ent:s}=e;if(!t&&""!==t&&!n&&!s)return!1;const i=typeof t;return!("string"!=i&&"undefined"!=i&&null!==t||void 0!==n&&!Array.isArray(n)&&null!==n||void 0!==s&&!Array.isArray(s)&&null!==s)},a.hasAttachments=function(e){if(e.ent&&e.ent.length>0)for(var t in e.ent)if(e.ent[t]&&"EX"==e.ent[t].tp)return!0;return!1},a.attachments=function(e,t,n){if(e.ent&&e.ent.length>0)for(var s in e.ent)e.ent[s]&&"EX"==e.ent[s].tp&&t.call(n,e.ent[s].data,s)},a.getDownloadUrl=function(e){let t=null;return"application/json"!=e.mime&&e.val?t=o(e.val,e.mime):"string"==typeof e.ref&&(t=e.ref),t},a.isUploading=function(e){return e.ref instanceof Promise},a.getPreviewUrl=function(e){return e.val?o(e.val,e.mime):null},a.getEntitySize=function(e){return e.size?e.size:e.val?.75*e.val.length|0:0},a.getEntityMimeType=function(e){return e.mime||"text/plain"},a.tagName=function(e){return i[e]?i[e].name:void 0},a.attrValue=function(e,t){if(t&&r[e])return r[e].props(t)},a.getContentType=function(){return"text/x-drafty"},void 0!==e&&(e.exports=a)}),s={exports:{}};return function(e){"use strict";if(void 0===i)var i=n({});var o=t({}).version;let r,a;"undefined"!=typeof WebSocket&&(r=WebSocket),"undefined"!=typeof XMLHttpRequest&&(a=XMLHttpRequest),function(){if("undefined"==typeof btoa){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";e.btoa=function(e=""){let n=e,s="";for(let i,o=0,r=0,a=t;n.charAt(0|r)||(a="=",r%1);s+=a.charAt(63&o>>8-r%1*8)){if((i=n.charCodeAt(r+=.75))>255)throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");o=o<<8|i}return s}}"undefined"==typeof atob&&(e.atob=function(e=""){let t=e.replace(/=+$/,""),n="";if(t.length%4==1)throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");for(let s,i=0,o=0,r=0;s=t.charAt(r++);~s&&(o=i%4?64*o+s:s,i++%4)?n+=String.fromCharCode(255&o>>(-2*i&6)):0)s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(s);return n}),"undefined"==typeof window&&(e.window={WebSocket:r,XMLHttpRequest:a,URL:{createObjectURL:function(){throw new Error("Unable to use URL.createObjectURL in a non-browser application")}}})}();const c="0",u=o||"0.16",h="tinodejs/"+u,l=503,d="Connection failed",f=418,p="Disconnected by client";function g(e){return btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,function(e,t){return String.fromCharCode("0x"+t)}))}function _(e,t,n){if("object"!=typeof t){if(t===x.DEL_CHAR)return;return void 0===t?e:t}if(null===t)return t;if(t instanceof Date)return e&&e instanceof Date&&!(e<t)?e:t;if(t instanceof T)return new T(t);if(t instanceof Array)return t;e&&e!==x.DEL_CHAR||(e=t.constructor());for(let s in t)!t.hasOwnProperty(s)||n&&n[s]||"_noForwarding"==s||(e[s]=_(e[s],t[s]));return e}function m(e,t,n,s){return e[t]=_(e[t],n,s),e[t]}function b(e){"string"==typeof e.created&&(e.created=new Date(e.created)),"string"==typeof e.updated&&(e.updated=new Date(e.updated)),"string"==typeof e.touched&&(e.touched=new Date(e.touched))}function w(e,t){if("ts"===e&&"string"==typeof t&&t.length>=20&&t.length<=24){let e=new Date(t);if(e)return e}else if("acs"===e&&"object"==typeof t)return new T(t);return t}function v(e,t){return"string"==typeof t&&t.length>128?"<"+t.length+", bytes: "+t.substring(0,12)+"..."+t.substring(t.length-12)+">":function(e,t){if(t instanceof Date)t=function(e){if(!e||0==e.getTime())return;function t(e,t){return"0".repeat((t=t||2)-(""+e).length)+e}const n=e.getUTCMilliseconds();return e.getUTCFullYear()+"-"+t(e.getUTCMonth()+1)+"-"+t(e.getUTCDate())+"T"+t(e.getUTCHours())+":"+t(e.getUTCMinutes())+":"+t(e.getUTCSeconds())+(n?"."+t(n,3):"")+"Z"}(t);else if(t instanceof T)t=t.jsonHelper();else if(null==t||!1===t||Array.isArray(t)&&0==t.length||"object"==typeof t&&0==Object.keys(t).length)return;return t}(0,t)}function M(e,t,n){let s=null;return"http"!==t&&"https"!==t&&"ws"!==t&&"wss"!==t||(s=t+"://","/"!==(s+=e).charAt(s.length-1)&&(s+="/"),s+="v"+c+"/channels","http"!==t&&"https"!==t||(s+="/lp"),s+="?apikey="+n),s}var S=function(e,t,n,s,i){let o=e,a=s,c=t,u=i;const h=2e3,g=10,_=.3;let m=null,b=0,v=!1,S=(e,...t)=>{this.logger&&this.logger(e,...t)};function x(){clearTimeout(m);let e=h*(Math.pow(2,b)*(1+_*Math.random()));b=b>=g?b:b+1,this.onAutoreconnectIteration&&this.onAutoreconnectIteration(e),m=setTimeout(()=>{if(S("Reconnecting, iter="+b+", timeout="+e),v)this.onAutoreconnectIteration&&this.onAutoreconnectIteration(-1);else{let e=this.connect();this.onAutoreconnectIteration?this.onAutoreconnectIteration(0,e):e.catch(()=>{})}},e)}function y(){clearTimeout(m),m=null}function T(e){let t=null;e.connect=function(n,s){if(v=!1,t){if(!s&&t.readyState==t.OPEN)return Promise.resolve();t.close(),t=null}return n&&(o=n),new Promise(function(n,s){const i=M(o,a?"wss":"ws",c);S("Connecting to: ",i);const h=new r(i);h.onerror=function(e){s(e)},h.onopen=function(t){u&&y(),e.onOpen&&e.onOpen(),n()},h.onclose=function(n){if(t=null,e.onDisconnect){const t=v?f:l;e.onDisconnect(new Error(v?p:d+" ("+t+")"),t)}!v&&u&&x.call(e)},h.onmessage=function(t){e.onMessage&&e.onMessage(t.data)},t=h})},e.reconnect=function(t){y(),e.connect(null,t)},e.disconnect=function(){v=!0,t&&(y(),t.close(),t=null)},e.sendText=function(e){if(!t||t.readyState!=t.OPEN)throw new Error("Websocket is not connected");t.send(e)},e.isConnected=function(){return t&&t.readyState==t.OPEN},e.transport=function(){return"ws"},e.probe=function(){e.sendText("1")}}function D(e){let t=null,n=null,s=null;e.connect=function(s,i){if(v=!1,n){if(!i)return Promise.resolve();n.onreadystatechange=void 0,n.abort(),n=null}return s&&(o=s),new Promise(function(s,i){const r=M(o,a?"https":"http",c);S("Connecting to: ",r),(n=function n(s,i,o){let r=new XMLHttpRequest,a=!1;return r.onreadystatechange=function(c){if(4==r.readyState)if(201==r.status){let o=JSON.parse(r.responseText,w);t=s+"&sid="+o.ctrl.params.sid,(r=n(t)).send(null),e.onOpen&&e.onOpen(),i&&(a=!0,i()),u&&y()}else if(r.status<400)e.onMessage&&e.onMessage(r.responseText),(r=n(t)).send(null);else{if(o&&!a&&(a=!0,o(r.responseText)),e.onMessage&&r.responseText&&e.onMessage(r.responseText),e.onDisconnect){const t=r.status||(v?f:l),n=r.responseText||(v?p:d);e.onDisconnect(new Error(n+" ("+t+")"),t)}r=null,!v&&u&&x.call(e)}},r.open("GET",s,!0),r}(r,s,i)).send(null)}).catch(e=>{console.log("LP connection failed:",e)})},e.reconnect=function(t){y(),e.connect(null,t)},e.disconnect=function(){v=!0,y(),s&&(s.onreadystatechange=void 0,s.abort(),s=null),n&&(n.onreadystatechange=void 0,n.abort(),n=null),e.onDisconnect&&e.onDisconnect(new Error(p+" ("+f+")"),f),t=null},e.sendText=function(e){if(!(s=function(e){let t=new XMLHttpRequest;return t.onreadystatechange=function(e){if(4==t.readyState&&t.status>=400)throw new Error("LP sender failed, "+t.status)},t.open("POST",e,!0),t}(t))||1!=s.readyState)throw new Error("Long poller failed to connect");s.send(e)},e.isConnected=function(){return n&&!0},e.transport=function(){return"lp"},e.probe=function(){e.sendText("1")}}let A=!1;if("lp"===n?(D(this),A=!0):"ws"===n?(T(this),A=!0):"object"==typeof window&&(window.WebSocket?(T(this),A=!0):window.XMLHttpRequest&&(D(this),A=!0)),!A)throw console.log("No network transport is available. Running under Node? Call 'Tinode.setNetworkProviders()'."),new Error("No network transport is available. Running under Node? Call 'Tinode.setNetworkProviders()'.");this.transportAvailable=function(e){switch(e){case"ws":return"object"==typeof window&&window.WebSocket;case"lp":return"object"==typeof window&&window.XMLHttpRequest;default:return console.log("Request for unknown transport",e),!1}},this.backoffReset=function(){b=0},this.onMessage=void 0,this.onDisconnect=void 0,this.onOpen=void 0,this.onAutoreconnectIteration=void 0,this.logger=void 0},x=function(e,t,n,s,i,o){this._appName=e||"Undefined",this._apiKey=n,this._browser="",this._platform=o,this._hwos="undefined",this._humanLanguage="xx","undefined"!=typeof navigator&&(this._browser=function(e,t){e=e||"";let n="";/reactnative/i.test(t)&&(n="ReactNative; ");let s,i=(e=e.replace(" (KHTML, like Gecko)","")).match(/(AppleWebKit\/[.\d]+)/i);if(i){let t=["chrome","safari","mobile","version"],n=e.substr(i.index+i[0].length).split(" "),o=[];for(let e=0;e<n.length;e++){let s=/([\w.]+)[\/]([\.\d]+)/.exec(n[e]);s&&o.push([s[1],s[2],t.findIndex(function(e){return e==s[1].toLowerCase()})])}o.sort(function(e,t){let n=e[2]-t[2];return 0!=n?n:t[0].length-e[0].length}),s=o.length>0?o[0][0]+"/"+o[0][1]:i[1]}else s=/trident/i.test(e)?(i=/(?:\brv[ :]+([.\d]+))|(?:\bMSIE ([.\d]+))/g.exec(e))?"MSIE/"+(i[1]||i[2]):"MSIE/?":/firefox/i.test(e)?(i=/Firefox\/([.\d]+)/g.exec(e))?"Firefox/"+i[1]:"Firefox/?":/presto/i.test(e)?(i=/Opera\/([.\d]+)/g.exec(e))?"Opera/"+i[1]:"Opera/?":(i=/([\w.]+)\/([.\d]+)/.exec(e))?i[1]+"/"+i[2]:(i=e.split(" "))[0];if((i=s.split("/")).length>1){let e=i[1].split(".");s=i[0]+"/"+e[0]+(e[1]?"."+e[1]:"")}return n+s}(navigator.userAgent,navigator.product),this._hwos=navigator.platform,this._humanLanguage=navigator.language||"en-US"),this._loggingEnabled=!1,this._trimLongStrings=!1,this._myUID=null,this._authenticated=!1,this._login=null,this._authToken=null,this._inPacketCount=0,this._messageId=Math.floor(65535*Math.random()+65535),this._serverInfo=null,this._deviceToken=null,this._pendingPromises={},this._connection=new S(t,n,s,i,!0),this.logger=((e,...t)=>{if(this._loggingEnabled){const n=new Date,s=("0"+n.getUTCHours()).slice(-2)+":"+("0"+n.getUTCMinutes()).slice(-2)+":"+("0"+n.getUTCSeconds()).slice(-2)+"."+("00"+n.getUTCMilliseconds()).slice(-3);console.log("["+s+"]",e,t.join(" "))}}),this._connection.logger=this.logger,this._cache={};const r=this.cachePut=((e,t,n)=>{this._cache[e+":"+t]=n}),a=this.cacheGet=((e,t)=>this._cache[e+":"+t]),c=this.cacheDel=((e,t)=>{delete this._cache[e+":"+t]}),d=this.cacheMap=((e,t)=>{for(let n in this._cache)if(e(this._cache[n],n,t))break});this.attachCacheToTopic=(e=>{e._tinode=this,e._cacheGetUser=(e=>{const t=a("user",e);if(t)return{user:e,public:_({},t)}}),e._cachePutUser=((e,t)=>r("user",e,_({},t.public))),e._cacheDelUser=(e=>c("user",e)),e._cachePutSelf=(()=>r("topic",e.name,e)),e._cacheDelSelf=(()=>c("topic",e.name))});const f=(e,t,n,s)=>{const i=this._pendingPromises[e];i&&(delete this._pendingPromises[e],t>=200&&t<400?i.resolve&&i.resolve(n):i.reject&&i.reject(new Error(s+" ("+t+")")))},p=e=>{let t=null;return e&&(t=new Promise((t,n)=>{this._pendingPromises[e]={resolve:t,reject:n,ts:new Date}})),t},g=(setInterval(()=>{const e=new Error("Timeout (504)"),t=new Date((new Date).getTime()-5e3);for(let n in this._pendingPromises){let s=this._pendingPromises[n];s&&s.ts<t&&(this.logger("Promise expired",n),delete this._pendingPromises[n],s.reject&&s.reject(e))}},1e3),this.getNextUniqueId=(()=>0!=this._messageId?""+this._messageId++:void 0)),m=()=>this._appName+" ("+(this._browser?this._browser+"; ":"")+this._hwos+"); "+h;this.initPacket=((e,t)=>{switch(e){case"hi":return{hi:{id:g(),ver:u,ua:m(),dev:this._deviceToken,lang:this._humanLanguage,platf:this._platform}};case"acc":return{acc:{id:g(),user:null,scheme:null,secret:null,login:!1,tags:null,desc:{},cred:{}}};case"login":return{login:{id:g(),scheme:null,secret:null}};case"sub":return{sub:{id:g(),topic:t,set:{},get:{}}};case"leave":return{leave:{id:g(),topic:t,unsub:!1}};case"pub":return{pub:{id:g(),topic:t,noecho:!1,head:null,content:{}}};case"get":return{get:{id:g(),topic:t,what:null,desc:{},sub:{},data:{}}};case"set":return{set:{id:g(),topic:t,desc:{},sub:{},tags:[]}};case"del":return{del:{id:g(),topic:t,what:null,delseq:null,user:null,hard:!1}};case"note":return{note:{topic:t,what:null,seq:void 0}};default:throw new Error("Unknown packet type requested: "+e)}}),this.send=((e,t)=>{let n;t&&(n=p(t)),e=function e(t){return Object.keys(t).forEach(function(n){"_"==n[0]?delete t[n]:t[n]?Array.isArray(t[n])&&0==t[n].length?delete t[n]:t[n]?"object"!=typeof t[n]||t[n]instanceof Date||(e(t[n]),0==Object.getOwnPropertyNames(t[n]).length&&delete t[n]):delete t[n]:delete t[n]}),t}(e);let s=JSON.stringify(e);this.logger("out: "+(this._trimLongStrings?JSON.stringify(e,v):s));try{this._connection.sendText(s)}catch(i){if(!t)throw i;f(t,l,null,i.message)}return n}),this.loginSuccessful=(e=>{e.params&&e.params.user&&(this._myUID=e.params.user,this._authenticated=e&&e.code>=200&&e.code<300,e.params&&e.params.token&&e.params.expires?this._authToken={token:e.params.token,expires:new Date(e.params.expires)}:this._authToken=null,this.onLogin&&this.onLogin(e.code,e.text))}),this._connection.onMessage=(e=>{if(!e)return;if(this._inPacketCount++,this.onRawMessage&&this.onRawMessage(e),"0"===e)return void(this.onNetworkProbe&&this.onNetworkProbe());let t=JSON.parse(e,w);t?(this.logger("in: "+(this._trimLongStrings?JSON.stringify(t,v):e)),this.onMessage&&this.onMessage(t),t.ctrl?(this.onCtrlMessage&&this.onCtrlMessage(t.ctrl),t.ctrl.id&&f(t.ctrl.id,t.ctrl.code,t.ctrl,t.ctrl.text),setTimeout(()=>{if(205==t.ctrl.code&&"evicted"==t.ctrl.text){const e=a("topic",t.ctrl.topic);e&&e._resetSub()}else if(t.ctrl.params&&"data"==t.ctrl.params.what){const e=a("topic",t.ctrl.topic);e&&e._allMessagesReceived(t.ctrl.params.count)}else if(t.ctrl.params&&"sub"==t.ctrl.params.what){const e=a("topic",t.ctrl.topic);e&&e._processMetaSub([])}},0)):setTimeout(()=>{if(t.meta){const e=a("topic",t.meta.topic);e&&e._routeMeta(t.meta),t.meta.id&&f(t.meta.id,200,t.meta,"META"),this.onMetaMessage&&this.onMetaMessage(t.meta)}else if(t.data){const e=a("topic",t.data.topic);e&&e._routeData(t.data),this.onDataMessage&&this.onDataMessage(t.data)}else if(t.pres){const e=a("topic",t.pres.topic);e&&e._routePres(t.pres),this.onPresMessage&&this.onPresMessage(t.pres)}else if(t.info){const e=a("topic",t.info.topic);e&&e._routeInfo(t.info),this.onInfoMessage&&this.onInfoMessage(t.info)}else this.logger("ERROR: Unknown packet received.")},0)):(this.logger("in: "+e),this.logger("ERROR: failed to parse data"))}),this._connection.onOpen=(()=>{this.hello()}),this._connection.onAutoreconnectIteration=((e,t)=>{this.onAutoreconnectIteration&&this.onAutoreconnectIteration(e,t)}),this._connection.onDisconnect=((e,t)=>{this._inPacketCount=0,this._serverInfo=null,this._authenticated=!1,d((e,t)=>{0===t.lastIndexOf("topic:",0)&&e._resetSub()});for(let n in this._pendingPromises){let t=this._pendingPromises[n];t&&t.reject&&t.reject(e)}this._pendingPromises={},this.onDisconnect&&this.onDisconnect(e)})};x.credential=function(e,t,n,s){return"object"==typeof e&&({val:t,params:n,resp:s,meth:e}=e),e&&(t||s)?[{meth:e,val:t,resp:s,params:n}]:null},x.topicType=function(e){return{me:"me",fnd:"fnd",grp:"grp",new:"grp",usr:"p2p",sys:"sys"}["string"==typeof e?e.substring(0,3):"xxx"]},x.isNewGroupTopicName=function(e){return"string"==typeof e&&"new"==e.substring(0,3)},x.getVersion=function(){return u},x.setNetworkProviders=function(e,t){r=wsprovider,a=xhrprovider},x.getLibrary=function(){return h},x.MESSAGE_STATUS_NONE=0,x.MESSAGE_STATUS_QUEUED=1,x.MESSAGE_STATUS_SENDING=2,x.MESSAGE_STATUS_FAILED=3,x.MESSAGE_STATUS_SENT=4,x.MESSAGE_STATUS_RECEIVED=5,x.MESSAGE_STATUS_READ=6,x.MESSAGE_STATUS_TO_ME=7,x.DEL_CHAR="\u2421",x.isNullValue=function(e){return e===x.DEL_CHAR},x.MAX_MESSAGE_SIZE="maxMessageSize",x.MAX_SUBSCRIBER_COUNT="maxSubscriberCount",x.MAX_TAG_COUNT="maxTagCount",x.MAX_FILE_UPLOAD_SIZE="maxFileUploadSize",x.prototype={connect:function(e){return this._connection.connect(e)},reconnect:function(e){this._connection.reconnect(e)},disconnect:function(){this._connection.disconnect()},networkProbe:function(){this._connection.probe()},isConnected:function(){return this._connection.isConnected()},isAuthenticated:function(){return this._authenticated},account:function(e,t,n,s,i){const o=this.initPacket("acc");return o.acc.user=e,o.acc.scheme=t,o.acc.secret=n,o.acc.login=s,i&&(o.acc.desc.defacs=i.defacs,o.acc.desc.public=i.public,o.acc.desc.private=i.private,o.acc.tags=i.tags,o.acc.cred=i.cred,o.acc.token=i.token),this.send(o,o.acc.id)},createAccount:function(e,t,n,s){let i=this.account("new",e,t,n,s);return n&&(i=i.then(e=>(this.loginSuccessful(e),e))),i},createAccountBasic:function(e,t,n){return e=e||"",t=t||"",this.createAccount("basic",g(e+":"+t),!0,n)},updateAccountBasic:function(e,t,n,s){return t=t||"",n=n||"",this.account(e,"basic",g(t+":"+n),!1,s)},hello:function(){const e=this.initPacket("hi");return this.send(e,e.hi.id).then(e=>(this._connection.backoffReset(),e.params&&(this._serverInfo=e.params),this.onConnect&&this.onConnect(),e)).catch(e=>{this._connection.reconnect(!0),this.onDisconnect&&this.onDisconnect(e)})},setDeviceToken:function(e,t){let n=!1;return e&&e!=this._deviceToken&&(this._deviceToken=e,t&&this.isConnected()&&this.isAuthenticated()&&(this.send({hi:{dev:e}}),n=!0)),n},login:function(e,t,n){const s=this.initPacket("login");return s.login.scheme=e,s.login.secret=t,s.login.cred=n,this.send(s,s.login.id).then(e=>(this.loginSuccessful(e),e))},loginBasic:function(e,t,n){return this.login("basic",g(e+":"+t),n).then(t=>(this._login=e,t))},loginToken:function(e,t){return this.login("token",e,t)},requestResetAuthSecret:function(e,t,n){return this.login("reset",g(e+":"+t+":"+n))},getAuthToken:function(){return this._authToken&&this._authToken.expires.getTime()>Date.now()?this._authToken:(this._authToken=null,null)},setAuthToken:function(e){this._authToken=e},subscribe:function(e,t,n){const s=this.initPacket("sub",e);return e||(e="new"),s.sub.get=t,n&&(n.sub&&(s.sub.set.sub=n.sub),n.desc&&(x.isNewGroupTopicName(e)?s.sub.set.desc=n.desc:"p2p"==x.topicType(e)&&n.desc.defacs&&(s.sub.set.desc={defacs:n.desc.defacs})),n.tags&&(s.sub.set.tags=n.tags)),this.send(s,s.sub.id)},leave:function(e,t){const n=this.initPacket("leave",e);return n.leave.unsub=t,this.send(n,n.leave.id)},createMessage:function(e,t,n){const s=this.initPacket("pub",e);let o="string"==typeof t?i.parse(t):t;return o&&!i.isPlainText(o)&&(s.pub.head={mime:i.getContentType()},t=o),s.pub.noecho=n,s.pub.content=t,s.pub},publish:function(e,t,n){return this.publishMessage(this.createMessage(e,t,n))},publishMessage:function(e){return(e=Object.assign({},e)).seq=void 0,e.from=void 0,e.ts=void 0,this.send({pub:e},e.id)},getMeta:function(e,t){const n=this.initPacket("get",e);return n.get=_(n.get,t),this.send(n,n.get.id)},setMeta:function(e,t){const n=this.initPacket("set",e),s=[];return t&&["desc","sub","tags","cred"].map(function(e){t.hasOwnProperty(e)&&(s.push(e),n.set[e]=t[e])}),0==s.length?Promise.reject(new Error("Invalid {set} parameters")):this.send(n,n.set.id)},delMessages:function(e,t,n){const s=this.initPacket("del",e);return s.del.what="msg",s.del.delseq=t,s.del.hard=n,this.send(s,s.del.id)},delTopic:function(e,t){const n=this.initPacket("del",e);return n.del.what="topic",n.del.hard=t,this.send(n,n.del.id).then(t=>(this.cacheDel("topic",e),this.ctrl))},delSubscription:function(e,t){const n=this.initPacket("del",e);return n.del.what="sub",n.del.user=t,this.send(n,n.del.id)},delCredential:function(e,t){const n=this.initPacket("del","me");return n.del.what="cred",n.del.cred={meth:e,val:t},this.send(n,n.del.id)},delCurrentUser:function(e){const t=this.initPacket("del",null);return t.del.what="user",t.del.hard=e,this.send(t,t.del.id).then(e=>{this._myUID=null})},note:function(e,t,n){if(n<=0||n>=268435455)throw new Error("Invalid message id "+n);const s=this.initPacket("note",e);s.note.what=t,s.note.seq=n,this.send(s)},noteKeyPress:function(e){const t=this.initPacket("note",e);t.note.what="kp",this.send(t)},getTopic:function(e){let t=this.cacheGet("topic",e);return!t&&e&&(t="me"==e?new A:"fnd"==e?new E:new D(e),this.cachePut("topic",e,t),this.attachCacheToTopic(t)),t},newTopic:function(e){const t=new D("new",e);return this.attachCacheToTopic(t),t},newGroupTopicName:function(){return"new"+this.getNextUniqueId()},newTopicWith:function(e,t){const n=new D(e,t);return this.attachCacheToTopic(n),n},getMeTopic:function(){return this.getTopic("me")},getFndTopic:function(){return this.getTopic("fnd")},getLargeFileHelper:function(){return new R(this)},getCurrentUserID:function(){return this._myUID},isMe:function(e){return this._myUID===e},getCurrentLogin:function(){return this._login},getServerInfo:function(){return this._serverInfo},getServerLimit:function(e,t){return(this._serverInfo?this._serverInfo[e]:null)||t},enableLogging:function(e,t){this._loggingEnabled=e,this._trimLongStrings=e&&t},setHumanLanguage:function(e){e&&(this._humanLanguage=e)},isTopicOnline:function(e){const t=this.getMeTopic(),n=t&&t.getContact(e);return n&&n.online},wantAkn:function(e){this._messageId=e?Math.floor(16777215*Math.random()+16777215):0},onWebsocketOpen:void 0,onConnect:void 0,onDisconnect:void 0,onLogin:void 0,onCtrlMessage:void 0,onDataMessage:void 0,onPresMessage:void 0,onMessage:void 0,onRawMessage:void 0,onNetworkProbe:void 0,onAutoreconnectIteration:void 0};var y=function(e){this.topic=e;const t=e._tinode.getMeTopic();this.contact=t&&t.getContact(e.name),this.what={}};y.prototype={_get_ims:function(){const e=this.contact&&this.contact.updated,t=this.topic._lastDescUpdate||0;return e>t?e:t},withData:function(e,t,n){return this.what.data={since:e,before:t,limit:n},this},withLaterData:function(e){return this.withData(this.topic._maxSeq>0?this.topic._maxSeq+1:void 0,void 0,e)},withEarlierData:function(e){return this.withData(void 0,this.topic._minSeq>0?this.topic._minSeq:void 0,e)},withDesc:function(e){return this.what.desc={ims:e},this},withLaterDesc:function(){return this.withDesc(this._get_ims())},withSub:function(e,t,n){const s={ims:e,limit:t};return"me"==this.topic.getType()?s.topic=n:s.user=n,this.what.sub=s,this},withOneSub:function(e,t){return this.withSub(e,void 0,t)},withLaterOneSub:function(e){return this.withOneSub(this.topic._lastSubsUpdate,e)},withLaterSub:function(e){return this.withSub("p2p"==this.topic.getType()?this._get_ims():this.topic._lastSubsUpdate,e)},withTags:function(){return this.what.tags=!0,this},withCred:function(){return"me"==this.topic.getType()?this.what.cred=!0:this.topic._tinode.logger("ERROR: Invalid topic type for MetaGetBuilder:withCreds",this.topic.getType()),this},withDel:function(e,t){return(e||t)&&(this.what.del={since:e,limit:t}),this},withLaterDel:function(e){return this.withDel(this.topic._maxSeq>0?this.topic._maxDel+1:void 0,e)},build:function(){const e=[],t=this;let n={};return["data","sub","desc","tags","cred","del"].map(function(s){t.what.hasOwnProperty(s)&&(e.push(s),Object.getOwnPropertyNames(t.what[s]).length>0&&(n[s]=t.what[s]))}),e.length>0?n.what=e.join(" "):n=void 0,n}};var T=function e(t){t&&(this.given="number"==typeof t.given?t.given:e.decode(t.given),this.want="number"==typeof t.want?t.want:e.decode(t.want),this.mode=t.mode?"number"==typeof t.mode?t.mode:e.decode(t.mode):this.given&this.want)};T._NONE=0,T._JOIN=1,T._READ=2,T._WRITE=4,T._PRES=8,T._APPROVE=16,T._SHARE=32,T._DELETE=64,T._OWNER=128,T._BITMASK=T._JOIN|T._READ|T._WRITE|T._PRES|T._APPROVE|T._SHARE|T._DELETE|T._OWNER,T._INVALID=1048576,T._checkFlag=function(e,t,n){if(["given","want","mode"].includes(t=t||"mode"))return 0!=(e[t]&n);throw new Error("Invalid AccessMode component '"+t+"'")},T.decode=function(e){if(!e)return null;if("number"==typeof e)return e&T._BITMASK;if("N"===e||"n"===e)return T._NONE;const t={J:T._JOIN,R:T._READ,W:T._WRITE,P:T._PRES,A:T._APPROVE,S:T._SHARE,D:T._DELETE,O:T._OWNER};let n=T._NONE;for(let s=0;s<e.length;s++){const i=t[e.charAt(s).toUpperCase()];i&&(n|=i)}return n},T.encode=function(e){if(null===e||e===T._INVALID)return null;if(e===T._NONE)return"N";const t=["J","R","W","P","A","S","D","O"];let n="";for(let s=0;s<t.length;s++)0!=(e&1<<s)&&(n+=t[s]);return n},T.update=function(e,t){if(!t||"string"!=typeof t)return e;let n=t.charAt(0);if("+"==n||"-"==n){let s=e;const i=t.split(/([-+])/);for(let t=1;t<i.length-1;t+=2){n=i[t];const o=T.decode(i[t+1]);if(o==T._INVALID)return e;null!=o&&("+"===n?s|=o:"-"===n&&(s&=~o))}e=s}else{const n=T.decode(t);n!=T._INVALID&&(e=n)}return e},T.diff=function(e,t){return e=T.decode(e),t=T.decode(t),e==T._INVALID||t==T._INVALID?T._INVALID:e&~t},T.prototype={toString:function(){return'{"mode": "'+T.encode(this.mode)+'", "given": "'+T.encode(this.given)+'", "want": "'+T.encode(this.want)+'"}'},jsonHelper:function(){return{mode:T.encode(this.mode),given:T.encode(this.given),want:T.encode(this.want)}},setMode:function(e){return this.mode=T.decode(e),this},updateMode:function(e){return this.mode=T.update(this.mode,e),this},getMode:function(){return T.encode(this.mode)},setGiven:function(e){return this.given=T.decode(e),this},updateGiven:function(e){return this.given=T.update(this.given,e),this},getGiven:function(){return T.encode(this.given)},setWant:function(e){return this.want=T.decode(e),this},updateWant:function(e){return this.want=T.update(this.want,e),this},getWant:function(){return T.encode(this.want)},getMissing:function(){return T.encode(this.want&~this.given)},getExcessive:function(){return T.encode(this.given&~this.want)},updateAll:function(e){return e&&(this.updateGiven(e.given),this.updateWant(e.want),this.mode=this.given&this.want),this},isOwner:function(e){return T._checkFlag(this,e,T._OWNER)},isPresencer:function(e){return T._checkFlag(this,e,T._PRES)},isMuted:function(e){return!this.isPresencer(e)},isJoiner:function(e){return T._checkFlag(this,e,T._JOIN)},isReader:function(e){return T._checkFlag(this,e,T._READ)},isWriter:function(e){return T._checkFlag(this,e,T._WRITE)},isApprover:function(e){return T._checkFlag(this,e,T._APPROVE)},isAdmin:function(e){return this.isOwner(e)||this.isApprover(e)},isSharer:function(e){return this.isAdmin(e)||T._checkFlag(this,e,T._SHARE)},isDeleter:function(e){return T._checkFlag(this,e,T._DELETE)}};var D=function(e,t){this._tinode=null,this.name=e,this.created=null,this.updated=null,this.touched=null,this.acs=new T(null),this.private=null,this.public=null,this._users={},this._queuedSeqId=268435455,this._maxSeq=0,this._minSeq=0,this._noEarlierMsgs=!1,this._maxDel=0,this._tags=[],this._credentials=[],this._messages=function(e,t){let n=[];function s(t,n,s){let i=0,o=n.length-1,r=0,a=0,c=!1;for(;i<=o;)if((a=e(n[r=(i+o)/2|0],t))<0)i=r+1;else{if(!(a>0)){c=!0;break}o=r-1}return c?{idx:r,exact:!0}:s?{idx:-1}:{idx:a<0?r+1:r}}function i(e,n){const i=s(e,n,!1),o=i.exact&&t?1:0;return n.splice(i.idx,o,e),n}return e=e||function(e,t){return e===t?0:e<t?-1:1},{getAt:function(e){return n[e]},getLast:function(){return n.length>0?n[n.length-1]:void 0},put:function(){let e;e=1==arguments.length&&Array.isArray(arguments[0])?arguments[0]:arguments;for(let t in e)i(e[t],n)},delAt:function(e){let t=n.splice(e,1);if(t&&t.length>0)return t[0]},delRange:function(e,t){return n.splice(e,t-e)},length:function(){return n.length},reset:function(){n=[]},forEach:function(e,t,s,i){t|=0,s=s||n.length;for(let o=t;o<s;o++)e.call(i,n[o],o)},find:function(e,t){const{idx:i}=s(e,n,!t);return i}}}(function(e,t){return e.seq-t.seq},!0),this._subscribed=!1,this._lastDescUpdate=null,this._lastSubsUpdate=null,this._new=!0,t&&(this.onData=t.onData,this.onMeta=t.onMeta,this.onPres=t.onPres,this.onInfo=t.onInfo,this.onMetaDesc=t.onMetaDesc,this.onMetaSub=t.onMetaSub,this.onSubsUpdated=t.onSubsUpdated,this.onTagsUpdated=t.onTagsUpdated,this.onCredsUpdated=t.onCredsUpdated,this.onDeleteTopic=t.onDeleteTopic,this.onAllMessagesReceived=t.onAllMessagesReceived)};D.prototype={isSubscribed:function(){return this._subscribed},subscribe:function(e,t){return this._subscribed?Promise.resolve(this):this._tinode.subscribe(this.name||"new",e,t).then(e=>{if(e.code>=300)return e;if(this._subscribed=!0,this.acs=e.params&&e.params.acs?e.params.acs:this.acs,this._new){if(this._new=!1,this.name=e.topic,this.created=e.ts,this.updated=e.ts,this._cachePutSelf(),"me"!=this.name&&"fnd"!=this.name){const t=this._tinode.getMeTopic();t&&t._processMetaSub([{_noForwarding:!0,topic:this.name,created:e.ts,updated:e.ts,acs:this.acs}])}t&&t.desc&&(t.desc._noForwarding=!0,this._processMetaDesc(t.desc))}return e})},createMessage:function(e,t){return this._tinode.createMessage(this.name,e,t)},publish:function(e,t){return this.publishMessage(this.createMessage(e,t))},publishMessage:function(e){if(!this._subscribed)return Promise.reject(new Error("Cannot publish on inactive topic"));if(i.hasAttachments(e.content)&&!e.head.attachments){let t=[];i.attachments(e.content,e=>{t.push(e.ref)}),e.head.attachments=t}return e._sending=!0,e._failed=!1,this._tinode.publishMessage(e).then(t=>(e._sending=!1,e.ts=t.ts,this.swapMessageId(e,t.params.seq),this._routeData(e),t)).catch(t=>{this._tinode.logger("WARNING: Message rejected by the server",t),e._sending=!1,e._failed=!0,this.onData&&this.onData()})},publishDraft:function(e,t){if(!t&&!this._subscribed)return Promise.reject(new Error("Cannot publish on inactive topic"));const n=e.seq||this._getQueuedSeqId();return e._noForwarding||(e._noForwarding=!0,e.seq=n,e.ts=new Date,e.from=this._tinode.getCurrentUserID(),e.noecho=!0,this._messages.put(e),this.onData&&this.onData(e)),(t||Promise.resolve()).then(()=>e._cancelled?{code:300,text:"cancelled"}:this.publishMessage(e),t=>{this._tinode.logger("WARNING: Message draft rejected by the server",t),e._sending=!1,e._failed=!0,this._messages.delAt(this._messages.find(e)),this.onData&&this.onData()})},leave:function(e){return this._subscribed||e?this._tinode.leave(this.name,e).then(t=>(this._resetSub(),e&&this._gone(),t)):Promise.reject(new Error("Cannot leave inactive topic"))},getMeta:function(e){return this._tinode.getMeta(this.name,e)},getMessagesPage:function(e,t){const n=this.startMetaQuery();t?n.withLaterData(e):n.withEarlierData(e);let s=this.getMeta(n.build());return t||(s=s.then(e=>{e&&e.params&&!e.params.count&&(this._noEarlierMsgs=!0)})),s},setMeta:function(e){return e.tags&&(e.tags=function(e){let t=[];if(Array.isArray(e)){for(let n=0,s=e.length;n<s;n++){let s=e[n];s&&(s=s.trim().toLowerCase()).length>1&&t.push(s)}t.sort().filter(function(e,t,n){return!t||e!=n[t-1]})}return 0==t.length&&t.push(x.DEL_CHAR),t}(e.tags)),this._tinode.setMeta(this.name,e).then(t=>t&&t.code>=300?t:(e.sub&&(e.sub.topic=this.name,t.params&&t.params.acs&&(e.sub.acs=t.params.acs,e.sub.updated=t.ts),e.sub.user||(e.sub.user=this._tinode.getCurrentUserID(),e.desc||(e.desc={})),e.sub._noForwarding=!0,this._processMetaSub([e.sub])),e.desc&&(t.params&&t.params.acs&&(e.desc.acs=t.params.acs,e.desc.updated=t.ts),this._processMetaDesc(e.desc)),e.tags&&this._processMetaTags(e.tags),e.cred&&this._processMetaCreds([e.cred],!0),t))},updateMode:function(e,t){const n=e?this.subscriber(e):null,s=n?n.acs.updateGiven(t).getGiven():this.getAccessMode().updateWant(t).getWant();return this.setMeta({sub:{user:e,mode:s}})},invite:function(e,t){return this.setMeta({sub:{user:e,mode:t}})},archive:function(e){return this.private&&this.private.arch==e?Promise.resolve(e):this.setMeta({desc:{private:{arch:!!e||x.DEL_CHAR}}})},delMessages:function(e,t){if(!this._subscribed)return Promise.reject(new Error("Cannot delete messages in inactive topic"));e.sort((e,t)=>e.low<t.low||e.low==t.low&&(!t.hi||e.hi>=t.hi));let n,s=e.reduce((e,t)=>(t.low<268435455&&(!t.hi||t.hi<268435455?e.push(t):e.push({low:t.low,hi:this._maxSeq+1})),e),[]);return(n=s.length>0?this._tinode.delMessages(this.name,s,t):Promise.resolve({params:{del:0}})).then(t=>(t.params.del>this._maxDel&&(this._maxDel=t.params.del),e.map(e=>{e.hi?this.flushMessageRange(e.low,e.hi):this.flushMessage(e.low)}),this._updateDeletedRanges(),this.onData&&this.onData(),t))},delMessagesAll:function(e){return!this._maxSeq||this._maxSeq<=0?Promise.resolve():this.delMessages([{low:1,hi:this._maxSeq+1,_all:!0}],e)},delMessagesList:function(e,t){e.sort((e,t)=>e-t);let n=e.reduce((e,t)=>{if(0==e.length)e.push({low:t});else{let n=e[e.length-1];!n.hi&&t!=n.low+1||t>n.hi?e.push({low:t}):n.hi=n.hi?Math.max(n.hi,t+1):t+1}return e},[]);return this.delMessages(n,t)},delTopic:function(e){return this._tinode.delTopic(this.name,e).then(e=>(this._resetSub(),this._gone(),e))},delSubscription:function(e){return this._subscribed?this._tinode.delSubscription(this.name,e).then(t=>(delete this._users[e],this.onSubsUpdated&&this.onSubsUpdated(Object.keys(this._users)),t)):Promise.reject(new Error("Cannot delete subscription in inactive topic"))},note:function(e,t){const n=this._users[this._tinode.getCurrentUserID()];n?(!n[e]||n[e]<t)&&(this._subscribed?this._tinode.note(this.name,e,t):this._tinode.logger("INFO: Not sending {note} on inactive topic"),n[e]=t):this._tinode.logger("ERROR: note(): user not found "+this._tinode.getCurrentUserID());const s=this._tinode.getMeTopic();s&&s.setMsgReadRecv(this.name,e,t)},noteRecv:function(e){this.note("recv",e)},noteRead:function(e){(e=e||this._maxSeq)>0&&this.note("read",e)},noteKeyPress:function(){this._subscribed?this._tinode.noteKeyPress(this.name):this._tinode.logger("INFO: Cannot send notification in inactive topic")},userDesc:function(e){const t=this._cacheGetUser(e);if(t)return t},p2pPeerDesc:function(){if("p2p"==this.getType())return this._users[this.name]},subscribers:function(e,t){const n=e||this.onMetaSub;if(n)for(let s in this._users)n.call(t,this._users[s],s,this._users)},tags:function(){return this._tags.slice(0)},subscriber:function(e){return this._users[e]},messages:function(e,t,n,s){const i=e||this.onData;if(i){let e="number"==typeof t?this._messages.find({seq:t},!0):void 0,o="number"==typeof n?this._messages.find({seq:n},!0):void 0;-1!=e&&-1!=o&&this._messages.forEach(i,e,o,s)}},queuedMessages:function(e,t){if(!e)throw new Error("Callback must be provided");this.messages(e,268435455,void 0,t)},msgReceiptCount:function(e,t){let n=0;if(t>0){const s=this._tinode.getCurrentUserID();for(let i in this._users){const o=this._users[i];o.user!==s&&o[e]>=t&&n++}}return n},msgReadCount:function(e){return this.msgReceiptCount("read",e)},msgRecvCount:function(e){return this.msgReceiptCount("recv",e)},msgHasMoreMessages:function(e){return e?this.seq>this._maxSeq:this._minSeq>1&&!this._noEarlierMsgs},isNewMessage:function(e){return this._maxSeq<=e},flushMessage:function(e){const t=this._messages.find({seq:e});return t>=0?this._messages.delAt(t):void 0},swapMessageId:function(e,t){const n=this._messages.find({seq:e.seq}),s=this._messages.length();e.seq=t,0<=n&&n<s&&(n>0&&this._messages.getAt(n-1).seq>=t||n+1<s&&t<this._messages.getAt(n+1).seq<=t)&&(this._messages.delAt(n),this._messages.put(e))},flushMessageRange:function(e,t){const n=this._messages.find({seq:e},!0);return n>=0?this._messages.delRange(n,this._messages.find({seq:t},!0)):[]},cancelSend:function(e){const t=this._messages.find({seq:e});if(t>=0){const e=this._messages.getAt(t),n=this.msgStatus(e);if(1==n||3==n)return e._cancelled=!0,this._messages.delAt(t),this.onData&&this.onData(),!0}return!1},getType:function(){return x.topicType(this.name)},getAccessMode:function(){return this.acs},getDefaultAccess:function(){return this.defacs},startMetaQuery:function(){return new y(this)},isArchived:function(){return!(!this.private||!this.private.arch)},msgStatus:function(e){let t=0;return this._tinode.isMe(e.from)?e._sending?t=2:e._failed?t=3:e.seq>=268435455?t=1:this.msgReadCount(e.seq)>0?t=6:this.msgRecvCount(e.seq)>0?t=5:e.seq>0&&(t=4):t=7,t},_routeData:function(e){e.content&&(!this.touched||this.touched<e.ts)&&(this.touched=e.ts),e.seq>this._maxSeq&&(this._maxSeq=e.seq),(e.seq<this._minSeq||0==this._minSeq)&&(this._minSeq=e.seq),e._noForwarding||(this._messages.put(e),this._updateDeletedRanges()),this.onData&&this.onData(e);const t=this._tinode.getMeTopic();t&&t.setMsgReadRecv(this.name,!e.from||this._tinode.isMe(e.from)?"read":"msg",e.seq,e.ts)},_routeMeta:function(e){e.desc&&(this._lastDescUpdate=e.ts,this._processMetaDesc(e.desc)),e.sub&&e.sub.length>0&&(this._lastSubsUpdate=e.ts,this._processMetaSub(e.sub)),e.del&&this._processDelMessages(e.del.clear,e.del.delseq),e.tags&&this._processMetaTags(e.tags),e.cred&&this._processMetaCreds(e.cred),this.onMeta&&this.onMeta(e)},_routePres:function(e){let t;switch(e.what){case"del":this._processDelMessages(e.clear,e.delseq);break;case"on":case"off":(t=this._users[e.src])?t.online="on"==e.what:this._tinode.logger("WARNING: Presence update for an unknown user",this.name,e.src);break;case"term":this._resetSub();break;case"acs":const n=e.src||this._tinode.getCurrentUserID();if(t=this._users[n])t.acs.updateAll(e.dacs),this._processMetaSub([{user:n,updated:new Date,acs:t.acs}]);else{const s=(new T).updateAll(e.dacs);s&&s.mode!=T._NONE&&((t=this._cacheGetUser(n))?t.acs=s:(t={user:n,acs:s},this.getMeta(this.startMetaQuery().withOneSub(void 0,n).build())),t.updated=new Date,this._processMetaSub([t]))}break;default:this._tinode.logger("INFO: Ignored presence update",e.what)}this.onPres&&this.onPres(e)},_routeInfo:function(e){if("kp"!==e.what){const t=this._users[e.from];if(t&&(t[e.what]=e.seq,t.recv<t.read&&(t.recv=t.read)),this._tinode.isMe(e.from)){const t=this._tinode.getMeTopic();t&&t.setMsgReadRecv(e.topic,e.what,e.seq)}}this.onInfo&&this.onInfo(e)},_processMetaDesc:function(e){if("p2p"==this.getType()&&delete e.defacs,_(this,e),b(this),"me"!==this.name&&!e._noForwarding){const t=this._tinode.getMeTopic();t&&t._processMetaSub([{_noForwarding:!0,topic:this.name,updated:this.updated,touched:this.touched,acs:e.acs,seq:e.seq,read:e.read,recv:e.recv,public:e.public,private:e.private}])}this.onMetaDesc&&this.onMetaDesc(this)},_processMetaSub:function(e){for(let t in e){const n=e[t];n.updated=new Date(n.updated),n.deleted=n.deleted?new Date(n.deleted):null;let s=null;n.deleted?(delete this._users[n.user],s=n):(this._tinode.isMe(n.user)&&n.acs&&this._processMetaDesc({updated:n.updated||new Date,touched:n.updated,acs:n.acs}),s=this._updateCachedUser(n.user,n)),this.onMetaSub&&this.onMetaSub(s)}this.onSubsUpdated&&this.onSubsUpdated(Object.keys(this._users))},_processMetaTags:function(e){1==e.length&&e[0]==x.DEL_CHAR&&(e=[]),this._tags=e,this.onTagsUpdated&&this.onTagsUpdated(e)},_processMetaCreds:function(e){},_processDelMessages:function(e,t){this._maxDel=Math.max(e,this._maxDel),this.clear=Math.max(e,this.clear);const n=this;let s=0;Array.isArray(t)&&t.map(function(e){if(e.hi)for(let t=e.low;t<e.hi;t++)s++,n.flushMessage(t);else s++,n.flushMessage(e.low)}),s>0&&(this._updateDeletedRanges(),this.onData&&this.onData())},_allMessagesReceived:function(e){this._updateDeletedRanges(),this.onAllMessagesReceived&&this.onAllMessagesReceived(e)},_resetSub:function(){this._subscribed=!1},_gone:function(){this._messages.reset(),this._users={},this.acs=new T(null),this.private=null,this.public=null,this._maxSeq=0,this._minSeq=0,this._subscribed=!1;const e=this._tinode.getMeTopic();e&&e._routePres({_noForwarding:!0,what:"gone",topic:"me",src:this.name}),this.onDeleteTopic&&this.onDeleteTopic()},_updateCachedUser:function(e,t){let n=this._cacheGetUser(e);return n=_(n||{},t),this._cachePutUser(e,n),m(this._users,e,n)},_getQueuedSeqId:function(){return this._queuedSeqId++},_updateDeletedRanges:function(){const e=[];let t=null;const n=this._messages.getAt(0);n&&this._minSeq>1&&!this._noEarlierMsgs?n.hi?(n.seq>1&&(n.seq=1),n.hi<this._minSeq-1&&(n.hi=this._minSeq-1),t=n):(t={seq:1,hi:this._minSeq-1},e.push(t)):t={seq:0,hi:0},this._messages.forEach(n=>{n.seq>=268435455||(n.seq!=(t.hi||t.seq)+1?t.hi?t.hi=n.hi||n.seq:(t={seq:(t.hi||t.seq)+1,hi:n.hi||n.seq},e.push(t)):t=n)});const s=this._messages.getLast(),i=Math.max(this.seq,this._maxSeq)||0;(i>0&&!s||s&&(s.hi||s.seq)<i)&&(s&&s.hi?s.hi=i:e.push({seq:s?s.seq+1:1,hi:i})),e.map(e=>{this._messages.put(e)})}};var A=function(e){D.call(this,"me",e),this._contacts={},e&&(this.onContactUpdate=e.onContactUpdate)};A.prototype=Object.create(D.prototype,{_processMetaDesc:{value:function(e){const t=e.acs&&!e.acs.isPresencer()&&this.acs&&this.acs.isPresencer();_(this,e),b(this),t&&Object.values(this._contacts).map(e=>{e.online&&(e.online=!1,e.seen?e.seen.when=new Date:e.seen={when:new Date},this.onContactUpdate&&this.onContactUpdate("off",e))}),this.onMetaDesc&&this.onMetaDesc(this)},enumerable:!0,configurable:!0,writable:!1},_processMetaSub:{value:function(e){let t=0;for(let n in e){const s=e[n],i=s.topic;if("fnd"==i||"me"==i)continue;s.updated=new Date(s.updated),s.touched=s.touched?new Date(s.touched):void 0,s.deleted=s.deleted?new Date(s.deleted):null;let o=null;if(s.deleted)o=s,delete this._contacts[i];else if(void 0!==s.seq&&(s.seq=0|s.seq,s.recv=0|s.recv,s.read=0|s.read,s.unread=s.seq-s.read),s.seen&&s.seen.when&&(s.seen.when=new Date(s.seen.when)),o=m(this._contacts,i,s),"p2p"==x.topicType(i)&&this._cachePutUser(i,o),!s._noForwarding){const e=this._tinode.getTopic(i);e&&(s._noForwarding=!0,e._processMetaDesc(s))}t++,this.onMetaSub&&this.onMetaSub(o)}this.onSubsUpdated&&this.onSubsUpdated(Object.keys(this._contacts),t)},enumerable:!0,configurable:!0,writable:!1},_processMetaCreds:{value:function(e,t){1==e.length&&e[0]==x.DEL_CHAR&&(e=[]),t?e.map(e=>{if(e.val){let t=this._credentials.findIndex(t=>t.meth==e.meth&&t.val==e.val);t<0?(e.done||(t=this._credentials.findIndex(t=>t.meth==e.meth&&!t.done))>=0&&this._credentials.splice(t,1),this._credentials.push(e)):this._credentials[t].done=e.done}else if(e.resp){const t=this._credentials.findIndex(t=>t.meth==e.meth&&!t.done);t>=0&&(this._credentials[t].done=!0)}}):this._credentials=e,this.onCredsUpdated&&this.onCredsUpdated(this._credentials)},enumerable:!0,configurable:!0,writable:!1},_routePres:{value:function(e){if("term"==e.what)return void this._resetSub();if("upd"==e.what&&"me"==e.src)return void this.getMeta(this.startMetaQuery().withDesc().build());const t=this._contacts[e.src];if(t){switch(e.what){case"on":t.online=!0;break;case"off":t.online&&(t.online=!1,t.seen?t.seen.when=new Date:t.seen={when:new Date});break;case"msg":t.touched=new Date,t.seq=0|e.seq,e.act&&!this._tinode.isMe(e.act)||(t.read=t.read?Math.max(t.read,t.seq):t.seq,t.recv=t.recv?Math.max(t.read,t.recv):t.recv),t.unread=t.seq-t.read;break;case"upd":this.getMeta(this.startMetaQuery().withLaterOneSub(e.src).build());break;case"acs":t.acs?t.acs.updateAll(e.dacs):t.acs=(new T).updateAll(e.dacs),t.touched=new Date;break;case"ua":t.seen={when:new Date,ua:e.ua};break;case"recv":e.seq=0|e.seq,t.recv=t.recv?Math.max(t.recv,e.seq):e.seq;break;case"read":e.seq=0|e.seq,t.read=t.read?Math.max(t.read,e.seq):e.seq,t.recv=t.recv?Math.max(t.read,t.recv):t.recv,t.unread=t.seq-t.read;break;case"gone":delete this._contacts[e.src];break;case"del":break;default:this._tinode.logger("INFO: Unsupported presence update in 'me'",e.what)}this.onContactUpdate&&this.onContactUpdate(e.what,t)}else if("acs"==e.what){const t=new T(e.dacs);if(!t||t.mode==T._INVALID)return void this._tinode.logger("ERROR: Invalid access mode update",e.src,e.dacs);if(t.mode==T._NONE)return void this._tinode.logger("WARNING: Removing non-existent subscription",e.src,e.dacs);this.getMeta(this.startMetaQuery().withOneSub(void 0,e.src).build()),this._contacts[e.src]={touched:new Date,topic:e.src,online:!1,acs:t}}else"tags"==e.what&&this.getMeta(this.startMetaQuery().withTags().build());this.onPres&&this.onPres(e)},enumerable:!0,configurable:!0,writable:!1},publish:{value:function(){return Promise.reject(new Error("Publishing to 'me' is not supported"))},enumerable:!0,configurable:!0,writable:!1},delCredential:{value:function(e,t){return this._subscribed?this._tinode.delCredential(e,t).then(n=>{const s=this._credentials.findIndex(n=>n.meth==e&&n.val==t);return s>-1&&this._credentials.splice(s,1),this.onCredsUpdated&&this.onCredsUpdated(this._credentials),n}):Promise.reject(new Error("Cannot delete credential in inactive 'me' topic"))},enumerable:!0,configurable:!0,writable:!1},contacts:{value:function(e,t,n){for(let s in this._contacts){const i=this._contacts[s];t&&!t(i)||e.call(n,i,s,this._contacts)}},enumerable:!0,configurable:!0,writable:!0},setMsgReadRecv:{value:function(e,t,n,s){const i=this._contacts[e];let o,r=!1;if(i){switch(n|=0,i.seq=0|i.seq,i.read=0|i.read,i.recv=0|i.recv,t){case"recv":o=i.recv,i.recv=Math.max(i.recv,n),r=o!=i.recv;break;case"read":o=i.read,i.read=Math.max(i.read,n),r=o!=i.read;break;case"msg":o=i.seq,i.seq=Math.max(i.seq,n),(!i.touched||i.touched<s)&&(i.touched=s),r=o!=i.seq}i.recv<i.read&&(i.recv=i.read,r=!0),i.seq<i.recv&&(i.seq=i.recv,(!i.touched||i.touched<s)&&(i.touched=s),r=!0),i.unread=i.seq-i.read,!r||i.acs&&i.acs.isMuted()||!this.onContactUpdate||this.onContactUpdate(t,i)}},enumerable:!0,configurable:!0,writable:!0},getContact:{value:function(e){return this._contacts[e]},enumerable:!0,configurable:!0,writable:!0},getAccessMode:{value:function(e){if(e){const t=this._contacts[e];return t?t.acs:null}return this.acs},enumerable:!0,configurable:!0,writable:!0},isArchived:{value:function(e){const t=this._contacts[e];return t?!(!t.private||!t.private.arch):null},enumerable:!0,configurable:!0,writable:!0},getCredentials:{value:function(){return this._credentials},enumerable:!0,configurable:!0,writable:!0}}),A.prototype.constructor=A;var E=function(e){D.call(this,"fnd",e),this._contacts={}};E.prototype=Object.create(D.prototype,{_processMetaSub:{value:function(e){let t=Object.getOwnPropertyNames(this._contacts).length;this._contacts={};for(let n in e){let s=e[n];const i=s.topic?s.topic:s.user;s.updated=new Date(s.updated),s.seen&&s.seen.when&&(s.seen.when=new Date(s.seen.when)),s=m(this._contacts,i,s),t++,this.onMetaSub&&this.onMetaSub(s)}t>0&&this.onSubsUpdated&&this.onSubsUpdated(Object.keys(this._contacts))},enumerable:!0,configurable:!0,writable:!1},publish:{value:function(){return Promise.reject(new Error("Publishing to 'fnd' is not supported"))},enumerable:!0,configurable:!0,writable:!1},setMeta:{value:function(e){const t=this;return Object.getPrototypeOf(E.prototype).setMeta.call(this,e).then(function(){Object.keys(t._contacts).length>0&&(t._contacts={},t.onSubsUpdated&&t.onSubsUpdated([]))})},enumerable:!0,configurable:!0,writable:!1},contacts:{value:function(e,t){const n=e||this.onMetaSub;if(n)for(let s in this._contacts)n.call(t,this._contacts[s],s,this._contacts)},enumerable:!0,configurable:!0,writable:!0}}),E.prototype.constructor=E;var R=function(e){this._tinode=e,this._apiKey=e._apiKey,this._authToken=e.getAuthToken(),this._msgId=e.getNextUniqueId(),this.xhr=new XMLHttpRequest,this.toResolve=null,this.toReject=null,this.onProgress=null,this.onSuccess=null,this.onFailure=null};R.prototype={uploadWithBaseUrl:function(e,t,n,s,i){if(!this._authToken)throw new Error("Must authenticate first");const o=this;let r="/v"+c+"/file/u/";if(e){if(0!=e.indexOf("http://")&&0!=e.indexOf("https://"))throw new Error("Invalid base URL '"+e+"'");r=e+r}this.xhr.open("POST",r,!0),this.xhr.setRequestHeader("X-Tinode-APIKey",this._apiKey),this.xhr.setRequestHeader("X-Tinode-Auth","Token "+this._authToken.token);const a=new Promise((e,t)=>{this.toResolve=e,this.toReject=t});this.onProgress=n,this.onSuccess=s,this.onFailure=i,this.xhr.upload.onprogress=function(e){e.lengthComputable&&o.onProgress&&o.onProgress(e.loaded/e.total)},this.xhr.onload=function(){let e;try{e=JSON.parse(this.response,w)}catch(t){o._tinode.logger("ERROR: Invalid server response in LargeFileHelper",this.response),e={ctrl:{code:this.status,text:this.statusText}}}this.status>=200&&this.status<300?(o.toResolve&&o.toResolve(e.ctrl.params.url),o.onSuccess&&o.onSuccess(e.ctrl)):this.status>=400?(o.toReject&&o.toReject(new Error(e.ctrl.text+" ("+e.ctrl.code+")")),o.onFailure&&o.onFailure(e.ctrl)):o._tinode.logger("ERROR: Unexpected server response status",this.status,this.response)},this.xhr.onerror=function(e){o.toReject&&o.toReject(new Error("failed")),o.onFailure&&o.onFailure(null)},this.xhr.onabort=function(e){o.toReject&&o.toReject(new Error("upload cancelled by user")),o.onFailure&&o.onFailure(null)};try{const e=new FormData;e.append("file",t),e.set("id",this._msgId),this.xhr.send(e)}catch(u){this.toReject&&this.toReject(u),this.onFailure&&this.onFailure(null)}return a},upload:function(e,t,n,s){return this.uploadWithBaseUrl(void 0,e,t,n,s)},download:function(e,t,n,s){if(/^\s*([a-z][a-z0-9+.-]*:|\/\/)/im.test(e))throw new Error("The URL '"+e+"' must be relative, not absolute");if(!this._authToken)throw new Error("Must authenticate first");const i=this;this.xhr.open("GET",e,!0),this.xhr.setRequestHeader("X-Tinode-APIKey",this._apiKey),this.xhr.setRequestHeader("X-Tinode-Auth","Token "+this._authToken.token),this.xhr.responseType="blob",this.onProgress=s,this.xhr.onprogress=function(e){i.onProgress&&i.onProgress(e.loaded)};const o=new Promise((e,t)=>{this.toResolve=e,this.toReject=t});this.xhr.onload=function(){if(200==this.status){const e=document.createElement("a");e.href=window.URL.createObjectURL(new Blob([this.response],{type:n})),e.style.display="none",e.setAttribute("download",t),document.body.appendChild(e),e.click(),document.body.removeChild(e),window.URL.revokeObjectURL(e.href),i.toResolve&&i.toResolve()}else if(this.status>=400&&i.toReject){const e=new FileReader;e.onload=function(){try{const t=JSON.parse(this.result,w);i.toReject(new Error(t.ctrl.text+" ("+t.ctrl.code+")"))}catch(e){i._tinode.logger("ERROR: Invalid server response in LargeFileHelper",this.result),i.toReject(e)}},e.readAsText(this.response)}},this.xhr.onerror=function(e){i.toReject&&i.toReject(new Error("failed"))},this.xhr.onabort=function(){i.toReject&&i.toReject(null)};try{this.xhr.send()}catch(r){this.toReject&&this.toReject(r)}return o},cancel:function(){this.xhr&&this.xhr.readyState<4&&this.xhr.abort()},getId:function(){return this._msgId}};var k=function e(t,n){this.status=e.STATUS_NONE,this.topic=t,this.content=n};k.STATUS_NONE=0,k.STATUS_QUEUED=1,k.STATUS_SENDING=2,k.STATUS_FAILED=3,k.STATUS_SENT=4,k.STATUS_RECEIVED=5,k.STATUS_READ=6,k.STATUS_TO_ME=7,(k.prototype={toJSON:function(){},fromJSON:function(e){}}).constructor=k,s.exports=x,s.exports.Drafty=i,s.exports.AccessMode=T}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{}),s=s.exports});