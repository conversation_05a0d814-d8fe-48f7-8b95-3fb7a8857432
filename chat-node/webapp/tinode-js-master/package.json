{"name": "tinode-sdk", "description": "Tinode SDK", "version": "0.16.7", "scripts": {"format": "js-beautify -r src/tinode.js && js-beautify -r src/drafty.js", "build": "npm run vers && npm run format && npm run build:prod && npm run build:dev", "build:prod": "browserify ./src/tinode.js --standalone Tinode -t [ babelify ] --plugin tinyify > ./umd/tinode.prod.js", "build:dev": "browserify ./src/tinode.js --debug --standalone Tinode > ./umd/tinode.dev.js", "build:docs": "jsdoc ./src -t ./node_modules/minami -d ../tinode.github.io/js-api", "vers": "echo \"{\\\"version\\\": \\\"`node -p -e \"require('./package.json').version\"`\\\"}\" > version.json", "test": "echo \"Error: no test specified\" && exit 1"}, "browserslist": "> 0.5%, not IE 11", "repository": {"type": "git", "url": "git+https://github.com/tinode/tinode-js.git"}, "files": ["src/tinode.js", "src/drafty.js", "umd/tinode.dev.js", "umd/tinode.prod.js", "version.json"], "keywords": ["instant messenger", "messenger", "chat"], "email": "<EMAIL>", "author": "Tinode Authors <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/tinode/tinode-js/issues"}, "homepage": "https://github.com/tinode/chat", "main": "./umd/tinode.prod.js", "devDependencies": {"@babel/core": "^7.10.5", "@babel/preset-env": "^7.10.4", "babelify": "^10.0.0", "browserify": "^16.5.1", "js-beautify": "^1.11.0", "jsdoc": "^3.6.4", "minami": "^1.2.3", "tinyify": "^2.5.2"}}