// Code generated by protoc-gen-go. DO NOT EDIT.
// source: model.proto

package pbx

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Authentication level
type AuthLevel int32

const (
	AuthLevel_NONE AuthLevel = 0
	AuthLevel_ANON AuthLevel = 10
	AuthLevel_AUTH AuthLevel = 20
	AuthLevel_ROOT AuthLevel = 30
)

var AuthLevel_name = map[int32]string{
	0:  "NONE",
	10: "ANON",
	20: "AUTH",
	30: "ROOT",
}
var AuthLevel_value = map[string]int32{
	"NONE": 0,
	"ANON": 10,
	"AUTH": 20,
	"ROOT": 30,
}

func (x AuthLevel) String() string {
	return proto.EnumName(AuthLevel_name, int32(x))
}
func (AuthLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{0}
}

type InfoNote int32

const (
	InfoNote_READ InfoNote = 0
	InfoNote_RECV InfoNote = 1
	InfoNote_KP   InfoNote = 2
)

var InfoNote_name = map[int32]string{
	0: "READ",
	1: "RECV",
	2: "KP",
}
var InfoNote_value = map[string]int32{
	"READ": 0,
	"RECV": 1,
	"KP":   2,
}

func (x InfoNote) String() string {
	return proto.EnumName(InfoNote_name, int32(x))
}
func (InfoNote) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{1}
}

// Plugin response codes
type RespCode int32

const (
	// Instruct Tinode server to continue with default processing of the client request.
	RespCode_CONTINUE RespCode = 0
	// Drop the request as if the client did not send it
	RespCode_DROP RespCode = 1
	// Send the the provided response to the client.
	RespCode_RESPOND RespCode = 2
	// Replace client's original request with the provided request then continue with
	// processing.
	RespCode_REPLACE RespCode = 3
)

var RespCode_name = map[int32]string{
	0: "CONTINUE",
	1: "DROP",
	2: "RESPOND",
	3: "REPLACE",
}
var RespCode_value = map[string]int32{
	"CONTINUE": 0,
	"DROP":     1,
	"RESPOND":  2,
	"REPLACE":  3,
}

func (x RespCode) String() string {
	return proto.EnumName(RespCode_name, int32(x))
}
func (RespCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{2}
}

type Crud int32

const (
	Crud_CREATE Crud = 0
	Crud_UPDATE Crud = 1
	Crud_DELETE Crud = 2
)

var Crud_name = map[int32]string{
	0: "CREATE",
	1: "UPDATE",
	2: "DELETE",
}
var Crud_value = map[string]int32{
	"CREATE": 0,
	"UPDATE": 1,
	"DELETE": 2,
}

func (x Crud) String() string {
	return proto.EnumName(Crud_name, int32(x))
}
func (Crud) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{3}
}

// What to delete, either "msg" to delete messages (default) or "topic" to delete the topic or "sub"
// to delete a subscription to topic.
type ClientDel_What int32

const (
	ClientDel_MSG   ClientDel_What = 0
	ClientDel_TOPIC ClientDel_What = 1
	ClientDel_SUB   ClientDel_What = 2
	ClientDel_USER  ClientDel_What = 3
	ClientDel_CRED  ClientDel_What = 4
)

var ClientDel_What_name = map[int32]string{
	0: "MSG",
	1: "TOPIC",
	2: "SUB",
	3: "USER",
	4: "CRED",
}
var ClientDel_What_value = map[string]int32{
	"MSG":   0,
	"TOPIC": 1,
	"SUB":   2,
	"USER":  3,
	"CRED":  4,
}

func (x ClientDel_What) String() string {
	return proto.EnumName(ClientDel_What_name, int32(x))
}
func (ClientDel_What) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{18, 0}
}

type ServerPres_What int32

const (
	ServerPres_ON   ServerPres_What = 0
	ServerPres_OFF  ServerPres_What = 1
	ServerPres_UA   ServerPres_What = 3
	ServerPres_UPD  ServerPres_What = 4
	ServerPres_GONE ServerPres_What = 5
	ServerPres_ACS  ServerPres_What = 6
	ServerPres_TERM ServerPres_What = 7
	ServerPres_MSG  ServerPres_What = 8
	ServerPres_READ ServerPres_What = 9
	ServerPres_RECV ServerPres_What = 10
	ServerPres_DEL  ServerPres_What = 11
	ServerPres_TAGS ServerPres_What = 12
)

var ServerPres_What_name = map[int32]string{
	0:  "ON",
	1:  "OFF",
	3:  "UA",
	4:  "UPD",
	5:  "GONE",
	6:  "ACS",
	7:  "TERM",
	8:  "MSG",
	9:  "READ",
	10: "RECV",
	11: "DEL",
	12: "TAGS",
}
var ServerPres_What_value = map[string]int32{
	"ON":   0,
	"OFF":  1,
	"UA":   3,
	"UPD":  4,
	"GONE": 5,
	"ACS":  6,
	"TERM": 7,
	"MSG":  8,
	"READ": 9,
	"RECV": 10,
	"DEL":  11,
	"TAGS": 12,
}

func (x ServerPres_What) String() string {
	return proto.EnumName(ServerPres_What_name, int32(x))
}
func (ServerPres_What) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{27, 0}
}

// Dummy placeholder message.
type Unused struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Unused) Reset()         { *m = Unused{} }
func (m *Unused) String() string { return proto.CompactTextString(m) }
func (*Unused) ProtoMessage()    {}
func (*Unused) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{0}
}
func (m *Unused) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Unused.Unmarshal(m, b)
}
func (m *Unused) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Unused.Marshal(b, m, deterministic)
}
func (dst *Unused) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Unused.Merge(dst, src)
}
func (m *Unused) XXX_Size() int {
	return xxx_messageInfo_Unused.Size(m)
}
func (m *Unused) XXX_DiscardUnknown() {
	xxx_messageInfo_Unused.DiscardUnknown(m)
}

var xxx_messageInfo_Unused proto.InternalMessageInfo

// Topic default access mode
type DefaultAcsMode struct {
	Auth                 string   `protobuf:"bytes,1,opt,name=auth" json:"auth,omitempty"`
	Anon                 string   `protobuf:"bytes,2,opt,name=anon" json:"anon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DefaultAcsMode) Reset()         { *m = DefaultAcsMode{} }
func (m *DefaultAcsMode) String() string { return proto.CompactTextString(m) }
func (*DefaultAcsMode) ProtoMessage()    {}
func (*DefaultAcsMode) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{1}
}
func (m *DefaultAcsMode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DefaultAcsMode.Unmarshal(m, b)
}
func (m *DefaultAcsMode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DefaultAcsMode.Marshal(b, m, deterministic)
}
func (dst *DefaultAcsMode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DefaultAcsMode.Merge(dst, src)
}
func (m *DefaultAcsMode) XXX_Size() int {
	return xxx_messageInfo_DefaultAcsMode.Size(m)
}
func (m *DefaultAcsMode) XXX_DiscardUnknown() {
	xxx_messageInfo_DefaultAcsMode.DiscardUnknown(m)
}

var xxx_messageInfo_DefaultAcsMode proto.InternalMessageInfo

func (m *DefaultAcsMode) GetAuth() string {
	if m != nil {
		return m.Auth
	}
	return ""
}

func (m *DefaultAcsMode) GetAnon() string {
	if m != nil {
		return m.Anon
	}
	return ""
}

// Actual access mode
type AccessMode struct {
	// Access mode requested by the user
	Want string `protobuf:"bytes,1,opt,name=want" json:"want,omitempty"`
	// Access mode granted to the user by the admin
	Given                string   `protobuf:"bytes,2,opt,name=given" json:"given,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AccessMode) Reset()         { *m = AccessMode{} }
func (m *AccessMode) String() string { return proto.CompactTextString(m) }
func (*AccessMode) ProtoMessage()    {}
func (*AccessMode) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{2}
}
func (m *AccessMode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccessMode.Unmarshal(m, b)
}
func (m *AccessMode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccessMode.Marshal(b, m, deterministic)
}
func (dst *AccessMode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccessMode.Merge(dst, src)
}
func (m *AccessMode) XXX_Size() int {
	return xxx_messageInfo_AccessMode.Size(m)
}
func (m *AccessMode) XXX_DiscardUnknown() {
	xxx_messageInfo_AccessMode.DiscardUnknown(m)
}

var xxx_messageInfo_AccessMode proto.InternalMessageInfo

func (m *AccessMode) GetWant() string {
	if m != nil {
		return m.Want
	}
	return ""
}

func (m *AccessMode) GetGiven() string {
	if m != nil {
		return m.Given
	}
	return ""
}

// SetSub: payload in set.sub request to update current subscription or invite another user, {sub.what} == "sub"
type SetSub struct {
	// User affected by this request. Default (empty): current user
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	// Access mode change, either Given or Want depending on context
	Mode                 string   `protobuf:"bytes,2,opt,name=mode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSub) Reset()         { *m = SetSub{} }
func (m *SetSub) String() string { return proto.CompactTextString(m) }
func (*SetSub) ProtoMessage()    {}
func (*SetSub) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{3}
}
func (m *SetSub) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSub.Unmarshal(m, b)
}
func (m *SetSub) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSub.Marshal(b, m, deterministic)
}
func (dst *SetSub) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSub.Merge(dst, src)
}
func (m *SetSub) XXX_Size() int {
	return xxx_messageInfo_SetSub.Size(m)
}
func (m *SetSub) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSub.DiscardUnknown(m)
}

var xxx_messageInfo_SetSub proto.InternalMessageInfo

func (m *SetSub) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SetSub) GetMode() string {
	if m != nil {
		return m.Mode
	}
	return ""
}

// Credentials such as email or phone number
type ClientCred struct {
	// Credential type, i.e. `email` or `tel`.
	Method string `protobuf:"bytes,1,opt,name=method" json:"method,omitempty"`
	// Value to verify, i.e. `<EMAIL>` or `+18003287448`
	Value string `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
	// Verification response
	Response string `protobuf:"bytes,3,opt,name=response" json:"response,omitempty"`
	// Request parameters, such as preferences or country code.
	Params               map[string][]byte `protobuf:"bytes,4,rep,name=params" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ClientCred) Reset()         { *m = ClientCred{} }
func (m *ClientCred) String() string { return proto.CompactTextString(m) }
func (*ClientCred) ProtoMessage()    {}
func (*ClientCred) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{4}
}
func (m *ClientCred) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientCred.Unmarshal(m, b)
}
func (m *ClientCred) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientCred.Marshal(b, m, deterministic)
}
func (dst *ClientCred) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientCred.Merge(dst, src)
}
func (m *ClientCred) XXX_Size() int {
	return xxx_messageInfo_ClientCred.Size(m)
}
func (m *ClientCred) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientCred.DiscardUnknown(m)
}

var xxx_messageInfo_ClientCred proto.InternalMessageInfo

func (m *ClientCred) GetMethod() string {
	if m != nil {
		return m.Method
	}
	return ""
}

func (m *ClientCred) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *ClientCred) GetResponse() string {
	if m != nil {
		return m.Response
	}
	return ""
}

func (m *ClientCred) GetParams() map[string][]byte {
	if m != nil {
		return m.Params
	}
	return nil
}

// SetDesc: C2S in set.what == "desc" and sub.init message
type SetDesc struct {
	DefaultAcs           *DefaultAcsMode `protobuf:"bytes,1,opt,name=default_acs,json=defaultAcs" json:"default_acs,omitempty"`
	Public               []byte          `protobuf:"bytes,2,opt,name=public,proto3" json:"public,omitempty"`
	Private              []byte          `protobuf:"bytes,3,opt,name=private,proto3" json:"private,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetDesc) Reset()         { *m = SetDesc{} }
func (m *SetDesc) String() string { return proto.CompactTextString(m) }
func (*SetDesc) ProtoMessage()    {}
func (*SetDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{5}
}
func (m *SetDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDesc.Unmarshal(m, b)
}
func (m *SetDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDesc.Marshal(b, m, deterministic)
}
func (dst *SetDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDesc.Merge(dst, src)
}
func (m *SetDesc) XXX_Size() int {
	return xxx_messageInfo_SetDesc.Size(m)
}
func (m *SetDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDesc.DiscardUnknown(m)
}

var xxx_messageInfo_SetDesc proto.InternalMessageInfo

func (m *SetDesc) GetDefaultAcs() *DefaultAcsMode {
	if m != nil {
		return m.DefaultAcs
	}
	return nil
}

func (m *SetDesc) GetPublic() []byte {
	if m != nil {
		return m.Public
	}
	return nil
}

func (m *SetDesc) GetPrivate() []byte {
	if m != nil {
		return m.Private
	}
	return nil
}

type GetOpts struct {
	// Timestamp in milliseconds since epoch 01/01/1970
	IfModifiedSince int64 `protobuf:"varint,1,opt,name=if_modified_since,json=ifModifiedSince" json:"if_modified_since,omitempty"`
	// Limit search to this user ID
	User string `protobuf:"bytes,2,opt,name=user" json:"user,omitempty"`
	// Limit search results to one topic;
	Topic string `protobuf:"bytes,3,opt,name=topic" json:"topic,omitempty"`
	// Load messages with seq id equal or greater than this
	SinceId int32 `protobuf:"varint,4,opt,name=since_id,json=sinceId" json:"since_id,omitempty"`
	// Load messages with seq id lower than this
	BeforeId int32 `protobuf:"varint,5,opt,name=before_id,json=beforeId" json:"before_id,omitempty"`
	// Maximum number of results to return
	Limit                int32    `protobuf:"varint,6,opt,name=limit" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOpts) Reset()         { *m = GetOpts{} }
func (m *GetOpts) String() string { return proto.CompactTextString(m) }
func (*GetOpts) ProtoMessage()    {}
func (*GetOpts) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{6}
}
func (m *GetOpts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpts.Unmarshal(m, b)
}
func (m *GetOpts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpts.Marshal(b, m, deterministic)
}
func (dst *GetOpts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpts.Merge(dst, src)
}
func (m *GetOpts) XXX_Size() int {
	return xxx_messageInfo_GetOpts.Size(m)
}
func (m *GetOpts) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpts.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpts proto.InternalMessageInfo

func (m *GetOpts) GetIfModifiedSince() int64 {
	if m != nil {
		return m.IfModifiedSince
	}
	return 0
}

func (m *GetOpts) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

func (m *GetOpts) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *GetOpts) GetSinceId() int32 {
	if m != nil {
		return m.SinceId
	}
	return 0
}

func (m *GetOpts) GetBeforeId() int32 {
	if m != nil {
		return m.BeforeId
	}
	return 0
}

func (m *GetOpts) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetQuery struct {
	What string `protobuf:"bytes,1,opt,name=what" json:"what,omitempty"`
	// Parameters of "desc" request
	Desc *GetOpts `protobuf:"bytes,2,opt,name=desc" json:"desc,omitempty"`
	// Parameters of "sub" request
	Sub *GetOpts `protobuf:"bytes,3,opt,name=sub" json:"sub,omitempty"`
	// Parameters of "data" request
	Data                 *GetOpts `protobuf:"bytes,4,opt,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuery) Reset()         { *m = GetQuery{} }
func (m *GetQuery) String() string { return proto.CompactTextString(m) }
func (*GetQuery) ProtoMessage()    {}
func (*GetQuery) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{7}
}
func (m *GetQuery) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuery.Unmarshal(m, b)
}
func (m *GetQuery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuery.Marshal(b, m, deterministic)
}
func (dst *GetQuery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuery.Merge(dst, src)
}
func (m *GetQuery) XXX_Size() int {
	return xxx_messageInfo_GetQuery.Size(m)
}
func (m *GetQuery) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuery.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuery proto.InternalMessageInfo

func (m *GetQuery) GetWhat() string {
	if m != nil {
		return m.What
	}
	return ""
}

func (m *GetQuery) GetDesc() *GetOpts {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (m *GetQuery) GetSub() *GetOpts {
	if m != nil {
		return m.Sub
	}
	return nil
}

func (m *GetQuery) GetData() *GetOpts {
	if m != nil {
		return m.Data
	}
	return nil
}

type SetQuery struct {
	// Topic metadata, new topic & new subscriptions only
	Desc *SetDesc `protobuf:"bytes,1,opt,name=desc" json:"desc,omitempty"`
	// Subscription parameters
	Sub *SetSub `protobuf:"bytes,2,opt,name=sub" json:"sub,omitempty"`
	// Indexable tags
	Tags []string `protobuf:"bytes,3,rep,name=tags" json:"tags,omitempty"`
	// Credential being updated.
	Cred                 *ClientCred `protobuf:"bytes,4,opt,name=cred" json:"cred,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetQuery) Reset()         { *m = SetQuery{} }
func (m *SetQuery) String() string { return proto.CompactTextString(m) }
func (*SetQuery) ProtoMessage()    {}
func (*SetQuery) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{8}
}
func (m *SetQuery) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetQuery.Unmarshal(m, b)
}
func (m *SetQuery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetQuery.Marshal(b, m, deterministic)
}
func (dst *SetQuery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetQuery.Merge(dst, src)
}
func (m *SetQuery) XXX_Size() int {
	return xxx_messageInfo_SetQuery.Size(m)
}
func (m *SetQuery) XXX_DiscardUnknown() {
	xxx_messageInfo_SetQuery.DiscardUnknown(m)
}

var xxx_messageInfo_SetQuery proto.InternalMessageInfo

func (m *SetQuery) GetDesc() *SetDesc {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (m *SetQuery) GetSub() *SetSub {
	if m != nil {
		return m.Sub
	}
	return nil
}

func (m *SetQuery) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *SetQuery) GetCred() *ClientCred {
	if m != nil {
		return m.Cred
	}
	return nil
}

type SeqRange struct {
	Low                  int32    `protobuf:"varint,1,opt,name=low" json:"low,omitempty"`
	Hi                   int32    `protobuf:"varint,2,opt,name=hi" json:"hi,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeqRange) Reset()         { *m = SeqRange{} }
func (m *SeqRange) String() string { return proto.CompactTextString(m) }
func (*SeqRange) ProtoMessage()    {}
func (*SeqRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{9}
}
func (m *SeqRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeqRange.Unmarshal(m, b)
}
func (m *SeqRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeqRange.Marshal(b, m, deterministic)
}
func (dst *SeqRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeqRange.Merge(dst, src)
}
func (m *SeqRange) XXX_Size() int {
	return xxx_messageInfo_SeqRange.Size(m)
}
func (m *SeqRange) XXX_DiscardUnknown() {
	xxx_messageInfo_SeqRange.DiscardUnknown(m)
}

var xxx_messageInfo_SeqRange proto.InternalMessageInfo

func (m *SeqRange) GetLow() int32 {
	if m != nil {
		return m.Low
	}
	return 0
}

func (m *SeqRange) GetHi() int32 {
	if m != nil {
		return m.Hi
	}
	return 0
}

// Client handshake
type ClientHi struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	UserAgent            string   `protobuf:"bytes,2,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	Ver                  string   `protobuf:"bytes,3,opt,name=ver" json:"ver,omitempty"`
	DeviceId             string   `protobuf:"bytes,4,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`
	Lang                 string   `protobuf:"bytes,5,opt,name=lang" json:"lang,omitempty"`
	Platform             string   `protobuf:"bytes,6,opt,name=platform" json:"platform,omitempty"`
	Background           bool     `protobuf:"varint,7,opt,name=background" json:"background,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientHi) Reset()         { *m = ClientHi{} }
func (m *ClientHi) String() string { return proto.CompactTextString(m) }
func (*ClientHi) ProtoMessage()    {}
func (*ClientHi) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{10}
}
func (m *ClientHi) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientHi.Unmarshal(m, b)
}
func (m *ClientHi) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientHi.Marshal(b, m, deterministic)
}
func (dst *ClientHi) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientHi.Merge(dst, src)
}
func (m *ClientHi) XXX_Size() int {
	return xxx_messageInfo_ClientHi.Size(m)
}
func (m *ClientHi) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientHi.DiscardUnknown(m)
}

var xxx_messageInfo_ClientHi proto.InternalMessageInfo

func (m *ClientHi) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientHi) GetUserAgent() string {
	if m != nil {
		return m.UserAgent
	}
	return ""
}

func (m *ClientHi) GetVer() string {
	if m != nil {
		return m.Ver
	}
	return ""
}

func (m *ClientHi) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ClientHi) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *ClientHi) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *ClientHi) GetBackground() bool {
	if m != nil {
		return m.Background
	}
	return false
}

// User creation message {acc}
type ClientAcc struct {
	Id string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// User being created or updated
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	// The initial authentication scheme the account can use
	Scheme string `protobuf:"bytes,3,opt,name=scheme" json:"scheme,omitempty"`
	// Shared secret
	Secret []byte `protobuf:"bytes,4,opt,name=secret,proto3" json:"secret,omitempty"`
	// Authenticate session with the newly created account
	Login bool `protobuf:"varint,5,opt,name=login" json:"login,omitempty"`
	// Indexable tags for user discovery
	Tags []string `protobuf:"bytes,6,rep,name=tags" json:"tags,omitempty"`
	// User initialization data when creating a new user, otherwise ignored
	Desc *SetDesc `protobuf:"bytes,7,opt,name=desc" json:"desc,omitempty"`
	// Credentials for verification.
	Cred []*ClientCred `protobuf:"bytes,8,rep,name=cred" json:"cred,omitempty"`
	// Authentication token used for resetting a password.
	Token []byte `protobuf:"bytes,9,opt,name=token,proto3" json:"token,omitempty"`
	// Account state: normal ("ok"), suspended
	State                string   `protobuf:"bytes,10,opt,name=state" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientAcc) Reset()         { *m = ClientAcc{} }
func (m *ClientAcc) String() string { return proto.CompactTextString(m) }
func (*ClientAcc) ProtoMessage()    {}
func (*ClientAcc) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{11}
}
func (m *ClientAcc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientAcc.Unmarshal(m, b)
}
func (m *ClientAcc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientAcc.Marshal(b, m, deterministic)
}
func (dst *ClientAcc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientAcc.Merge(dst, src)
}
func (m *ClientAcc) XXX_Size() int {
	return xxx_messageInfo_ClientAcc.Size(m)
}
func (m *ClientAcc) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientAcc.DiscardUnknown(m)
}

var xxx_messageInfo_ClientAcc proto.InternalMessageInfo

func (m *ClientAcc) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientAcc) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ClientAcc) GetScheme() string {
	if m != nil {
		return m.Scheme
	}
	return ""
}

func (m *ClientAcc) GetSecret() []byte {
	if m != nil {
		return m.Secret
	}
	return nil
}

func (m *ClientAcc) GetLogin() bool {
	if m != nil {
		return m.Login
	}
	return false
}

func (m *ClientAcc) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *ClientAcc) GetDesc() *SetDesc {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (m *ClientAcc) GetCred() []*ClientCred {
	if m != nil {
		return m.Cred
	}
	return nil
}

func (m *ClientAcc) GetToken() []byte {
	if m != nil {
		return m.Token
	}
	return nil
}

func (m *ClientAcc) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

// Login {login} message
type ClientLogin struct {
	Id string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	// Authentication scheme
	Scheme string `protobuf:"bytes,2,opt,name=scheme" json:"scheme,omitempty"`
	// Shared secret
	Secret []byte `protobuf:"bytes,3,opt,name=secret,proto3" json:"secret,omitempty"`
	// Credentials for verification.
	Cred                 []*ClientCred `protobuf:"bytes,4,rep,name=cred" json:"cred,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ClientLogin) Reset()         { *m = ClientLogin{} }
func (m *ClientLogin) String() string { return proto.CompactTextString(m) }
func (*ClientLogin) ProtoMessage()    {}
func (*ClientLogin) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{12}
}
func (m *ClientLogin) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientLogin.Unmarshal(m, b)
}
func (m *ClientLogin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientLogin.Marshal(b, m, deterministic)
}
func (dst *ClientLogin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientLogin.Merge(dst, src)
}
func (m *ClientLogin) XXX_Size() int {
	return xxx_messageInfo_ClientLogin.Size(m)
}
func (m *ClientLogin) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientLogin.DiscardUnknown(m)
}

var xxx_messageInfo_ClientLogin proto.InternalMessageInfo

func (m *ClientLogin) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientLogin) GetScheme() string {
	if m != nil {
		return m.Scheme
	}
	return ""
}

func (m *ClientLogin) GetSecret() []byte {
	if m != nil {
		return m.Secret
	}
	return nil
}

func (m *ClientLogin) GetCred() []*ClientCred {
	if m != nil {
		return m.Cred
	}
	return nil
}

// Subscription request {sub} message
type ClientSub struct {
	Id    string `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Topic string `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	// mirrors {set}
	SetQuery *SetQuery `protobuf:"bytes,3,opt,name=set_query,json=setQuery" json:"set_query,omitempty"`
	// mirrors {get}
	GetQuery             *GetQuery `protobuf:"bytes,4,opt,name=get_query,json=getQuery" json:"get_query,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ClientSub) Reset()         { *m = ClientSub{} }
func (m *ClientSub) String() string { return proto.CompactTextString(m) }
func (*ClientSub) ProtoMessage()    {}
func (*ClientSub) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{13}
}
func (m *ClientSub) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientSub.Unmarshal(m, b)
}
func (m *ClientSub) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientSub.Marshal(b, m, deterministic)
}
func (dst *ClientSub) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientSub.Merge(dst, src)
}
func (m *ClientSub) XXX_Size() int {
	return xxx_messageInfo_ClientSub.Size(m)
}
func (m *ClientSub) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientSub.DiscardUnknown(m)
}

var xxx_messageInfo_ClientSub proto.InternalMessageInfo

func (m *ClientSub) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientSub) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ClientSub) GetSetQuery() *SetQuery {
	if m != nil {
		return m.SetQuery
	}
	return nil
}

func (m *ClientSub) GetGetQuery() *GetQuery {
	if m != nil {
		return m.GetQuery
	}
	return nil
}

// Unsubscribe {leave} request message
type ClientLeave struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Topic                string   `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	Unsub                bool     `protobuf:"varint,3,opt,name=unsub" json:"unsub,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientLeave) Reset()         { *m = ClientLeave{} }
func (m *ClientLeave) String() string { return proto.CompactTextString(m) }
func (*ClientLeave) ProtoMessage()    {}
func (*ClientLeave) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{14}
}
func (m *ClientLeave) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientLeave.Unmarshal(m, b)
}
func (m *ClientLeave) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientLeave.Marshal(b, m, deterministic)
}
func (dst *ClientLeave) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientLeave.Merge(dst, src)
}
func (m *ClientLeave) XXX_Size() int {
	return xxx_messageInfo_ClientLeave.Size(m)
}
func (m *ClientLeave) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientLeave.DiscardUnknown(m)
}

var xxx_messageInfo_ClientLeave proto.InternalMessageInfo

func (m *ClientLeave) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientLeave) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ClientLeave) GetUnsub() bool {
	if m != nil {
		return m.Unsub
	}
	return false
}

// ClientPub is client's request to publish data to topic subscribers {pub}
type ClientPub struct {
	Id                   string            `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Topic                string            `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	NoEcho               bool              `protobuf:"varint,3,opt,name=no_echo,json=noEcho" json:"no_echo,omitempty"`
	Head                 map[string][]byte `protobuf:"bytes,4,rep,name=head" json:"head,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Content              []byte            `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ClientPub) Reset()         { *m = ClientPub{} }
func (m *ClientPub) String() string { return proto.CompactTextString(m) }
func (*ClientPub) ProtoMessage()    {}
func (*ClientPub) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{15}
}
func (m *ClientPub) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientPub.Unmarshal(m, b)
}
func (m *ClientPub) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientPub.Marshal(b, m, deterministic)
}
func (dst *ClientPub) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientPub.Merge(dst, src)
}
func (m *ClientPub) XXX_Size() int {
	return xxx_messageInfo_ClientPub.Size(m)
}
func (m *ClientPub) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientPub.DiscardUnknown(m)
}

var xxx_messageInfo_ClientPub proto.InternalMessageInfo

func (m *ClientPub) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientPub) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ClientPub) GetNoEcho() bool {
	if m != nil {
		return m.NoEcho
	}
	return false
}

func (m *ClientPub) GetHead() map[string][]byte {
	if m != nil {
		return m.Head
	}
	return nil
}

func (m *ClientPub) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

// Query topic state {get}
type ClientGet struct {
	Id                   string    `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Topic                string    `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	Query                *GetQuery `protobuf:"bytes,3,opt,name=query" json:"query,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ClientGet) Reset()         { *m = ClientGet{} }
func (m *ClientGet) String() string { return proto.CompactTextString(m) }
func (*ClientGet) ProtoMessage()    {}
func (*ClientGet) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{16}
}
func (m *ClientGet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientGet.Unmarshal(m, b)
}
func (m *ClientGet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientGet.Marshal(b, m, deterministic)
}
func (dst *ClientGet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientGet.Merge(dst, src)
}
func (m *ClientGet) XXX_Size() int {
	return xxx_messageInfo_ClientGet.Size(m)
}
func (m *ClientGet) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientGet.DiscardUnknown(m)
}

var xxx_messageInfo_ClientGet proto.InternalMessageInfo

func (m *ClientGet) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientGet) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ClientGet) GetQuery() *GetQuery {
	if m != nil {
		return m.Query
	}
	return nil
}

// Update topic state {set}
type ClientSet struct {
	Id                   string    `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Topic                string    `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	Query                *SetQuery `protobuf:"bytes,3,opt,name=query" json:"query,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ClientSet) Reset()         { *m = ClientSet{} }
func (m *ClientSet) String() string { return proto.CompactTextString(m) }
func (*ClientSet) ProtoMessage()    {}
func (*ClientSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{17}
}
func (m *ClientSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientSet.Unmarshal(m, b)
}
func (m *ClientSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientSet.Marshal(b, m, deterministic)
}
func (dst *ClientSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientSet.Merge(dst, src)
}
func (m *ClientSet) XXX_Size() int {
	return xxx_messageInfo_ClientSet.Size(m)
}
func (m *ClientSet) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientSet.DiscardUnknown(m)
}

var xxx_messageInfo_ClientSet proto.InternalMessageInfo

func (m *ClientSet) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientSet) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ClientSet) GetQuery() *SetQuery {
	if m != nil {
		return m.Query
	}
	return nil
}

// ClientDel delete messages or topic
type ClientDel struct {
	Id    string         `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Topic string         `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	What  ClientDel_What `protobuf:"varint,3,opt,name=what,enum=pbx.ClientDel_What" json:"what,omitempty"`
	// Delete messages by id or range of ids
	DelSeq []*SeqRange `protobuf:"bytes,4,rep,name=del_seq,json=delSeq" json:"del_seq,omitempty"`
	// User ID of the subscription to delete
	UserId string `protobuf:"bytes,5,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	// Credential to delete.
	Cred *ClientCred `protobuf:"bytes,6,opt,name=cred" json:"cred,omitempty"`
	// Request to hard-delete messages for all users, if such option is available.
	Hard                 bool     `protobuf:"varint,7,opt,name=hard" json:"hard,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientDel) Reset()         { *m = ClientDel{} }
func (m *ClientDel) String() string { return proto.CompactTextString(m) }
func (*ClientDel) ProtoMessage()    {}
func (*ClientDel) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{18}
}
func (m *ClientDel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientDel.Unmarshal(m, b)
}
func (m *ClientDel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientDel.Marshal(b, m, deterministic)
}
func (dst *ClientDel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientDel.Merge(dst, src)
}
func (m *ClientDel) XXX_Size() int {
	return xxx_messageInfo_ClientDel.Size(m)
}
func (m *ClientDel) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientDel.DiscardUnknown(m)
}

var xxx_messageInfo_ClientDel proto.InternalMessageInfo

func (m *ClientDel) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ClientDel) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ClientDel) GetWhat() ClientDel_What {
	if m != nil {
		return m.What
	}
	return ClientDel_MSG
}

func (m *ClientDel) GetDelSeq() []*SeqRange {
	if m != nil {
		return m.DelSeq
	}
	return nil
}

func (m *ClientDel) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ClientDel) GetCred() *ClientCred {
	if m != nil {
		return m.Cred
	}
	return nil
}

func (m *ClientDel) GetHard() bool {
	if m != nil {
		return m.Hard
	}
	return false
}

// ClientNote is a client-generated notification for topic subscribers
type ClientNote struct {
	Topic string `protobuf:"bytes,1,opt,name=topic" json:"topic,omitempty"`
	// what is being reported: "recv" - message received, "read" - message read, "kp" - typing notification
	What InfoNote `protobuf:"varint,2,opt,name=what,enum=pbx.InfoNote" json:"what,omitempty"`
	// Server-issued message ID being reported
	SeqId                int32    `protobuf:"varint,3,opt,name=seq_id,json=seqId" json:"seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientNote) Reset()         { *m = ClientNote{} }
func (m *ClientNote) String() string { return proto.CompactTextString(m) }
func (*ClientNote) ProtoMessage()    {}
func (*ClientNote) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{19}
}
func (m *ClientNote) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientNote.Unmarshal(m, b)
}
func (m *ClientNote) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientNote.Marshal(b, m, deterministic)
}
func (dst *ClientNote) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientNote.Merge(dst, src)
}
func (m *ClientNote) XXX_Size() int {
	return xxx_messageInfo_ClientNote.Size(m)
}
func (m *ClientNote) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientNote.DiscardUnknown(m)
}

var xxx_messageInfo_ClientNote proto.InternalMessageInfo

func (m *ClientNote) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ClientNote) GetWhat() InfoNote {
	if m != nil {
		return m.What
	}
	return InfoNote_READ
}

func (m *ClientNote) GetSeqId() int32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type ClientMsg struct {
	// Types that are valid to be assigned to Message:
	//	*ClientMsg_Hi
	//	*ClientMsg_Acc
	//	*ClientMsg_Login
	//	*ClientMsg_Sub
	//	*ClientMsg_Leave
	//	*ClientMsg_Pub
	//	*ClientMsg_Get
	//	*ClientMsg_Set
	//	*ClientMsg_Del
	//	*ClientMsg_Note
	Message isClientMsg_Message `protobuf_oneof:"Message"`
	// Root user may send messages on behalf of other users.
	OnBehalfOf           string    `protobuf:"bytes,11,opt,name=on_behalf_of,json=onBehalfOf" json:"on_behalf_of,omitempty"`
	AuthLevel            AuthLevel `protobuf:"varint,12,opt,name=auth_level,json=authLevel,enum=pbx.AuthLevel" json:"auth_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ClientMsg) Reset()         { *m = ClientMsg{} }
func (m *ClientMsg) String() string { return proto.CompactTextString(m) }
func (*ClientMsg) ProtoMessage()    {}
func (*ClientMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{20}
}
func (m *ClientMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientMsg.Unmarshal(m, b)
}
func (m *ClientMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientMsg.Marshal(b, m, deterministic)
}
func (dst *ClientMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientMsg.Merge(dst, src)
}
func (m *ClientMsg) XXX_Size() int {
	return xxx_messageInfo_ClientMsg.Size(m)
}
func (m *ClientMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ClientMsg proto.InternalMessageInfo

type isClientMsg_Message interface {
	isClientMsg_Message()
}

type ClientMsg_Hi struct {
	Hi *ClientHi `protobuf:"bytes,1,opt,name=hi,oneof"`
}
type ClientMsg_Acc struct {
	Acc *ClientAcc `protobuf:"bytes,2,opt,name=acc,oneof"`
}
type ClientMsg_Login struct {
	Login *ClientLogin `protobuf:"bytes,3,opt,name=login,oneof"`
}
type ClientMsg_Sub struct {
	Sub *ClientSub `protobuf:"bytes,4,opt,name=sub,oneof"`
}
type ClientMsg_Leave struct {
	Leave *ClientLeave `protobuf:"bytes,5,opt,name=leave,oneof"`
}
type ClientMsg_Pub struct {
	Pub *ClientPub `protobuf:"bytes,6,opt,name=pub,oneof"`
}
type ClientMsg_Get struct {
	Get *ClientGet `protobuf:"bytes,7,opt,name=get,oneof"`
}
type ClientMsg_Set struct {
	Set *ClientSet `protobuf:"bytes,8,opt,name=set,oneof"`
}
type ClientMsg_Del struct {
	Del *ClientDel `protobuf:"bytes,9,opt,name=del,oneof"`
}
type ClientMsg_Note struct {
	Note *ClientNote `protobuf:"bytes,10,opt,name=note,oneof"`
}

func (*ClientMsg_Hi) isClientMsg_Message()    {}
func (*ClientMsg_Acc) isClientMsg_Message()   {}
func (*ClientMsg_Login) isClientMsg_Message() {}
func (*ClientMsg_Sub) isClientMsg_Message()   {}
func (*ClientMsg_Leave) isClientMsg_Message() {}
func (*ClientMsg_Pub) isClientMsg_Message()   {}
func (*ClientMsg_Get) isClientMsg_Message()   {}
func (*ClientMsg_Set) isClientMsg_Message()   {}
func (*ClientMsg_Del) isClientMsg_Message()   {}
func (*ClientMsg_Note) isClientMsg_Message()  {}

func (m *ClientMsg) GetMessage() isClientMsg_Message {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *ClientMsg) GetHi() *ClientHi {
	if x, ok := m.GetMessage().(*ClientMsg_Hi); ok {
		return x.Hi
	}
	return nil
}

func (m *ClientMsg) GetAcc() *ClientAcc {
	if x, ok := m.GetMessage().(*ClientMsg_Acc); ok {
		return x.Acc
	}
	return nil
}

func (m *ClientMsg) GetLogin() *ClientLogin {
	if x, ok := m.GetMessage().(*ClientMsg_Login); ok {
		return x.Login
	}
	return nil
}

func (m *ClientMsg) GetSub() *ClientSub {
	if x, ok := m.GetMessage().(*ClientMsg_Sub); ok {
		return x.Sub
	}
	return nil
}

func (m *ClientMsg) GetLeave() *ClientLeave {
	if x, ok := m.GetMessage().(*ClientMsg_Leave); ok {
		return x.Leave
	}
	return nil
}

func (m *ClientMsg) GetPub() *ClientPub {
	if x, ok := m.GetMessage().(*ClientMsg_Pub); ok {
		return x.Pub
	}
	return nil
}

func (m *ClientMsg) GetGet() *ClientGet {
	if x, ok := m.GetMessage().(*ClientMsg_Get); ok {
		return x.Get
	}
	return nil
}

func (m *ClientMsg) GetSet() *ClientSet {
	if x, ok := m.GetMessage().(*ClientMsg_Set); ok {
		return x.Set
	}
	return nil
}

func (m *ClientMsg) GetDel() *ClientDel {
	if x, ok := m.GetMessage().(*ClientMsg_Del); ok {
		return x.Del
	}
	return nil
}

func (m *ClientMsg) GetNote() *ClientNote {
	if x, ok := m.GetMessage().(*ClientMsg_Note); ok {
		return x.Note
	}
	return nil
}

func (m *ClientMsg) GetOnBehalfOf() string {
	if m != nil {
		return m.OnBehalfOf
	}
	return ""
}

func (m *ClientMsg) GetAuthLevel() AuthLevel {
	if m != nil {
		return m.AuthLevel
	}
	return AuthLevel_NONE
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*ClientMsg) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _ClientMsg_OneofMarshaler, _ClientMsg_OneofUnmarshaler, _ClientMsg_OneofSizer, []interface{}{
		(*ClientMsg_Hi)(nil),
		(*ClientMsg_Acc)(nil),
		(*ClientMsg_Login)(nil),
		(*ClientMsg_Sub)(nil),
		(*ClientMsg_Leave)(nil),
		(*ClientMsg_Pub)(nil),
		(*ClientMsg_Get)(nil),
		(*ClientMsg_Set)(nil),
		(*ClientMsg_Del)(nil),
		(*ClientMsg_Note)(nil),
	}
}

func _ClientMsg_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*ClientMsg)
	// Message
	switch x := m.Message.(type) {
	case *ClientMsg_Hi:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Hi); err != nil {
			return err
		}
	case *ClientMsg_Acc:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Acc); err != nil {
			return err
		}
	case *ClientMsg_Login:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Login); err != nil {
			return err
		}
	case *ClientMsg_Sub:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Sub); err != nil {
			return err
		}
	case *ClientMsg_Leave:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Leave); err != nil {
			return err
		}
	case *ClientMsg_Pub:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Pub); err != nil {
			return err
		}
	case *ClientMsg_Get:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Get); err != nil {
			return err
		}
	case *ClientMsg_Set:
		b.EncodeVarint(8<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Set); err != nil {
			return err
		}
	case *ClientMsg_Del:
		b.EncodeVarint(9<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Del); err != nil {
			return err
		}
	case *ClientMsg_Note:
		b.EncodeVarint(10<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Note); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("ClientMsg.Message has unexpected type %T", x)
	}
	return nil
}

func _ClientMsg_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*ClientMsg)
	switch tag {
	case 1: // Message.hi
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientHi)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Hi{msg}
		return true, err
	case 2: // Message.acc
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientAcc)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Acc{msg}
		return true, err
	case 3: // Message.login
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientLogin)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Login{msg}
		return true, err
	case 4: // Message.sub
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientSub)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Sub{msg}
		return true, err
	case 5: // Message.leave
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientLeave)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Leave{msg}
		return true, err
	case 6: // Message.pub
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientPub)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Pub{msg}
		return true, err
	case 7: // Message.get
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientGet)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Get{msg}
		return true, err
	case 8: // Message.set
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientSet)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Set{msg}
		return true, err
	case 9: // Message.del
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientDel)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Del{msg}
		return true, err
	case 10: // Message.note
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ClientNote)
		err := b.DecodeMessage(msg)
		m.Message = &ClientMsg_Note{msg}
		return true, err
	default:
		return false, nil
	}
}

func _ClientMsg_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*ClientMsg)
	// Message
	switch x := m.Message.(type) {
	case *ClientMsg_Hi:
		s := proto.Size(x.Hi)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Acc:
		s := proto.Size(x.Acc)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Login:
		s := proto.Size(x.Login)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Sub:
		s := proto.Size(x.Sub)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Leave:
		s := proto.Size(x.Leave)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Pub:
		s := proto.Size(x.Pub)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Get:
		s := proto.Size(x.Get)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Set:
		s := proto.Size(x.Set)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Del:
		s := proto.Size(x.Del)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ClientMsg_Note:
		s := proto.Size(x.Note)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// Credentials
type ServerCred struct {
	// Credential type, i.e. `email` or `tel`.
	Method string `protobuf:"bytes,1,opt,name=method" json:"method,omitempty"`
	// Value to verify, i.e. `<EMAIL>` or `+18003287448`
	Value string `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
	// Indicator that the credential is validated
	Done                 bool     `protobuf:"varint,3,opt,name=done" json:"done,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServerCred) Reset()         { *m = ServerCred{} }
func (m *ServerCred) String() string { return proto.CompactTextString(m) }
func (*ServerCred) ProtoMessage()    {}
func (*ServerCred) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{21}
}
func (m *ServerCred) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerCred.Unmarshal(m, b)
}
func (m *ServerCred) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerCred.Marshal(b, m, deterministic)
}
func (dst *ServerCred) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerCred.Merge(dst, src)
}
func (m *ServerCred) XXX_Size() int {
	return xxx_messageInfo_ServerCred.Size(m)
}
func (m *ServerCred) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerCred.DiscardUnknown(m)
}

var xxx_messageInfo_ServerCred proto.InternalMessageInfo

func (m *ServerCred) GetMethod() string {
	if m != nil {
		return m.Method
	}
	return ""
}

func (m *ServerCred) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *ServerCred) GetDone() bool {
	if m != nil {
		return m.Done
	}
	return false
}

// Topic description, S2C in Meta message
type TopicDesc struct {
	CreatedAt            int64           `protobuf:"varint,1,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt            int64           `protobuf:"varint,2,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	TouchedAt            int64           `protobuf:"varint,3,opt,name=touched_at,json=touchedAt" json:"touched_at,omitempty"`
	Defacs               *DefaultAcsMode `protobuf:"bytes,4,opt,name=defacs" json:"defacs,omitempty"`
	Acs                  *AccessMode     `protobuf:"bytes,5,opt,name=acs" json:"acs,omitempty"`
	SeqId                int32           `protobuf:"varint,6,opt,name=seq_id,json=seqId" json:"seq_id,omitempty"`
	ReadId               int32           `protobuf:"varint,7,opt,name=read_id,json=readId" json:"read_id,omitempty"`
	RecvId               int32           `protobuf:"varint,8,opt,name=recv_id,json=recvId" json:"recv_id,omitempty"`
	DelId                int32           `protobuf:"varint,9,opt,name=del_id,json=delId" json:"del_id,omitempty"`
	Public               []byte          `protobuf:"bytes,10,opt,name=public,proto3" json:"public,omitempty"`
	Private              []byte          `protobuf:"bytes,11,opt,name=private,proto3" json:"private,omitempty"`
	State                string          `protobuf:"bytes,12,opt,name=state" json:"state,omitempty"`
	StateAt              int64           `protobuf:"varint,13,opt,name=state_at,json=stateAt" json:"state_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TopicDesc) Reset()         { *m = TopicDesc{} }
func (m *TopicDesc) String() string { return proto.CompactTextString(m) }
func (*TopicDesc) ProtoMessage()    {}
func (*TopicDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{22}
}
func (m *TopicDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicDesc.Unmarshal(m, b)
}
func (m *TopicDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicDesc.Marshal(b, m, deterministic)
}
func (dst *TopicDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicDesc.Merge(dst, src)
}
func (m *TopicDesc) XXX_Size() int {
	return xxx_messageInfo_TopicDesc.Size(m)
}
func (m *TopicDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicDesc.DiscardUnknown(m)
}

var xxx_messageInfo_TopicDesc proto.InternalMessageInfo

func (m *TopicDesc) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *TopicDesc) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *TopicDesc) GetTouchedAt() int64 {
	if m != nil {
		return m.TouchedAt
	}
	return 0
}

func (m *TopicDesc) GetDefacs() *DefaultAcsMode {
	if m != nil {
		return m.Defacs
	}
	return nil
}

func (m *TopicDesc) GetAcs() *AccessMode {
	if m != nil {
		return m.Acs
	}
	return nil
}

func (m *TopicDesc) GetSeqId() int32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *TopicDesc) GetReadId() int32 {
	if m != nil {
		return m.ReadId
	}
	return 0
}

func (m *TopicDesc) GetRecvId() int32 {
	if m != nil {
		return m.RecvId
	}
	return 0
}

func (m *TopicDesc) GetDelId() int32 {
	if m != nil {
		return m.DelId
	}
	return 0
}

func (m *TopicDesc) GetPublic() []byte {
	if m != nil {
		return m.Public
	}
	return nil
}

func (m *TopicDesc) GetPrivate() []byte {
	if m != nil {
		return m.Private
	}
	return nil
}

func (m *TopicDesc) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *TopicDesc) GetStateAt() int64 {
	if m != nil {
		return m.StateAt
	}
	return 0
}

// MsgTopicSub: topic subscription details, sent in Meta message
type TopicSub struct {
	UpdatedAt int64       `protobuf:"varint,1,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	DeletedAt int64       `protobuf:"varint,2,opt,name=deleted_at,json=deletedAt" json:"deleted_at,omitempty"`
	Online    bool        `protobuf:"varint,3,opt,name=online" json:"online,omitempty"`
	Acs       *AccessMode `protobuf:"bytes,4,opt,name=acs" json:"acs,omitempty"`
	ReadId    int32       `protobuf:"varint,5,opt,name=read_id,json=readId" json:"read_id,omitempty"`
	RecvId    int32       `protobuf:"varint,6,opt,name=recv_id,json=recvId" json:"recv_id,omitempty"`
	Public    []byte      `protobuf:"bytes,7,opt,name=public,proto3" json:"public,omitempty"`
	Private   []byte      `protobuf:"bytes,8,opt,name=private,proto3" json:"private,omitempty"`
	// Uid of the subscribed user
	UserId string `protobuf:"bytes,9,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	// Topic name of this subscription
	Topic     string `protobuf:"bytes,10,opt,name=topic" json:"topic,omitempty"`
	TouchedAt int64  `protobuf:"varint,11,opt,name=touched_at,json=touchedAt" json:"touched_at,omitempty"`
	// ID of the last {data} message in a topic
	SeqId int32 `protobuf:"varint,12,opt,name=seq_id,json=seqId" json:"seq_id,omitempty"`
	// Messages are deleted up to this ID
	DelId int32 `protobuf:"varint,13,opt,name=del_id,json=delId" json:"del_id,omitempty"`
	// Other user's last online timestamp & user agent
	LastSeenTime         int64    `protobuf:"varint,14,opt,name=last_seen_time,json=lastSeenTime" json:"last_seen_time,omitempty"`
	LastSeenUserAgent    string   `protobuf:"bytes,15,opt,name=last_seen_user_agent,json=lastSeenUserAgent" json:"last_seen_user_agent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicSub) Reset()         { *m = TopicSub{} }
func (m *TopicSub) String() string { return proto.CompactTextString(m) }
func (*TopicSub) ProtoMessage()    {}
func (*TopicSub) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{23}
}
func (m *TopicSub) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicSub.Unmarshal(m, b)
}
func (m *TopicSub) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicSub.Marshal(b, m, deterministic)
}
func (dst *TopicSub) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicSub.Merge(dst, src)
}
func (m *TopicSub) XXX_Size() int {
	return xxx_messageInfo_TopicSub.Size(m)
}
func (m *TopicSub) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicSub.DiscardUnknown(m)
}

var xxx_messageInfo_TopicSub proto.InternalMessageInfo

func (m *TopicSub) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *TopicSub) GetDeletedAt() int64 {
	if m != nil {
		return m.DeletedAt
	}
	return 0
}

func (m *TopicSub) GetOnline() bool {
	if m != nil {
		return m.Online
	}
	return false
}

func (m *TopicSub) GetAcs() *AccessMode {
	if m != nil {
		return m.Acs
	}
	return nil
}

func (m *TopicSub) GetReadId() int32 {
	if m != nil {
		return m.ReadId
	}
	return 0
}

func (m *TopicSub) GetRecvId() int32 {
	if m != nil {
		return m.RecvId
	}
	return 0
}

func (m *TopicSub) GetPublic() []byte {
	if m != nil {
		return m.Public
	}
	return nil
}

func (m *TopicSub) GetPrivate() []byte {
	if m != nil {
		return m.Private
	}
	return nil
}

func (m *TopicSub) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *TopicSub) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *TopicSub) GetTouchedAt() int64 {
	if m != nil {
		return m.TouchedAt
	}
	return 0
}

func (m *TopicSub) GetSeqId() int32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *TopicSub) GetDelId() int32 {
	if m != nil {
		return m.DelId
	}
	return 0
}

func (m *TopicSub) GetLastSeenTime() int64 {
	if m != nil {
		return m.LastSeenTime
	}
	return 0
}

func (m *TopicSub) GetLastSeenUserAgent() string {
	if m != nil {
		return m.LastSeenUserAgent
	}
	return ""
}

type DelValues struct {
	DelId                int32       `protobuf:"varint,1,opt,name=del_id,json=delId" json:"del_id,omitempty"`
	DelSeq               []*SeqRange `protobuf:"bytes,2,rep,name=del_seq,json=delSeq" json:"del_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DelValues) Reset()         { *m = DelValues{} }
func (m *DelValues) String() string { return proto.CompactTextString(m) }
func (*DelValues) ProtoMessage()    {}
func (*DelValues) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{24}
}
func (m *DelValues) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelValues.Unmarshal(m, b)
}
func (m *DelValues) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelValues.Marshal(b, m, deterministic)
}
func (dst *DelValues) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelValues.Merge(dst, src)
}
func (m *DelValues) XXX_Size() int {
	return xxx_messageInfo_DelValues.Size(m)
}
func (m *DelValues) XXX_DiscardUnknown() {
	xxx_messageInfo_DelValues.DiscardUnknown(m)
}

var xxx_messageInfo_DelValues proto.InternalMessageInfo

func (m *DelValues) GetDelId() int32 {
	if m != nil {
		return m.DelId
	}
	return 0
}

func (m *DelValues) GetDelSeq() []*SeqRange {
	if m != nil {
		return m.DelSeq
	}
	return nil
}

// {ctrl} message
type ServerCtrl struct {
	Id                   string            `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Topic                string            `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	Code                 int32             `protobuf:"varint,3,opt,name=code" json:"code,omitempty"`
	Text                 string            `protobuf:"bytes,4,opt,name=text" json:"text,omitempty"`
	Params               map[string][]byte `protobuf:"bytes,5,rep,name=params" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ServerCtrl) Reset()         { *m = ServerCtrl{} }
func (m *ServerCtrl) String() string { return proto.CompactTextString(m) }
func (*ServerCtrl) ProtoMessage()    {}
func (*ServerCtrl) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{25}
}
func (m *ServerCtrl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerCtrl.Unmarshal(m, b)
}
func (m *ServerCtrl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerCtrl.Marshal(b, m, deterministic)
}
func (dst *ServerCtrl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerCtrl.Merge(dst, src)
}
func (m *ServerCtrl) XXX_Size() int {
	return xxx_messageInfo_ServerCtrl.Size(m)
}
func (m *ServerCtrl) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerCtrl.DiscardUnknown(m)
}

var xxx_messageInfo_ServerCtrl proto.InternalMessageInfo

func (m *ServerCtrl) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ServerCtrl) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ServerCtrl) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ServerCtrl) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ServerCtrl) GetParams() map[string][]byte {
	if m != nil {
		return m.Params
	}
	return nil
}

// {data} message
type ServerData struct {
	Topic string `protobuf:"bytes,1,opt,name=topic" json:"topic,omitempty"`
	// ID of the user who originated the message as {pub}, could be empty if sent by the system
	FromUserId string `protobuf:"bytes,2,opt,name=from_user_id,json=fromUserId" json:"from_user_id,omitempty"`
	// Timestamp when the message was sent.
	Timestamp int64 `protobuf:"varint,7,opt,name=timestamp" json:"timestamp,omitempty"`
	// Timestamp when the message was deleted or 0. Milliseconds since the epoch 01/01/1970
	DeletedAt            int64             `protobuf:"varint,3,opt,name=deleted_at,json=deletedAt" json:"deleted_at,omitempty"`
	SeqId                int32             `protobuf:"varint,4,opt,name=seq_id,json=seqId" json:"seq_id,omitempty"`
	Head                 map[string][]byte `protobuf:"bytes,5,rep,name=head" json:"head,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Content              []byte            `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ServerData) Reset()         { *m = ServerData{} }
func (m *ServerData) String() string { return proto.CompactTextString(m) }
func (*ServerData) ProtoMessage()    {}
func (*ServerData) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{26}
}
func (m *ServerData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerData.Unmarshal(m, b)
}
func (m *ServerData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerData.Marshal(b, m, deterministic)
}
func (dst *ServerData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerData.Merge(dst, src)
}
func (m *ServerData) XXX_Size() int {
	return xxx_messageInfo_ServerData.Size(m)
}
func (m *ServerData) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerData.DiscardUnknown(m)
}

var xxx_messageInfo_ServerData proto.InternalMessageInfo

func (m *ServerData) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ServerData) GetFromUserId() string {
	if m != nil {
		return m.FromUserId
	}
	return ""
}

func (m *ServerData) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ServerData) GetDeletedAt() int64 {
	if m != nil {
		return m.DeletedAt
	}
	return 0
}

func (m *ServerData) GetSeqId() int32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *ServerData) GetHead() map[string][]byte {
	if m != nil {
		return m.Head
	}
	return nil
}

func (m *ServerData) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

// {pres} message
type ServerPres struct {
	Topic                string          `protobuf:"bytes,1,opt,name=topic" json:"topic,omitempty"`
	Src                  string          `protobuf:"bytes,2,opt,name=src" json:"src,omitempty"`
	What                 ServerPres_What `protobuf:"varint,3,opt,name=what,enum=pbx.ServerPres_What" json:"what,omitempty"`
	UserAgent            string          `protobuf:"bytes,4,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	SeqId                int32           `protobuf:"varint,5,opt,name=seq_id,json=seqId" json:"seq_id,omitempty"`
	DelId                int32           `protobuf:"varint,6,opt,name=del_id,json=delId" json:"del_id,omitempty"`
	DelSeq               []*SeqRange     `protobuf:"bytes,7,rep,name=del_seq,json=delSeq" json:"del_seq,omitempty"`
	TargetUserId         string          `protobuf:"bytes,8,opt,name=target_user_id,json=targetUserId" json:"target_user_id,omitempty"`
	ActorUserId          string          `protobuf:"bytes,9,opt,name=actor_user_id,json=actorUserId" json:"actor_user_id,omitempty"`
	Acs                  *AccessMode     `protobuf:"bytes,10,opt,name=acs" json:"acs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ServerPres) Reset()         { *m = ServerPres{} }
func (m *ServerPres) String() string { return proto.CompactTextString(m) }
func (*ServerPres) ProtoMessage()    {}
func (*ServerPres) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{27}
}
func (m *ServerPres) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerPres.Unmarshal(m, b)
}
func (m *ServerPres) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerPres.Marshal(b, m, deterministic)
}
func (dst *ServerPres) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerPres.Merge(dst, src)
}
func (m *ServerPres) XXX_Size() int {
	return xxx_messageInfo_ServerPres.Size(m)
}
func (m *ServerPres) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerPres.DiscardUnknown(m)
}

var xxx_messageInfo_ServerPres proto.InternalMessageInfo

func (m *ServerPres) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ServerPres) GetSrc() string {
	if m != nil {
		return m.Src
	}
	return ""
}

func (m *ServerPres) GetWhat() ServerPres_What {
	if m != nil {
		return m.What
	}
	return ServerPres_ON
}

func (m *ServerPres) GetUserAgent() string {
	if m != nil {
		return m.UserAgent
	}
	return ""
}

func (m *ServerPres) GetSeqId() int32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *ServerPres) GetDelId() int32 {
	if m != nil {
		return m.DelId
	}
	return 0
}

func (m *ServerPres) GetDelSeq() []*SeqRange {
	if m != nil {
		return m.DelSeq
	}
	return nil
}

func (m *ServerPres) GetTargetUserId() string {
	if m != nil {
		return m.TargetUserId
	}
	return ""
}

func (m *ServerPres) GetActorUserId() string {
	if m != nil {
		return m.ActorUserId
	}
	return ""
}

func (m *ServerPres) GetAcs() *AccessMode {
	if m != nil {
		return m.Acs
	}
	return nil
}

// {meta} message
type ServerMeta struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id" json:"id,omitempty"`
	Topic                string        `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	Desc                 *TopicDesc    `protobuf:"bytes,3,opt,name=desc" json:"desc,omitempty"`
	Sub                  []*TopicSub   `protobuf:"bytes,4,rep,name=sub" json:"sub,omitempty"`
	Del                  *DelValues    `protobuf:"bytes,5,opt,name=del" json:"del,omitempty"`
	Tags                 []string      `protobuf:"bytes,6,rep,name=tags" json:"tags,omitempty"`
	Cred                 []*ServerCred `protobuf:"bytes,7,rep,name=cred" json:"cred,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ServerMeta) Reset()         { *m = ServerMeta{} }
func (m *ServerMeta) String() string { return proto.CompactTextString(m) }
func (*ServerMeta) ProtoMessage()    {}
func (*ServerMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{28}
}
func (m *ServerMeta) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerMeta.Unmarshal(m, b)
}
func (m *ServerMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerMeta.Marshal(b, m, deterministic)
}
func (dst *ServerMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerMeta.Merge(dst, src)
}
func (m *ServerMeta) XXX_Size() int {
	return xxx_messageInfo_ServerMeta.Size(m)
}
func (m *ServerMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerMeta.DiscardUnknown(m)
}

var xxx_messageInfo_ServerMeta proto.InternalMessageInfo

func (m *ServerMeta) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ServerMeta) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ServerMeta) GetDesc() *TopicDesc {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (m *ServerMeta) GetSub() []*TopicSub {
	if m != nil {
		return m.Sub
	}
	return nil
}

func (m *ServerMeta) GetDel() *DelValues {
	if m != nil {
		return m.Del
	}
	return nil
}

func (m *ServerMeta) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *ServerMeta) GetCred() []*ServerCred {
	if m != nil {
		return m.Cred
	}
	return nil
}

// {info} message: server-side copy of ClientNote with From added
type ServerInfo struct {
	Topic                string   `protobuf:"bytes,1,opt,name=topic" json:"topic,omitempty"`
	FromUserId           string   `protobuf:"bytes,2,opt,name=from_user_id,json=fromUserId" json:"from_user_id,omitempty"`
	What                 InfoNote `protobuf:"varint,3,opt,name=what,enum=pbx.InfoNote" json:"what,omitempty"`
	SeqId                int32    `protobuf:"varint,4,opt,name=seq_id,json=seqId" json:"seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServerInfo) Reset()         { *m = ServerInfo{} }
func (m *ServerInfo) String() string { return proto.CompactTextString(m) }
func (*ServerInfo) ProtoMessage()    {}
func (*ServerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{29}
}
func (m *ServerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerInfo.Unmarshal(m, b)
}
func (m *ServerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerInfo.Marshal(b, m, deterministic)
}
func (dst *ServerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerInfo.Merge(dst, src)
}
func (m *ServerInfo) XXX_Size() int {
	return xxx_messageInfo_ServerInfo.Size(m)
}
func (m *ServerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ServerInfo proto.InternalMessageInfo

func (m *ServerInfo) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *ServerInfo) GetFromUserId() string {
	if m != nil {
		return m.FromUserId
	}
	return ""
}

func (m *ServerInfo) GetWhat() InfoNote {
	if m != nil {
		return m.What
	}
	return InfoNote_READ
}

func (m *ServerInfo) GetSeqId() int32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

// Cumulative message
type ServerMsg struct {
	// Types that are valid to be assigned to Message:
	//	*ServerMsg_Ctrl
	//	*ServerMsg_Data
	//	*ServerMsg_Pres
	//	*ServerMsg_Meta
	//	*ServerMsg_Info
	Message isServerMsg_Message `protobuf_oneof:"Message"`
	// When response is sent to Root, send internal topic name too.
	Topic                string   `protobuf:"bytes,6,opt,name=topic" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServerMsg) Reset()         { *m = ServerMsg{} }
func (m *ServerMsg) String() string { return proto.CompactTextString(m) }
func (*ServerMsg) ProtoMessage()    {}
func (*ServerMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{30}
}
func (m *ServerMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerMsg.Unmarshal(m, b)
}
func (m *ServerMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerMsg.Marshal(b, m, deterministic)
}
func (dst *ServerMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerMsg.Merge(dst, src)
}
func (m *ServerMsg) XXX_Size() int {
	return xxx_messageInfo_ServerMsg.Size(m)
}
func (m *ServerMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ServerMsg proto.InternalMessageInfo

type isServerMsg_Message interface {
	isServerMsg_Message()
}

type ServerMsg_Ctrl struct {
	Ctrl *ServerCtrl `protobuf:"bytes,1,opt,name=ctrl,oneof"`
}
type ServerMsg_Data struct {
	Data *ServerData `protobuf:"bytes,2,opt,name=data,oneof"`
}
type ServerMsg_Pres struct {
	Pres *ServerPres `protobuf:"bytes,3,opt,name=pres,oneof"`
}
type ServerMsg_Meta struct {
	Meta *ServerMeta `protobuf:"bytes,4,opt,name=meta,oneof"`
}
type ServerMsg_Info struct {
	Info *ServerInfo `protobuf:"bytes,5,opt,name=info,oneof"`
}

func (*ServerMsg_Ctrl) isServerMsg_Message() {}
func (*ServerMsg_Data) isServerMsg_Message() {}
func (*ServerMsg_Pres) isServerMsg_Message() {}
func (*ServerMsg_Meta) isServerMsg_Message() {}
func (*ServerMsg_Info) isServerMsg_Message() {}

func (m *ServerMsg) GetMessage() isServerMsg_Message {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *ServerMsg) GetCtrl() *ServerCtrl {
	if x, ok := m.GetMessage().(*ServerMsg_Ctrl); ok {
		return x.Ctrl
	}
	return nil
}

func (m *ServerMsg) GetData() *ServerData {
	if x, ok := m.GetMessage().(*ServerMsg_Data); ok {
		return x.Data
	}
	return nil
}

func (m *ServerMsg) GetPres() *ServerPres {
	if x, ok := m.GetMessage().(*ServerMsg_Pres); ok {
		return x.Pres
	}
	return nil
}

func (m *ServerMsg) GetMeta() *ServerMeta {
	if x, ok := m.GetMessage().(*ServerMsg_Meta); ok {
		return x.Meta
	}
	return nil
}

func (m *ServerMsg) GetInfo() *ServerInfo {
	if x, ok := m.GetMessage().(*ServerMsg_Info); ok {
		return x.Info
	}
	return nil
}

func (m *ServerMsg) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*ServerMsg) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _ServerMsg_OneofMarshaler, _ServerMsg_OneofUnmarshaler, _ServerMsg_OneofSizer, []interface{}{
		(*ServerMsg_Ctrl)(nil),
		(*ServerMsg_Data)(nil),
		(*ServerMsg_Pres)(nil),
		(*ServerMsg_Meta)(nil),
		(*ServerMsg_Info)(nil),
	}
}

func _ServerMsg_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*ServerMsg)
	// Message
	switch x := m.Message.(type) {
	case *ServerMsg_Ctrl:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Ctrl); err != nil {
			return err
		}
	case *ServerMsg_Data:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Data); err != nil {
			return err
		}
	case *ServerMsg_Pres:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Pres); err != nil {
			return err
		}
	case *ServerMsg_Meta:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Meta); err != nil {
			return err
		}
	case *ServerMsg_Info:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Info); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("ServerMsg.Message has unexpected type %T", x)
	}
	return nil
}

func _ServerMsg_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*ServerMsg)
	switch tag {
	case 1: // Message.ctrl
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ServerCtrl)
		err := b.DecodeMessage(msg)
		m.Message = &ServerMsg_Ctrl{msg}
		return true, err
	case 2: // Message.data
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ServerData)
		err := b.DecodeMessage(msg)
		m.Message = &ServerMsg_Data{msg}
		return true, err
	case 3: // Message.pres
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ServerPres)
		err := b.DecodeMessage(msg)
		m.Message = &ServerMsg_Pres{msg}
		return true, err
	case 4: // Message.meta
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ServerMeta)
		err := b.DecodeMessage(msg)
		m.Message = &ServerMsg_Meta{msg}
		return true, err
	case 5: // Message.info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(ServerInfo)
		err := b.DecodeMessage(msg)
		m.Message = &ServerMsg_Info{msg}
		return true, err
	default:
		return false, nil
	}
}

func _ServerMsg_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*ServerMsg)
	// Message
	switch x := m.Message.(type) {
	case *ServerMsg_Ctrl:
		s := proto.Size(x.Ctrl)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ServerMsg_Data:
		s := proto.Size(x.Data)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ServerMsg_Pres:
		s := proto.Size(x.Pres)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ServerMsg_Meta:
		s := proto.Size(x.Meta)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ServerMsg_Info:
		s := proto.Size(x.Info)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type ServerResp struct {
	Status               RespCode   `protobuf:"varint,1,opt,name=status,enum=pbx.RespCode" json:"status,omitempty"`
	Srvmsg               *ServerMsg `protobuf:"bytes,2,opt,name=srvmsg" json:"srvmsg,omitempty"`
	Clmsg                *ClientMsg `protobuf:"bytes,3,opt,name=clmsg" json:"clmsg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ServerResp) Reset()         { *m = ServerResp{} }
func (m *ServerResp) String() string { return proto.CompactTextString(m) }
func (*ServerResp) ProtoMessage()    {}
func (*ServerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{31}
}
func (m *ServerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerResp.Unmarshal(m, b)
}
func (m *ServerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerResp.Marshal(b, m, deterministic)
}
func (dst *ServerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerResp.Merge(dst, src)
}
func (m *ServerResp) XXX_Size() int {
	return xxx_messageInfo_ServerResp.Size(m)
}
func (m *ServerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ServerResp proto.InternalMessageInfo

func (m *ServerResp) GetStatus() RespCode {
	if m != nil {
		return m.Status
	}
	return RespCode_CONTINUE
}

func (m *ServerResp) GetSrvmsg() *ServerMsg {
	if m != nil {
		return m.Srvmsg
	}
	return nil
}

func (m *ServerResp) GetClmsg() *ClientMsg {
	if m != nil {
		return m.Clmsg
	}
	return nil
}

// Context message
type Session struct {
	SessionId            string    `protobuf:"bytes,1,opt,name=session_id,json=sessionId" json:"session_id,omitempty"`
	UserId               string    `protobuf:"bytes,2,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	AuthLevel            AuthLevel `protobuf:"varint,3,opt,name=auth_level,json=authLevel,enum=pbx.AuthLevel" json:"auth_level,omitempty"`
	RemoteAddr           string    `protobuf:"bytes,4,opt,name=remote_addr,json=remoteAddr" json:"remote_addr,omitempty"`
	UserAgent            string    `protobuf:"bytes,5,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	DeviceId             string    `protobuf:"bytes,6,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`
	Language             string    `protobuf:"bytes,7,opt,name=language" json:"language,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Session) Reset()         { *m = Session{} }
func (m *Session) String() string { return proto.CompactTextString(m) }
func (*Session) ProtoMessage()    {}
func (*Session) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{32}
}
func (m *Session) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Session.Unmarshal(m, b)
}
func (m *Session) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Session.Marshal(b, m, deterministic)
}
func (dst *Session) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Session.Merge(dst, src)
}
func (m *Session) XXX_Size() int {
	return xxx_messageInfo_Session.Size(m)
}
func (m *Session) XXX_DiscardUnknown() {
	xxx_messageInfo_Session.DiscardUnknown(m)
}

var xxx_messageInfo_Session proto.InternalMessageInfo

func (m *Session) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *Session) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *Session) GetAuthLevel() AuthLevel {
	if m != nil {
		return m.AuthLevel
	}
	return AuthLevel_NONE
}

func (m *Session) GetRemoteAddr() string {
	if m != nil {
		return m.RemoteAddr
	}
	return ""
}

func (m *Session) GetUserAgent() string {
	if m != nil {
		return m.UserAgent
	}
	return ""
}

func (m *Session) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *Session) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

type ClientReq struct {
	Msg                  *ClientMsg `protobuf:"bytes,1,opt,name=msg" json:"msg,omitempty"`
	Sess                 *Session   `protobuf:"bytes,2,opt,name=sess" json:"sess,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ClientReq) Reset()         { *m = ClientReq{} }
func (m *ClientReq) String() string { return proto.CompactTextString(m) }
func (*ClientReq) ProtoMessage()    {}
func (*ClientReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{33}
}
func (m *ClientReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientReq.Unmarshal(m, b)
}
func (m *ClientReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientReq.Marshal(b, m, deterministic)
}
func (dst *ClientReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientReq.Merge(dst, src)
}
func (m *ClientReq) XXX_Size() int {
	return xxx_messageInfo_ClientReq.Size(m)
}
func (m *ClientReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClientReq proto.InternalMessageInfo

func (m *ClientReq) GetMsg() *ClientMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *ClientReq) GetSess() *Session {
	if m != nil {
		return m.Sess
	}
	return nil
}

type SearchQuery struct {
	UserId               string   `protobuf:"bytes,1,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	Query                string   `protobuf:"bytes,2,opt,name=query" json:"query,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchQuery) Reset()         { *m = SearchQuery{} }
func (m *SearchQuery) String() string { return proto.CompactTextString(m) }
func (*SearchQuery) ProtoMessage()    {}
func (*SearchQuery) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{34}
}
func (m *SearchQuery) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchQuery.Unmarshal(m, b)
}
func (m *SearchQuery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchQuery.Marshal(b, m, deterministic)
}
func (dst *SearchQuery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchQuery.Merge(dst, src)
}
func (m *SearchQuery) XXX_Size() int {
	return xxx_messageInfo_SearchQuery.Size(m)
}
func (m *SearchQuery) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchQuery.DiscardUnknown(m)
}

var xxx_messageInfo_SearchQuery proto.InternalMessageInfo

func (m *SearchQuery) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SearchQuery) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

type SearchFound struct {
	Status RespCode `protobuf:"varint,1,opt,name=status,enum=pbx.RespCode" json:"status,omitempty"`
	// New search query If status == REPLACE, otherwise unset.
	Query string `protobuf:"bytes,2,opt,name=query" json:"query,omitempty"`
	// Search results.
	Result               []*TopicSub `protobuf:"bytes,3,rep,name=result" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SearchFound) Reset()         { *m = SearchFound{} }
func (m *SearchFound) String() string { return proto.CompactTextString(m) }
func (*SearchFound) ProtoMessage()    {}
func (*SearchFound) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{35}
}
func (m *SearchFound) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchFound.Unmarshal(m, b)
}
func (m *SearchFound) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchFound.Marshal(b, m, deterministic)
}
func (dst *SearchFound) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchFound.Merge(dst, src)
}
func (m *SearchFound) XXX_Size() int {
	return xxx_messageInfo_SearchFound.Size(m)
}
func (m *SearchFound) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchFound.DiscardUnknown(m)
}

var xxx_messageInfo_SearchFound proto.InternalMessageInfo

func (m *SearchFound) GetStatus() RespCode {
	if m != nil {
		return m.Status
	}
	return RespCode_CONTINUE
}

func (m *SearchFound) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func (m *SearchFound) GetResult() []*TopicSub {
	if m != nil {
		return m.Result
	}
	return nil
}

type TopicEvent struct {
	Action               Crud       `protobuf:"varint,1,opt,name=action,enum=pbx.Crud" json:"action,omitempty"`
	Name                 string     `protobuf:"bytes,2,opt,name=name" json:"name,omitempty"`
	Desc                 *TopicDesc `protobuf:"bytes,3,opt,name=desc" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *TopicEvent) Reset()         { *m = TopicEvent{} }
func (m *TopicEvent) String() string { return proto.CompactTextString(m) }
func (*TopicEvent) ProtoMessage()    {}
func (*TopicEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{36}
}
func (m *TopicEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicEvent.Unmarshal(m, b)
}
func (m *TopicEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicEvent.Marshal(b, m, deterministic)
}
func (dst *TopicEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicEvent.Merge(dst, src)
}
func (m *TopicEvent) XXX_Size() int {
	return xxx_messageInfo_TopicEvent.Size(m)
}
func (m *TopicEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicEvent.DiscardUnknown(m)
}

var xxx_messageInfo_TopicEvent proto.InternalMessageInfo

func (m *TopicEvent) GetAction() Crud {
	if m != nil {
		return m.Action
	}
	return Crud_CREATE
}

func (m *TopicEvent) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TopicEvent) GetDesc() *TopicDesc {
	if m != nil {
		return m.Desc
	}
	return nil
}

type AccountEvent struct {
	Action     Crud            `protobuf:"varint,1,opt,name=action,enum=pbx.Crud" json:"action,omitempty"`
	UserId     string          `protobuf:"bytes,2,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	DefaultAcs *DefaultAcsMode `protobuf:"bytes,3,opt,name=default_acs,json=defaultAcs" json:"default_acs,omitempty"`
	Public     []byte          `protobuf:"bytes,4,opt,name=public,proto3" json:"public,omitempty"`
	// Indexable tags for user discovery
	Tags                 []string `protobuf:"bytes,8,rep,name=tags" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AccountEvent) Reset()         { *m = AccountEvent{} }
func (m *AccountEvent) String() string { return proto.CompactTextString(m) }
func (*AccountEvent) ProtoMessage()    {}
func (*AccountEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{37}
}
func (m *AccountEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccountEvent.Unmarshal(m, b)
}
func (m *AccountEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccountEvent.Marshal(b, m, deterministic)
}
func (dst *AccountEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccountEvent.Merge(dst, src)
}
func (m *AccountEvent) XXX_Size() int {
	return xxx_messageInfo_AccountEvent.Size(m)
}
func (m *AccountEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_AccountEvent.DiscardUnknown(m)
}

var xxx_messageInfo_AccountEvent proto.InternalMessageInfo

func (m *AccountEvent) GetAction() Crud {
	if m != nil {
		return m.Action
	}
	return Crud_CREATE
}

func (m *AccountEvent) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *AccountEvent) GetDefaultAcs() *DefaultAcsMode {
	if m != nil {
		return m.DefaultAcs
	}
	return nil
}

func (m *AccountEvent) GetPublic() []byte {
	if m != nil {
		return m.Public
	}
	return nil
}

func (m *AccountEvent) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

type SubscriptionEvent struct {
	Action               Crud        `protobuf:"varint,1,opt,name=action,enum=pbx.Crud" json:"action,omitempty"`
	Topic                string      `protobuf:"bytes,2,opt,name=topic" json:"topic,omitempty"`
	UserId               string      `protobuf:"bytes,3,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	DelId                int32       `protobuf:"varint,4,opt,name=del_id,json=delId" json:"del_id,omitempty"`
	ReadId               int32       `protobuf:"varint,5,opt,name=read_id,json=readId" json:"read_id,omitempty"`
	RecvId               int32       `protobuf:"varint,6,opt,name=recv_id,json=recvId" json:"recv_id,omitempty"`
	Mode                 *AccessMode `protobuf:"bytes,7,opt,name=mode" json:"mode,omitempty"`
	Private              []byte      `protobuf:"bytes,8,opt,name=private,proto3" json:"private,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SubscriptionEvent) Reset()         { *m = SubscriptionEvent{} }
func (m *SubscriptionEvent) String() string { return proto.CompactTextString(m) }
func (*SubscriptionEvent) ProtoMessage()    {}
func (*SubscriptionEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{38}
}
func (m *SubscriptionEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscriptionEvent.Unmarshal(m, b)
}
func (m *SubscriptionEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscriptionEvent.Marshal(b, m, deterministic)
}
func (dst *SubscriptionEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscriptionEvent.Merge(dst, src)
}
func (m *SubscriptionEvent) XXX_Size() int {
	return xxx_messageInfo_SubscriptionEvent.Size(m)
}
func (m *SubscriptionEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscriptionEvent.DiscardUnknown(m)
}

var xxx_messageInfo_SubscriptionEvent proto.InternalMessageInfo

func (m *SubscriptionEvent) GetAction() Crud {
	if m != nil {
		return m.Action
	}
	return Crud_CREATE
}

func (m *SubscriptionEvent) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *SubscriptionEvent) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *SubscriptionEvent) GetDelId() int32 {
	if m != nil {
		return m.DelId
	}
	return 0
}

func (m *SubscriptionEvent) GetReadId() int32 {
	if m != nil {
		return m.ReadId
	}
	return 0
}

func (m *SubscriptionEvent) GetRecvId() int32 {
	if m != nil {
		return m.RecvId
	}
	return 0
}

func (m *SubscriptionEvent) GetMode() *AccessMode {
	if m != nil {
		return m.Mode
	}
	return nil
}

func (m *SubscriptionEvent) GetPrivate() []byte {
	if m != nil {
		return m.Private
	}
	return nil
}

type MessageEvent struct {
	Action               Crud        `protobuf:"varint,1,opt,name=action,enum=pbx.Crud" json:"action,omitempty"`
	Msg                  *ServerData `protobuf:"bytes,2,opt,name=msg" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *MessageEvent) Reset()         { *m = MessageEvent{} }
func (m *MessageEvent) String() string { return proto.CompactTextString(m) }
func (*MessageEvent) ProtoMessage()    {}
func (*MessageEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_model_92588c8639c67d56, []int{39}
}
func (m *MessageEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageEvent.Unmarshal(m, b)
}
func (m *MessageEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageEvent.Marshal(b, m, deterministic)
}
func (dst *MessageEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageEvent.Merge(dst, src)
}
func (m *MessageEvent) XXX_Size() int {
	return xxx_messageInfo_MessageEvent.Size(m)
}
func (m *MessageEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MessageEvent proto.InternalMessageInfo

func (m *MessageEvent) GetAction() Crud {
	if m != nil {
		return m.Action
	}
	return Crud_CREATE
}

func (m *MessageEvent) GetMsg() *ServerData {
	if m != nil {
		return m.Msg
	}
	return nil
}

func init() {
	proto.RegisterType((*Unused)(nil), "pbx.Unused")
	proto.RegisterType((*DefaultAcsMode)(nil), "pbx.DefaultAcsMode")
	proto.RegisterType((*AccessMode)(nil), "pbx.AccessMode")
	proto.RegisterType((*SetSub)(nil), "pbx.SetSub")
	proto.RegisterType((*ClientCred)(nil), "pbx.ClientCred")
	proto.RegisterMapType((map[string][]byte)(nil), "pbx.ClientCred.ParamsEntry")
	proto.RegisterType((*SetDesc)(nil), "pbx.SetDesc")
	proto.RegisterType((*GetOpts)(nil), "pbx.GetOpts")
	proto.RegisterType((*GetQuery)(nil), "pbx.GetQuery")
	proto.RegisterType((*SetQuery)(nil), "pbx.SetQuery")
	proto.RegisterType((*SeqRange)(nil), "pbx.SeqRange")
	proto.RegisterType((*ClientHi)(nil), "pbx.ClientHi")
	proto.RegisterType((*ClientAcc)(nil), "pbx.ClientAcc")
	proto.RegisterType((*ClientLogin)(nil), "pbx.ClientLogin")
	proto.RegisterType((*ClientSub)(nil), "pbx.ClientSub")
	proto.RegisterType((*ClientLeave)(nil), "pbx.ClientLeave")
	proto.RegisterType((*ClientPub)(nil), "pbx.ClientPub")
	proto.RegisterMapType((map[string][]byte)(nil), "pbx.ClientPub.HeadEntry")
	proto.RegisterType((*ClientGet)(nil), "pbx.ClientGet")
	proto.RegisterType((*ClientSet)(nil), "pbx.ClientSet")
	proto.RegisterType((*ClientDel)(nil), "pbx.ClientDel")
	proto.RegisterType((*ClientNote)(nil), "pbx.ClientNote")
	proto.RegisterType((*ClientMsg)(nil), "pbx.ClientMsg")
	proto.RegisterType((*ServerCred)(nil), "pbx.ServerCred")
	proto.RegisterType((*TopicDesc)(nil), "pbx.TopicDesc")
	proto.RegisterType((*TopicSub)(nil), "pbx.TopicSub")
	proto.RegisterType((*DelValues)(nil), "pbx.DelValues")
	proto.RegisterType((*ServerCtrl)(nil), "pbx.ServerCtrl")
	proto.RegisterMapType((map[string][]byte)(nil), "pbx.ServerCtrl.ParamsEntry")
	proto.RegisterType((*ServerData)(nil), "pbx.ServerData")
	proto.RegisterMapType((map[string][]byte)(nil), "pbx.ServerData.HeadEntry")
	proto.RegisterType((*ServerPres)(nil), "pbx.ServerPres")
	proto.RegisterType((*ServerMeta)(nil), "pbx.ServerMeta")
	proto.RegisterType((*ServerInfo)(nil), "pbx.ServerInfo")
	proto.RegisterType((*ServerMsg)(nil), "pbx.ServerMsg")
	proto.RegisterType((*ServerResp)(nil), "pbx.ServerResp")
	proto.RegisterType((*Session)(nil), "pbx.Session")
	proto.RegisterType((*ClientReq)(nil), "pbx.ClientReq")
	proto.RegisterType((*SearchQuery)(nil), "pbx.SearchQuery")
	proto.RegisterType((*SearchFound)(nil), "pbx.SearchFound")
	proto.RegisterType((*TopicEvent)(nil), "pbx.TopicEvent")
	proto.RegisterType((*AccountEvent)(nil), "pbx.AccountEvent")
	proto.RegisterType((*SubscriptionEvent)(nil), "pbx.SubscriptionEvent")
	proto.RegisterType((*MessageEvent)(nil), "pbx.MessageEvent")
	proto.RegisterEnum("pbx.AuthLevel", AuthLevel_name, AuthLevel_value)
	proto.RegisterEnum("pbx.InfoNote", InfoNote_name, InfoNote_value)
	proto.RegisterEnum("pbx.RespCode", RespCode_name, RespCode_value)
	proto.RegisterEnum("pbx.Crud", Crud_name, Crud_value)
	proto.RegisterEnum("pbx.ClientDel_What", ClientDel_What_name, ClientDel_What_value)
	proto.RegisterEnum("pbx.ServerPres_What", ServerPres_What_name, ServerPres_What_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Node service

type NodeClient interface {
	// Client sends a stream of ClientMsg, server responds with a stream of ServerMsg
	MessageLoop(ctx context.Context, opts ...grpc.CallOption) (Node_MessageLoopClient, error)
}

type nodeClient struct {
	cc *grpc.ClientConn
}

func NewNodeClient(cc *grpc.ClientConn) NodeClient {
	return &nodeClient{cc}
}

func (c *nodeClient) MessageLoop(ctx context.Context, opts ...grpc.CallOption) (Node_MessageLoopClient, error) {
	stream, err := grpc.NewClientStream(ctx, &_Node_serviceDesc.Streams[0], c.cc, "/pbx.Node/MessageLoop", opts...)
	if err != nil {
		return nil, err
	}
	x := &nodeMessageLoopClient{stream}
	return x, nil
}

type Node_MessageLoopClient interface {
	Send(*ClientMsg) error
	Recv() (*ServerMsg, error)
	grpc.ClientStream
}

type nodeMessageLoopClient struct {
	grpc.ClientStream
}

func (x *nodeMessageLoopClient) Send(m *ClientMsg) error {
	return x.ClientStream.SendMsg(m)
}

func (x *nodeMessageLoopClient) Recv() (*ServerMsg, error) {
	m := new(ServerMsg)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// Server API for Node service

type NodeServer interface {
	// Client sends a stream of ClientMsg, server responds with a stream of ServerMsg
	MessageLoop(Node_MessageLoopServer) error
}

func RegisterNodeServer(s *grpc.Server, srv NodeServer) {
	s.RegisterService(&_Node_serviceDesc, srv)
}

func _Node_MessageLoop_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(NodeServer).MessageLoop(&nodeMessageLoopServer{stream})
}

type Node_MessageLoopServer interface {
	Send(*ServerMsg) error
	Recv() (*ClientMsg, error)
	grpc.ServerStream
}

type nodeMessageLoopServer struct {
	grpc.ServerStream
}

func (x *nodeMessageLoopServer) Send(m *ServerMsg) error {
	return x.ServerStream.SendMsg(m)
}

func (x *nodeMessageLoopServer) Recv() (*ClientMsg, error) {
	m := new(ClientMsg)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _Node_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pbx.Node",
	HandlerType: (*NodeServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "MessageLoop",
			Handler:       _Node_MessageLoop_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "model.proto",
}

// Client API for Plugin service

type PluginClient interface {
	// This plugin method is called by Tinode server for every message received from the clients. The
	// method returns a ServerCtrl message. Non-zero ServerCtrl.code indicates that no further
	// processing is needed. The Tinode server will generate a {ctrl} message from the returned ServerCtrl
	// and forward it to the client session.
	// ServerCtrl.code equals to 0 instructs the server to continue with default processing of the client message.
	FireHose(ctx context.Context, in *ClientReq, opts ...grpc.CallOption) (*ServerResp, error)
	// An alteranative user and topic discovery mechanism.
	// A search request issued on a 'fnd' topic. This method is called to generate an alternative result set.
	Find(ctx context.Context, in *SearchQuery, opts ...grpc.CallOption) (*SearchFound, error)
	// Account created, updated or deleted
	Account(ctx context.Context, in *AccountEvent, opts ...grpc.CallOption) (*Unused, error)
	// Topic created, updated [or deleted -- not supported yet]
	Topic(ctx context.Context, in *TopicEvent, opts ...grpc.CallOption) (*Unused, error)
	// Subscription created, updated or deleted
	Subscription(ctx context.Context, in *SubscriptionEvent, opts ...grpc.CallOption) (*Unused, error)
	// Message published or deleted
	Message(ctx context.Context, in *MessageEvent, opts ...grpc.CallOption) (*Unused, error)
}

type pluginClient struct {
	cc *grpc.ClientConn
}

func NewPluginClient(cc *grpc.ClientConn) PluginClient {
	return &pluginClient{cc}
}

func (c *pluginClient) FireHose(ctx context.Context, in *ClientReq, opts ...grpc.CallOption) (*ServerResp, error) {
	out := new(ServerResp)
	err := grpc.Invoke(ctx, "/pbx.Plugin/FireHose", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pluginClient) Find(ctx context.Context, in *SearchQuery, opts ...grpc.CallOption) (*SearchFound, error) {
	out := new(SearchFound)
	err := grpc.Invoke(ctx, "/pbx.Plugin/Find", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pluginClient) Account(ctx context.Context, in *AccountEvent, opts ...grpc.CallOption) (*Unused, error) {
	out := new(Unused)
	err := grpc.Invoke(ctx, "/pbx.Plugin/Account", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pluginClient) Topic(ctx context.Context, in *TopicEvent, opts ...grpc.CallOption) (*Unused, error) {
	out := new(Unused)
	err := grpc.Invoke(ctx, "/pbx.Plugin/Topic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pluginClient) Subscription(ctx context.Context, in *SubscriptionEvent, opts ...grpc.CallOption) (*Unused, error) {
	out := new(Unused)
	err := grpc.Invoke(ctx, "/pbx.Plugin/Subscription", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pluginClient) Message(ctx context.Context, in *MessageEvent, opts ...grpc.CallOption) (*Unused, error) {
	out := new(Unused)
	err := grpc.Invoke(ctx, "/pbx.Plugin/Message", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Plugin service

type PluginServer interface {
	// This plugin method is called by Tinode server for every message received from the clients. The
	// method returns a ServerCtrl message. Non-zero ServerCtrl.code indicates that no further
	// processing is needed. The Tinode server will generate a {ctrl} message from the returned ServerCtrl
	// and forward it to the client session.
	// ServerCtrl.code equals to 0 instructs the server to continue with default processing of the client message.
	FireHose(context.Context, *ClientReq) (*ServerResp, error)
	// An alteranative user and topic discovery mechanism.
	// A search request issued on a 'fnd' topic. This method is called to generate an alternative result set.
	Find(context.Context, *SearchQuery) (*SearchFound, error)
	// Account created, updated or deleted
	Account(context.Context, *AccountEvent) (*Unused, error)
	// Topic created, updated [or deleted -- not supported yet]
	Topic(context.Context, *TopicEvent) (*Unused, error)
	// Subscription created, updated or deleted
	Subscription(context.Context, *SubscriptionEvent) (*Unused, error)
	// Message published or deleted
	Message(context.Context, *MessageEvent) (*Unused, error)
}

func RegisterPluginServer(s *grpc.Server, srv PluginServer) {
	s.RegisterService(&_Plugin_serviceDesc, srv)
}

func _Plugin_FireHose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PluginServer).FireHose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.Plugin/FireHose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PluginServer).FireHose(ctx, req.(*ClientReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Plugin_Find_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PluginServer).Find(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.Plugin/Find",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PluginServer).Find(ctx, req.(*SearchQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _Plugin_Account_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PluginServer).Account(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.Plugin/Account",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PluginServer).Account(ctx, req.(*AccountEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _Plugin_Topic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TopicEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PluginServer).Topic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.Plugin/Topic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PluginServer).Topic(ctx, req.(*TopicEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _Plugin_Subscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscriptionEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PluginServer).Subscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.Plugin/Subscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PluginServer).Subscription(ctx, req.(*SubscriptionEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _Plugin_Message_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PluginServer).Message(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.Plugin/Message",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PluginServer).Message(ctx, req.(*MessageEvent))
	}
	return interceptor(ctx, in, info, handler)
}

var _Plugin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pbx.Plugin",
	HandlerType: (*PluginServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FireHose",
			Handler:    _Plugin_FireHose_Handler,
		},
		{
			MethodName: "Find",
			Handler:    _Plugin_Find_Handler,
		},
		{
			MethodName: "Account",
			Handler:    _Plugin_Account_Handler,
		},
		{
			MethodName: "Topic",
			Handler:    _Plugin_Topic_Handler,
		},
		{
			MethodName: "Subscription",
			Handler:    _Plugin_Subscription_Handler,
		},
		{
			MethodName: "Message",
			Handler:    _Plugin_Message_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "model.proto",
}

func init() { proto.RegisterFile("model.proto", fileDescriptor_model_92588c8639c67d56) }

var fileDescriptor_model_92588c8639c67d56 = []byte{
	// 2623 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0x4f, 0x93, 0xe3, 0x46,
	0x15, 0x1f, 0x59, 0xb2, 0x2c, 0x3d, 0x7b, 0x67, 0xb5, 0xcd, 0x92, 0x38, 0x13, 0x92, 0xcc, 0x2a,
	0xff, 0xa6, 0x26, 0xc9, 0x42, 0xed, 0x12, 0x12, 0x20, 0x17, 0x67, 0xec, 0x9d, 0x19, 0xd8, 0x19,
	0x0f, 0xf2, 0x4c, 0xb8, 0x50, 0xe5, 0x92, 0xa5, 0x1e, 0x5b, 0x15, 0x59, 0xf2, 0x48, 0x2d, 0x27,
	0xb9, 0x50, 0x15, 0x8a, 0x0b, 0x07, 0xbe, 0x00, 0x17, 0xbe, 0x00, 0x9c, 0xa1, 0xf8, 0x00, 0x1c,
	0xe0, 0xc6, 0x95, 0x4f, 0xc0, 0x81, 0xe2, 0x03, 0x70, 0xa1, 0x5e, 0xff, 0x91, 0x25, 0xcf, 0x78,
	0x33, 0x1b, 0x72, 0xeb, 0x7e, 0xef, 0xa9, 0xfb, 0xfd, 0xe9, 0xf7, 0xde, 0xaf, 0x5b, 0xd0, 0x9e,
	0xa7, 0x21, 0x8d, 0x1f, 0x2e, 0xb2, 0x94, 0xa5, 0x44, 0x5f, 0x4c, 0x3e, 0x77, 0x2d, 0x30, 0x2f,
	0x92, 0x22, 0xa7, 0xa1, 0xfb, 0x21, 0x6c, 0xf7, 0xe9, 0xa5, 0x5f, 0xc4, 0xac, 0x17, 0xe4, 0x27,
	0x69, 0x48, 0x09, 0x01, 0xc3, 0x2f, 0xd8, 0xac, 0xab, 0xed, 0x6a, 0x7b, 0xb6, 0xc7, 0xc7, 0x9c,
	0x96, 0xa4, 0x49, 0xb7, 0x21, 0x69, 0x49, 0x9a, 0xb8, 0x3f, 0x00, 0xe8, 0x05, 0x01, 0xcd, 0xcb,
	0xaf, 0x3e, 0xf3, 0x13, 0xa6, 0xbe, 0xc2, 0x31, 0xb9, 0x0f, 0xcd, 0x69, 0xb4, 0xa4, 0xea, 0x33,
	0x31, 0x71, 0xdf, 0x07, 0x73, 0x44, 0xd9, 0xa8, 0x98, 0x90, 0x17, 0xa1, 0x55, 0xe4, 0x34, 0x1b,
	0x47, 0xa1, 0xfc, 0xcc, 0xc4, 0xe9, 0x71, 0x88, 0x8b, 0xa1, 0xca, 0x6a, 0x3b, 0x1c, 0xbb, 0x7f,
	0xd5, 0x00, 0x0e, 0xe2, 0x88, 0x26, 0xec, 0x20, 0xa3, 0x21, 0x79, 0x01, 0xcc, 0x39, 0x65, 0xb3,
	0xb4, 0xfc, 0x54, 0xcc, 0x70, 0xcf, 0xa5, 0x1f, 0x17, 0xea, 0x5b, 0x31, 0x21, 0x3b, 0x60, 0x65,
	0x34, 0x5f, 0xa4, 0x49, 0x4e, 0xbb, 0x3a, 0x67, 0x94, 0x73, 0xf2, 0x18, 0xcc, 0x85, 0x9f, 0xf9,
	0xf3, 0xbc, 0x6b, 0xec, 0xea, 0x7b, 0xed, 0x47, 0x2f, 0x3f, 0x5c, 0x4c, 0x3e, 0x7f, 0xb8, 0xda,
	0xea, 0xe1, 0x19, 0xe7, 0x0e, 0x12, 0x96, 0x7d, 0xe1, 0x49, 0xd1, 0x9d, 0x1f, 0x42, 0xbb, 0x42,
	0x26, 0x0e, 0xe8, 0x9f, 0xd2, 0x2f, 0xa4, 0x2a, 0x38, 0xac, 0xeb, 0xd1, 0x91, 0x7a, 0xfc, 0xa8,
	0xf1, 0xa1, 0xe6, 0x5e, 0x41, 0x6b, 0x44, 0x59, 0x9f, 0xe6, 0x01, 0xf9, 0x3e, 0xb4, 0x43, 0xe1,
	0xfc, 0xb1, 0x1f, 0xe4, 0xfc, 0xf3, 0xf6, 0xa3, 0x6f, 0xf1, 0xfd, 0xeb, 0x41, 0xf1, 0x20, 0x2c,
	0xe7, 0x68, 0xfa, 0xa2, 0x98, 0xc4, 0x51, 0x20, 0xd7, 0x96, 0x33, 0xd2, 0x85, 0xd6, 0x22, 0x8b,
	0x96, 0x3e, 0x13, 0x36, 0x76, 0x3c, 0x35, 0x75, 0xff, 0xa8, 0x41, 0xeb, 0x90, 0xb2, 0xe1, 0x82,
	0xe5, 0x64, 0x1f, 0xee, 0x45, 0x97, 0xe3, 0x79, 0x1a, 0x46, 0x97, 0x11, 0x0d, 0xc7, 0x79, 0x94,
	0x04, 0x94, 0xef, 0xac, 0x7b, 0x77, 0xa3, 0xcb, 0x13, 0x49, 0x1f, 0x21, 0x19, 0xe3, 0x80, 0x11,
	0x51, 0x71, 0xc0, 0x31, 0x1a, 0xc6, 0xd2, 0x45, 0x14, 0x48, 0x3f, 0x8a, 0x09, 0x79, 0x09, 0x2c,
	0xbe, 0x12, 0xc6, 0xd2, 0xd8, 0xd5, 0xf6, 0x9a, 0x5e, 0x8b, 0xcf, 0x8f, 0x43, 0xf2, 0x32, 0xd8,
	0x13, 0x7a, 0x99, 0x66, 0x9c, 0xd7, 0xe4, 0x3c, 0x4b, 0x10, 0x8e, 0x79, 0xb8, 0xe2, 0x68, 0x1e,
	0xb1, 0xae, 0xc9, 0x19, 0x62, 0xe2, 0xfe, 0x4a, 0x03, 0xeb, 0x90, 0xb2, 0x9f, 0x15, 0x34, 0xfb,
	0x82, 0x9f, 0xac, 0x99, 0xbf, 0x3a, 0x59, 0x33, 0x9f, 0x91, 0x5d, 0x30, 0x42, 0x9a, 0x0b, 0x07,
	0xb4, 0x1f, 0x75, 0xb8, 0xc7, 0xa4, 0x81, 0x1e, 0xe7, 0x90, 0x57, 0x41, 0xcf, 0x8b, 0x09, 0x57,
	0x72, 0x5d, 0x00, 0x19, 0x7c, 0x05, 0x9f, 0xf9, 0x5c, 0xd9, 0xeb, 0x2b, 0xf8, 0xcc, 0x77, 0x7f,
	0xad, 0x81, 0x35, 0x52, 0x4a, 0xa8, 0x0d, 0xb5, 0x8a, 0xb8, 0x8c, 0xa2, 0xdc, 0xf0, 0x15, 0xb1,
	0xa1, 0xd0, 0xa8, 0xad, 0x04, 0x46, 0xc5, 0x44, 0xec, 0x47, 0xc0, 0x60, 0xfe, 0x34, 0xef, 0xea,
	0xbb, 0x3a, 0x5a, 0x81, 0x63, 0xf2, 0x3a, 0x18, 0x41, 0x46, 0x43, 0xa9, 0xc3, 0xdd, 0xb5, 0x73,
	0xe7, 0x71, 0xa6, 0xfb, 0x2e, 0x6a, 0x71, 0xe5, 0xf9, 0xc9, 0x94, 0xe2, 0x31, 0x8b, 0xd3, 0xcf,
	0xb8, 0x12, 0x4d, 0x0f, 0x87, 0x64, 0x1b, 0x1a, 0xb3, 0x88, 0x6f, 0xda, 0xf4, 0x1a, 0xb3, 0xc8,
	0xfd, 0xb3, 0x06, 0x96, 0x58, 0xe2, 0x28, 0x42, 0x66, 0x99, 0x5a, 0x8d, 0x28, 0x24, 0xaf, 0x00,
	0xf0, 0x7c, 0xf3, 0xa7, 0x34, 0x61, 0x32, 0xa8, 0x36, 0x52, 0x7a, 0x48, 0xc0, 0xd5, 0x97, 0x34,
	0x93, 0x71, 0xc5, 0x21, 0x86, 0x2e, 0xa4, 0xcb, 0x68, 0x15, 0x56, 0xdb, 0xb3, 0x04, 0x41, 0x24,
	0x69, 0xec, 0x27, 0x53, 0x1e, 0x52, 0xdb, 0xe3, 0x63, 0xcc, 0xb3, 0x45, 0xec, 0xb3, 0xcb, 0x34,
	0x9b, 0xf3, 0x88, 0xda, 0x5e, 0x39, 0x27, 0xaf, 0x02, 0x4c, 0xfc, 0xe0, 0xd3, 0x69, 0x96, 0x16,
	0x49, 0xd8, 0x6d, 0xed, 0x6a, 0x7b, 0x96, 0x57, 0xa1, 0xb8, 0x5f, 0x36, 0xc0, 0x16, 0xaa, 0xf7,
	0x82, 0xe0, 0x9a, 0xee, 0x95, 0x5a, 0xd1, 0xa8, 0xd5, 0x8a, 0x17, 0xc0, 0xcc, 0x83, 0x19, 0x9d,
	0xab, 0xc4, 0x96, 0x33, 0x4e, 0xa7, 0x41, 0x46, 0x19, 0x57, 0xbc, 0xe3, 0xc9, 0x19, 0x3f, 0x71,
	0xe9, 0x34, 0x4a, 0xb8, 0xde, 0x96, 0x27, 0x26, 0x65, 0x78, 0xcc, 0x4a, 0x78, 0x54, 0xcc, 0x5b,
	0x1b, 0x63, 0xae, 0x02, 0x68, 0xf1, 0xc2, 0x71, 0x73, 0x00, 0x45, 0xc2, 0x7c, 0x4a, 0x93, 0xae,
	0x2d, 0x2a, 0x01, 0x9f, 0x20, 0x35, 0x67, 0x98, 0xaa, 0x20, 0xd2, 0x88, 0x4f, 0xdc, 0x0c, 0xda,
	0xe2, 0xfb, 0xa7, 0x5c, 0xab, 0x75, 0x27, 0xac, 0x6c, 0x6d, 0x6c, 0xb0, 0x55, 0xaf, 0xd9, 0xba,
	0x3a, 0x60, 0x9b, 0xf5, 0x73, 0x7f, 0xa3, 0x29, 0xbf, 0x63, 0x4d, 0x5e, 0xdf, 0xb2, 0x4c, 0xf7,
	0x46, 0x35, 0xdd, 0xf7, 0xc1, 0xce, 0x29, 0x1b, 0x5f, 0x61, 0x6e, 0xc8, 0x1c, 0xbb, 0xa3, 0xfc,
	0xc3, 0x13, 0xc6, 0xb3, 0x72, 0x95, 0x3a, 0xfb, 0x60, 0x4f, 0x4b, 0x59, 0xa3, 0x22, 0x7b, 0x58,
	0xca, 0x4e, 0xe5, 0xc8, 0x3d, 0x2e, 0xed, 0xa7, 0xfe, 0x92, 0xde, 0x52, 0x99, 0xfb, 0xd0, 0x2c,
	0x12, 0x95, 0xec, 0x96, 0x27, 0x26, 0xee, 0xdf, 0x4b, 0xb3, 0xce, 0x6e, 0x6d, 0xd6, 0x8b, 0xd0,
	0x4a, 0xd2, 0x31, 0x0d, 0x66, 0xa9, 0x5c, 0xcb, 0x4c, 0xd2, 0x41, 0x30, 0x4b, 0xc9, 0xbb, 0x60,
	0xcc, 0xa8, 0xaf, 0x1c, 0xd9, 0xad, 0x38, 0xf2, 0xac, 0x98, 0x3c, 0x3c, 0xa2, 0x7e, 0x28, 0xda,
	0x03, 0x97, 0xc2, 0x42, 0x1c, 0xa4, 0x09, 0xc3, 0x24, 0x6b, 0x8a, 0x42, 0x2c, 0xa7, 0x3b, 0x1f,
	0x80, 0x5d, 0x0a, 0x3f, 0x57, 0xd3, 0xf8, 0x44, 0x19, 0x73, 0x48, 0xd9, 0x2d, 0x8d, 0x79, 0x1d,
	0x9a, 0xd7, 0xe3, 0x53, 0xfa, 0x5c, 0xf0, 0x56, 0xeb, 0x8e, 0xfe, 0xbf, 0x75, 0x47, 0x6b, 0xeb,
	0xfe, 0xb6, 0x4c, 0xe6, 0x3e, 0x8d, 0x6f, 0xb9, 0xf0, 0xdb, 0xb2, 0xd0, 0xe3, 0xba, 0xdb, 0xb2,
	0x0d, 0x96, 0x6b, 0x3c, 0xfc, 0xf9, 0xcc, 0x67, 0xb2, 0xfa, 0xbf, 0x05, 0xad, 0x90, 0xc6, 0xe3,
	0x9c, 0x5e, 0xc9, 0x80, 0x28, 0x1d, 0x44, 0x99, 0xf4, 0xcc, 0x90, 0xc6, 0x23, 0x7a, 0x55, 0xad,
	0x19, 0xcd, 0x5a, 0xcd, 0x50, 0x79, 0x61, 0x3e, 0xa3, 0xf0, 0x62, 0x49, 0x98, 0xf9, 0x99, 0xaa,
	0x54, 0x7c, 0xec, 0x7e, 0x00, 0x06, 0xea, 0x41, 0x5a, 0xa0, 0x9f, 0x8c, 0x0e, 0x9d, 0x2d, 0x62,
	0x43, 0xf3, 0x7c, 0x78, 0x76, 0x7c, 0xe0, 0x68, 0x48, 0x1b, 0x5d, 0x7c, 0xec, 0x34, 0x88, 0x05,
	0xc6, 0xc5, 0x68, 0xe0, 0x39, 0x3a, 0x8e, 0x0e, 0xbc, 0x41, 0xdf, 0x31, 0xdc, 0x5f, 0x28, 0xf0,
	0x72, 0x9a, 0x32, 0xba, 0xb2, 0x5f, 0xab, 0xda, 0xff, 0x40, 0xda, 0xdf, 0xe0, 0xf6, 0x0b, 0x9b,
	0x8e, 0x93, 0xcb, 0x14, 0x3f, 0x91, 0x96, 0x7f, 0x1b, 0x13, 0xfd, 0x0a, 0x0d, 0xd2, 0x45, 0xbf,
	0xcc, 0xe9, 0xd5, 0x71, 0xe8, 0xfe, 0x53, 0x57, 0xde, 0x3e, 0xc9, 0xa7, 0xe4, 0x35, 0xde, 0x13,
	0xb4, 0x4a, 0x74, 0x54, 0x47, 0x38, 0xda, 0xc2, 0x26, 0x41, 0x5c, 0xd0, 0xfd, 0x40, 0x35, 0xcf,
	0xed, 0x8a, 0x44, 0x2f, 0x08, 0x8e, 0xb6, 0x3c, 0x64, 0x92, 0x3d, 0x55, 0x26, 0x45, 0x94, 0x9d,
	0x8a, 0x14, 0xaf, 0x4d, 0x47, 0x5b, 0xaa, 0x74, 0xba, 0xa2, 0xf1, 0x19, 0xd7, 0x56, 0x1b, 0x15,
	0x13, 0x5c, 0x0d, 0xbb, 0x1f, 0xae, 0x86, 0x19, 0xcd, 0xe3, 0xb0, 0xb6, 0x1a, 0xd2, 0xf9, 0x6a,
	0x3c, 0xe5, 0x5d, 0xd0, 0x17, 0xc5, 0x44, 0x46, 0x66, 0xbb, 0x9e, 0x68, 0xb8, 0xda, 0xa2, 0x98,
	0xa0, 0xcc, 0x94, 0x32, 0x59, 0x97, 0xab, 0x32, 0x87, 0x94, 0xa1, 0xcc, 0x94, 0x32, 0xae, 0x15,
	0x65, 0x5d, 0xeb, 0xba, 0x56, 0x42, 0x26, 0x17, 0x32, 0x21, 0x8d, 0x79, 0x5d, 0xae, 0xcb, 0xf4,
	0x69, 0x8c, 0x32, 0x21, 0x8d, 0xc9, 0x9b, 0x60, 0x24, 0xa9, 0x2c, 0xd3, 0xf5, 0xa3, 0x82, 0x61,
	0x39, 0xda, 0xf2, 0x38, 0x9b, 0xec, 0x42, 0x27, 0x4d, 0xc6, 0x13, 0x3a, 0xf3, 0xe3, 0xcb, 0x71,
	0x7a, 0xd9, 0x6d, 0xf3, 0xc0, 0x42, 0x9a, 0x7c, 0xcc, 0x49, 0xc3, 0x4b, 0xf2, 0x1e, 0x00, 0x42,
	0xe9, 0x71, 0x4c, 0x97, 0x34, 0xee, 0x76, 0x78, 0x8c, 0xc5, 0x9e, 0xbd, 0x82, 0xcd, 0x9e, 0x22,
	0xd5, 0xb3, 0x7d, 0x35, 0xfc, 0xd8, 0x86, 0xd6, 0x09, 0xcd, 0x73, 0x7f, 0x4a, 0xdd, 0x53, 0x80,
	0x11, 0xcd, 0x96, 0x34, 0xfb, 0x1a, 0xc0, 0x97, 0x80, 0x11, 0xa6, 0x09, 0x95, 0xe5, 0x8c, 0x8f,
	0xdd, 0xff, 0x36, 0xc0, 0x3e, 0xc7, 0x13, 0xd7, 0x17, 0xb8, 0x05, 0x82, 0x8c, 0xfa, 0x8c, 0x86,
	0x63, 0x09, 0xb2, 0x74, 0xcf, 0x96, 0x94, 0x1e, 0xe3, 0x98, 0x61, 0x11, 0x2a, 0x76, 0x43, 0xb0,
	0x25, 0x45, 0xb0, 0x59, 0x5a, 0x04, 0x33, 0xc1, 0xd6, 0x05, 0x5b, 0x52, 0x7a, 0x8c, 0xbc, 0x03,
	0x26, 0x02, 0xd7, 0x20, 0x97, 0xc7, 0xe3, 0x46, 0x6c, 0x2b, 0x45, 0xc8, 0x03, 0x3c, 0x96, 0xb9,
	0x3c, 0x22, 0xc2, 0xd3, 0xab, 0x0b, 0x06, 0x9e, 0xca, 0xbc, 0x72, 0xfe, 0xcd, 0xca, 0xf9, 0xc7,
	0x44, 0xcf, 0xa8, 0x1f, 0x22, 0xbd, 0xc5, 0xe9, 0x26, 0x4e, 0x15, 0x23, 0x58, 0x22, 0xc3, 0x52,
	0x8c, 0x60, 0x79, 0x1c, 0xe2, 0x42, 0x58, 0x42, 0xa2, 0x90, 0x47, 0xbf, 0xe9, 0x35, 0x43, 0x1a,
	0x0b, 0x30, 0x21, 0xa1, 0x35, 0x6c, 0x82, 0xd6, 0xed, 0x1a, 0xb4, 0x5e, 0xf5, 0xf1, 0x4e, 0xa5,
	0x8f, 0x73, 0x38, 0x8c, 0x03, 0x74, 0xca, 0x1d, 0xee, 0x94, 0x16, 0x9f, 0xf7, 0x98, 0xfb, 0x17,
	0x1d, 0x2c, 0xee, 0x7d, 0xec, 0xb6, 0x75, 0xef, 0x6a, 0x37, 0x78, 0x37, 0xa4, 0x31, 0xad, 0x3b,
	0x5f, 0x52, 0x7a, 0x0c, 0xb5, 0x4d, 0x93, 0x38, 0x2a, 0xc3, 0x2b, 0x67, 0xca, 0x91, 0xc6, 0x33,
	0x1c, 0x59, 0xf1, 0x58, 0x73, 0x93, 0xc7, 0xcc, 0x9a, 0xc7, 0x56, 0xae, 0x69, 0x6d, 0x72, 0x8d,
	0x55, 0x77, 0x4d, 0xa5, 0xfc, 0xda, 0xb5, 0xf2, 0x5b, 0x96, 0x3f, 0xa8, 0x96, 0xbf, 0xfa, 0x51,
	0x6a, 0xaf, 0x1f, 0xa5, 0x55, 0xe8, 0x3b, 0xd5, 0xd0, 0xaf, 0x02, 0x79, 0xa7, 0x1a, 0xc8, 0x37,
	0x60, 0x3b, 0xf6, 0x73, 0x36, 0xce, 0x29, 0x4d, 0xc6, 0x2c, 0x9a, 0xd3, 0xee, 0x36, 0x5f, 0xb0,
	0x83, 0xd4, 0x11, 0xa5, 0xc9, 0x79, 0x34, 0xa7, 0xe4, 0xbb, 0x70, 0x7f, 0x25, 0x55, 0x81, 0xc6,
	0x77, 0xb9, 0x5e, 0xf7, 0x94, 0xec, 0x85, 0x82, 0xc8, 0xee, 0x4f, 0xc0, 0xee, 0xd3, 0xf8, 0x13,
	0x4c, 0xad, 0xbc, 0xb2, 0xb5, 0x56, 0xdd, 0xba, 0xd2, 0x9d, 0x1a, 0xcf, 0xe8, 0x4e, 0xee, 0xdf,
	0xb4, 0x32, 0xaf, 0x59, 0x76, 0xdb, 0x1e, 0x49, 0xc0, 0x08, 0xf0, 0x66, 0x2c, 0xca, 0x3f, 0x1f,
	0x73, 0xec, 0x4a, 0x3f, 0x67, 0x12, 0xa0, 0xf3, 0x71, 0xe5, 0x52, 0xdb, 0xac, 0x5c, 0x6a, 0x57,
	0xdb, 0x7d, 0xd3, 0x97, 0xda, 0xdf, 0x35, 0x94, 0x31, 0x7d, 0x9f, 0xf9, 0x1b, 0x1a, 0xdc, 0x2e,
	0x74, 0x2e, 0xb3, 0x74, 0x3e, 0xae, 0x03, 0x79, 0x40, 0xda, 0x85, 0x38, 0x19, 0xdf, 0x01, 0x1b,
	0x83, 0x95, 0x33, 0x7f, 0xbe, 0xe0, 0xe7, 0x0c, 0x8f, 0x80, 0x22, 0xac, 0xa5, 0x83, 0xbe, 0x9e,
	0x0e, 0xab, 0x13, 0x62, 0x54, 0x4f, 0xc8, 0x7b, 0x12, 0xbb, 0x09, 0x47, 0xbc, 0x54, 0x71, 0x04,
	0xaa, 0xfa, 0x2c, 0xf0, 0x66, 0x7e, 0x43, 0xe0, 0xed, 0x4f, 0xba, 0x72, 0xce, 0x59, 0x46, 0xf3,
	0x0d, 0xce, 0x71, 0x40, 0xcf, 0x33, 0x15, 0x6d, 0x1c, 0x92, 0xbd, 0x1a, 0x1e, 0xba, 0x5f, 0x51,
	0x1c, 0x97, 0xa9, 0x02, 0xa2, 0xfa, 0xc5, 0xce, 0x58, 0xbf, 0xd8, 0xad, 0x1c, 0xd3, 0xbc, 0x39,
	0x75, 0xcc, 0x0d, 0xe7, 0xb7, 0xf5, 0x2c, 0x74, 0xf5, 0x06, 0x6c, 0x33, 0x3f, 0x43, 0x68, 0xaf,
	0xe2, 0x69, 0xf1, 0x8d, 0x3b, 0x82, 0x2a, 0x23, 0xea, 0xc2, 0x1d, 0x3f, 0x60, 0x69, 0x36, 0xae,
	0x97, 0x82, 0x36, 0x27, 0x4a, 0x19, 0x59, 0xaf, 0x60, 0x73, 0xbd, 0x72, 0x0b, 0x09, 0xbc, 0x4c,
	0x68, 0x0c, 0x4f, 0x9d, 0x2d, 0x04, 0x5b, 0xc3, 0x27, 0x4f, 0x1c, 0x0d, 0x09, 0x17, 0x3d, 0x47,
	0x47, 0xc2, 0xc5, 0x59, 0xdf, 0x31, 0x10, 0x73, 0x1d, 0x0e, 0x4f, 0x07, 0x4e, 0x13, 0x49, 0xbd,
	0x83, 0x91, 0x63, 0x22, 0xe9, 0x7c, 0xe0, 0x9d, 0x38, 0x2d, 0x85, 0xdb, 0x2c, 0x24, 0x79, 0x83,
	0x5e, 0xdf, 0xb1, 0xc5, 0xe8, 0xe0, 0x13, 0x07, 0x90, 0xd9, 0x1f, 0x3c, 0x75, 0xda, 0x5c, 0xbe,
	0x77, 0x38, 0x72, 0x3a, 0xee, 0x3f, 0xca, 0x1c, 0x3d, 0xa1, 0xcc, 0xbf, 0x65, 0x8e, 0xba, 0xf2,
	0xde, 0xa8, 0x57, 0x70, 0x45, 0xd9, 0x6f, 0xe5, 0xcd, 0xf1, 0x35, 0x05, 0x9a, 0x56, 0x0e, 0x56,
	0x4d, 0x41, 0xbd, 0x4f, 0x70, 0x6c, 0xd2, 0xac, 0xac, 0x51, 0x56, 0x1e, 0x81, 0x4c, 0x6e, 0xba,
	0xb2, 0x2a, 0x60, 0xdb, 0xaa, 0x5c, 0xf8, 0x56, 0xd8, 0x41, 0x5e, 0xf8, 0x7e, 0xa9, 0x6c, 0x42,
	0x70, 0xf9, 0xb5, 0x53, 0xf5, 0x41, 0xed, 0x74, 0x7e, 0x05, 0x5a, 0xad, 0x26, 0xa4, 0xfb, 0x1f,
	0x0d, 0x6c, 0xe9, 0xd4, 0x7c, 0x8a, 0x00, 0x2b, 0x60, 0x59, 0x2c, 0xf1, 0xea, 0xdd, 0xb5, 0x3a,
	0x85, 0x00, 0x0b, 0xd9, 0x28, 0xc6, 0xdf, 0x6b, 0x1a, 0xd7, 0xc4, 0x30, 0x8b, 0x51, 0x0c, 0xd9,
	0x28, 0xb6, 0xc8, 0x68, 0x2e, 0x7d, 0x7f, 0x77, 0x2d, 0x67, 0x50, 0x0c, 0xd9, 0x28, 0x36, 0xa7,
	0xe5, 0xeb, 0x4f, 0x55, 0x0c, 0xe3, 0x8c, 0x62, 0xc8, 0x46, 0xb1, 0x28, 0xb9, 0x4c, 0x6b, 0x90,
	0x64, 0xe5, 0x3a, 0x14, 0x8b, 0x6a, 0x2e, 0x34, 0x2b, 0x2e, 0xac, 0x22, 0xb8, 0x2f, 0xcb, 0x63,
	0xe4, 0xd1, 0x7c, 0x41, 0xde, 0x04, 0x13, 0xd1, 0x40, 0x21, 0x5e, 0xfc, 0x94, 0xf3, 0x90, 0x75,
	0xc0, 0xf1, 0x90, 0x60, 0x92, 0xb7, 0xc0, 0xcc, 0xb3, 0xe5, 0x3c, 0x9f, 0xd6, 0x90, 0x7a, 0xe9,
	0x39, 0x4f, 0x72, 0xc9, 0x1b, 0xd0, 0x0c, 0x62, 0x14, 0xd3, 0xaf, 0x01, 0x59, 0x14, 0x13, 0x4c,
	0xf7, 0x5f, 0x1a, 0xb4, 0x46, 0x34, 0xcf, 0xa3, 0x34, 0xc1, 0x7a, 0x91, 0x8b, 0xe1, 0xea, 0xed,
	0xd5, 0x96, 0x94, 0xe3, 0x67, 0xbc, 0xb5, 0xd4, 0x31, 0xac, 0xfe, 0x15, 0x18, 0x96, 0xbc, 0x06,
	0xed, 0x8c, 0xce, 0x53, 0x84, 0x41, 0x61, 0x98, 0xc9, 0xba, 0x04, 0x82, 0xd4, 0x0b, 0xc3, 0x6c,
	0xad, 0x6e, 0x35, 0xd7, 0xeb, 0x56, 0xed, 0xf9, 0xc9, 0x5c, 0x7b, 0x7e, 0xda, 0x01, 0x2b, 0xf6,
	0x93, 0x69, 0xe1, 0x4f, 0x29, 0xef, 0x14, 0xb6, 0x57, 0xce, 0xdd, 0xa1, 0xba, 0x0e, 0x79, 0xf4,
	0x0a, 0x33, 0x09, 0x9d, 0xa3, 0xdd, 0xe8, 0x1c, 0x64, 0x91, 0x5d, 0x30, 0xd0, 0xf8, 0xda, 0x6b,
	0xa2, 0x74, 0x95, 0xc7, 0x39, 0xee, 0x47, 0xd0, 0x1e, 0x51, 0x3f, 0x0b, 0x66, 0xe2, 0x49, 0x63,
	0xe3, 0xc3, 0xf5, 0x7d, 0x75, 0x37, 0x96, 0x05, 0x41, 0x5c, 0x86, 0xaf, 0xd4, 0xd7, 0x4f, 0xd2,
	0x22, 0x09, 0x6f, 0x1b, 0xfe, 0x1b, 0xd7, 0xc2, 0x8f, 0x33, 0x9a, 0x17, 0x31, 0xe3, 0x2f, 0x89,
	0xd7, 0x6a, 0x87, 0x64, 0xba, 0x53, 0x00, 0x4e, 0x1b, 0x2c, 0xd1, 0x91, 0x0f, 0xc0, 0xf4, 0x03,
	0x16, 0xa5, 0x89, 0xdc, 0xd1, 0x16, 0x5e, 0xc8, 0x8a, 0xd0, 0x93, 0x0c, 0xac, 0x26, 0x89, 0x5f,
	0x3e, 0x2c, 0xf1, 0xf1, 0x6d, 0x0a, 0x99, 0xfb, 0x07, 0x0d, 0x3a, 0xbd, 0x20, 0x48, 0x8b, 0x84,
	0xdd, 0x7a, 0xaf, 0x8d, 0xe7, 0x6b, 0xed, 0x3d, 0x5c, 0x7f, 0xde, 0xf7, 0x70, 0xa3, 0x86, 0x4c,
	0x55, 0x81, 0xb4, 0x56, 0x05, 0xd2, 0xfd, 0xb7, 0x06, 0xf7, 0x46, 0xc5, 0x24, 0x0f, 0xb2, 0x68,
	0x81, 0xba, 0xdc, 0x5a, 0xe7, 0x8d, 0x0f, 0x46, 0xca, 0x12, 0xbd, 0x66, 0xc9, 0xaa, 0xb7, 0x1a,
	0xd5, 0xde, 0xfa, 0xfc, 0xb0, 0xfb, 0x75, 0xf9, 0x2b, 0xa4, 0x75, 0x73, 0x73, 0xe4, 0xcc, 0xcd,
	0x18, 0xdc, 0x3d, 0x87, 0x8e, 0x2c, 0x42, 0xb7, 0xb6, 0xf4, 0x81, 0xc8, 0x97, 0x9b, 0x0b, 0x2d,
	0x4f, 0x98, 0xfd, 0xc7, 0x60, 0x97, 0x09, 0x8f, 0xdd, 0xf2, 0x14, 0x1b, 0xee, 0x16, 0x8e, 0x7a,
	0xa7, 0xc3, 0x53, 0x07, 0xf8, 0xe8, 0xe2, 0xfc, 0xc8, 0xb9, 0xcf, 0xdb, 0xeb, 0x70, 0x78, 0xee,
	0xbc, 0xba, 0xff, 0x16, 0x58, 0xaa, 0x3f, 0x94, 0xed, 0x77, 0xab, 0x6c, 0xbf, 0xbc, 0x93, 0xff,
	0xf4, 0xcc, 0x69, 0xec, 0x7f, 0x04, 0x96, 0xca, 0x05, 0xd2, 0x01, 0xeb, 0x60, 0x78, 0x7a, 0x7e,
	0x7c, 0x7a, 0x21, 0xd7, 0xef, 0x7b, 0xc3, 0x33, 0x47, 0x23, 0x6d, 0x68, 0x79, 0x83, 0xd1, 0xd9,
	0xf0, 0xb4, 0xef, 0x34, 0xc4, 0xe4, 0xec, 0x69, 0xef, 0x60, 0xe0, 0xe8, 0xfb, 0xfb, 0x60, 0xa0,
	0x35, 0x04, 0xc0, 0x3c, 0xf0, 0x06, 0xbd, 0x73, 0xfc, 0x0e, 0xc0, 0xbc, 0x38, 0xeb, 0xe3, 0x58,
	0xc3, 0x71, 0x7f, 0xf0, 0x74, 0x70, 0x3e, 0x70, 0x1a, 0x8f, 0x7e, 0x0c, 0xc6, 0x29, 0xee, 0xf2,
	0x18, 0xda, 0xd2, 0x49, 0x4f, 0xd3, 0x74, 0x41, 0xd6, 0x6a, 0xc4, 0xce, 0x5a, 0xdd, 0x75, 0xb7,
	0xf6, 0xb4, 0xef, 0x69, 0x8f, 0x7e, 0xdf, 0x00, 0xf3, 0x2c, 0x2e, 0xa6, 0x51, 0x42, 0xde, 0x03,
	0xeb, 0x49, 0x94, 0xd1, 0xa3, 0x34, 0xa7, 0xb5, 0x8f, 0x3d, 0x7a, 0xb5, 0x53, 0x75, 0x20, 0x9a,
	0xe5, 0x6e, 0x91, 0x77, 0xc1, 0x78, 0x12, 0x25, 0x21, 0x71, 0x24, 0xab, 0xac, 0x2b, 0x3b, 0x55,
	0x0a, 0xaf, 0x15, 0xee, 0x16, 0x79, 0x07, 0x5a, 0x32, 0xbf, 0xc8, 0x3d, 0x15, 0xfd, 0x32, 0xdb,
	0x76, 0xc4, 0x8f, 0x06, 0xf9, 0x2f, 0x6f, 0x8b, 0xbc, 0x0d, 0x4d, 0x9e, 0xa0, 0xe4, 0xee, 0x2a,
	0x59, 0x6f, 0x14, 0x7c, 0x1f, 0x3a, 0xd5, 0x34, 0x20, 0x2f, 0x88, 0x9d, 0xd7, 0x33, 0x63, 0xfd,
	0xb3, 0x77, 0xca, 0x9e, 0x26, 0x95, 0xa9, 0x1e, 0xae, 0x35, 0xe1, 0x89, 0xc9, 0x7f, 0x38, 0x3e,
	0xfe, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x65, 0x88, 0x3a, 0xba, 0x7f, 0x1c, 0x00, 0x00,
}
