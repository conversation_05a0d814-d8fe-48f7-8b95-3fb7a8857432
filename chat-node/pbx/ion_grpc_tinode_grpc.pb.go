// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package pbx

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ConfChatClient is the client API for ConfChat service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfChatClient interface {
	CreateConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error)
	LeaveConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error)
	CloseConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error)
	UpdatConfHost(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error)
}

type confChatClient struct {
	cc grpc.ClientConnInterface
}

func NewConfChatClient(cc grpc.ClientConnInterface) ConfChatClient {
	return &confChatClient{cc}
}

func (c *confChatClient) CreateConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error) {
	out := new(ConferenceReply)
	err := c.cc.Invoke(ctx, "/pbx.ConfChat/CreateConfChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confChatClient) LeaveConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error) {
	out := new(ConferenceReply)
	err := c.cc.Invoke(ctx, "/pbx.ConfChat/LeaveConfChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confChatClient) CloseConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error) {
	out := new(ConferenceReply)
	err := c.cc.Invoke(ctx, "/pbx.ConfChat/CloseConfChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confChatClient) UpdatConfHost(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error) {
	out := new(ConferenceReply)
	err := c.cc.Invoke(ctx, "/pbx.ConfChat/UpdatConfHost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfChatServer is the server API for ConfChat service.
// All implementations must embed UnimplementedConfChatServer
// for forward compatibility
type ConfChatServer interface {
	CreateConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error)
	LeaveConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error)
	CloseConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error)
	UpdatConfHost(context.Context, *ConferenceRequest) (*ConferenceReply, error)
	mustEmbedUnimplementedConfChatServer()
}

// UnimplementedConfChatServer must be embedded to have forward compatible implementations.
type UnimplementedConfChatServer struct {
}

func (UnimplementedConfChatServer) CreateConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateConfChat not implemented")
}
func (UnimplementedConfChatServer) LeaveConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LeaveConfChat not implemented")
}
func (UnimplementedConfChatServer) CloseConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseConfChat not implemented")
}
func (UnimplementedConfChatServer) UpdatConfHost(context.Context, *ConferenceRequest) (*ConferenceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatConfHost not implemented")
}
func (UnimplementedConfChatServer) mustEmbedUnimplementedConfChatServer() {}

// UnsafeConfChatServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfChatServer will
// result in compilation errors.
type UnsafeConfChatServer interface {
	mustEmbedUnimplementedConfChatServer()
}

func RegisterConfChatServer(s *grpc.Server, srv ConfChatServer) {
	s.RegisterService(&_ConfChat_serviceDesc, srv)
}

func _ConfChat_CreateConfChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfChatServer).CreateConfChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.ConfChat/CreateConfChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfChatServer).CreateConfChat(ctx, req.(*ConferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfChat_LeaveConfChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfChatServer).LeaveConfChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.ConfChat/LeaveConfChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfChatServer).LeaveConfChat(ctx, req.(*ConferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfChat_CloseConfChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfChatServer).CloseConfChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.ConfChat/CloseConfChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfChatServer).CloseConfChat(ctx, req.(*ConferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfChat_UpdatConfHost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfChatServer).UpdatConfHost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.ConfChat/UpdatConfHost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfChatServer).UpdatConfHost(ctx, req.(*ConferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ConfChat_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pbx.ConfChat",
	HandlerType: (*ConfChatServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateConfChat",
			Handler:    _ConfChat_CreateConfChat_Handler,
		},
		{
			MethodName: "LeaveConfChat",
			Handler:    _ConfChat_LeaveConfChat_Handler,
		},
		{
			MethodName: "CloseConfChat",
			Handler:    _ConfChat_CloseConfChat_Handler,
		},
		{
			MethodName: "UpdatConfHost",
			Handler:    _ConfChat_UpdatConfHost_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ion_grpc_tinode.proto",
}
