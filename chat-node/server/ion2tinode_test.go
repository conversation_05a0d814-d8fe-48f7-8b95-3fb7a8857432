package main

import (
	"context"
	"log"
	"net"
	"testing"

	"github.com/tinode/chat/pbx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/grpc/test/bufconn"
)

func dialer() func(context.Context, string) (net.Conn, error) {
	listener := bufconn.Listen(1024 * 1024)

	server := grpc.NewServer()

	pbx.RegisterConfChatServer(server, &chatNodeRpcService{})

	go func() {
		if err := server.Serve(listener); err != nil {
			log.Fatal(err)
		}
	}()

	return func(context.Context, string) (net.Conn, error) {
		return listener.Dial()
	}
}

func TestIon2Tion(t *testing.T) {
	tests := []struct {
		topic  string
		userId string
	}{
		{
			"confoeHC8r14ugI",
			"usrev5nycBYtg8",
		},
		{
			"confoeHC8r14ugI",
			"usrev5nycBYtg8",
		},
	}

	ctx := context.Background()

	conn, err := grpc.DialContext(ctx, "", grpc.WithInsecure(), grpc.WithContextDialer(dialer()))
	if err != nil {
		log.Fatal(err)
	}
	defer conn.Close()

	client := pbx.NewConfChatClient(conn)

	for _, tt := range tests {
		t.Run(tt.topic, func(t *testing.T) {
			request := &pbx.ConferenceRequest{Topic: tt.topic, UserId: tt.userId}

			response, err := client.CreateConfChat(ctx, request)

			if response != nil {
				if response.GetCode() != 200 {
					t.Error("response: expected 200 received", response.GetCode())
				}
			}

			if err != nil {
				if er, ok := status.FromError(err); ok {
					if er.Code() != 500 || er.Code() != 400 {
						t.Error("error code: expected", codes.InvalidArgument, "received", er.Code())
					}
				}
			}
		})
	}
}
