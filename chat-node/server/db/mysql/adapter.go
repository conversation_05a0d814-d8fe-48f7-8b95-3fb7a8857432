// +build mysql

// Package mysql is a database adapter for MySQL.
package mysql

import (
	"database/sql"
	"encoding/json"
	"errors"
	"hash/fnv"
	"log"
	"strconv"
	"strings"
	"time"

	ms "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/tinode/chat/server/auth"
	"github.com/tinode/chat/server/store"
	t "github.com/tinode/chat/server/store/types"
)

// adapter holds MySQL connection data.
type adapter struct {
	db               *sqlx.DB
	dsn              string
	dbName           string
	avatarDomainName string
	// Maximum number of records to return
	maxResults int
	// Maximum number of message records to return
	maxMessageResults int
	version           int
	userHardDeletion  bool
}

const (
	defaultDSN      = "root:@tcp(localhost:3306)/tinode?parseTime=true"
	defaultDatabase = "tinode"

	defaultAvatarBaseUrl      = "http://localhost:5000"
	defaultAvatarUrl          = "/templates/avatar/default_avatar.png"
	defaultGroupChatAvatarUrl = "/templates/avatar/group_chat_avatar.png"

	adpVersion = 111

	adapterName = "mysql"

	defaultMaxResults = 1024
	// This is capped by the Session's send queue limit (128).
	defaultMaxMessageResults = 100
)

type configType struct {
	DSN              string `json:"dsn,omitempty"`
	DBName           string `json:"database,omitempty"`

	AvatarDomainName string `json:"avatar_domain_name,omitempty"`
	UserHardDeletion bool   `json:"user_hard_deletion"`
}

// Open initializes database session
func (a *adapter) Open(jsonconfig json.RawMessage) error {
	if a.db != nil {
		return errors.New("mysql adapter is already connected")
	}

	var err error
	var config configType

	if err = json.Unmarshal(jsonconfig, &config); err != nil {
		return errors.New("mysql adapter failed to parse config: " + err.Error())
	}

	a.dsn = config.DSN
	if a.dsn == "" {
		a.dsn = defaultDSN
	}

	a.dbName = config.DBName
	if a.dbName == "" {
		a.dbName = defaultDatabase
	}
	a.avatarDomainName = config.AvatarDomainName
	if a.avatarDomainName == "" {
		a.avatarDomainName = defaultAvatarBaseUrl
	}

	if a.maxResults <= 0 {
		a.maxResults = defaultMaxResults
	}

	if a.maxMessageResults <= 0 {
		a.maxMessageResults = defaultMaxMessageResults
	}

	a.userHardDeletion = config.UserHardDeletion

	// This just initializes the driver but does not open the network connection.
	a.db, err = sqlx.Open("mysql", a.dsn)
	if err != nil {
		return err
	}

	// Actually opening the network connection.
	err = a.db.Ping()
	if isMissingDb(err) {
		// Ignore missing database here. If we are initializing the database
		// missing DB is OK.
		err = nil
	}
	return err
}

// Close closes the underlying database connection
func (a *adapter) Close() error {
	var err error
	if a.db != nil {
		err = a.db.Close()
		a.db = nil
		a.version = -1
	}
	return err
}

// IsOpen returns true if connection to database has been established. It does not check if
// connection is actually live.
func (a *adapter) IsOpen() bool {
	return a.db != nil
}

// GetDbVersion returns current database version.
func (a *adapter) GetDbVersion() (int, error) {
	if a.version > 0 {
		return a.version, nil
	}

	var vers int
	err := a.db.Get(&vers, "SELECT `value` FROM kvmeta WHERE `key`='version'")
	if err != nil {
		if isMissingDb(err) || isMissingTable(err) || err == sql.ErrNoRows {
			err = errors.New("Database not initialized")
		}
		return -1, err
	}

	a.version = vers

	return vers, nil
}

func (a *adapter) updateDbVersion(v int) error {
	a.version = -1
	if _, err := a.db.Exec("UPDATE kvmeta SET `value`=? WHERE `key`='version'", v); err != nil {
		return err
	}
	return nil
}

// CheckDbVersion checks whether the actual DB version matches the expected version of this adapter.
func (a *adapter) CheckDbVersion() error {
	version, err := a.GetDbVersion()
	if err != nil {
		return err
	}

	if version != adpVersion {
		return errors.New("Invalid database version " + strconv.Itoa(version) +
			". Expected " + strconv.Itoa(adpVersion))
	}

	return nil
}

// Version returns adapter version.
func (adapter) Version() int {
	return adpVersion
}

// GetName returns string that adapter uses to register itself with store.
func (a *adapter) GetName() string {
	return adapterName
}

// SetMaxResults configures how many results can be returned in a single DB call.
func (a *adapter) SetMaxResults(val int) error {
	if val <= 0 {
		a.maxResults = defaultMaxResults
	} else {
		a.maxResults = val
	}

	return nil
}

// CreateDb initializes the storage.
func (a *adapter) CreateDb(reset bool) error {
	var err error
	var tx *sql.Tx

	// Can't use an existing connection because it's configured with a database name which may not exist.
	// Don't care if it does not close cleanly.
	a.db.Close()

	// This DSN has been parsed before and produced no error, not checking for errors here.
	cfg, _ := ms.ParseDSN(a.dsn)
	// Clear database name
	cfg.DBName = ""

	a.db, err = sqlx.Open("mysql", cfg.FormatDSN())
	if err != nil {
		return err
	}

	if tx, err = a.db.Begin(); err != nil {
		return err
	}

	defer func() {
		if err != nil {
			// FIXME: This is useless: MySQL auto-commits on every CREATE TABLE.
			// Maybe DROP DATABASE instead.
			tx.Rollback()
		}
	}()

	if reset {
		if _, err = tx.Exec("DROP DATABASE IF EXISTS " + a.dbName); err != nil {
			return err
		}

		if _, err = tx.Exec("CREATE DATABASE " + a.dbName + " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"); err != nil {
			return err
		}
	}

	if _, err = tx.Exec("USE " + a.dbName); err != nil {
		return err
	}

	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS users(
			id         BIGINT NOT NULL AUTO_INCREMENT,
			uid        BIGINT NOT NULL UNIQUE,
			created_at DATETIME(3) NOT NULL,
			updated_at DATETIME(3) NOT NULL,
			deleted_at DATETIME(3),
			state     SMALLINT NOT NULL DEFAULT 0,
			stateat   DATETIME(3),
			access    JSON,
			lastseen  DATETIME,
			useragent VARCHAR(255) DEFAULT '',
			public    JSON,
			tags      JSON,
			username  varchar(255),
			email     varchar(255),
			phone     varchar(100),
			password  varchar(255),
			salt      varchar(255),
			pmi       varchar(255) NOT NULL UNIQUE,
			wechat    varchar(50) DEFAULT NULL,
			qq        varchar(50) DEFAULT NULL,
			PRIMARY KEY(id),
			INDEX users_state_stateat(state, stateat)
		)`); err != nil {
		return err
	}

	// Indexed user blocking
	if _, err = tx.Exec(`CREATE TABLE IF NOT EXISTS userblockings(
		id        BIGINT NOT NULL AUTO_INCREMENT, -- The identifier for user blocking
		initiator BIGINT NOT NULL,      -- User who initiated the blocking
		effector  BIGINT NOT NULL,      -- User who being blocked,
		deleted_at  DATETIME(3),
		PRIMARY KEY (id),
		INDEX blocking_initiator (initiator),
		INDEX blocked_user (effector),
		FOREIGN KEY (initiator) REFERENCES users (uid),
		FOREIGN KEY (effector) REFERENCES users (uid),
		UNIQUE INDEX blocking_pairs (initiator, effector)
	)`); err != nil {
		return err
	}
	// Indexed user tags.
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS usertags(
			id     INT NOT NULL AUTO_INCREMENT,
			userid BIGINT NOT NULL,
			tag    VARCHAR(96) NOT NULL,
			created_at DATETIME(3),
			updated_at DATETIME(3),
			deleted_at DATETIME(3),
			PRIMARY KEY(id),
			FOREIGN KEY(userid) REFERENCES users(uid),
			INDEX usertags_tag(tag),
			UNIQUE INDEX usertags_userid_tag(userid, tag)
		)`); err != nil {
		return err
	}

	// Indexed subrequest.
	//messages :[{uid1: ""},{uid2: ""}]
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS friendrequest(
			id     INT NOT NULL AUTO_INCREMENT,
			sender   BIGINT NOT NULL,
			receiver BIGINT NOT NULL,
			messages JSON,
			created_at DATETIME(3),
			updated_at DATETIME(3),
			deleted_at DATETIME(3),
			status INT DEFAULT 0 NOT NULL,
			PRIMARY KEY(id),
			FOREIGN KEY(sender) REFERENCES users(uid),
			FOREIGN KEY(receiver) REFERENCES users(uid),
			INDEX sent_request(sender),
			INDEX received_request(receiver),
			UNIQUE INDEX sender_receiver(sender, receiver)
		)`); err != nil {
		return err
	}

	// Indexed devices. Normalized into a separate table.
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS devices(
			id       INT NOT NULL AUTO_INCREMENT,
			userid   BIGINT UNIQUE NOT NULL,
			hash     CHAR(16) UNIQUE NOT NULL,
			deviceid TEXT NOT NULL,
			platform VARCHAR(32),
			ip       CHAR(20) DEFAULT NULL,
			lastseen DATETIME NOT NULL,
			lang     VARCHAR(8),
			created_at DATETIME(3),
			updated_at DATETIME(3),
			deleted_at DATETIME(3),
			PRIMARY KEY(id),
			FOREIGN KEY(userid) REFERENCES users(uid),
			UNIQUE INDEX devices_hash (hash)
		)`); err != nil {
		return err
	}

	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS sensitivemessages (
			id           INT NOT NULL AUTO_INCREMENT,
			type         INT NOT NULL DEFAULT '0',
			topic        VARCHAR(45) NOT NULL,` +
			"`from`         BIGINT NOT NULL," +
			`seqid        INT NOT NULL,
			description  JSON DEFAULT NULL,
			PRIMARY KEY (id),
			UNIQUE KEY id_UNIQUE (id)
		)`); err != nil {
		return err
	}

	// Authentication records for the basic authentication scheme.
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS auth(
			id      INT NOT NULL AUTO_INCREMENT,
			uname   VARCHAR(32) NOT NULL,
			userid  BIGINT NOT NULL,
			scheme  VARCHAR(16) NOT NULL,
			authlvl INT NOT NULL,
			secret  VARCHAR(255) NOT NULL,
			expires DATETIME,
			PRIMARY KEY(id),
			FOREIGN KEY(userid) REFERENCES users(uid),
			UNIQUE INDEX auth_userid_scheme(userid, scheme),
			UNIQUE INDEX auth_uname(uname)
		)`); err != nil {
		return err
	}

	// Topics
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS topics(
			id        INT NOT NULL AUTO_INCREMENT,
			created_at DATETIME(3) NOT NULL,
			updated_at DATETIME(3) NOT NULL,
			state     SMALLINT NOT NULL DEFAULT 0,
			stateat   DATETIME(3),
			touchedat DATETIME(3),
			name      VARCHAR(50) NOT NULL,
			status    INT DEFAULT 0,
			usebt     INT DEFAULT 0,
			owner     BIGINT NOT NULL DEFAULT 0,
			access    JSON,
			seqid     INT NOT NULL DEFAULT 0,
			delid     INT DEFAULT 0,
			public    JSON,
			tags      JSON,
			PRIMARY KEY(id),
			UNIQUE INDEX topics_name(name),
			INDEX topics_owner(owner),
			INDEX topics_state_stateat(state, stateat)
		)`); err != nil {
		return err
	}

	// Create system topic 'sys'.
	if err = createSystemTopic(tx); err != nil {
		return err
	}

	// Indexed topic tags.
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS topictags(
			id    INT NOT NULL AUTO_INCREMENT,
			topic VARCHAR(50) NOT NULL,
			tag   VARCHAR(96) NOT NULL,
			PRIMARY KEY(id),
			FOREIGN KEY(topic) REFERENCES topics(name),
			INDEX topictags_tag(tag),
			UNIQUE INDEX topictags_userid_tag(topic, tag)
		)`); err != nil {
		return err
	}

	// Subscriptions
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS subscriptions(
			id        INT NOT NULL AUTO_INCREMENT,
			created_at DATETIME(3) NOT NULL,
			updated_at DATETIME(3) NOT NULL,
			deleted_at DATETIME(3),
			userid    BIGINT NOT NULL,
			topic     VARCHAR(50) NOT NULL,
			delid     INT DEFAULT 0,
			recvseqid INT DEFAULT 0,
			readseqid INT DEFAULT 0,
			modewant  CHAR(8),
			modegiven CHAR(8),
			private   JSON,
			PRIMARY KEY(id),
			FOREIGN KEY(userid) REFERENCES users(uid),
			UNIQUE INDEX subscriptions_topic_userid(topic, userid),
			INDEX subscriptions_topic(topic),
			INDEX subscriptions_deleted_at(deleted_at)
		)`); err != nil {
		return err
	}

	// Messages
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS messages(
			id        INT NOT NULL AUTO_INCREMENT,
			created_at DATETIME(3) NOT NULL,
			updated_at DATETIME(3) NOT NULL,
			deleted_at DATETIME(3),
			delid      INT DEFAULT 0,
			seqid      INT NOT NULL,
			topic      VARCHAR(50) NOT NULL,` +
			"`from`    BIGINT NOT NULL," +
			`head      JSON,
			content    JSON,
			reference  INT DEFAULT 0,
			refcontent JSON DEFAULT NULL,
			type       INT DEFAULT 0,
			PRIMARY KEY(id),
			FOREIGN KEY(topic) REFERENCES topics(name),
			UNIQUE INDEX messages_topic_seqid(topic, seqid)
		);`); err != nil {
		return err
	}

	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS messagereaction(
			id     INT NOT NULL AUTO_INCREMENT,
			messageseq   INT NOT NULL,
			topic  VARCHAR(50) NOT NULL,
			status INT  NOT NULL DEFAULT 0,
			emoji CHAR(25)  NOT NULL,` +
			"`from`   BIGINT NOT NULL," +
			`created_at DATETIME(3),
			updated_at DATETIME(3),
			deleted_at DATETIME(3),
			PRIMARY KEY(id),
			FOREIGN KEY(topic) REFERENCES topics(name),
			UNIQUE INDEX messageseq_from_emoji(topic, messageseq,` + "`from`," + `emoji)
		)`); err != nil {
		return err
	}

	// Deletion log
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS dellog(
			id         INT NOT NULL AUTO_INCREMENT,
			topic      VARCHAR(50) NOT NULL,
			deletedfor BIGINT NOT NULL DEFAULT 0,
			delid      INT NOT NULL,
			low        INT NOT NULL,
			hi         INT NOT NULL,
			type       INT NOT NULL DEFAULT 0,
			PRIMARY KEY(id),
			FOREIGN KEY(topic) REFERENCES topics(name),
			INDEX dellog_topic_delid_deletedfor(topic,delid,deletedfor),
			INDEX dellog_topic_deletedfor_low_hi(topic,deletedfor,low,hi),
			INDEX dellog_deletedfor(deletedfor)
		);`); err != nil {
		return err
	}

	// User credentials
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS credentials(
			id        INT NOT NULL AUTO_INCREMENT,
			created_at DATETIME(3) NOT NULL,
			updated_at DATETIME(3) NOT NULL,
			deleted_at DATETIME(3),
			method    VARCHAR(16) NOT NULL,
			value     VARCHAR(128) NOT NULL,
			synthetic VARCHAR(192) NOT NULL,
			userid    BIGINT NOT NULL,
			resp      VARCHAR(255),
			done      TINYINT NOT NULL DEFAULT 0,
			retries   INT NOT NULL DEFAULT 0,
			PRIMARY KEY(id),
			UNIQUE credentials_uniqueness(synthetic),
			FOREIGN KEY(userid) REFERENCES users(uid)
		);`); err != nil {
		return err
	}

	// Records of uploaded files.
	// Don't add FOREIGN KEY on userid. It's not needed and it will break user deletion.
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS fileuploads(
			id        BIGINT NOT NULL,
			created_at DATETIME(3) NOT NULL,
			updated_at DATETIME(3) NOT NULL,
			userid    BIGINT NOT NULL,
			status    INT NOT NULL,
			mimetype  VARCHAR(255) NOT NULL,
			size      BIGINT NOT NULL,
			location  VARCHAR(2048) NOT NULL,
			type     INT DEFAULT 0,
			PRIMARY KEY(id)
		)`); err != nil {
		return err
	}

	// Links between uploaded files and the messages they are attached to.
	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS filemsglinks(
			id			INT NOT NULL AUTO_INCREMENT,
			created_at	DATETIME(3) NOT NULL,
			fileid		BIGINT NOT NULL,
			msgid 		INT NOT NULL,
			PRIMARY KEY(id),
			FOREIGN KEY(fileid) REFERENCES fileuploads(id) ON DELETE CASCADE,
			FOREIGN KEY(msgid) REFERENCES messages(id) ON DELETE CASCADE
		)`); err != nil {
		return err
	}

	if _, err = tx.Exec(
		`CREATE TABLE IF NOT EXISTS kvmeta(` +
			"`key`   CHAR(32)," +
			"`value` TEXT," +
			"PRIMARY KEY(`key`)" +
			`)`); err != nil {
		return err
	}
	if _, err = tx.Exec("INSERT INTO kvmeta(`key`, `value`) VALUES('version', ?) ON DUPLICATE KEY UPDATE value=value", adpVersion); err != nil {
		return err
	}

	return tx.Commit()
}

// UpgradeDb upgrades the database, if necessary.
func (a *adapter) UpgradeDb() error {
	bumpVersion := func(a *adapter, x int) error {
		if err := a.updateDbVersion(x); err != nil {
			return err
		}
		_, err := a.GetDbVersion()
		return err
	}

	if _, err := a.GetDbVersion(); err != nil {
		return err
	}

	if a.version == 106 {
		// Perform database upgrade from version 106 to version 107.

		if _, err := a.db.Exec("CREATE UNIQUE INDEX usertags_userid_tag ON usertags(userid, tag)"); err != nil {
			return err
		}

		if _, err := a.db.Exec("CREATE UNIQUE INDEX topictags_userid_tag ON topictags(topic, tag)"); err != nil {
			return err
		}

		if _, err := a.db.Exec("ALTER TABLE credentials ADD deleted_at DATETIME(3) AFTER updated_at"); err != nil {
			return err
		}

		if err := bumpVersion(a, 107); err != nil {
			return err
		}
	}

	if a.version == 107 {
		// Perform database upgrade from version 107 to version 108.

		// Replace default user access JRWPA with JRWPAS.
		if _, err := a.db.Exec(`UPDATE users SET access=JSON_REPLACE(access, '$.Auth', 'JRWPAS') 
			WHERE CAST(JSON_EXTRACT(access, '$.Auth') AS CHAR) LIKE '"JRWPA"'`); err != nil {
			return err
		}

		if err := bumpVersion(a, 108); err != nil {
			return err
		}
	}

	if a.version == 108 {
		// Perform database upgrade from version 108 to version 109.

		tx, err := a.db.Begin()
		if err != nil {
			return err
		}
		if err = createSystemTopic(tx); err != nil {
			tx.Rollback()
			return err
		}
		if err = tx.Commit(); err != nil {
			return err
		}

		if err = bumpVersion(a, 109); err != nil {
			return err
		}
	}

	if a.version == 109 {
		// Perform database upgrade from version 109 to version 110.
		if _, err := a.db.Exec(`UPDATE topics SET touchedat=updated_at WHERE touchedat IS NULL`); err != nil {
			return err
		}

		if err := bumpVersion(a, 110); err != nil {
			return err
		}
	}

	if a.version == 110 {
		// Users
		if _, err := a.db.Exec("ALTER TABLE users MODIFY state SMALLINT NOT NULL DEFAULT 0 AFTER updated_at"); err != nil {
			return err
		}

		if _, err := a.db.Exec("ALTER TABLE users CHANGE deleted_at stateat DATETIME(3)"); err != nil {
			return err
		}

		if _, err := a.db.Exec("ALTER TABLE users DROP INDEX users_deleted_at"); err != nil {
			return err
		}

		// Add status to formerly soft-deleted users.
		if _, err := a.db.Exec("UPDATE users SET state=? WHERE stateat IS NOT NULL", t.StateDeleted); err != nil {
			return err
		}

		if _, err := a.db.Exec("ALTER TABLE users ADD INDEX users_state(state)"); err != nil {
			return err
		}

		// Topics
		if _, err := a.db.Exec("ALTER TABLE topics ADD state SMALLINT NOT NULL DEFAULT 0 AFTER updated_at"); err != nil {
			return err
		}

		if _, err := a.db.Exec("ALTER TABLE topics CHANGE deleted_at stateat DATETIME(3)"); err != nil {
			return err
		}

		// Add status to formerly soft-deleted topics.
		if _, err := a.db.Exec("UPDATE topics SET state=? WHERE stateat IS NOT NULL", t.StateDeleted); err != nil {
			return err
		}

		if _, err := a.db.Exec("ALTER TABLE topics ADD INDEX topics_state(state)"); err != nil {
			return err
		}

		// Subscriptions
		if _, err := a.db.Exec("ALTER TABLE subscriptions ADD INDEX topics_deleted_at(deleted_at)"); err != nil {
			return err
		}

		if err := bumpVersion(a, 111); err != nil {
			return err
		}
	}

	if a.version != adpVersion {
		return errors.New("Failed to perform database upgrade to version " + strconv.Itoa(adpVersion) +
			". DB is still at " + strconv.Itoa(a.version))
	}
	return nil
}

func createSystemTopic(tx *sql.Tx) error {
	now := t.TimeNow()
	sql := `INSERT INTO topics(created_at,updated_at,state,touchedat,name,access,public)
				VALUES(?,?,?,?,'sys','{"Auth": "N","Anon": "N"}','{"fn": "System"}') ON DUPLICATE KEY UPDATE updated_at=updated_at`
	_, err := tx.Exec(sql, now, now, t.StateOK, now)
	return err
}

func addTags(tx *sqlx.Tx, table, keyName string, keyVal interface{}, tags []string, ignoreDups bool) error {

	if len(tags) == 0 {
		return nil
	}

	var insert *sql.Stmt
	var err error
	insert, err = tx.Prepare("INSERT INTO " + table + "(" + keyName + ",tag) VALUES(?,?)")
	if err != nil {
		return err
	}

	for _, tag := range tags {
		_, err = insert.Exec(keyVal, tag)

		if err != nil {
			if isDupe(err) {
				if ignoreDups {
					continue
				}
				return t.ErrDuplicate
			}
			return err
		}
	}

	return nil
}

func removeTags(tx *sqlx.Tx, table, keyName string, keyVal interface{}, tags []string) error {
	if len(tags) == 0 {
		return nil
	}

	var args []interface{}
	for _, tag := range tags {
		args = append(args, tag)
	}

	query, args, _ := sqlx.In("DELETE FROM "+table+" WHERE "+keyName+"=? AND tag IN (?)", keyVal, args)
	query = tx.Rebind(query)
	_, err := tx.Exec(query, args...)

	return err
}

// UserCreate creates a new user. Returns error and true if error is due to duplicate user name,
// false for any other error
func (a *adapter) UserCreate(user *t.User) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	decoded_uid := store.DecodeUid(user.Uid())
	if _, err = tx.Exec("INSERT INTO users(id,uid,created_at,updated_at,state,access,public,tags) VALUES(?,?,?,?,?,?,?,?)",
		decoded_uid,
		decoded_uid,
		user.CreatedAt, user.UpdatedAt,
		user.State, user.Access,
		toJSON(user.Public), user.Tags); err != nil {
		return err
	}

	// Save user's tags to a separate table to make user findable.
	if err = addTags(tx, "usertags", "userid", decoded_uid, user.Tags, false); err != nil {
		return err
	}

	return tx.Commit()
}

// Add user's authentication record
func (a *adapter) AuthAddRecord(uid t.Uid, scheme, unique string, authLvl auth.Level,
	secret []byte, expires time.Time) error {

	var exp *time.Time
	if !expires.IsZero() {
		exp = &expires
	}
	_, err := a.db.Exec("INSERT INTO auth(uname,userid,scheme,authLvl,secret,expires) VALUES(?,?,?,?,?,?)",
		unique, store.DecodeUid(uid), scheme, authLvl, secret, exp)
	if err != nil {
		if isDupe(err) {
			return t.ErrDuplicate
		}
		return err
	}
	return nil
}

// AuthDelScheme deletes an existing authentication scheme for the user.
func (a *adapter) AuthDelScheme(user t.Uid, scheme string) error {
	_, err := a.db.Exec("DELETE FROM auth WHERE userid=? AND scheme=?", store.DecodeUid(user), scheme)
	return err
}

// AuthDelAllRecords deletes all authentication records for the user.
func (a *adapter) AuthDelAllRecords(user t.Uid) (int, error) {
	res, err := a.db.Exec("DELETE FROM auth WHERE userid=?", store.DecodeUid(user))
	if err != nil {
		return 0, err
	}
	count, _ := res.RowsAffected()

	return int(count), nil
}

// Update user's authentication secret
func (a *adapter) AuthUpdRecord(uid t.Uid, scheme, unique string, authLvl auth.Level,
	secret []byte, expires time.Time) error {
	var exp *time.Time
	if !expires.IsZero() {
		exp = &expires
	}

	_, err := a.db.Exec("UPDATE auth SET uname=?,authLvl=?,secret=?,expires=? WHERE userid=? AND scheme=?",
		unique, authLvl, secret, exp, store.DecodeUid(uid), scheme)
	if isDupe(err) {
		return t.ErrDuplicate
	}

	return err
}

// Retrieve user's authentication record
func (a *adapter) AuthGetRecord(uid t.Uid, scheme string) (string, auth.Level, []byte, time.Time, error) {
	var expires time.Time

	var record struct {
		Uname   string
		Authlvl auth.Level
		Secret  []byte
		Expires *time.Time
	}

	if err := a.db.Get(&record, "SELECT uname,secret,expires,authlvl FROM auth WHERE userid=? AND scheme=?",
		store.DecodeUid(uid), scheme); err != nil {
		if err == sql.ErrNoRows {
			// Nothing found - clear the error
			err = nil
		}
		return "", 0, nil, expires, err
	}

	if record.Expires != nil {
		expires = *record.Expires
	}

	return record.Uname, record.Authlvl, record.Secret, expires, nil
}

// Retrieve user's authentication record
func (a *adapter) AuthGetUniqueRecord(unique string) (t.Uid, auth.Level, []byte, time.Time, error) {
	var expires time.Time

	var record struct {
		Userid  int64
		Authlvl auth.Level
		Secret  []byte
		Expires *time.Time
	}
	if err := a.db.Get(&record, "SELECT userid,secret,expires,authlvl FROM auth WHERE uname=?", unique); err != nil {
		if err == sql.ErrNoRows {
			// Nothing found - clear the error
			err = nil
		}
		return t.ZeroUid, 0, nil, expires, err
	}

	if record.Expires != nil {
		expires = *record.Expires
	}

	return store.EncodeUid(record.Userid), record.Authlvl, record.Secret, expires, nil
}

// UserGet fetches a single user by user id. If user is not found it returns (nil, nil)
func (a *adapter) UserGet(uid t.Uid) (*t.User, error) {
	var user t.User
	err := a.db.Get(&user, "SELECT uid, created_at, updated_at, state, stateat, access, lastseen, useragent, public, tags FROM users WHERE uid=? AND state!=?", store.DecodeUid(uid), t.StateDeleted)
	if err == nil {
		user.SetUid(uid)
		user.Public = reformAvatartUrl(user.Public, a.avatarDomainName)
		return &user, nil
	}
	if err == sql.ErrNoRows {
		// Clear the error if user does not exist or marked as soft-deleted.
		return nil, nil
	}

	return nil, err
}

func (a *adapter) UserGetAll(ids ...t.Uid) ([]t.User, error) {
	uids := make([]interface{}, len(ids))
	for i, id := range ids {
		uids[i] = store.DecodeUid(id)
	}

	users := []t.User{}
	q, uids, _ := sqlx.In("SELECT uid, created_at, updated_at, state, stateat, access, lastseen, useragent, public, tags FROM users WHERE uid IN (?) AND state!=?", uids, t.StateDeleted)
	q = a.db.Rebind(q)
	rows, err := a.db.Queryx(q, uids...)
	if err != nil {
		return nil, err
	}

	var user t.User
	for rows.Next() {
		if err = rows.StructScan(&user); err != nil {
			users = nil
			break
		}

		if user.State == t.StateDeleted {
			continue
		}

		user.SetUid(encodeUidString(user.Userid))
		user.Public = fromJSON(user.Public)

		users = append(users, user)
	}
	rows.Close()

	return users, err
}

// UserDelete deletes specified user: wipes completely (hard-delete) or marks as deleted.
// TODO: report when the user is not found.
func (a *adapter) UserDelete(uid t.Uid, hard bool) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	decoded_uid := store.DecodeUid(uid)
	if hard && a.userHardDeletion {
		// Delete user's devices
		// t.ErrNotFound = user has no devices.
		if err = deviceDelete(tx, uid, ""); err != nil && err != t.ErrNotFound {
			return err
		}

		// Delete user's subscriptions in all topics.
		if err = subsDelForUser(tx, uid, true); err != nil {
			return err
		}

		// Disable all participator info of  conference owner by the owner
		if _, err = tx.Exec("DELETE feedbacks WHERE uid=?", decoded_uid); err != nil {
			return err
		}

		// Disable all participator info of  conference owner by the owner
		if _, err = tx.Exec("DELETE reports WHERE uid=?", decoded_uid); err != nil {
			return err
		}

		// Disable all participator info of  conference owner by the owner
		if _, err = tx.Exec("DELETE participators p LEFT JOIN conferences c ON p.conference_id = c.conference_id " +
			"WHERE c.host_id=?", decoded_uid); err != nil {
			return err
		}

		// Disable all conference owner by the owner
		if _, err = tx.Exec("DELETE conferences WHERE host_id=?", decoded_uid); err != nil {
			return err
		}

		// Disable all user blocks created by this user
		if _, err = tx.Exec("DELETE userblockings WHERE initiator=? or effector=?", decoded_uid, decoded_uid); err != nil {
			return err
		}

		// Disable all messagereaction owner by the owner
		if _, err = tx.Exec("DELETE messagereaction mr LEFT JOIN topics t ON mr.topic = t.name WHERE t.owner=?",
			decoded_uid); err != nil {
			return err
		}

		// Delete records of messages soft-deleted for the user.
		if _, err = tx.Exec("DELETE FROM dellog WHERE deletedfor=?", decoded_uid); err != nil {
			return err
		}

		// Delete records of friendrequest sent or received by user
		if _, err = tx.Exec("DELETE FROM friendrequest WHERE sender=? OR receiver=?", decoded_uid, decoded_uid); err != nil {
			return err
		}

		// Can't delete user's messages in all topics because we cannot notify topics of such deletion.
		// Just leave the messages there marked as sent by "not found" user.

		// Delete topics where the user is the owner.

		// First delete all messages in those topics.
		if _, err = tx.Exec("DELETE dellog FROM dellog LEFT JOIN topics ON topics.name=dellog.topic WHERE topics.owner=?",
			decoded_uid); err != nil {
			return err
		}
		if _, err = tx.Exec("DELETE messages FROM messages LEFT JOIN topics ON topics.name=messages.topic WHERE topics.owner=?",
			decoded_uid); err != nil {
			return err
		}

		// Delete all subscriptions.
		if _, err = tx.Exec("DELETE sub FROM subscriptions AS sub LEFT JOIN topics ON topics.name=sub.topic WHERE topics.owner=?",
			decoded_uid); err != nil {
			return err
		}

		// Delete topic tags
		if _, err = tx.Exec("DELETE topictags FROM topictags LEFT JOIN topics ON topics.name=topictags.topic WHERE topics.owner=?",
			decoded_uid); err != nil {
			return err
		}

		// And finally delete the topics.
		if _, err = tx.Exec("DELETE FROM topics WHERE owner=?", decoded_uid); err != nil {
			return err
		}

		// Delete user's authentication records.
		if _, err = tx.Exec("DELETE FROM auth WHERE userid=?", decoded_uid); err != nil {
			return err
		}

		// Delete all credentials.
		if err = credDel(tx, uid, "", ""); err != nil && err != t.ErrNotFound {
			return err
		}

		if _, err = tx.Exec("DELETE FROM usertags WHERE userid=?", decoded_uid); err != nil {
			return err
		}

		if _, err = tx.Exec("DELETE FROM users WHERE uid=?", decoded_uid); err != nil {
			return err
		}
	} else {
		now := t.TimeNow()
		// Disable all user's subscriptions. That includes p2p subscriptions. No need to delete them.
		if err = subsDelForUser(tx, uid, false); err != nil {
			return err
		}
		// Disable all feedback info of  conference owner by the owner
		if _, err = tx.Exec("UPDATE feedbacks SET updated_at=?, deleted_at=? WHERE uid=?",
			now, now, decoded_uid); err != nil {
			return err
		}

		// Disable all reports info of  conference owner by the owner
		if _, err = tx.Exec("UPDATE reports SET updated_at=?, deleted_at=? WHERE uid=?",
			now, now, decoded_uid); err != nil {
			return err
		}

		// Disable all participator info of  conference owner by the owner
		if _, err = tx.Exec("UPDATE participators p LEFT JOIN conferences c ON p.conference_id = c.conference_id " +
			"SET p.updated_at=?, p.deleted_at=?, p.status=?, c.updated_at=?, c.deleted_at=?, c.status=? WHERE c.host_id=?",
			now, now, t.ParticipatorDeleted, now, now, t.ConferenceClosed, decoded_uid); err != nil {
			return err
		}

		// Disable all user blocks created by this user
		if _, err = tx.Exec("UPDATE userblockings SET deleted_at=? WHERE initiator=? OR effector=?",
			now, decoded_uid, decoded_uid); err != nil {
			return err
		}

		// Disable all  message reactions in grp topic which own by the owner
		if _, err = tx.Exec("UPDATE messagereaction mr LEFT JOIN topics t ON mr.topic = t.name " +
			" SET mr.updated_at=?, mr.deleted_at=? WHERE t.owner=?",
			now, now, decoded_uid); err != nil {
			return err
		}

		// Disable all messages in topics where the user is the owner.
		if _, err = tx.Exec("UPDATE messages LEFT JOIN topics ON messages.topic=topics.name "+
			"SET messages.updated_at=?, messages.deleted_at=? WHERE topics.owner=?",
			now, now, decoded_uid); err != nil {
			return err
		}
		// Disable group topics where the user is the owner.
		if _, err = tx.Exec("UPDATE topics SET updated_at=?, state=?, stateat=?, status=? WHERE owner=?",
			now, t.StateDeleted, now, t.TopicInvalid, decoded_uid); err != nil {
			return err
		}
		// Disable p2p topics with the user (p2p topic's owner is 0).
		if _, err = tx.Exec("UPDATE topics LEFT JOIN subscriptions ON topics.name=subscriptions.topic "+
			"SET topics.updated_at=?, topics.state=?, topics.stateat=?, topics.status=? WHERE topics.owner=0 AND subscriptions.userid=?",
			now, t.StateDeleted, now, t.TopicInvalid, decoded_uid); err != nil {
			return err
		}

		// Disable the other user's subscription to a disabled p2p topic.
		if _, err = tx.Exec("UPDATE subscriptions AS s_one LEFT JOIN subscriptions AS s_two "+
			"ON s_one.topic=s_two.topic "+
			"SET s_two.updated_at=?, s_two.deleted_at=? WHERE s_one.userid=? AND s_one.topic NOT LIKE 'grp%'",
			now, now, decoded_uid); err != nil {
			return err
		}
		// Disable friendrequest.
		if _, err = tx.Exec("UPDATE friendrequest SET updated_at=?, deleted_at=?, status=? WHERE sender=? OR receiver=?",
			now, now, t.FriendRequestExpired, decoded_uid, decoded_uid); err != nil {
			return err
		}

		public := map[string]interface{}{
			"fn": uid.UserId(),
			"avatarUrl": defaultAvatarUrl,
		}
		// Disable user.
		if _, err = tx.Exec("UPDATE users SET updated_at=?, state=?, stateat=?, password=NULL, public=? WHERE uid=?",
			now, t.StateDeleted, now, toJSON(public), decoded_uid); err != nil {
			return err
		}
	}

	return tx.Commit()
}

// topicStateForUser is called by UserUpdate when the update contains state change.
func (a *adapter) topicStateForUser(tx *sqlx.Tx, decoded_uid int64, now time.Time, update interface{}) error {
	var err error

	state, ok := update.(t.ObjState)
	if !ok {
		return t.ErrMalformed
	}

	if now.IsZero() {
		now = t.TimeNow()
	}

	// Change state of all topics where the user is the owner.
	if _, err = tx.Exec("UPDATE topics SET state=?, stateat=? WHERE owner=? AND state!=?",
		state, now, decoded_uid, t.StateDeleted); err != nil {
		return err
	}

	// Change state of p2p topics with the user (p2p topic's owner is 0)
	if _, err = tx.Exec("UPDATE topics LEFT JOIN subscriptions ON topics.name=subscriptions.topic "+
		"SET topics.state=?, topics.stateat=? WHERE topics.owner=0 AND subscriptions.userid=? AND topics.state!=?",
		state, now, decoded_uid, t.StateDeleted); err != nil {
		return err
	}

	// Subscriptions don't need to be updated:
	// subscriptions of a disabled user are not disabled and still can be manipulated.

	return nil
}

// UserUpdate updates user object.
func (a *adapter) UserUpdate(uid t.Uid, update map[string]interface{}) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	cols, args := updateByMap(update)
	decoded_uid := store.DecodeUid(uid)
	args = append(args, decoded_uid)
	_, err = tx.Exec("UPDATE users SET "+strings.Join(cols, ",")+" WHERE uid=?", args...)
	if err != nil {
		return err
	}

	if state, ok := update["State"]; ok {
		now, _ := update["StateAt"].(time.Time)
		err = a.topicStateForUser(tx, decoded_uid, now, state)
		if err != nil {
			return err
		}
	}

	// Tags are also stored in a separate table
	if tags := extractTags(update); tags != nil {
		// First delete all user tags
		_, err = tx.Exec("DELETE FROM usertags WHERE userid=?", decoded_uid)
		if err != nil {
			return err
		}
		// Now insert new tags
		err = addTags(tx, "usertags", "userid", decoded_uid, tags, false)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// UserUpdateTags adds or resets user's tags
func (a *adapter) UserUpdateTags(uid t.Uid, add, remove, reset []string) ([]string, error) {
	tx, err := a.db.Beginx()
	if err != nil {
		return nil, err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	decoded_uid := store.DecodeUid(uid)

	if reset != nil {
		// Delete all tags first if resetting.
		_, err = tx.Exec("DELETE FROM usertags WHERE userid=?", decoded_uid)
		if err != nil {
			return nil, err
		}
		add = reset
		remove = nil
	}

	// Now insert new tags. Ignore duplicates if resetting.
	err = addTags(tx, "usertags", "userid", decoded_uid, add, reset == nil)
	if err != nil {
		return nil, err
	}

	// Delete tags.
	err = removeTags(tx, "usertags", "userid", decoded_uid, remove)
	if err != nil {
		return nil, err
	}

	var allTags []string
	err = tx.Select(&allTags, "SELECT tag FROM usertags WHERE userid=?", decoded_uid)
	if err != nil {
		return nil, err
	}

	_, err = tx.Exec("UPDATE users SET tags=? WHERE uid=?", t.StringSlice(allTags), decoded_uid)
	if err != nil {
		return nil, err
	}

	return allTags, tx.Commit()
}

// UserGetByCred returns user ID for the given validated credential.
func (a *adapter) UserGetByCred(method, value string) (t.Uid, error) {
	var decoded_uid int64
	err := a.db.Get(&decoded_uid, "SELECT userid FROM credentials WHERE synthetic=?", method+":"+value)
	if err == nil {
		return store.EncodeUid(decoded_uid), nil
	}

	if err == sql.ErrNoRows {
		// Clear the error if user does not exist
		return t.ZeroUid, nil
	}
	return t.ZeroUid, err
}

// UserUnreadCount returns the total number of unread messages in all topics with
// the R permission.
func (a *adapter) UserUnreadCount(uid t.Uid) (int, error) {
	var count int
	err := a.db.Get(&count, "SELECT SUM(t.seqid)-SUM(s.readseqid) FROM topics AS t, subscriptions AS s "+
		"WHERE s.userid=? AND t.name=s.topic AND s.deleted_at IS NULL AND t.state!=? AND "+
		"INSTR(s.modewant, 'R')>0 AND INSTR(s.modegiven, 'R')>0", store.DecodeUid(uid), t.StateDeleted)
	if err == nil {
		return count, nil
	}

	if err == sql.ErrNoRows {
		return 0, nil
	}

	return -1, err
}

// *****************************

func (a *adapter) FriendRequestCreate(tx *sqlx.Tx, friendRequest *t.FriendRequest) error {
	_, err := tx.Exec("INSERT INTO friendrequest(created_at,updated_at,sender, receiver, messages, status) "+
		"VALUES(?,?,?,?,?,?) ON DUPLICATE KEY UPDATE updated_at=?, messages=?, status=?",
		friendRequest.CreatedAt, friendRequest.UpdatedAt, store.DecodeUid(friendRequest.SenderUid),
		store.DecodeUid(friendRequest.ReceiverUid), toJSON(friendRequest.Messages), friendRequest.Status, friendRequest.UpdatedAt,
		toJSON(friendRequest.Messages), friendRequest.Status)
	return err
}

func (a *adapter) CreateFriendRequest(friendRequest *t.FriendRequest) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	if err = a.FriendRequestCreate(tx, friendRequest); err != nil {
		return err
	}
	topic := friendRequest.SenderUid.P2PName(friendRequest.ReceiverUid)
	updateTopic := map[string]interface{}{
		"state": t.StateOK,
		"stateat": t.TimeNow(),
	}
	if err = a.TopicUpdate(topic, updateTopic); err != nil {
		return err
	}
	updateSubs := map[string]interface{}{
		"deleted_at": nil,
		"updated_at": t.TimeNow(),
	}
	if err = a.SubsUpdate(topic, t.ZeroUid, updateSubs); err != nil {
		return err
	}
	return tx.Commit()
}

//ZeroUid
func (a *adapter) FriendRequestsGet(sender, receiver t.Uid, status int) ([]t.FriendRequest, error) {
	var args []interface{}
	if sender.IsZero() && receiver.IsZero() {
		return nil, errors.New("miss user id to query friend requests")
	}

	q := `SELECT id, sender, receiver, messages, created_at, updated_at, status from friendrequest where 1=1`

	if !sender.IsZero() {
		q += " AND sender=?"
		args = append(args, store.DecodeUid(sender))
	}

	if !receiver.IsZero() {
		q += " AND receiver=?"
		args = append(args, store.DecodeUid(receiver))
	}

	if status != 0 {
		q += " AND status=?"
		args = append(args, status)
	}

	if status == t.FriendRequestExpired {
		expiredTime := time.Unix(t.FriendReqExpiredTime, 0)
		timeBefore := time.Now().Before(expiredTime)
		q += "AND created_time<?"
		args = append(args, timeBefore)
	}
	rows, err := a.db.Queryx(q, args...)
	if err != nil {
		return nil, err
	}
	var friendReq t.FriendRequest
	var requests []t.FriendRequest
	var uids []string
	collections := make(map[string]t.FriendRequest) // Keeping these to make a join with table for .private and .access
	for rows.Next() {
		if err = rows.StructScan(&friendReq); err != nil {
			log.Printf("scan friendrequest strucr error =====> ", err.Error())
			break
		}
		uids = append(uids, friendReq.Sender)
		collections[friendReq.Sender] = friendReq
	}
	rows.Close()
	if err == nil && len(uids) > 0 {
		q, uids, _ := sqlx.In(
			"SELECT uid,state,created_at,updated_at,state,stateat,access,lastseen,useragent,public,tags "+
				"FROM users WHERE uid IN (?)",
			uids)
		rows, err = a.db.Queryx(q, uids...)
		if err != nil {
			return nil, err
		}
		var usr t.User
		for rows.Next() {
			if err = rows.StructScan(&usr); err != nil {
				break
			}
			req := collections[usr.Userid]
			req.SenderPublic = fromJSON(usr.Public)

			usrBase64, encodeErr := store.EncodeUid2Base64(req.Sender)
			if encodeErr == nil {
				req.Sender = usrBase64
			}
			requests = append(requests, req)
		}
		rows.Close()
	}

	if status == t.FriendRequestSent {
		//filter request expired
		curTime := time.Now().Unix()
		var expiedReqIDs []string
		var validRequests []t.FriendRequest
		for _, req := range requests {
			dist := curTime - req.CreatedAt.Unix()
			if dist < t.FriendReqExpiredTime {
				validRequests = append(validRequests, req)
			} else {
				expiedReqIDs = append(expiedReqIDs, req.Id)
			}
		}

		if len(expiedReqIDs) > 0 {
			//update friendRequst status
			log.Printf("update friend request status")
			a.updateFriendReq(expiedReqIDs, t.FriendRequestExpired)
		}
		return validRequests, err

	} else {
		return requests, err
	}
}

func (a *adapter) FriendRequestsDelete(sender, receiver t.Uid) error {
	if sender.IsZero() && receiver.IsZero() {
		return errors.New("either sender or receiver uid is required")
	}

	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	q := "DELETE FROM friendrequest WHERE 1=1"
	var args []interface{}
	if !sender.IsZero() {
		q += " AND sender=?"
		args = append(args, store.DecodeUid(sender))
	} else {
		q += " AND receiver=?"
		args = append(args, store.DecodeUid(receiver))
	}
	if _, err = tx.Exec(q, args...); err != nil {
		return err
	}
	return tx.Commit()
}
func (a *adapter) updateFriendReq(reqIDs []string, status int) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()
	now := t.TimeNow()

	q, args, err := sqlx.In("Update friendrequest SET status=?, updated_at=? where id IN (?)", status, now, reqIDs)
	// Optionally skip deleted topics.
	if err != nil {
		return err
	}
	if _, err = tx.Exec(q, args...); err != nil {
		log.Printf("Failed to update friend request by id => %v", err.Error())
		return err
	}
	return tx.Commit()
}

func (a *adapter) FriendReqUpdateStatus(sender, receiver t.Uid, status int) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()
	now := t.TimeNow()
	_, err = tx.Exec("UPDATE friendrequest SET status=?, updated_at=? where sender=? and receiver=?",
		status, now, store.DecodeUid(sender), store.DecodeUid(receiver))
	if err != nil {
		log.Printf("FriendReqUpdateStatus update friendrequest status error => ", err.Error())
		return err
	}

	if status == t.FriendRequestAccepted {
		topicName := sender.P2PName(receiver)
		_, err = tx.Exec("UPDATE topics SET status=?, updated_at=? where name=?",
			t.TopicOk, now, topicName)

	}
	if err != nil {
		log.Printf("FriendReqUpdateStatus update topic status error => ", err.Error())
		return err
	}
	return tx.Commit()
}

func (a *adapter) topicCreate(tx *sqlx.Tx, topic *t.Topic) error {

	if topic.Id == "" {
		return errors.New("invalid parameters")
	}
	tcat := t.GetTopicCat(topic.Id)
	if public, ok := topic.Public.(map[string]interface{}); ok && (tcat == t.TopicCatGrp || tcat == t.TopicCatConf) {
		if _, avatarOk := public["avatarUrl"]; !avatarOk {
			public["avatarUrl"] = defaultGroupChatAvatarUrl
		}
		topic.Public = public
	}
	_, err := tx.Exec("INSERT INTO topics(created_at,updated_at,touchedat,state,name,status,owner,access,public,tags) "+
		"VALUES(?,?,?,?,?,?,?,?,?,?)",
		topic.CreatedAt, topic.UpdatedAt, topic.TouchedAt, topic.State, topic.Id, topic.Status,
		store.DecodeUid(t.ParseUid(topic.Owner)), topic.Access, toJSON(topic.Public), topic.Tags)
	if err != nil {
		log.Printf(err.Error())
		return err
	}
	// Save topic's tags to a separate table to make topic findable.
	return addTags(tx, "topictags", "topic", topic.Id, topic.Tags, false)
}

// TopicCreate saves topic object to database.
func (a *adapter) TopicCreate(topic *t.Topic) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	err = a.topicCreate(tx, topic)
	if err != nil {
		return err
	}
	return tx.Commit()
}

// func (a *adapter) FindConferenceTopicsWithUid(uid t.Uid) ([]string, error) {
// 	query := "SELECT s.topic FROM subscriptions s left join topics t on s.topic = t.name WHERE (LEFT(s.topic, 3) = 'con' OR LEFT(s.topic, 3) ='grp') AND s.uid = ? AND t.status = 2"
// 	var topics []t.Topic
// 	err := a.db.Get(topics, query, store.DecodeUid(uid))
// 	if err != nil {
// 		if err == sql.ErrNoRows {
// 			// Nothing found - clear the error
// 			err = nil
// 		}
// 		return nil, err
// 	}
// 	return topics, err
// }

// If undelete = true - update subscription on duplicate key, otherwise ignore the duplicate.
func createSubscription(tx *sqlx.Tx, sub *t.Subscription, undelete bool) error {

	isOwner := (sub.ModeGiven & sub.ModeWant).IsOwner()

	jpriv := toJSON(sub.Private)
	decoded_uid := store.DecodeUid(t.ParseUid(sub.User))
	_, err := tx.Exec(
		"INSERT INTO subscriptions(created_at,updated_at,deleted_at,userid,topic,modeWant,modeGiven,private) "+
			"VALUES(?,?,NULL,?,?,?,?,?)",
		sub.CreatedAt, sub.UpdatedAt, decoded_uid, sub.Topic, sub.ModeWant.String(), sub.ModeGiven.String(), jpriv)
	if err != nil && isDupe(err) {
		if undelete {
			_, err = tx.Exec("UPDATE subscriptions SET created_at=?,updated_at=?,deleted_at=NULL,modeGiven=? "+
				"WHERE topic=? AND userid=?",
				sub.CreatedAt, sub.UpdatedAt, sub.ModeGiven.String(), sub.Topic, decoded_uid)
		} else {
			_, err = tx.Exec(
				"UPDATE subscriptions SET created_at=?,updated_at=?,deleted_at=NULL,modeWant=?,modeGiven=?,private=? "+
					"WHERE topic=? AND userid=?",
				sub.CreatedAt, sub.UpdatedAt, sub.ModeWant.String(), sub.ModeGiven.String(),
				jpriv, sub.Topic, decoded_uid)
		}
	}
	if err == nil && isOwner {
		_, err = tx.Exec("UPDATE topics SET owner=? WHERE name=?", decoded_uid, sub.Topic)
	}

	return err
}

// TopicCreateP2P given two users creates a p2p topic
func (a *adapter) TopicCreateP2P(initiator, invited *t.Subscription, status t.TopicStatus) error {

	log.Printf("create p2p topic")
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	err = createSubscription(tx, initiator, false)
	if err != nil {
		return err
	}

	err = createSubscription(tx, invited, true)
	if err != nil {
		return err
	}

	topic := &t.Topic{
		ObjHeader: t.ObjHeader{Id: initiator.Topic},
		Status:    status,
	}
	topic.ObjHeader.MergeTimes(&initiator.ObjHeader)
	topic.TouchedAt = initiator.GetTouchedAt()
	err = a.topicCreate(tx, topic)
	if err != nil {
		return err
	}

	return tx.Commit()
}

func (a *adapter) TopicsGetByUid(uid t.Uid, topicType string) ([]t.Topic, error) {

	q := "SELECT created_at, updated_at, name status FROM topics t INNER JOIN subscriptions s on t.name == s.topic where userid=?"

	if topicType == "p2p" {
		q += " AND t.name like 'p2p%'"
	}

	if topicType == "group" {
		q += " AND t.name like 'grp%'"
	}

	var topics []t.Topic
	err := a.db.Get(topics, q, store.DecodeUid(uid))

	if err != nil {
		if err == sql.ErrNoRows {
			// Nothing found - clear the error
			err = nil
		}
		return nil, err
	}

	return topics, err
}

// TopicGet loads a single topic by name, if it exists. If the topic does not exist the call returns (nil, nil)
func (a *adapter) TopicGet(topic string) (*t.Topic, error) {
	// Fetch topic by name
	var tt = new(t.Topic)
	err := a.db.Get(tt,
		"SELECT created_at,updated_at,state,stateat,touchedat,name AS id, status, access,owner,seqid,delid,public,tags "+
			"FROM topics WHERE name=?",
		topic)

	if err != nil {
		if err == sql.ErrNoRows {
			// Nothing found - clear the error
			err = nil
		}
		return nil, err
	}

	tt.Owner = encodeUidString(tt.Owner).String()
	tt.Public = fromJSON(tt.Public)
	return tt, nil
}

// TopicsForUser loads user's contact list: p2p and grp topics, except for 'me' & 'fnd' subscriptions.
// Reads and denormalizes Public value.
func (a *adapter) TopicsForUser(uid t.Uid, keepDeleted bool, opts *t.QueryOpt) ([]t.Subscription, error) {
	// Fetch user's subscriptions
	q := `SELECT created_at,updated_at,deleted_at,topic,delid,recvseqid,
		readseqid,modewant,modegiven,private FROM subscriptions WHERE userid=?`
	args := []interface{}{store.DecodeUid(uid)}
	if !keepDeleted {
		// Filter out deleted rows.
		q += " AND deleted_at IS NULL"
	}

	limit := a.maxResults
	if opts != nil {
		// Ignore IfModifiedSince - we must return all entries
		// Those unmodified will be stripped of Public & Private.

		if opts.Topic != "" {
			q += " AND topic=?"
			args = append(args, opts.Topic)
		}
		if opts.Limit > 0 && opts.Limit < limit {
			limit = opts.Limit
		}

	}

	q += " LIMIT ?"
	args = append(args, limit)

	rows, err := a.db.Queryx(q, args...)
	if err != nil {
		return nil, err
	}

	// Fetch subscriptions. Two queries are needed: users table (me & p2p) and topics table (p2p and grp).
	// Prepare a list of Separate subscriptions to users vs topics
	var sub t.Subscription
	join := make(map[string]t.Subscription) // Keeping these to make a join with table for .private and .access
	topq := make([]interface{}, 0, 16)
	usrq := make([]interface{}, 0, 16)
	for rows.Next() {
		if err = rows.StructScan(&sub); err != nil {
			break
		}

		sub.User = uid.String()
		tcat := t.GetTopicCat(sub.Topic)

		// 'me' or 'fnd' subscription, skip
		if tcat == t.TopicCatMe || tcat == t.TopicCatFnd {
			continue

			// p2p subscription, find the other user to get user.Public
		} else if tcat == t.TopicCatP2P {
			uid1, uid2, _ := t.ParseP2P(sub.Topic)
			if uid1 == uid {
				usrq = append(usrq, store.DecodeUid(uid2))
			} else {
				usrq = append(usrq, store.DecodeUid(uid1))
			}
			topq = append(topq, sub.Topic)

			// grp subscription
		} else {
			topq = append(topq, sub.Topic)
		}

		sub.Private = fromJSON(sub.Private)
		join[sub.Topic] = sub
	}
	rows.Close()
	if err != nil {
		return nil, err
	}

	var subs []t.Subscription
	if len(topq) > 0 || len(usrq) > 0 {
		subs = make([]t.Subscription, 0, len(join))
	}

	if len(topq) > 0 {
		// Fetch grp & p2p topics
		q, topq, _ := sqlx.In(
			"SELECT created_at,updated_at,state,stateat,touchedat,name AS id,status, access,seqid,delid,public,tags "+
				"FROM topics WHERE name IN (?)", topq)
		// Optionally skip deleted topics.
		if !keepDeleted {
			q += " AND state!=?"
			topq = append(topq, t.StateDeleted)
		}
		// if opts != nil && opts.Status != t.TopicNoStatus  {
		// 	q += " AND status=?"
		// 	topq = append(topq, opts.Status)
		// }
		q = a.db.Rebind(q)
		rows, err = a.db.Queryx(q, topq...)
		if err != nil {
			return nil, err
		}

		var top t.Topic
		for rows.Next() {
			if err = rows.StructScan(&top); err != nil {
				break
			}

			sub = join[top.Id]
			sub.ObjHeader.MergeTimes(&top.ObjHeader)
			sub.SetState(top.State)
			sub.SetTouchedAt(top.TouchedAt)
			sub.SetSeqId(top.SeqId)
			if t.GetTopicCat(sub.Topic) == t.TopicCatGrp {
				// all done with a grp topic
				top.Public = fromJSON(top.Public)
				if public, ok := top.Public.(map[string]interface{}); ok {
					if avatarUrl, avatarOk := public["avatarUrl"].(string); avatarOk {
						public["avatarUrl"] = a.avatarDomainName + avatarUrl
						top.Public = public
					}
				}
				sub.SetPublic(top.Public)
				subs = append(subs, sub)
			} else {
				// put back the updated value of a p2p subsription, will process further below
				if opts != nil && opts.Status != t.TopicNoStatus && top.Status != opts.Status {
					//remove subscription whose topic does not accepted by the other user
					delete(join, top.Id)
				} else {
					join[top.Id] = sub
				}
			}
		}
		rows.Close()
	}

	if err != nil {
		log.Printf("TopicsForUser error *********** %v", err)
	}
	// Fetch p2p users and join to p2p tables
	if err == nil && len(usrq) > 0 {
		q, usrq, _ := sqlx.In(
			"SELECT uid,state,created_at,updated_at,state,stateat,access,lastseen,useragent,public,tags "+
				"FROM users WHERE uid IN (?)",
			usrq)
		// Optionally skip deleted users.
		if !keepDeleted {
			q += " AND state!=?"
			usrq = append(usrq, t.StateDeleted)
		}
		rows, err = a.db.Queryx(q, usrq...)
		if err != nil {
			return nil, err
		}

		var usr t.User
		for rows.Next() {
			if err = rows.StructScan(&usr); err != nil {
				break
			}

			uid2 := encodeUidString(usr.Userid)
			if sub, ok := join[uid.P2PName(uid2)]; ok {
				sub.ObjHeader.MergeTimes(&usr.ObjHeader)
				sub.SetState(usr.State)
				publicMap := reformAvatartUrl(usr.Public, a.avatarDomainName)
				sub.SetPublic(publicMap)
				sub.SetWith(uid2.UserId())
				sub.SetDefaultAccess(usr.Access.Auth, usr.Access.Anon)
				sub.SetLastSeenAndUA(usr.LastSeen, usr.UserAgent)
				subs = append(subs, sub)
			}
		}
		rows.Close()
	}
	return subs, err
}

// GetAllP2pTopic retrieve all p2p sub info of the other user given target uids
func (a *adapter) GetAllP2pTopic(asUser t.Uid, uids ...t.Uid) ([]t.Subscription, error) {
	var topics []string
	var uidsInt64 []int64
	var subs []t.Subscription
	for _, uid := range uids {
		p2pTopic := asUser.P2PName(uid)
		if p2pTopic != "" {
			topics = append(topics, p2pTopic)
			uidsInt64 = append(uidsInt64, store.DecodeUid(uid))
		}
	}
	if len(topics) == 0 {
		return nil, errors.New("Invalid uids")
	}

	asUserInt64 := store.DecodeUid(asUser)
	arg := map[string]interface{}{
		"topics": topics,
		"asUser": asUserInt64,
	}
	q, args, err := sqlx.Named(`SELECT sub.userid as user, sub.topic FROM topics t INNER JOIN subscriptions sub on t.name = sub.topic
		WHERE t.name IN (:topics) AND t.status = 2 AND sub.userid != :asUser`, arg)
	if err != nil {
		return nil, err
	}
	q, args, err = sqlx.In(q, args...)
	q = a.db.Rebind(q)
	rows, err := a.db.Queryx(q, args...)
	if err != nil {
		log.Printf("MySQL GetAllP2pTopic failed to get Subscription info")
		if err == sql.ErrNoRows {
			return subs, nil
		}
		return nil, err
	}

	var sub t.Subscription
	uid2Topic := make(map[string]t.Subscription)
	for rows.Next() {
		if err = rows.StructScan(&sub); err != nil {
			break
		}
		uid2Topic[sub.User] = sub
	}
	rows.Close()
	// add user info
	q, params, _ := sqlx.In(
		"SELECT uid,state,created_at,updated_at,state,stateat,access,lastseen,useragent,public,tags "+
			"FROM users WHERE uid IN (?)",
		uidsInt64)
	rows, err = a.db.Queryx(q, params...)
	if err != nil {
		log.Printf("MySQL GetAllP2pTopic failed to get User info")
		if err == sql.ErrNoRows {
			return subs, nil
		}
		return nil, err
	}
	var usr t.User
	for rows.Next() {
		if err = rows.StructScan(&usr); err != nil {
			break
		}
		sub := uid2Topic[usr.Userid]
		sub.SetState(usr.State)
		sub.SetDefaultAccess(usr.Access.Auth, usr.Access.Anon)
		sub.SetLastSeenAndUA(usr.LastSeen, usr.UserAgent)
		subs = append(subs, sub)
	}
	rows.Close()
	return subs, nil
}

// UsersForTopic loads users subscribed to the given topic.
// The difference between UsersForTopic vs SubsForTopic is that the former loads user.public,
// the latter does not.
func (a *adapter) UsersForTopic(topic string, keepDeleted bool, opts *t.QueryOpt) ([]t.Subscription, error) {
	tcat := t.GetTopicCat(topic)

	// Fetch all subscribed users. The number of users is not large
	q := `SELECT s.created_at,s.updated_at,s.deleted_at,s.userid,s.topic,s.delid,s.recvseqid,
		s.readseqid,s.modewant,s.modegiven,u.public,s.private
		FROM subscriptions AS s JOIN users AS u ON s.userid=u.uid
		WHERE s.topic=?`
	args := []interface{}{topic}
	if !keepDeleted {
		// Filter out rows with users deleted
		q += " AND u.state!=?"
		args = append(args, t.StateDeleted)

		// For p2p topics we must load all subscriptions including deleted.
		// Otherwise it will be impossible to swipe Public values.
		if tcat != t.TopicCatP2P {
			// Filter out deleted subscriptions.
			q += " AND s.deleted_at IS NULL"
		}
	}
	limit := a.maxResults
	var oneUser t.Uid
	if opts != nil {
		// Ignore IfModifiedSince - we must return all entries
		// Those unmodified will be stripped of Public & Private.

		if !opts.User.IsZero() {
			// For p2p topics we have to fetch both users otherwise public cannot be swapped.
			if tcat != t.TopicCatP2P {
				q += " AND s.userid=?"
				args = append(args, store.DecodeUid(opts.User))
			}
			oneUser = opts.User
		}
		if opts.Limit > 0 && opts.Limit < limit {
			limit = opts.Limit
		}
	}
	q += " LIMIT ?"
	args = append(args, limit)

	rows, err := a.db.Queryx(q, args...)
	if err != nil {
		return nil, err
	}

	// Fetch subscriptions
	var sub t.Subscription
	var subs []t.Subscription
	var public interface{}
	for rows.Next() {
		if err = rows.Scan(
			&sub.CreatedAt, &sub.UpdatedAt, &sub.DeletedAt,
			&sub.User, &sub.Topic, &sub.DelId, &sub.RecvSeqId,
			&sub.ReadSeqId, &sub.ModeWant, &sub.ModeGiven,
			&public, &sub.Private); err != nil {
			break
		}

		sub.User = encodeUidString(sub.User).String()
		sub.Private = fromJSON(sub.Private)
		publicMap := reformAvatartUrl(public, a.avatarDomainName)
		sub.SetPublic(publicMap)
		subs = append(subs, sub)
	}
	rows.Close()

	if err == nil && tcat == t.TopicCatP2P && len(subs) > 0 {
		// Swap public values of P2P topics as expected.
		if len(subs) == 1 {
			// The other user is deleted, nothing we can do.
			subs[0].SetPublic(nil)
		} else {
			pub := subs[0].GetPublic()
			subs[0].SetPublic(subs[1].GetPublic())
			subs[1].SetPublic(pub)
		}

		// Remove deleted and unneeded subscriptions
		if !keepDeleted || !oneUser.IsZero() {
			var xsubs []t.Subscription
			for i := range subs {
				if (subs[i].DeletedAt != nil && !keepDeleted) || (!oneUser.IsZero() && subs[i].Uid() != oneUser) {
					continue
				}
				xsubs = append(xsubs, subs[i])
			}
			subs = xsubs
		}
	}

	return subs, err
}

// OwnTopics loads a slice of topic names where the user is the owner.
func (a *adapter) OwnTopics(uid t.Uid) ([]string, error) {
	rows, err := a.db.Queryx("SELECT name FROM topics WHERE owner=?", store.DecodeUid(uid))
	if err != nil {
		return nil, err
	}

	var names []string
	var name string
	for rows.Next() {
		if err = rows.Scan(&name); err != nil {
			break
		}
		names = append(names, name)
	}
	rows.Close()

	return names, err
}

func (a *adapter) TopicShare(shares []*t.Subscription) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	for _, sub := range shares {
		err = createSubscription(tx, sub, true)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// TopicDelete deletes specified topic.
func (a *adapter) TopicDelete(topic string, hard bool) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	if hard {
		if _, err = tx.Exec("DELETE FROM subscriptions WHERE topic=?", topic); err != nil {
			return err
		}

		if err = messageDeleteList(tx, topic, nil); err != nil {
			return err
		}

		if _, err = tx.Exec("DELETE FROM topictags WHERE topic=?", topic); err != nil {
			return err
		}

		if _, err = tx.Exec("DELETE FROM topics WHERE name=?", topic); err != nil {
			return err
		}
	} else {
		now := t.TimeNow()
		if _, err = tx.Exec("UPDATE subscriptions SET updated_at=?,deleted_at=? WHERE topic=?",
			now, now, topic); err != nil {
			return err
		}

		tcat := t.GetTopicCat(topic)
		status := t.TopicOk
		if tcat == t.TopicCatConf {
			status = t.TopicInvalid
		}
		if _, err = tx.Exec("UPDATE topics SET updated_at=?,state=?,stateat=?,status=? WHERE name=?",
			now, t.StateDeleted, now, status, topic); err != nil {
			return err
		}
	}
	return tx.Commit()
}

func (a *adapter) TopicUpdateOnMessage(topic string, msg *t.Message) error {
	_, err := a.db.Exec("UPDATE topics SET seqid=?,touchedat=? WHERE name=?", msg.SeqId, msg.CreatedAt, topic)

	return err
}

func (a *adapter) TopicUpdate(topic string, update map[string]interface{}) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	cols, args := updateByMap(update)
	args = append(args, topic)
	_, err = tx.Exec("UPDATE topics SET "+strings.Join(cols, ",")+" WHERE name=?", args...)
	if err != nil {
		return err
	}

	// Tags are also stored in a separate table
	if tags := extractTags(update); tags != nil {
		// First delete all user tags
		_, err = tx.Exec("DELETE FROM topictags WHERE topic=?", topic)
		if err != nil {
			return err
		}
		// Now insert new tags
		err = addTags(tx, "topictags", "topic", topic, tags, false)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

func (a *adapter) TopicOwnerChange(topic string, newOwner t.Uid) error {
	_, err := a.db.Exec("UPDATE topics SET owner=? WHERE name=?", store.DecodeUid(newOwner), topic)
	return err
}

// Get a subscription of a user to a topic
func (a *adapter) SubscriptionGet(topic string, user t.Uid) (*t.Subscription, error) {
	var sub t.Subscription
	err := a.db.Get(&sub, `SELECT created_at,updated_at,deleted_at,userid AS user,topic,delid,recvseqid,
		readseqid,modewant,modegiven,private FROM subscriptions WHERE topic=? AND userid=?`,
		topic, store.DecodeUid(user))

	if err != nil {
		if err == sql.ErrNoRows {
			// Nothing found - clear the error
			err = nil
		}
		return nil, err
	}

	if sub.DeletedAt != nil {
		return nil, nil
	}

	sub.Private = fromJSON(sub.Private)

	return &sub, nil
}

/*
// Update time when the user was last attached to the topic.
// TODO: remove, use SubsUpdate().
func (a *adapter) SubsLastSeen(topic string, user t.Uid, lastSeen map[string]time.Time) error {
	_, err := a.db.Exec("UPDATE subscriptions SET lastseen=?,useragent=? WHERE topic=? AND userid=?",
		lastSeen["LastSeen"], lastSeen["UserAgent"], topic, store.DecodeUid(user))

	return err
}
*/

// SubsForUser loads a list of user's subscriptions to topics. Does NOT load Public value.
// TODO: this is used only for presence notifications, no need to load Private either.
func (a *adapter) SubsForUser(forUser t.Uid, keepDeleted bool, opts *t.QueryOpt) ([]t.Subscription, error) {
	q := `SELECT created_at,updated_at,deleted_at,userid AS user,topic,delid,recvseqid,
		readseqid,modewant,modegiven,private FROM subscriptions WHERE userid=?`

	args := []interface{}{store.DecodeUid(forUser)}
	if !keepDeleted {
		// Filter out deleted rows.
		q += " AND deleted_at IS NULL"
	}

	limit := a.maxResults // maxResults here, not maxSubscribers
	if opts != nil {
		// Ignore IfModifiedSince - we must return all entries
		// Those unmodified will be stripped of Public & Private.

		if opts.Topic != "" {
			q += " AND topic=?"
			args = append(args, opts.Topic)
		}
		if opts.Limit > 0 && opts.Limit < limit {
			limit = opts.Limit
		}
	}
	q += " LIMIT ?"
	args = append(args, limit)

	rows, err := a.db.Queryx(q, args...)
	if err != nil {
		return nil, err
	}

	var subs []t.Subscription
	var ss t.Subscription
	for rows.Next() {
		if err = rows.StructScan(&ss); err != nil {
			break
		}
		ss.User = forUser.String()
		ss.Private = fromJSON(ss.Private)
		subs = append(subs, ss)
	}
	rows.Close()

	return subs, err
}

// SubsForTopic fetches all subsciptions for a topic. Does NOT load Public value.
// The difference between UsersForTopic vs SubsForTopic is that the former loads user.public,
// the latter does not.
func (a *adapter) SubsForTopic(topic string, keepDeleted bool, opts *t.QueryOpt) ([]t.Subscription, error) {
	q := `SELECT created_at,updated_at,deleted_at,userid AS user,topic,delid,recvseqid,
		readseqid,modewant,modegiven,private FROM subscriptions WHERE topic=?`

	args := []interface{}{topic}
	if !keepDeleted {
		// Filter out deleted rows.
		q += " AND deleted_at IS NULL"
	}
	limit := a.maxResults
	if opts != nil {
		// Ignore IfModifiedSince - we must return all entries
		// Those unmodified will be stripped of Public & Private.

		if !opts.User.IsZero() {
			q += " AND userid=?"
			args = append(args, store.DecodeUid(opts.User))
		}
		if opts.Limit > 0 && opts.Limit < limit {
			limit = opts.Limit
		}
	}

	q += " LIMIT ?"
	args = append(args, limit)

	rows, err := a.db.Queryx(q, args...)
	if err != nil {
		return nil, err
	}

	var subs []t.Subscription
	var ss t.Subscription
	for rows.Next() {
		if err = rows.StructScan(&ss); err != nil {
			break
		}

		ss.User = encodeUidString(ss.User).String()
		ss.Private = fromJSON(ss.Private)
		subs = append(subs, ss)
	}
	rows.Close()

	return subs, err
}

// SubsUpdate updates one or multiple subscriptions to a topic.
func (a *adapter) SubsUpdate(topic string, user t.Uid, update map[string]interface{}) error {
	tx, err := a.db.Begin()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	if _, ok := update["Modegiven"]; ok {
		log.Printf("update subscription ***************")
		log.Printf("%+v\n", update)
	}

	cols, args := updateByMap(update)
	q := "UPDATE subscriptions SET " + strings.Join(cols, ",") + " WHERE topic=?"
	args = append(args, topic)
	if !user.IsZero() {
		// Update just one topic subscription
		q += " AND userid=?"
		args = append(args, store.DecodeUid(user))
	}

	if _, err = tx.Exec(q, args...); err != nil {
		return err
	}

	return tx.Commit()
}

// SubsDelete marks subscription as deleted.
func (a *adapter) SubsDelete(topic string, user t.Uid) error {
	now := t.TimeNow()
	res, err := a.db.Exec(
		"UPDATE subscriptions SET updated_at=?,deleted_at=? WHERE topic=? AND userid=? AND deleted_at IS NULL",
		now, now, topic, store.DecodeUid(user))
	if err != nil {
		return err
	}
	affected, err := res.RowsAffected()
	if err == nil && affected == 0 {
		err = t.ErrNotFound
	}
	return err
}

// SubsDelForTopic marks all subscriptions to the given topic as deleted
func (a *adapter) SubsDelForTopic(topic string, hard bool) error {
	var err error
	if hard {
		_, err = a.db.Exec("DELETE FROM subscriptions WHERE topic=?", topic)
	} else {
		now := t.TimeNow()
		_, err = a.db.Exec("UPDATE subscriptions SET updated_at=?,deleted_at=? WHERE topic=? AND deleted_at IS NULL",
			now, t.StateDeleted, now, topic)
	}
	return err
}

func (a *adapter) DeleteSubForTopic(topic string, uid t.Uid) error {
	if topic == "" || uid == t.ZeroUid {
		return errors.New("parameters missed to delete topic")
	}
	tx, err := a.db.Begin()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	if _, err = tx.Exec("DELETE FROM subscriptions WHERE userid=? and topic=?", store.DecodeUid(uid), topic); err != nil {
		return err
	}
	if tcat := t.GetTopicCat(topic); tcat == t.TopicCatP2P {
		if _, err = tx.Exec("UPDATE topics SET status=? where name=?", t.TopicInvalid, topic); err != nil {
			return err
		}
	}
	return tx.Commit()
}

// subsDelForTopic marks user's subscriptions as deleted
func subsDelForUser(tx *sqlx.Tx, user t.Uid, hard bool) error {
	var err error
	if hard {
		_, err = tx.Exec("DELETE FROM subscriptions WHERE userid=?", store.DecodeUid(user))
	} else {
		now := t.TimeNow()
		_, err = tx.Exec("UPDATE subscriptions SET updated_at=?,deleted_at=? WHERE userid=? AND deleted_at IS NULL",
			now, now, store.DecodeUid(user))
	}
	return err
}

// SubsDelForTopic marks user's subscriptions as deleted
func (a *adapter) SubsDelForUser(user t.Uid, hard bool) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	if err = subsDelForUser(tx, user, hard); err != nil {
		return err
	}

	return tx.Commit()

}

// Returns a list of users who match given tags, such as "email:<EMAIL>" or "tel:+18003287448".
// Searching the 'users.Tags' for the given tags using respective index.
func (a *adapter) FindUsers(uid t.Uid, req [][]string, opt []string) ([]t.Subscription, error) {

	index := make(map[string]struct{})
	var args []interface{}
	args = append(args, t.StateOK)
	allReq := t.FlattenDoubleSlice(req)
	for _, tag := range append(allReq, opt...) {
		args = append(args, tag)
		index[tag] = struct{}{}
	}

	query := "SELECT u.uid,u.created_at,u.updated_at,u.access,u.public,u.tags,COUNT(*) AS matches " +
		"FROM users AS u LEFT JOIN usertags AS t ON t.userid=u.uid " +
		"WHERE u.state=? AND t.tag IN (?" + strings.Repeat(",?", len(allReq)+len(opt)-1) + ") " +
		"GROUP BY u.uid,u.created_at,u.updated_at,u.public,u.tags "
	if len(allReq) > 0 {
		query += "HAVING"
		first := true
		for _, reqDisjunction := range req {
			if len(reqDisjunction) > 0 {
				if !first {
					query += " AND"
				} else {
					first = false
				}
				// At least one of the tags must be present.
				query += " COUNT(t.tag IN (?" + strings.Repeat(",?", len(reqDisjunction)-1) + ") OR NULL)>=1 "
				for _, tag := range reqDisjunction {
					args = append(args, tag)
				}
			}
		}
	}
	query += "ORDER BY matches DESC LIMIT ?"

	// Get users matched by tags, sort by number of matches from high to low.
	rows, err := a.db.Queryx(query, append(args, a.maxResults)...)

	if err != nil {
		return nil, err
	}

	var userId int64
	var public interface{}
	var access t.DefaultAccess
	var userTags t.StringSlice
	var ignored int
	var sub t.Subscription
	var subs []t.Subscription
	thisUser := store.DecodeUid(uid)
	uidMapSub := make(map[string]t.Subscription)
	for rows.Next() {
		if err = rows.Scan(&userId, &sub.CreatedAt, &sub.UpdatedAt, &access, &public, &userTags, &ignored); err != nil {
			subs = nil
			break
		}

		if userId == thisUser {
			// Skip the callee
			continue
		}
		subUid := store.EncodeUid(userId)
		sub.User = subUid.String()
		publicMap := reformAvatartUrl(public, a.avatarDomainName)
		sub.SetPublic(publicMap)
		sub.SetDefaultAccess(access.Auth, access.Anon)
		foundTags := make([]string, 0, 1)
		for _, tag := range userTags {
			if _, ok := index[tag]; ok {
				foundTags = append(foundTags, tag)
			}
		}
		sub.Private = foundTags
		usrbase64 := subUid.UserId()
		uidMapSub[usrbase64] = sub
	}
	rows.Close()

	if len(uidMapSub) > 0 {
		// Query users who block this user
		blkinfos, err := a.BlockInfoGet(t.ZeroUid, uid)
		if err != nil {
			return nil, err
		}
		for _, blkinfo := range blkinfos {
			if _, ok := uidMapSub[blkinfo.Initiator]; ok {
				delete(uidMapSub, blkinfo.Initiator)
			}
		}

		for _, sub := range uidMapSub {
			subs = append(subs, sub)
		}

		log.Printf("this user has been blocked by %v users", len(blkinfos))
	}

	return subs, err

}

// Returns a list of topics with matching tags.
// Searching the 'topics.Tags' for the given tags using respective index.
func (a *adapter) FindTopics(req [][]string, opt []string) ([]t.Subscription, error) {
	index := make(map[string]struct{})
	var args []interface{}
	args = append(args, t.StateOK)
	var allReq []string
	for _, el := range req {
		allReq = append(allReq, el...)
	}
	for _, tag := range append(allReq, opt...) {
		args = append(args, tag)
		index[tag] = struct{}{}
	}

	query := "SELECT t.name AS topic,t.created_at,t.updated_at,t.access,t.public,t.tags,COUNT(*) AS matches " +
		"FROM topics AS t LEFT JOIN topictags AS tt ON t.name=tt.topic " +
		"WHERE t.state=? AND tt.tag IN (?" + strings.Repeat(",?", len(allReq)+len(opt)-1) + ") " +
		"GROUP BY t.name,t.created_at,t.updated_at,t.public,t.tags "
	if len(allReq) > 0 {
		query += "HAVING"
		first := true
		for _, reqDisjunction := range req {
			if len(reqDisjunction) > 0 {
				if !first {
					query += " AND"
				} else {
					first = false
				}
				// At least one of the tags must be present.
				query += " COUNT(tt.tag IN (?" + strings.Repeat(",?", len(reqDisjunction)-1) + ") OR NULL)>=1 "
				for _, tag := range reqDisjunction {
					args = append(args, tag)
				}
			}
		}
	}
	query += "ORDER BY matches DESC LIMIT ?"
	rows, err := a.db.Queryx(query, append(args, a.maxResults)...)

	if err != nil {
		return nil, err
	}

	var access t.DefaultAccess
	var public interface{}
	var topicTags t.StringSlice
	var ignored int
	var sub t.Subscription
	var subs []t.Subscription
	for rows.Next() {
		if err = rows.Scan(&sub.Topic, &sub.CreatedAt, &sub.UpdatedAt, &access, &public, &topicTags, &ignored); err != nil {
			subs = nil
			break
		}

		sub.SetPublic(fromJSON(public))
		sub.SetDefaultAccess(access.Auth, access.Anon)
		foundTags := make([]string, 0, 1)
		for _, tag := range topicTags {
			if _, ok := index[tag]; ok {
				foundTags = append(foundTags, tag)
			}
		}
		sub.Private = foundTags
		subs = append(subs, sub)
	}
	rows.Close()

	if err != nil {
		return nil, err
	}
	return subs, nil

}

// Messages
func (a *adapter) MessageSave(msg *t.Message) (interface{}, error) {
	var refcontent interface{}
	if msg.Reference != 0 {
		row := a.db.QueryRow("SELECT content from messages where seqid=? AND topic=?", msg.Reference, msg.Topic)
		err := row.Scan(&refcontent)
		if err != nil && err != sql.ErrNoRows {
			return nil, err
		}
	}
	// store assignes message ID, but we don't use it. Message IDs are not used anywhere.
	// Using a sequential ID provided by the database.
	res, err := a.db.Exec(
		"INSERT INTO messages(created_at,updated_at,seqid,topic,`from`,head,content,reference,refcontent,type) VALUES(?,?,?,?,?,?,?,?,?,?)",
		msg.CreatedAt, msg.UpdatedAt, msg.SeqId, msg.Topic,
		store.DecodeUid(t.ParseUid(msg.From)), msg.Head, toJSON(msg.Content), msg.Reference, refcontent, msg.Type)
	if err == nil {
		id, _ := res.LastInsertId()
		// Replacing ID given by store by ID given by the DB.
		msg.SetUid(t.Uid(id))
	}
	return fromJSON(refcontent), err
}

func (a *adapter) MessageUpdate(topic string, asUser t.Uid, msg t.MsgSetContent) error {
	now := t.TimeNow()
	var q string
	var args []interface{}
	if msg.Content != nil {
		q = "UPDATE messages SET content=?, updated_at=? where head = CAST('null' AS JSON) AND topic=? AND `from`=? AND seqid=?"
		args = append(args, toJSON(msg.Content), now, topic, store.DecodeUid(asUser), msg.Seq)
	} else if msg.CallStatus != "" {
		q = "UPDATE messages SET head=JSON_SET(head, '$.callStatus', ?, '$.duration',?), updated_at=? WHERE topic=? AND seqid=? AND (type=? OR type=?)"
		args = append(args, msg.CallStatus, msg.Duration, now, topic, msg.Seq, t.MessageVideoCall, t.MessageVoiceCall)
	} else {
		q = `INSERT INTO messagereaction (messageseq, topic, emoji,` + "`from`," + `status, created_at,updated_at) VALUES (?,?,?,?,?,?,?)
			ON DUPLICATE KEY UPDATE status = IF(status=1,0,1), updated_at=?`
		args = append(args, msg.Seq, topic, msg.Emoji, store.DecodeUid(asUser), t.EmojiValid, now, now, now)
	}
	res, err := a.db.Exec(q, args...)
	if err != nil {
		return err
	}

	if count, _ := res.RowsAffected(); count == 0 {
		return errors.New("Failed to update message.")
	}
	return nil
}

func (a *adapter) MessageGetAll(topic string, forUser t.Uid, opts *t.QueryOpt) ([]t.Message, error) {
	var limit = a.maxMessageResults
	var lower = 0
	var upper = 1<<31 - 1

	sql := "SELECT m.created_at,m.updated_at,m.deleted_at,m.delid,m.seqid,m.topic,m.`from`,m.head,m.content,m.reference, m.refcontent" +
		" FROM messages AS m LEFT JOIN dellog AS d" +
		" ON d.topic=m.topic AND m.seqid BETWEEN d.low AND d.hi-1 AND d.deletedfor=?" +
		" WHERE m.delid=0 AND m.topic=? AND m.seqid BETWEEN ? AND ? AND d.deletedfor IS NULL"
	var optsParams []interface{}
	if opts != nil {
		if opts.Since > 0 {
			lower = opts.Since
		}
		if opts.Before > 0 {
			// MySQL BETWEEN is inclusive-inclusive, Tinode API requires inclusive-exclusive, thus -1
			upper = opts.Before - 1
		}

		if opts.Limit > 0 && opts.Limit < limit {
			limit = opts.Limit
		}
		if opts.Type != "" {
			mtp := t.GetMessageType(opts.Type)
			optsParams = append(optsParams, mtp)
			sql += " AND m.type=?"
		}

		if opts.MsgAfter != nil {
			timeAfter := opts.MsgAfter.Format("2006-01-02 15:04:05")
			optsParams = append(optsParams, timeAfter)
			sql += " AND m.created_at>?"
		}
	}

	sql += " ORDER BY m.seqid DESC LIMIT ?"
	unum := store.DecodeUid(forUser)
	args := []interface{}{unum, topic, lower, upper}
	if len(optsParams) > 0 {
		args = append(args, optsParams...)
	}
	args = append(args, limit)
	rows, err := a.db.Queryx(sql, args...)

	if err != nil {
		return nil, err
	}

	seq2Msg := make(map[int]*t.Message)
	var msgSeq []int
	for rows.Next() {
		var msg t.Message
		if err = rows.StructScan(&msg); err != nil {
			break
		}
		msg.From = encodeUidString(msg.From).String()
		msg.Content = fromJSON(msg.Content)
		msg.Refcontent = fromJSON(msg.Refcontent)
		seq2Msg[msg.SeqId] = &msg
		msgSeq = append(msgSeq, msg.SeqId)
	}

	rows.Close()
	if len(msgSeq) > 0 {
		//get reply and. reaction
		arg := map[string]interface{}{
			"msgSeqs": msgSeq,
			"topic":   topic,
		}
		q, args, err := sqlx.Named("SELECT messageseq, `from`, emoji from messagereaction WHERE messageseq IN (:msgSeqs) AND topic=:topic AND status=1", arg)
		q, args, err = sqlx.In(q, args...)
		q = a.db.Rebind(q)
		rows, err = a.db.Queryx(q, args...)
		if err != nil {
			return nil, err
		}
		for rows.Next() {
			var msgReply t.MessageReply
			if err = rows.StructScan(&msgReply); err != nil {
				break
			}

			if seq2Msg[msgReply.Replyid].EmojiRxns == nil {
				seq2Msg[msgReply.Replyid].EmojiRxns = make(map[string][]string)
			}
			emojiRxns := seq2Msg[msgReply.Replyid].EmojiRxns
			uidBase64, err := store.EncodeUid2Base64(msgReply.From)
			if err != nil {
				// return nil, err
				break
			}
			emojiRxns[msgReply.Emoji] = append(emojiRxns[msgReply.Emoji], uidBase64)
		}
		rows.Close()
	}
	var msgs []t.Message
	for _, val := range msgSeq {
		msg := *seq2Msg[val]
		msgs = append(msgs, msg)
	}
	return msgs, err
}

var dellog struct {
	Topic      string
	Deletedfor int64
	Delid      int
	Low        int
	Hi         int
}

// Get ranges of deleted messages
func (a *adapter) MessageGetDeleted(topic string, forUser t.Uid, opts *t.QueryOpt) ([]t.DelMessage, error) {
	var limit = a.maxResults
	var lower = 0
	var upper = 1<<31 - 1

	if opts != nil {
		if opts.Since > 0 {
			lower = opts.Since
		}
		if opts.Before > 1 {
			// DelRange is inclusive-exclusive, while BETWEEN is inclusive-inclisive.
			upper = opts.Before - 1
		}

		if opts.Limit > 0 && opts.Limit < limit {
			limit = opts.Limit
		}
	}

	// Fetch log of deletions
	rows, err := a.db.Queryx("SELECT topic,deletedfor,delid,low,hi FROM dellog WHERE topic=? AND delid BETWEEN ? AND ?"+
		" AND (deletedFor=0 OR deletedFor=?)"+
		" ORDER BY delid LIMIT ?", topic, lower, upper, store.DecodeUid(forUser), limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var dmsgs []t.DelMessage
	var dmsg t.DelMessage
	for rows.Next() {
		if err = rows.StructScan(&dellog); err != nil {
			dmsgs = nil
			break
		}

		if dellog.Delid != dmsg.DelId {
			if dmsg.DelId > 0 {
				dmsgs = append(dmsgs, dmsg)
			}
			dmsg.DelId = dellog.Delid
			dmsg.Topic = dellog.Topic
			if dellog.Deletedfor > 0 {
				dmsg.DeletedFor = store.EncodeUid(dellog.Deletedfor).String()
			} else {
				dmsg.DeletedFor = ""
			}
			dmsg.SeqIdRanges = nil
		}
		if dellog.Hi <= dellog.Low+1 {
			dellog.Hi = 0
		}
		dmsg.SeqIdRanges = append(dmsg.SeqIdRanges, t.Range{dellog.Low, dellog.Hi})
	}

	if err == nil {
		if dmsg.DelId > 0 {
			dmsgs = append(dmsgs, dmsg)
		}
	}

	return dmsgs, err
}

func messageDeleteList(tx *sqlx.Tx, topic string, toDel *t.DelMessage) error {
	var err error
	if toDel == nil {
		// Whole topic is being deleted, thus also deleting all messages.
		_, err = tx.Exec("DELETE FROM dellog WHERE topic=?", topic)
		if err == nil {
			_, err = tx.Exec("DELETE FROM messages WHERE topic=?", topic)
		}
		// filemsglinks will be deleted because of ON DELETE CASCADE

	} else {
		// Only some messages are being deleted
		// Start with making log entries
		forUser := decodeUidString(toDel.DeletedFor)
		var insert *sql.Stmt
		if insert, err = tx.Prepare(
			"INSERT INTO dellog(topic,deletedfor,delid,low,hi) VALUES(?,?,?,?,?)"); err != nil {
			return err
		}

		// Counter of deleted messages
		seqCount := 0
		for _, rng := range toDel.SeqIdRanges {
			if rng.Hi == 0 {
				// Dellog must contain valid Low and *Hi*.
				rng.Hi = rng.Low + 1
			}
			seqCount += rng.Hi - rng.Low
			if _, err = insert.Exec(topic, forUser, toDel.DelId, rng.Low, rng.Hi); err != nil {
				break
			}
		}

		if err == nil && toDel.DeletedFor == "" {
			// Hard-deleting messages requires updates to the messages table
			where := "m.topic=? AND "
			args := []interface{}{topic}
			if len(toDel.SeqIdRanges) > 1 || toDel.SeqIdRanges[0].Hi == 0 {
				for _, r := range toDel.SeqIdRanges {
					if r.Hi == 0 {
						args = append(args, r.Low)
					} else {
						for i := r.Low; i < r.Hi; i++ {
							args = append(args, i)
						}
					}
				}

				where += "m.seqid IN (?" + strings.Repeat(",?", seqCount-1) + ")"
			} else {
				// Optimizing for a special case of single range low..hi.
				where += "m.seqid BETWEEN ? AND ?"
				// MySQL's BETWEEN is inclusive-inclusive thus decrement Hi by 1.
				args = append(args, toDel.SeqIdRanges[0].Low, toDel.SeqIdRanges[0].Hi-1)
			}
			where += " AND m.deleted_at IS NULL"

			_, err = tx.Exec("DELETE fml.* FROM filemsglinks AS fml INNER JOIN messages AS m ON m.id=fml.msgid WHERE "+
				where, args...)
			if err != nil {
				return err
			}

			_, err = tx.Exec("UPDATE messages AS m SET m.deleted_at=?,m.delId=?,m.head=NULL,m.content=NULL WHERE "+
				where,
				append([]interface{}{t.TimeNow(), toDel.DelId}, args...)...)
		}
	}

	return err
}

// MessageDeleteList deletes messages in the given topic with seqIds from the list
func (a *adapter) MessageDeleteList(topic string, toDel *t.DelMessage) (err error) {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	if err = messageDeleteList(tx, topic, toDel); err != nil {
		return err
	}

	return tx.Commit()
}

// MessageAttachments connects given message to a list of file record IDs.
func (a *adapter) MessageAttachments(msgId t.Uid, fids []string) error {
	var args []interface{}
	var values []string
	strNow := t.TimeNow().Format("2006-01-02T15:04:05.999")
	// created_at,fileid,msgid
	val := "('" + strNow + "',?," + strconv.FormatInt(int64(msgId), 10) + ")"
	for _, fid := range fids {
		id := t.ParseUid(fid)
		if id.IsZero() {
			return t.ErrMalformed
		}
		values = append(values, val)
		args = append(args, store.DecodeUid(id))
	}
	if len(args) == 0 {
		return t.ErrMalformed
	}

	tx, err := a.db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	_, err = a.db.Exec("INSERT INTO filemsglinks(created_at,fileid,msgid) VALUES "+strings.Join(values, ","), args...)
	if err != nil {
		return err
	}

	_, err = tx.Exec("UPDATE fileuploads SET updated_at='"+strNow+"' WHERE id IN (?"+
		strings.Repeat(",?", len(args)-1)+")", args...)
	if err != nil {
		return err
	}

	return tx.Commit()
}

func (a *adapter) MessageLastGet(forUser t.Uid, topics []string) ([]t.Message, error) {
	if len(topics) == 0 {
		return nil, errors.New("empty topics")
	}
	sqlStr := "SELECT b.seqid, b.topic, b.head, b.content, b.`from`, b.created_at, b.type " +
		`FROM 
		  (
			SELECT m.topic, max(m.seqid) as seqid 
			FROM 
			  messages AS m 
			  LEFT JOIN dellog AS d ON d.topic = m.topic 
			  AND m.seqid BETWEEN d.low AND d.hi - 1 
			  AND d.deletedfor = ? 
			WHERE 
			  m.delid = 0 
			  AND m.topic IN (?) 
			  AND d.deletedfor IS NULL 
			  GROUP BY m.topic
		  ) as a 
		  LEFT JOIN messages b ON a.topic = b.topic 
		  AND a.seqid = b.seqid`

	q, args, err := sqlx.In(sqlStr, store.DecodeUid(forUser), topics)
	rows, err := a.db.Queryx(q, args...)
	var msgs []t.Message
	if err != nil {
		if err == sql.ErrNoRows {
			return msgs, nil
		}
		return nil, err
	}
	for rows.Next() {
		var msg t.Message
		if err = rows.StructScan(&msg); err != nil {
			break
		}
		// if t.GetTopicCat(msg.Topic) == t.TopicCatP2P {
		// 	theOtherUid := t.P2pOtherUser(forUser, msg.Topic)
		// 	if theOtherUid == t.ZeroUid {
		// 		break
		// 	}
		// 	msg.Topic = theOtherUid.PrefixId("usr")
		// }
		msg.From = encodeUidString(msg.From).String()
		msg.Content = fromJSON(msg.Content)
		msg.Refcontent = fromJSON(msg.Refcontent)
		msgs = append(msgs, msg)
	}
	rows.Close()
	return msgs, nil
}

func deviceHasher(deviceID string) string {
	// Generate custom key as [64-bit hash of device id] to ensure predictable
	// length of the key
	hasher := fnv.New64()
	hasher.Write([]byte(deviceID))
	return strconv.FormatUint(uint64(hasher.Sum64()), 16)
}

// Device management for push notifications
func (a *adapter) DeviceUpsert(uid t.Uid, def *t.DeviceDef) error {
	hash := deviceHasher(def.DeviceId)

	tx, err := a.db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Ensure uniqueness of the device ID: delete all records of the device ID
	_, err = tx.Exec("DELETE FROM devices WHERE hash=?", hash)
	if err != nil {
		return err
	}

	// Actually add/update DeviceId for the new user
	_, err = tx.Exec("INSERT INTO devices(userid, hash, deviceId, platform, lastseen, lang) VALUES(?,?,?,?,?,?)",
		store.DecodeUid(uid), hash, def.DeviceId, def.Platform, def.LastSeen, def.Lang)
	if err != nil {
		return err
	}

	return tx.Commit()
}

func (a *adapter) DeviceGetAll(uids ...t.Uid) (map[t.Uid][]t.DeviceDef, int, error) {
	var unums []interface{}
	for _, uid := range uids {
		unums = append(unums, store.DecodeUid(uid))
	}

	q, unums, _ := sqlx.In("SELECT userid,deviceid,platform,lastseen,lang, hash FROM devices WHERE userid IN (?)", unums)
	rows, err := a.db.Queryx(q, unums...)
	if err != nil {
		return nil, 0, err
	}

	var device struct {
		Userid   int64
		Deviceid string
		Platform string
		Lastseen time.Time
		Lang     string
		Hash     string
	}

	result := make(map[t.Uid][]t.DeviceDef)
	count := 0
	for rows.Next() {
		if err = rows.StructScan(&device); err != nil {
			break
		}
		uid := store.EncodeUid(device.Userid)
		udev := result[uid]
		udev = append(udev, t.DeviceDef{
			DeviceId: device.Deviceid,
			Platform: device.Platform,
			LastSeen: device.Lastseen,
			Lang:     device.Lang,
			Hash:     device.Hash,
		})
		result[uid] = udev
		count++
	}
	rows.Close()

	return result, count, err
}

func deviceDelete(tx *sqlx.Tx, uid t.Uid, deviceID string) error {
	var err error
	var res sql.Result
	if deviceID == "" {
		res, err = tx.Exec("DELETE FROM devices WHERE userid=?", store.DecodeUid(uid))
	} else {
		res, err = tx.Exec("DELETE FROM devices WHERE userid=? AND hash=?", store.DecodeUid(uid), deviceHasher(deviceID))
	}

	if err == nil {
		if count, _ := res.RowsAffected(); count == 0 {
			err = t.ErrNotFound
		}
	}

	return err
}

func (a *adapter) DeviceDelete(uid t.Uid, deviceID string) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	err = deviceDelete(tx, uid, deviceID)
	if err != nil {
		return err
	}

	return tx.Commit()
}

// Credential management

// CredUpsert adds or updates a validation record. Returns true if inserted, false if updated.
// 1. if credential is validated:
// 1.1 Hard-delete unconfirmed equivalent record, if exists.
// 1.2 Insert new. Report error if duplicate.
// 2. if credential is not validated:
// 2.1 Check if validated equivalent exist. If so, report an error.
// 2.2 Soft-delete all unvalidated records of the same method.
// 2.3 Undelete existing credential. Return if successful.
// 2.4 Insert new credential record.
func (a *adapter) CredUpsert(cred *t.Credential) (bool, error) {
	var err error

	tx, err := a.db.Beginx()
	if err != nil {
		return false, err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	now := t.TimeNow()
	userId := decodeUidString(cred.User)

	// Enforce uniqueness: if credential is confirmed, "method:value" must be unique.
	// if credential is not yet confirmed, "userid:method:value" is unique.
	synth := cred.Method + ":" + cred.Value

	if !cred.Done {
		// Check if this credential is already validated.
		var done bool
		err = tx.Get(&done, "SELECT done FROM credentials WHERE synthetic=?", synth)
		if err == nil {
			return false, t.ErrDuplicate
		}
		if err != sql.ErrNoRows {
			return false, err
		}
		// We are going to insert new record.
		synth = cred.User + ":" + synth

		// Adding new unvalidated credential. Deactivate all unvalidated records of this user and method.
		_, err = tx.Exec("UPDATE credentials SET deleted_at=? WHERE userid=? AND method=? AND done=false",
			now, userId, cred.Method)
		// Assume that the record exists and try to update it: undelete, update timestamp and response value.
		res, err := tx.Exec("UPDATE credentials SET updated_at=?,deleted_at=NULL,resp=?,done=0 WHERE synthetic=?",
			cred.UpdatedAt, cred.Resp, synth)
		if err != nil {
			return false, err
		}
		// If record was updated, then all is fine.
		if numrows, _ := res.RowsAffected(); numrows > 0 {
			return false, tx.Commit()
		}
	} else {
		// Hard-deleting unconformed record if it exists.
		_, err = tx.Exec("DELETE FROM credentials WHERE synthetic=?", cred.User+":"+synth)
		if err != nil {
			return false, err
		}
	}
	// Add new record.
	_, err = tx.Exec("INSERT INTO credentials(created_at,updated_at,method,value,synthetic,userid,resp,done) "+
		"VALUES(?,?,?,?,?,?,?,?)",
		cred.CreatedAt, cred.UpdatedAt, cred.Method, cred.Value, synth, userId, cred.Resp, cred.Done)
	if err != nil {
		if isDupe(err) {
			return true, t.ErrDuplicate
		}
		return true, err
	}
	return true, tx.Commit()
}

// Create userblockings
func (a *adapter) CreateUserblockings(initiator t.Uid, affected t.Uid) error {

	decodedInitiatorUid := store.DecodeUid(initiator)
	decodedAffectedUid := store.DecodeUid(affected)
	if _, err := a.db.Exec("INSERT INTO userblockings (initiator, effector) VALUE (?,?)",
		decodedInitiatorUid,
		decodedAffectedUid); err != nil {
		return err
	}
	return nil
}

func (a *adapter) BlockInfoGet(initiator t.Uid, effector t.Uid) ([]t.BlockInfo, error) {
	if initiator == t.ZeroUid && effector == t.ZeroUid {
		return nil, errors.New("invalid parameters")
	}
	query := "SELECT id, initiator, effector FROM userblockings where 1=1"
	var args []interface{}
	if initiator != t.ZeroUid {
		query += " AND initiator=?"
		args = append(args, store.DecodeUid(initiator))
	}

	if effector != t.ZeroUid {
		query += " AND effector=?"
		args = append(args, store.DecodeUid(effector))
	}

	rows, err := a.db.Queryx(query, args...)
	var blkinfos []t.BlockInfo
	if err != nil {
		if err == sql.ErrNoRows {
			// Nothing found - clear the error
			return blkinfos, nil
		}
		return nil, err
	}

	for rows.Next() {
		var blkinfo t.BlockInfo
		if err = rows.StructScan(&blkinfo); err != nil {
			break
		}
		blkinfo.Initiator, _ = store.EncodeUid2Base64(blkinfo.Initiator)
		blkinfo.Effector, _ = store.EncodeUid2Base64(blkinfo.Effector)
		blkinfos = append(blkinfos, blkinfo)
	}
	return blkinfos, nil
}

// BlockedListGet return block list of a user
func (a *adapter) BlockedListGet(initiator t.Uid) ([]t.User, error) {
	if initiator == t.ZeroUid {
		return nil, errors.New("invalid parameter")
	}

	query := "SELECT u.uid, u.public, u.access, u.tags FROM userblockings b LEFT JOIN users u ON b.initiator=u.uid WHERE b.initiator=?"

	rows, err := a.db.Queryx(query, store.DecodeUid(initiator))
	var users []t.User
	if err != nil {
		if err == sql.ErrNoRows {
			return users, nil
		}
		return nil, err
	}

	for rows.Next() {
		var user t.User
		if err = rows.StructScan(&user); err != nil {
			break
		}
		user.Public = reformAvatartUrl(user.Public, a.avatarDomainName)
		user.Userid, _ = store.EncodeUid2Base64(user.Userid)
		users = append(users, user)
	}

	return users, nil
}

// Query all the initiators who has been blocked current user
func (a *adapter) BlockInfoInTopicGet(topic string) (map[string]string, error) {
	tcat := t.GetTopicCat(topic)
	if tcat != t.TopicCatGrp && tcat != t.TopicCatConf {
		return nil, errors.New("invalid topic category")
	}
	query := "SELECT inits.initiator, inits.effector FROM (SELECT b.initiator, b.effector FROM userblockings b left join subscriptions s on s.userid = b.initiator where topic =?) inits " +
		"LEFT JOIN subscriptions s ON s.userid = inits.effector"
	rows, err := a.db.Queryx(query, topic)
	if err != nil {
		return nil, err
	}
	result := make(map[string]string)
	for rows.Next() {
		var blkinfo t.BlockInfo
		if err = rows.StructScan(&blkinfo); err != nil {
			break
		}
		initiator, _ := store.EncodeUid2Base64(blkinfo.Initiator)
		effector, _ := store.EncodeUid2Base64(blkinfo.Effector)
		result[initiator] = effector
	}
	return result, nil
}

func (a *adapter) BlockInfoDel(initiator t.Uid, effector t.Uid) error {
	if initiator == t.ZeroUid || effector == t.ZeroUid {
		return errors.New("invalid parameters")
	}
	sqlStr := "DELETE FROM userblockings where 1=1"
	var args []interface{}
	if initiator != t.ZeroUid {
		sqlStr += " AND initiator=?"
		args = append(args, store.DecodeUid(initiator))
	}

	if effector != t.ZeroUid {
		sqlStr += " AND effector=?"
		args = append(args, store.DecodeUid(effector))
	}

	_, err := a.db.Exec(sqlStr, args...)

	if err != nil {
		return err
	}
	return nil
}

func (a *adapter) BlockInfoRowGet(initiator t.Uid, effector t.Uid) (*t.BlockInfo, error) {
	if initiator == t.ZeroUid && effector == t.ZeroUid {
		return nil, errors.New("invalid parameters")
	}
	query := "SELECT id FROM userblockings WHERE initiator=? AND effector=?"
	var blkinfo t.BlockInfo
	row := a.db.QueryRowx(query, store.DecodeUid(initiator), store.DecodeUid(effector))
	err := row.StructScan(&blkinfo)
	if err != nil {
		if err == sql.ErrNoRows {
			// Nothing found - clear the error
			return nil, nil
		}
		return nil, err
	}
	return &blkinfo, nil
}

func (a *adapter) SensitiveInfoCreate(info *t.SensitiveMessage) error {
	if info.Topic == "" || info.From.IsZero() || info.Seqid <= 0 {
		return errors.New("miss key values")
	}

	sql := "INSERT INTO sensitivemessages (type, topic, `from`, seqid, description) VALUES(?,?,?,?,?)"
	_, err := a.db.Exec(sql, info.Type, info.Topic, store.DecodeUid(info.From), info.Seqid, toJSON(info.Description))
	return err
}

// credDel deletes given validation method or all methods of the given user.
// 1. If user is being deleted, hard-delete all records (method == "")
// 2. If one value is being deleted:
// 2.1 Delete it if it's valiated or if there were no attempts at validation
// (otherwise it could be used to circumvent the limit on validation attempts).
// 2.2 In that case mark it as soft-deleted.
func credDel(tx *sqlx.Tx, uid t.Uid, method, value string) error {
	constraints := " WHERE userid=?"
	args := []interface{}{store.DecodeUid(uid)}

	if method != "" {
		constraints += " AND method=?"
		args = append(args, method)

		if value != "" {
			constraints += " AND value=?"
			args = append(args, value)
		}
	}

	var err error
	var res sql.Result
	if method == "" {
		// Case 1
		res, err = tx.Exec("DELETE FROM credentials"+constraints, args...)
		if err == nil {
			if count, _ := res.RowsAffected(); count == 0 {
				err = t.ErrNotFound
			}
		}
		return err
	}

	// Case 2.1
	res, err = tx.Exec("DELETE FROM credentials"+constraints+" AND (done=true OR retries=0)", args...)
	if err != nil {
		return err
	}
	if count, _ := res.RowsAffected(); count > 0 {
		return nil
	}

	// Case 2.2
	args = append([]interface{}{t.TimeNow()}, args...)
	res, err = tx.Exec("UPDATE credentials SET deleted_at=?"+constraints, args...)
	if err == nil {
		if count, _ := res.RowsAffected(); count >= 0 {
			err = t.ErrNotFound
		}
	}

	return err
}

// CredDel deletes either credentials of the given user. If method is blank all
// credentials are removed. If value is blank all credentials of the given the
// method are removed.
func (a *adapter) CredDel(uid t.Uid, method, value string) error {
	tx, err := a.db.Beginx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	err = credDel(tx, uid, method, value)
	if err != nil {
		return err
	}

	return tx.Commit()
}

// CredConfirm marks given credential method as confirmed.
func (a *adapter) CredConfirm(uid t.Uid, method string) error {
	res, err := a.db.Exec(
		"UPDATE credentials SET updated_at=?,done=true,synthetic=CONCAT(method,':',value) "+
			"WHERE userid=? AND method=? AND deleted_at IS NULL AND done=false",
		t.TimeNow(), store.DecodeUid(uid), method)
	if err != nil {
		if isDupe(err) {
			return t.ErrDuplicate
		}
		return err
	}
	if numrows, _ := res.RowsAffected(); numrows < 1 {
		return t.ErrNotFound
	}
	return nil
}

// CredFail increments failure count of the given validation method.
func (a *adapter) CredFail(uid t.Uid, method string) error {
	_, err := a.db.Exec("UPDATE credentials SET updated_at=?,retries=retries+1 WHERE userid=? AND method=? AND done=false",
		t.TimeNow(), store.DecodeUid(uid), method)
	return err
}

// CredGetActive returns currently active unvalidated credential of the given user and method.
func (a *adapter) CredGetActive(uid t.Uid, method string) (*t.Credential, error) {
	var cred t.Credential
	err := a.db.Get(&cred, "SELECT created_at,updated_at,method,value,resp,done,retries "+
		"FROM credentials WHERE userid=? AND deleted_at IS NULL AND method=? AND done=false",
		store.DecodeUid(uid), method)
	if err != nil {
		if err == sql.ErrNoRows {
			err = nil
		}
		return nil, err
	}
	cred.User = uid.String()

	return &cred, nil
}

// CredGetAll returns credential records for the given user and method, all or validated only.
func (a *adapter) CredGetAll(uid t.Uid, method string, validatedOnly bool) ([]t.Credential, error) {
	query := "SELECT created_at,updated_at,method,value,resp,done,retries FROM credentials WHERE userid=? AND deleted_at IS NULL"
	args := []interface{}{store.DecodeUid(uid)}
	if method != "" {
		query += " AND method=?"
		args = append(args, method)
	}
	if validatedOnly {
		query += " AND done=true"
	}

	var credentials []t.Credential
	err := a.db.Select(&credentials, query, args...)
	if err != nil {
		return nil, err
	}

	user := uid.String()
	for i := range credentials {
		credentials[i].User = user
	}

	return credentials, err
}

// FileUploads

// FileStartUpload initializes a file upload
func (a *adapter) FileStartUpload(fd *t.FileDef) error {
	_, err := a.db.Exec("INSERT INTO fileuploads(id,created_at,updated_at,userid,status,mimetype,size,location)"+
		" VALUES(?,?,?,?,?,?,?,?)",
		store.DecodeUid(fd.Uid()), fd.CreatedAt, fd.UpdatedAt,
		store.DecodeUid(t.ParseUid(fd.User)), fd.Status, fd.MimeType, fd.Size, fd.Location)
	return err
}

// FileFinishUpload marks file upload as completed, successfully or otherwise
func (a *adapter) FileFinishUpload(fid string, status int, size int64) (*t.FileDef, error) {
	id := t.ParseUid(fid)
	if id.IsZero() {
		return nil, t.ErrMalformed
	}

	fd, err := a.FileGet(fid)
	if err != nil {
		return nil, err
	}
	if fd == nil {
		return nil, t.ErrNotFound
	}

	fd.UpdatedAt = t.TimeNow()
	_, err = a.db.Exec("UPDATE fileuploads SET updated_at=?,status=?,size=? WHERE id=?",
		fd.UpdatedAt, status, size, store.DecodeUid(id))
	if err == nil {
		fd.Status = status
		fd.Size = size
	} else {
		fd = nil
	}
	return fd, err
}

// FileGet fetches a record of a specific file
func (a *adapter) FileGet(fid string) (*t.FileDef, error) {
	id := t.ParseUid(fid)
	if id.IsZero() {
		return nil, t.ErrMalformed
	}

	var fd t.FileDef
	err := a.db.Get(&fd, "SELECT id,created_at,updated_at,userid AS user,status,mimetype,size,location "+
		"FROM fileuploads WHERE id=?", store.DecodeUid(id))
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	fd.Id = encodeUidString(fd.Id).String()
	fd.User = encodeUidString(fd.User).String()

	return &fd, nil

}

// FileDeleteUnused deletes file upload records.
func (a *adapter) FileDeleteUnused(olderThan time.Time, limit int) ([]string, error) {
	tx, err := a.db.Begin()
	if err != nil {
		return nil, err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	query := "SELECT fu.id,fu.location FROM fileuploads AS fu LEFT JOIN filemsglinks AS fml ON fml.fileid=fu.id WHERE fml.id IS NULL AND fu.type=? "
	args := []interface{}{t.FileTemperal}
	if !olderThan.IsZero() {
		query += "AND fu.updated_at<? "
		args = append(args, olderThan)
	}
	if limit > 0 {
		query += "LIMIT ?"
		args = append(args, limit)
	}
	rows, err := tx.Query(query, args...)
	if err != nil {
		return nil, err
	}

	var locations []string
	var ids []interface{}
	for rows.Next() {
		var id int
		var loc string
		if err = rows.Scan(&id, &loc); err != nil {
			break
		}
		locations = append(locations, loc)
		ids = append(ids, id)
	}
	rows.Close()

	if err != nil {
		return nil, err
	}

	if len(ids) > 0 {
		query, ids, _ = sqlx.In("DELETE FROM fileuploads WHERE id IN (?)", ids)
		_, err = tx.Exec(query, ids...)
		if err != nil {
			return nil, err
		}
	}

	return locations, tx.Commit()
}

// Helper functions

// Check if MySQL error is a Error Code: 1062. Duplicate entry ... for key ...
func isDupe(err error) bool {
	if err == nil {
		return false
	}

	myerr, ok := err.(*ms.MySQLError)
	return ok && myerr.Number == 1062
}

func isMissingTable(err error) bool {
	if err == nil {
		return false
	}

	myerr, ok := err.(*ms.MySQLError)
	return ok && myerr.Number == 1146
}

func isMissingDb(err error) bool {
	if err == nil {
		return false
	}

	myerr, ok := err.(*ms.MySQLError)
	return ok && myerr.Number == 1049
}

// Convert to JSON before storing to JSON field.
func toJSON(src interface{}) []byte {
	if src == nil {
		return nil
	}

	jval, _ := json.Marshal(src)
	return jval
}

// Deserialize JSON data from DB.
func fromJSON(src interface{}) interface{} {
	if src == nil {
		return nil
	}
	if bb, ok := src.([]byte); ok {
		var out interface{}
		json.Unmarshal(bb, &out)
		return out
	}
	return nil
}

func reformAvatartUrl(src interface{}, avatarDomainName string) map[string]interface{} {
	public := fromJSON(src).(map[string]interface{})
	if avatarUrl, ok := public["avatarUrl"].(string); ok {
		if !strings.HasPrefix(avatarUrl, "http") {
			public["avatarUrl"] = avatarDomainName + avatarUrl
		}
	} else {
		public["avatarUrl"] = avatarDomainName + defaultAvatarUrl
	}
	return public
}

// UIDs are stored as decoded int64 values. Take decoded string representation of int64, produce UID.
func encodeUidString(str string) t.Uid {
	unum, _ := strconv.ParseInt(str, 10, 64)
	return store.EncodeUid(unum)
}

func decodeUidString(str string) int64 {
	uid := t.ParseUid(str)
	return store.DecodeUid(uid)
}

// Convert update to a list of columns and arguments.
func updateByMap(update map[string]interface{}) (cols []string, args []interface{}) {
	for col, arg := range update {
		col = strings.ToLower(col)
		if col == "public" || col == "private" {
			arg = toJSON(arg)
		}
		cols = append(cols, col+"=?")
		args = append(args, arg)
	}
	return
}

// If Tags field is updated, get the tags so tags table cab be updated too.
func extractTags(update map[string]interface{}) []string {
	var tags []string

	val := update["Tags"]
	if val != nil {
		tags, _ = val.(t.StringSlice)
	}

	return []string(tags)
}

func init() {
	store.RegisterAdapter(&adapter{})
}
