// The JSON comments are somewhat brittle. Don't try anything too fancy.
{
	// HTTP(S) address to listen on for websocket and long polling clients. Either a TCP host:port pair
	// or a path to Unix socket as "unix:/path/to/socket.sock".
	// The TCP port is numeric value or a canonical name, e.g. ":80" or ":https". May include the host name,
	///e.g. "localhost:80" or "hostname.example.com:https".
	// It could be blank: if TLS is not configured it will default to ":80", otherwise to ":443".
	// Can be overridden from the command line, see option --listen.
	"listen": ":6060",

	"enable_web": true,

	// secret to decrypt user private info
	"content_secret": "this-is-test-codo-not-for-remote",

	// Base URL path for serving streaming and large file API calls.
	// Can be overridden from the command line, see option --api_path.
	"api_path": "/",

	// Cach-Control header for static content. 11 hours.
	"cache_control": 39600,

	// URL path for mounting the directory with static files.
	"static_mount": "/",

	// TCP host:port or unix:/path/to/socket to listen for gRPC clients.
	// Leave blank to disable gRPC support.
	// Could be overridden from the command line with --grpc_listen.
	"grpc_listen": ":16060",

	// Enable handling of gRPC keepalives https://github.com/grpc/grpc/blob/master/doc/keepalive.md
	// This sets server's GRPC_ARG_KEEPALIVE_TIME_MS to 60 seconds instead of the default 2 hours.
	"grpc_keepalive_enabled": true,

	// Salt for signing API key. 32 random bytes base64-encoded. Use 'keygen' to generate
	// the API key and the salt.
	"api_key_salt": "T713/rYYgW7g4m3vG6zGRh7+FM1t0T8j13koXScOAj4=",

	// Maximum message size allowed from client in bytes (262144 = 256KB).
	// Intended to prevent malicious clients from sending very large messages inband (does
	// not affect out-of-band large files).
	"max_message_size": 262144,

	// Maximum number of subscribers per group topic.
	"max_subscriber_count": 128,

	// Maximum number of indexable tags per topic or user.
	"max_tag_count": 16,

	// URL path for exposing runtime stats. Disabled if the path is blank or "-".
	// Could be overriden from the command line with --expvar.
	"expvar": "/debug/vars",

	// Take IP address of the client from HTTP header 'X-Forwarded-For'.
	// Useful when tinode is behind a proxy. If missing, fallback to default RemoteAddr.
	"use_x_forwarded_for": true,

	// 2-letter country code to assign to sessions by default when the country isn't specified
	// by the client explicitly and it's impossible to infer it.
	// If missing, the server will default to "US".
	"default_country_code": "CN",

	// Large media/blob handlers.
	"media": {
		// Media handler to use
		"use_handler": "oss",
		// Maximum size of uploaded file (8MB here for testing, maybe increase to 100MB = 104857600 in prod)
		"max_size": 8388608,
		// Garbage collection periodicity in seconds
		"gc_period": 60,
		// Number of unused entries to delete in one pass
		"gc_block_size": 100,
		// Censor content of uploaded file
		"enable_censor": true,
		// Configurations for various handlers.
		"handlers": {
			// File system storage.
			"fs": {
				// File system location to store uploaded files. In case of a cluster it
				// must be accessible by all cluster members, i.e. a network drive.
				"upload_dir": "uploads"
			},
			// Amazon AWS S3 storage.
			"s3":{
				// Use AWS console to get Access Key ID and Secret Access Key.
				// https://aws.amazon.com/blogs/security/wheres-my-secret-access-key/
				"access_key_id": "your_s3_access_key_id",
				"secret_access_key": "your_s3_secret_access_key",
				// Region where the bucket is hosted.
				"region": "s3 region, like us-east-2",
				// Name of the S3 bucket.
				"bucket": "your_s3_bucket_name",
				// Origin URLs allowed to download files. See
				// https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Origin
				"cors_origins": ["*"]
			},
			// Aliyun oss storage
			"oss": {
				// system mode: realise or debug
				"mode": "debug",
                // if bucket is private, set 'sts_enable' to true(make system to generate temperory token which can make
                // client access bucket resource
                "sts_enable": false,
                "access_key_id": "",
                "secret_access_key": "",
                // bucket region
                "region": "",
                // endpoint to access cloud resource
                "endpoint": "",
                // bucket name
                "bucket": "",
                // use IAM account(limit access permission) to access bucket
                "account_id": "",
                "role_name": "",
            }
		}
	},

	// TLS (httpS) configuration. Applies to both web and gRPC interfaces.
	"tls": {
		// Enable TLS.
		"enabled": false,

		// Listen for connections on this port and redirect them to HTTPS port.
		// Cannot be a Unix socket.
		"http_redirect": ":80",

		// Add Strict-Transport-Security to headers, the value signifies age.
		// Zero or negative value turns it off.
		"strict_max_age": 604800,

		// Letsencrypt configuration
		"autocert": {
			// Location of certificates.
			"cache": "/etc/letsencrypt/live/your.domain.here",

			// Contact address for this installation. LetsEncrypt will send
			// messages to this address in case of problems. Replace with your
			// own address or remove this line.
			"email": "*******",

			// Domains served. Replace with your own domain name.
			"domains": ["whatever.example.com"]
		},

		// If "autocert" config is not defined, read static certificates from
		// these locations. Ignored if "autocert" is defined.
		"cert_file": "/etc/httpd/conf/your.domain.crt",
		"key_file": "/etc/httpd/conf/your.domain.key"
	},

	"censor": {
		"enable": true,
		"provider": "baidu",
		"access_token_url": "https://aip.baidubce.com/oauth/2.0/token",
		"censor_url": "https://aip.baidubce.com/rest/2.0/solution/v1",
		"api_key": "mGhVrHZ3ZHDwQh2uIWHeRDUN",
		"secret_key": "G45V45XRGGlkz6jVchA9uUrrqZy58UNV"
	},
	// Authentication configuration.
	"auth_config": {
		// Optional mapping of externally-visible authenticator names to internal names.
		// For example use ["login-password:basic", "basic:"] to rename "basic" authenticator to
		// "login-password" and make it unaccessible by the old name.
		// Default is identity mapping.
		"logical_names": [],

		// Basic (login + password) authentication.
		"basic": {
			// Add 'auth-name:username' to tags making user discoverable by username.
			"add_to_tags": true,
			// The minimum length of a login in unicode runes, i.e. "登录" is length 2, not 6.
			// The maximum length is 32 and it cannot be changed.
			"min_login_length": 4,
			// The minimum length of a password in unicode runes, "пароль" is length 6, not 12.
			// There is no maximum length.
			"min_password_length": 6
		},

		// Token authentication
		"token": {
			// Lifetime of a security token in seconds. 1209600 = 2 weeks.
			"expire_in": 1209600,

			// Serial number of the token. Can be used to invalidate all issued tokens at once.
			"serial_num": 1,

			// Secret key (HMAC salt) for signing the tokens. Generate your own then keep it secret.
			// Any 32 random bytes base64 encoded.
			//
			// === IMPORTANT ===
			//
			// CHANGE IT IN PRODUCTION!!! Otherwise anyone will be able to log in
			// to your server without the password. It's just random bytes, use any suitable
			// means to get it.
			"key": "wfaY2RgF2S1OQI/ZlK+LSrp1KB2jwAdGAIHQ7JZn+Kc="
		}
	},

	// Database configuration
	"store_config": {
		// XTEA encryption key for user IDs and topic names. 16 random bytes base64-encoded.
		// Generate your own and keep it secret. Otherwise your user IDs will be predictable
		// and it will be easy to spam your users.
		"uid_key": "",
		// Init database when perform K8s deployment
		"init_db": false,
		// Maximum number of results fetched in one DB call.
		"max_results": 1024,

		// DB adapter name to communicate with the DB backend.
		// Must be one of the adapters from the list below.
		"use_adapter": "mysql",
		// domain name get user system
		"domain_name": "http://192.168.1.215:6060",

		"user_hard_deletion": false,
		// Configurations of individual adapters.
		"adapters": {
			// MySQL configuration. See https://godoc.org/github.com/go-sql-driver/mysql#Config
			// for other possible options.
			"mysql": {
				// DSN, passed unchanged to MySQL driver. The 'parseTime=true' is required.
				// The 'collation=utf8mb4_unicode_ci' is optional but highly recommended for
				// emoji and certain CJK characters.
				// See https://github.com/go-sql-driver/mysql#dsn-data-source-name for syntax.
                "dsn": "tinode:Hello123!@(localhost)/tinode?parseTime=true&collation=utf8mb4_unicode_ci",
                // "dsn": "root@tcp(:3306)/fangyu?parseTime=true&collation=utf8mb4_unicode_ci&loc=Local",
				// Name of the main database.
				"database": "tinode",
			},

			// RethinkDB configuration. See
			// https://godoc.org/github.com/rethinkdb/rethinkdb-go#ConnectOpts for other possible
			// options.
			"rethinkdb": {
				// Address(es) of RethinkDB node(s): either a string or an array of strings.
				"addresses": "localhost:28015",
				// Name of the main database.
				"database": "tinode"
			},

			// MongoDB configuration.
			"mongodb": {
				// Address(es) of MongoDB node(s): either a string or an array of strings.
				"addresses": "localhost:27017",
				// Name of the main database.
				"database": "tinode",
				// Name of replica set of mongodb instance. Remove this line to use a standalone instance.
				// If replica_set is disabled, transactions will be disabled as well.
				"replica_set": "rs0",

				// Authentication options. Uncomment if auth is configured on your MongoDB.
				// "auth_source" is name of database that has the collection with the user credentials.
				// Default is 'admin' if not set:
				// "auth_source": "admin",

				// Username:
				// "username": "tinode",

				// Password:
				// "password": "tinode",

				// Driver's TLS configuration. Uncomment to enable TLS.
				//"tls": true,

				// Path to the client certificate. Optional.
				//"tls_cert_file": "/path/to/cert_file",

				// Path to private key. Optional.
				//"tls_private_key": "/path/to/private_key",

				// Specifies whether or not certificates and hostnames received from the server should be validated.
				// Not recommended to enable in production. Default is false.
				//"tls_skip_verify": false
			}
		}
	},

	// Account validators (email or SMS or captcha)
	"acc_validation": {

		// Email validator config.
		"email": {
			// Restrict use of "email" namespace.
			"add_to_tags": true,

			// List of authentication levels which require this validation method.
			// Remove this line to disable email validation.
			"required": ["auth"],

			// Configuration passed to the validator unchanged.
			"config": {
				// Address of the host where the Tinode server is running. This will be used
				// in URLs in the email.
				"host_url": "http://localhost:6060/",

				// Address of the SMPT server to use.
				"smtp_server": "smtp.example.com",

				// SMTP port to use. "25" for basic email RFC 5321 (2821, 821), "587" for RFC 3207 (TLS).
				"smtp_port": "25",

				// RFC 5322 email address to show in the From: field and optionally to use for authentication.
				// Only 'addr-spec' of RFC 5322 string will be used for authentication.
				"sender": "\"Tinode\" <*******>",

				// Optional login to use for authentication; if missing, "sender" will be used.
				"login": "*******",

				// Password to use when authenticating the sender.
				"sender_password": "your-password-here",

				// Optional list of human languages to try to load templates for. If you don't care about i18n,
				// leave it blank or remove. The first language in the list is the default language.
				"languages": ["en", "ru"],

				// Message template for credential validation.
				// The file path itself is treated as a template. It's resolved by using the
				// "languages" field above. One template per language.
				// See the template file for the explanation of the expected structure.
				"validation_templ": "../server/templ/email-validation-{{.Language}}.templ",

				// Message template for resetting authentication secret.
				// One template per language. See email-validation-en template for the explanation
				// of the expected structure.
				"reset_secret_templ": "../server/templ/email-password-reset-{{.Language}}.templ",

				// Allow this many confirmation attempts before blocking the credential.
				"max_retries": 4,

				// List of email domains allowed to be used for registration.
				// Missing or empty list means any email domain is accepted.
				"domains": [],

				// Dummy response to accept. Remove the line in production.
				"debug_response": "123456"
			}
		},

		// Dummy placeholder validator for SMS and voice validation. Disabled by default.
		// Use something like twillio.com in production.
		"tel": {
			"add_to_tags": true,
			"required": ["auth"],
			"config": {
				"languages": ["en"],
				"template": "../server/templ/sms-validation.templ",
				"max_retries": 4,
				"debug_response": "123456"
			}
		}
	},

	// Configuration of push notifications.
	"push": [
		{
			// Notificator which writes to STDOUT. Useful for debugging.
			"name":"stdout",
			"config": {
				// Disabled.
				"enabled": false
			}
		},
		{
			// Google FCM notificator.
			"name":"fcm",
			"config": {
				// Disabled. Won't work without the server key anyway. See below.
				"enabled": false,

				// Firebase project ID.
				"project_id": "your-project-id",

				// Service account credentials as json.
				// See instructions how to download the service account credentials file:
				// https://cloud.google.com/iam/docs/creating-managing-service-account-keys
				// Then insert the file contents here. Yes, this is convoluted, but that's Google's fault.
				"credentials": {
					"type": "service_account",
						"project_id": "your-project-id",
						"private_key_id": "some-random-looking-hex-number",
						"private_key": "-----BEGIN PRIVATE KEY----- base64-encoded bits of your private key \n-----END PRIVATE KEY-----\n",
						"client_email": "*******",
						"client_id": "1234567890123456789",
						"auth_uri": "https://accounts.google.com/o/oauth2/auth",
						"token_uri": "https://oauth2.googleapis.com/token",
						"auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
						"client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-abc123%40your-project-id.iam.gserviceaccount.com"
				},

				// An alternative way to provide Firebase service account credentials.
				"credentials_file": "/path/to/service-account-file-with-credentials.json",

				// Time in seconds before notification is discarded (by Google) if undelivered.
				"time_to_live": 3600,

				// Payload of AndroidNotification. If enabled, this will take precedence over data payload.
				"android": {
					// Set to false to push a data-only message.
					"enabled": false,

					// Android drawable resource ID to use as a notification icon.
					"icon": "ic_logo_push",

					// Notification color.
					"color": "#3949AB",

					// Name of intent filter which will catch this notification.
					"click_action": ".MessageActivity",

					// Notification of a new message. You can include custom "icon", "color", "click_action"
					// into this section and it will override the value above.
					"msg": {
						// Literal title string. Not recommended because it's not localized.
						"title": "",

						// Literal message body. Not recommended because it's not localized.
						"body": "",

						// Android string resource ID to use as a notification title. Localized.
						// Takes precedence over "title". "new_message" is "New message" in Tindroid.
						"title_loc_key": "new_message",

						// Android string resource ID to use as a notification body. Localized.
						// Takes precedence over "body".
						"body_loc_key": ""
					},

					// Notification of a new subscription. Same rules as section "msg" above.
					"sub": {
						// Android resource string ID to use as notification title. Localized.
						// "new_chat" is "New chat" in Tindroid.
						"title_loc_key": "new_chat",

						// Android resource string ID to use as notification body. Localized.
						"body_loc_key": ""
					}
				}
			}
		},
		{
			// Tinode Push Gateway.
			"name":"tnpg",
			"config": {
				// Disabled. Configure first then enable.
				"enabled": false,
				// Name of the organization you registered at console.tinode.co.
				"org": "test",
				// Authentication token obtained from console.tinode.co
				"token": "jwt-security-token-obtained-from-console.tinode.co",
			}
		}
	],

	// Cluster-mode configuration.
	"cluster_config": {
		// Name of this node. Can be assigned from the command line.
		// Empty string disables clustering.
		"self": "",

		// List of available nodes.
		"nodes": [
			// Name and TCP address of every node in the cluster. The ports 12001..12003
			// are cluster communication ports. They don't need to be exposed to clients.
			{"name": "one", "addr":"localhost:12001"},
			{"name": "two", "addr":"localhost:12002"},
			{"name": "three", "addr":"localhost:12003"}
		],

		// Failover config.
		"failover": {
			// Failover is enabled.
			"enabled": true,
			// Time in milliseconds between heartbeats.
			"heartbeat": 100,
			// Initiate leader election when the leader is not available for this many heartbeats.
			"vote_after": 8,
			// Consider node failed when it missed this many heartbeats.
			"node_fail_after": 16
		}
	},

	// Configuration of plugins
	"plugins": [
		{
			// Enable or disable this plugin.
			"enabled": false,

			// Name of the plugin, must be unique.
			"name": "python_chat_bot",

			// Timeout in microseconds.
			"timeout": 20000,

			// Events to send to the plugin.
			"filters": {
				// Account creation events.
				"account": "C"
			},

			// Error code to use in case plugin has failed.
			"failure_code": 0,

			// Text of an error message to report in case of plugin failure.
			"failure_text": null,

			// Address of the plugin.
			"service_addr": "tcp://localhost:40051"
		}
	]
}
