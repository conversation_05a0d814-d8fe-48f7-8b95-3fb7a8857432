package oss
import(
	"encoding/json"
	"errors"
	"io"
	"mime"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/aliyun/aliyun-sts-go-sdk/sts"
	"github.com/tinode/chat/server/store"
	"github.com/tinode/chat/server/media"
	"github.com/tinode/chat/server/store/types"
)

const handlerName = "oss"

type aliyunconfig struct {
	Mode            string `json:"mode"`
	StsEnable       bool   `json:"sts_enable"`
	AccessKeyId     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	Region          string `json:"region"`
	Endpoint        string `json:"endpoint"`
	BucketName      string `json:"bucket"`
	AccountID       string `json:"account_id"`
	Rolename        string `json:"role_name"`
}

type aliyunhandler struct {
	stsclient *sts.Client
	bucket    *oss.Bucket
	conf      *aliyunconfig
	location  string
}

func (ah *aliyunhandler) Init(jsconf string) error {
	var err error
	if err = json.Unmarshal([]byte(jsconf), &ah.conf); err != nil {
		return errors.New("failed to parse config: " + err.Error())
	}
	if ah.conf.AccessKeyId == "" {
		return errors.New("missing Access Key ID")
	}
	if ah.conf.SecretAccessKey == "" {
		return errors.New("missing Secret Access Key")
	}
	if ah.conf.Region == "" {
		return errors.New("missing Region")
	}
	if ah.conf.Endpoint == "" {
		return errors.New("missing Endpoint")
	}
	if ah.conf.BucketName == "" {
		return errors.New("missing Bucket")
	}

	ossclient, err := oss.New(ah.conf.Endpoint, ah.conf.AccessKeyId, ah.conf.SecretAccessKey)
	if err != nil {
		return err
	}
	roleArn := fmt.Sprintf("acs:ram::%v:role/%v", ah.conf.AccountID, ah.conf.Rolename)
	ah.stsclient = sts.NewClient(ah.conf.AccessKeyId, ah.conf.SecretAccessKey, roleArn, "fangyu-http-server")

	// // Check if bucket already exists.
	// isExist, err := ossclient.IsBucketExist(ah.conf.BucketName)
	// if err != nil {
	// 	return err
	// }
	// if !isExist {
	// 	err = ossclient.CreateBucket(ah.conf.BucketName , oss.ACL(oss.ACLPublicRead))
	// 	if err != nil {
	// 		return err
	// 	}
	// 	rule := oss.CORSRule{
	//         AllowedOrigin: []string{"*"},
	//         AllowedMethod: []string{"GET"},
	//         AllowedHeader: []string{},
	//         ExposeHeader:  []string{},
	//         MaxAgeSeconds: 100,
	//     }
	//     return ossclient.SetBucketCORS(ah.conf.BucketName, []oss.CORSRule{rule})
	// }

	bucket, err := ossclient.Bucket(ah.conf.BucketName)
	if err != nil {
		return err
	}
	ah.bucket = bucket
	ah.location = "dev/"
	if ah.conf.Mode == "release" {
		ah.location = "prod/"
	}
	return nil
}

func (ah *aliyunhandler) Redirect(method, url string) (string, error){
	return "", nil
}

func (ah *aliyunhandler) Upload(fdef *types.FileDef, file io.ReadSeeker) (string, error){
	//s3 obj key is not case sensitive
	fname := fdef.Uid().String32()
	ext, _ := mime.ExtensionsByType(fdef.MimeType)
	if len(ext) > 0 {
		fname += ext[0]
	}
	fdef.Location = ah.location + fname
	var err error
	if err = store.Files.StartUpload(fdef); err != nil {
		fmt.Println("failed to create file record", fdef.Id, err)
		return "", err
	}
	key := fdef.Location
	if ah.conf.StsEnable {
		if signedURL, err := ah.bucket.SignURL(key, oss.HTTPPut, 60); err == nil {
			err = ah.bucket.PutObjectWithURL(signedURL, file)
		}
	
	} else {
		err = ah.bucket.PutObject(key, file)
	}
	if err != nil {
		return "", err
	}
	rc := types.ReaderCounter{Reader: file}
	url := fmt.Sprintf("https://%v.oss-%v.aliyuncs.com/%v", ah.conf.BucketName, ah.conf.Region, fdef.Location)
	fdef, err = store.Files.FinishUpload(fdef.Id, true, rc.Count)
	if err != nil {
		// Best effort. Error ignored.
		ah.bucket.DeleteObject(key)
		return "", err
	}

	return url, nil
}

func (ah *aliyunhandler) Download(filename string) (*types.FileDef, media.ReadSeekCloser, error) {
	return nil, nil, nil
}

func (ah *aliyunhandler)  Delete(locations []string) error {
    _, err := ah.bucket.DeleteObjects(locations, oss.DeleteObjectsQuiet(true))
  	return err
}

// GetIdFromUrl converts an attahment URL to a file UID.
func (ah *aliyunhandler) GetIdFromUrl(url string) types.Uid {
	return media.GetIdFromUrl(url, ah.location)
}

func (ah *aliyunhandler) AccessToken()(map[string]interface{}, error) {	
	resp, err := ah.stsclient.AssumeRole(3600)
    if err != nil {
        fmt.Print(err.Error())
        return nil, err
    }
	fmt.Printf("Credentials:\n")
	fmt.Printf("    AccessKeyID:%s\n", resp.Credentials.AccessKeyId)
	fmt.Printf("    AccessKeySecret:%s\n", resp.Credentials.AccessKeySecret)
	fmt.Printf("    SecurityToken:%s\n", resp.Credentials.SecurityToken)
	fmt.Printf("    Expiration:%s\n", resp.Credentials.Expiration)

	stsparams := map[string]interface{}{
		"accessKeyID":     resp.Credentials.AccessKeyId,
		"accessKeySecret": resp.Credentials.AccessKeyId,
		"securityToken":   resp.Credentials.SecurityToken,
		"expiration":      resp.Credentials.Expiration,
		"bucket":          ah.conf.BucketName,
		"endpoint":        ah.conf.Endpoint,
	}
	return stsparams, nil
}

func (ah *aliyunhandler) GetBaseUrl() string {
	return fmt.Sprintf("https://%v.oss-%v.aliyuncs.com", ah.conf.BucketName, ah.conf.Region)
}

func init() {
	store.RegisterMediaHandler(handlerName, &aliyunhandler{})
}
