package main

import (
	"context"
	"github.com/tinode/chat/pbx"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"log"
	"strings"
	"time"
)

type chatNodeRpcService struct {
	pbx.UnimplementedConfChatServer
}

// CreateGroupChat create group chating tempararily
func (gcs *chatNodeRpcService) CreateConfChat(ctx context.Context, in *pbx.ConferenceRequest) (*pbx.ConferenceReply, error) {

	topic := in.Topic
	userId := in.UserId
	log.Printf("create group, id: %s, user id: %s\n", topic, userId)
	if !strings.HasPrefix(topic, "conf") || len(topic) > 50 {
		return nil, status.Errorf(codes.InvalidArgument, `Conference topic name should has prefix "conf" and length is less than 50`)
	}
	msg := &ClientComMessage{
		Original: "ionrpc",
		AsUser:   userId,
		RcptTo:   topic,
		Sub: &MsgClientSub{
			Topic: topic,
		},
	}
	respChan := make(chan *RpcResp)
	globals.hub.join <- &sessionJoin{
		pkt: msg,
		rpc: respChan}

	var reply pbx.ConferenceReply
	select {
	case rpcResp := <-respChan:
		reply = pbx.ConferenceReply{Message: rpcResp.Message, Code: rpcResp.Code}
	case <-time.After(10 * time.Second):
		log.Println("CreateConfChat timeout by 10 sec")
		reply = pbx.ConferenceReply{Message: "", Code: 408}
	}
	close(respChan)
	return &reply, nil
}

// LeaveGroupChat someone leave group chating
func (gcs *chatNodeRpcService) LeaveConfChat(ctx context.Context, in *pbx.ConferenceRequest) (*pbx.ConferenceReply, error) {
	topic := in.Topic
	userId := in.UserId
	log.Printf("leave group, id: %s, user id: %s", topic, userId)
	// msg := &ClientComMessage{
	// 	AsUser: userId,
	// 	RcptTo: topic,
	// 	Del: &MsgClientDel{
	// 		Topic: topic,
	// 		What: "sub",
	// 		Hard: false,
	// 	},
	// }
	// respChan := make(chan *RpcResp)
	// globals.hub.join <- &sessionLeave{
	// 		pkt:  msg,
	// 		rpc: respChan}
	// rpcResp := <- respChan
	// close(respChan)
	return &pbx.ConferenceReply{Message: userId + " leave group " + topic}, nil
}

// CloseGroupChat  group chating close
func (gcs *chatNodeRpcService) CloseConfChat(ctx context.Context, in *pbx.ConferenceRequest) (*pbx.ConferenceReply, error) {
	topic := in.Topic
	userId := in.UserId
	log.Printf("leave group, id: %s, user id: %s", topic, userId)
	respChan := make(chan *RpcResp)
	msg := &ClientComMessage{
		AsUser: userId,
		RcptTo: topic,
		Del: &MsgClientDel{
			Topic: topic,
			What:  "topic",
			Hard:  false,
		},
	}
	globals.hub.unreg <- &topicUnreg{
		rcptTo: topic,
		pkt:    msg,
		rpc:    respChan}
	rpcResp := <-respChan
	log.Printf("close group, id: %s, user id: %s", topic, userId)
	return &pbx.ConferenceReply{Message: rpcResp.Message, Code: rpcResp.Code}, nil
}

// UpdateGroupHost update group chat host
func (gcs *chatNodeRpcService) UpdatConfHost(ctx context.Context, in *pbx.ConferenceRequest) (*pbx.ConferenceReply, error) {
	topic := in.Topic
	userId := in.UserId
	log.Printf("update group host, group: %s, host id: %s", topic, userId)
	return &pbx.ConferenceReply{Message: topic + " update group " + userId}, nil
}
