package main

import (
    "bytes"
    "crypto/aes"
    "crypto/cipher"
    "encoding/base64"
    "runtime"
    "runtime/debug"
    "log"
)


var (
    commonIV = []byte{0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f}
)

// TO-DO: aes authenticated encryption mode
func EncryptString(orig string) string {
    if orig == "" {
        return ""
    }

    defer Recover("EncryptString")
    // 转成字节数组
    origData := []byte(orig)
    k := []byte(globals.contentSecret)

    // 分组秘钥
    block, _ := aes.NewCipher(k)
    // 获取秘钥块的长度
    blockSize := block.BlockSize()
    // 补全码
    origData = PKCS7Padding(origData, blockSize)
    // 加密模式
    blockMode := cipher.NewCBCEncrypter(block, k[:blockSize])
    // 创建数组
    cryted := make([]byte, len(origData))
    // 加密
    blockMode.CryptBlocks(cryted, origData)

    return base64.StdEncoding.EncodeToString(cryted)

}

func DecryptString(cryted string) string {
    if cryted == "" {
        return ""
    }
    defer Recover("EncryptString")
    // 转成字节数组
    crytedByte, _ := base64.StdEncoding.DecodeString(cryted)
    k := []byte(globals.contentSecret)

    // 分组秘钥
    block, _ := aes.NewCipher(k)
    // 获取秘钥块的长度
    blockSize := block.BlockSize()
    // 加密模式
    blockMode := cipher.NewCBCDecrypter(block, k[:blockSize])
    // 创建数组
    orig := make([]byte, len(crytedByte))
    // 解密
    blockMode.CryptBlocks(orig, crytedByte)
    // 去补全码
    orig = PKCS7UnPadding(orig)
    return string(orig)
}

//补码
func PKCS7Padding(ciphertext []byte, blocksize int) []byte {
    padding := blocksize - len(ciphertext)%blocksize
    padtext := bytes.Repeat([]byte{byte(padding)}, padding)
    return append(ciphertext, padtext...)
}

//去码
func PKCS7UnPadding(origData []byte) []byte {
    length := len(origData)
    unpadding := int(origData[length-1])
    return origData[:(length - unpadding)]
}

func Recover(flag string) {
    _, _, l, _ := runtime.Caller(1)
    if err := recover(); err != nil {
        log.Printf("[%s] Recover panic line => %v", flag, l)
        log.Printf("[%s] Recover err => %v", flag, err)
        debug.PrintStack()
    }
}