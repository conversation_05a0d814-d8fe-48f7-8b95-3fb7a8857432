package request

import (
    "log"
    "net/http/httptrace"
)

// GotConn prints whether the connection has been used previously
// for the current request.
func GotConn(info httptrace.GotConnInfo) {
    log.Printf("Connection reused for %v, RemoteAddr %v\n", info.Reused, info.Conn.RemoteAddr().String())
}

// ConnectDone is called when a new connection's Dial completes.
func ConnectDone(network, addr string, err error) {
	if err != nil {
		log.Printf("Connection done with error: %v", err.Error())
		return
	}
	log.Printf("Connection done for %v, RemoteAddr %v\n", network, addr)
}

func GotFirstResponseByte() {
	log.Printf("Connection got first response byte.")	
}