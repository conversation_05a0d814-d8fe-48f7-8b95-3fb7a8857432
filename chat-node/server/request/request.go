package request
import (
    "fmt"
    "net/http"
    "encoding/json"
    "crypto/tls"
    "io/ioutil"
    "strings"
    gurl "net/url"
    "net"
    "bytes"
    "time"
)


type HttpClient struct {
    client *http.Client
}

func NewHttpClient (timeout int) *HttpClient{
    // close http2 of golang
    disableHttp2 := make(map[string]func(string, *tls.Conn) http.RoundTripper)
    tr := &http.Transport{
        DialContext: (&net.Dialer{
            Timeout:   5 * time.Second,
            KeepAlive: 5 * time.Second,
        }).DialContext,
        MaxIdleConns:       200,
        MaxIdleConnsPerHost:  200,
        IdleConnTimeout:    time.Duration(timeout)* time.Second,
        TLSNextProto: disableHttp2,
    }
    c := &http.Client{Transport: tr}
    return &HttpClient{
        client: c,
    }
}
func (hc *HttpClient) Get(url string, headers, params map[string]interface{})(map[string]interface{}, error){
    req, err := http.NewRequest("GET", url, nil)
    if err != nil {
        fmt.Print(err)
        return nil, err
    }
    if params != nil {
        q := req.URL.Query()
        for key, element := range params {
            if eleStr , ok := element.(string); ok {
                q.Add(key, eleStr)
            }
        }
        req.URL.RawQuery = q.Encode()
    }

    if headers != nil {
        for header, val := range headers {
            if valStr , ok := val.(string); ok {
                req.Header.Set(header, valStr)
            }
        }
    }
    req.Close = true
    resp, err := hc.client.Do(req)
    if err != nil {
        fmt.Println(err)
        return nil, err
    }
    defer resp.Body.Close()
    body, err :=ioutil.ReadAll(resp.Body)
    if err != nil {
        fmt.Println(err)
        return nil, err
    }
    jsonStr := string(body)
    fmt.Println(jsonStr)

    jsonMap := make(map[string]interface{})
    err = json.Unmarshal([]byte(jsonStr), &jsonMap)
    return jsonMap, err
}

func (hc *HttpClient) Post(url string, headers, params map[string]interface{}) (map[string]interface{}, error){
    var contentType string
    if ctype, ok := headers["Content-Type"].(string); ok {
        contentType = ctype
    }

    req, err := getPostReqByContentType(url, contentType, params)
    req.Close = true
    if err != nil {
        fmt.Println("Failed to get construct request", err)
        return nil , err
    }
    if contentType == "" {
        req.Header.Set("Content-Type", "application/json")
    }
    for header, val := range headers {
        if valStr, ok := val.(string); ok {
            req.Header.Set(header, valStr)
        }
    }

    resp, err := hc.client.Do(req)
    if err != nil {
        fmt.Println("Failed to send post request",err)
        return nil, err
    }
    defer resp.Body.Close()
    jsonMap := make(map[string]interface{})
    err = json.NewDecoder(resp.Body).Decode(&jsonMap)
    return jsonMap, err
}


func getPostReqByContentType(url, contentType string, params map[string]interface{}) (*http.Request, error) {
    var req *http.Request
    var err error
    if contentType == "application/x-www-form-urlencoded" {
        data := gurl.Values{}
        for key, val := range params {
            if valStr, ok := val.(string); ok {
                data.Set(key, valStr)
            }
        }
        req, err = http.NewRequest(http.MethodPost, url, strings.NewReader(data.Encode())) // URL-encoded payload
    } else {
        requestBody, jerr := json.Marshal(params)
        if jerr != nil {
            fmt.Println("Failed to parse params %v", err)
            return nil, err
        }
        req, err = http.NewRequest(http.MethodPost, url, bytes.NewBuffer(requestBody))
    }
    if err != nil {
        return nil, err
    }

    // trace := &httptrace.ClientTrace{
    //     GotConn: GotConn,
    //     ConnectDone: ConnectDone,
    //     GotFirstResponseByte: GotFirstResponseByte,
    // }
    // req = req.WithContext(httptrace.WithClientTrace(req.Context(), trace))
    return req, nil
}

// formatRequest generates ascii representation of a request
func formatRequest(r *http.Request) string {
 // Create return string
 var request []string
 // Add the request string
 url := fmt.Sprintf("%v %v %v", r.Method, r.URL, r.Proto)
 request = append(request, url)
 // Add the host
 request = append(request, fmt.Sprintf("Host: %v", r.Host))
 // Loop through headers
 for name, headers := range r.Header {
   name = strings.ToLower(name)
   for _, h := range headers {
     request = append(request, fmt.Sprintf("%v: %v", name, h))
   }
 }
 
 // If this is a POST, add post data
 if r.Method == "POST" {
    r.ParseForm()
    request = append(request, "\n")
    request = append(request, r.Form.Encode())
 } 
  // Return the request as a string
  return strings.Join(request, "\n")
}
