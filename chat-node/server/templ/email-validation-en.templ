{{/*
  ENGLISH

  This template defines content of the email sent to users as a request to confirm registration email address.
  See https://golang.org/pkg/text/template/ for syntax.

  The template must contain the following parts parts:
   - 'subject': Subject line of an email message
   - One or both of the following:
     - 'body_html': HTML content of the message. A header "Content-type: text/html" will be added.
     - 'body_plain': plain text content of the message. A header "Content-type: text/plain" will be added.

   If both body_html and body_plain are included, both are sent as parts of 'multipart/alternative' message.
*/}}

{{define "subject" -}}
Tinode registration: confirm email
{{- end}}

{{define "body_html" -}}
<html>
<body>

<p>Hello.</p>

<p>You're receiving this message because someone used your email to register at
<a href="{{.HostUrl}}">Tinode</a>.</p>

<p><a href="{{.HostUrl}}#cred?method=email&code={{.Code}}&token={{.Token}}">Click</a> to confirm
or go to
<a href="{{.HostUrl}}#cred?what=email">{{.HostUrl}}#cred?method=email</a>
and enter the following code:</p>
<blockquote>{{.Code}}</blockquote>
<p>You may need to enter login and password.</p>

<p>If you did not register at Tinode just ignore this message.</p>

<p><a href="https://tinode.co/">Tinode Team</a></p>

</body>
</html>
{{- end}}

{{define "body_plain" -}}

Hello.

You're receiving this message because someone used your email to register at Tinode ({{.HostUrl}}).

Click the link {{.HostUrl}}#cred?method=email&code={{.Code}}&token={{.Token}} to confirm or go to {{.HostUrl}}#cred?what=email
and enter the following code:

	{{.Code}}

You may need to enter login and password.

If you did not register at Tinode just ignore this message.

Tinode Team
https://tinode.co/

{{- end}}
