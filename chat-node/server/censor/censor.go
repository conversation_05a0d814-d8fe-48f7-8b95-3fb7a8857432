package censor
import(
	"github.com/mitchellh/mapstructure"
	"github.com/tinode/chat/server/request"
	"github.com/tinode/chat/server/store/types"
	gurl "net/url"
	"encoding/json"
	"time"
	"errors"
	"fmt"
)
const (
	timeout = 30
	tokenValidatyTime = 30 * 24 * time.Hour
)
var supportedImageTypes = map[string]bool{
	"image/png": true,
	"image/jpeg": true,
	"image/bmp": true,
	"image/gif": true,
	"image/webp": true,
	"image/tiff": true,
}

type Censor struct {
	Provider       string
	AccessTokenUrl string
	CensorUrl      string
	ApiKey         string
	Secret         string
	TokenFetchedAt time.Time
	AccessToken    string

	httpClient    *request.HttpClient
}
type configType struct {
	Enable         bool   `json:"enable,omitempty"`
	Provider       string `json:"provider,omitempty"`
	AccessTokenUrl string `json:"access_token_url,omitempty"`
	CensorUrl      string `json:"censor_url,omitempty"`
	ApiKey         string `json:"api_key,omitempty"`
	Secret         string `json:"secret_key,omitempty"`
}

type Hit struct {
	Probability float64  `mapstructure: "probability"`
	Database    string   `mapstructure: "database"`
	Words       []string `mapstructure: "words"`
}

type CensorData struct {
	Type           int    `mapstructure: "type"`
	Subtype        int    `mapstructure: "subtype"`
	Conclusion     string `mapstructure: "conclusion"`
	ConclusionType int    `mapstructure: "conclusionType"`
	Msg            string `mapstructure: "msg"`
	Hits           []Hit  `mapstructure: "hits"`
}

type CensorResponse struct {
	ContentType    types.MessageType
	Logid          float64     `mapstructure: "log_id"`
	Conclusion     string      `mapstructure: "conclusion"`
	ConclusionType int         `mapstructure: "conclusionType"`
	Data           interface{} `mapstructure: "data"`
}

var illegalImgBase64 string

func NewCensor (jsonconfig json.RawMessage) (*Censor, error) {
	var config configType
	if err := json.Unmarshal(jsonconfig, &config); err != nil {
		return nil, errors.New("censor failed to parse config: " + err.Error())
	}

	if config.AccessTokenUrl == "" || config.ApiKey == "" || config.Secret == "" {
		return nil, errors.New("miss key config value")
	}

	if !config.Enable {
		return nil, nil
	}

	hc := request.NewHttpClient(timeout)
	c := &Censor{
		Provider:       config.Provider,
		AccessTokenUrl: config.AccessTokenUrl,
		CensorUrl:      config.CensorUrl,
		ApiKey:         config.ApiKey,
		Secret:         config.Secret,
		httpClient:     hc,
	}
	if err := c.fetchToken(); err != nil {
		return nil, err
	}
	return c, nil
}

func (c *Censor) fetchToken() error {
	url := fmt.Sprintf("%v?grant_type=client_credentials&client_id=%v&client_secret=%v", c.AccessTokenUrl, c.ApiKey, c.Secret)

	res, err := c.httpClient.Post(url, nil, nil)
	if err != nil {
		return err
	}

	if _, ok := res["error"]; ok {
		return errors.New(res["error_description"].(string))
	}

	if token, ok := res["access_token"].(string); ok {
		c.AccessToken = token
		c.TokenFetchedAt = types.TimeNow()
	} else {
		return errors.New("miss access token")
	}

	return nil
}

func (c *Censor) ImageCensor(params map[string]interface{}) (*CensorResponse, error) {
	_, isContent := params["image"]
	_, isUrl := params["imgUrl"]

	if !isContent && !isUrl {
		return nil, errors.New("invalid params")
	}

	if err := c.isTokenExpired(); err != nil {
		fmt.Println("fetchToken failed")
		return nil, err
	}
	url := fmt.Sprintf("%v/img_censor/v2/user_defined?access_token=%v", c.CensorUrl, c.AccessToken)
	header := map[string]interface{}{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	res, err := c.httpClient.Post(url, header, params)
	if err != nil {
		fmt.Println("post failed")
		return nil, err
	}
	if _, hasErrCode := res["error_code"]; hasErrCode {
		return nil, errors.New("censor error")
	}
	censorRes := &CensorResponse{}
	if err = mapstructure.Decode(res, censorRes); err != nil {
		return nil, err
	}
	censorRes.ContentType = types.MessageImage
	return censorRes, nil
}

func (c *Censor) ImageContentCensor(imgBase64 string) (*CensorResponse, error) {
	if imgBase64 == "" {
		return nil, errors.New("image url cannot be empty")
	}

	params := map[string]interface{}{
		"image": imgBase64,
	}
	return c.ImageCensor(params)
}

func (c *Censor) ImageUrlCensor(url string) (*CensorResponse, error) {
	if url == "" {
		return nil, errors.New("image url cannot be empty")
	}
	_, err := gurl.ParseRequestURI(url)
	if err != nil {
		return nil, err
	}
	params := map[string]interface{}{
		"imgUrl": url,
	}
	return c.ImageCensor(params)
}

func (c *Censor) TextCensor(text string)(*CensorResponse, error) {
	if err := c.isTokenExpired(); err != nil {
		return nil, err
	}
	url := fmt.Sprintf("%v/text_censor/v2/user_defined?access_token=%v", c.CensorUrl, c.AccessToken)
	header := map[string]interface{}{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	params := map[string]interface{}{
		"text": text,
	}
	res, err := c.httpClient.Post(url, header, params)
	if err != nil {
		return nil, err
	}
	if _, hasErrCode := res["error_code"]; hasErrCode {
		return nil, errors.New("censor error")
	}
	censorRes := &CensorResponse{}
	if err = mapstructure.Decode(res, censorRes); err != nil {
		return nil, err
	}
	censorRes.ContentType = types.MessagePlainText
	return censorRes, nil
}

func (cr *CensorResponse) GetFilterMsg() ([]types.CensorResult, []string, bool) {
	if cr == nil || cr.Conclusion == "" {
		return nil, nil, true
	}

	list, ok := cr.Data.([]interface{})
	if !ok {
		fmt.Println("Info: invalid CensorData data in censor response")
		return nil, nil, true
	}

	var words []string
	resultMsg := make([]types.CensorResult, 0, len(list))
	for _, data := range list {
		cdata := CensorData{}
		if err := mapstructure.Decode(data, &cdata); err != nil {
			return nil, nil, true
		}
		resultMsg = append(resultMsg, types.CensorResult{
			Type: cdata.Type,
			Subtype: cdata.Subtype,
			ConclusionType: cdata.ConclusionType,
		})
		if cr.ContentType == types.MessagePlainText {
			for _, hit := range cdata.Hits {
				if words == nil {
					words = hit.Words
					continue
				}
				words = append(words, hit.Words...)
			}
		}
	}
	return resultMsg, words, false
}

func (c *Censor) isTokenExpired() error {
	now := types.TimeNow()
	duration := now.Sub(c.TokenFetchedAt)
	var err error
	if duration > tokenValidatyTime {
		err = c.fetchToken()
	}
	return err
}

func (c *Censor) IsImageTypeSuported(mimeType string) bool {
	// only support image PNG、JPG、JPEG、BMP、GIF、Webp、TIFF
	_, isSupported := supportedImageTypes[mimeType]
	return isSupported
}

func (c *Censor) ImageSizeUpdate(size int64) int64 {
	// image size should be greater than 5kb, and less than 4M
	const minSize = 5 * 1000
	const maxSize = 4 * 1000000
	if size < minSize {
		return minSize - size
	}
	if size > maxSize {
		return maxSize - size
	}
	return 0
}
