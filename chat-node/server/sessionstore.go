/******************************************************************************
 *
 *  Description:
 *
 *  Session management.
 *
 *****************************************************************************/

package main

import (
	"container/list"
	"errors"
	"github.com/dgrijalva/jwt-go"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/tinode/chat/pbx"
	"github.com/tinode/chat/server/auth"
	"github.com/tinode/chat/server/store"
	"github.com/tinode/chat/server/store/types"
)

// SessionStore holds live sessions. Long polling sessions are stored in a linked list with
// most recent sessions on top. In addition all sessions are stored in a map indexed by session ID.
type SessionStore struct {
	lock sync.Mutex

	// Support for long polling sessions: a list of sessions sorted by last access time.
	// Needed for cleaning abandoned sessions.
	lru      *list.List
	lifeTime time.Duration

	// All sessions indexed by session ID
	sessCache map[string]*Session
	//store all valid session poited by uid
	uidSessCache map[string]*Session
}

// NewSession creates a new session and saves it to the session store.
func (ss *SessionStore) NewSession(conn interface{}, sid, token string) (*Session, int) {

	var s Session

	if sid == "" {
		s.sid = store.GetUidString()
	} else {
		s.sid = sid
	}

	if token != "" {
		s.token = token

		claims, err := ValidateJWT(token)
		var userInfo *types.User
		var uid types.Uid
		if _, ok := claims["uid"]; err == nil && ok {
			uid = types.ParseUserId(claims["uid"].(string))
			if uid != types.ZeroUid {
				userInfo, err = store.Users.Get(uid)
			}
		}
		if _, ok := claims["issuedTime"]; ok {
			layout := "2006-01-02 15:04:05.999999999 -0700 MST"
			s.tokenIssueTime, _ = time.Parse(layout, claims["issuedTime"].(string))
		}

		if _, ok := claims["deviceHash"]; ok {
			s.deviceHash = claims["deviceHash"].(string)
		}
		if err == nil && userInfo != nil && userInfo.Userid != "" {
			s.authLvl = auth.LevelAuth
			s.uid = uid
		}
	}
	if s.uid != types.ZeroUid {
		// check device table
		devinfo, count, err := store.Devices.GetAll(s.uid)
		if err != nil || count == 0 {
			log.Printf("Error: Failed to get device info for user usr%v", s.uid.String())
			s.hasLoginOther = true
		}
		if s.deviceHash != "" && !s.tokenIssueTime.IsZero() {
			s.hasLoginOther = devinfo[s.uid][0].Hash != s.deviceHash && s.tokenIssueTime.Before(devinfo[s.uid][0].LastSeen)
			log.Printf("print time %v with %v", s.tokenIssueTime.String(), devinfo[s.uid][0].LastSeen.String())
			log.Printf("this session value = %v", s.hasLoginOther)
		} else {
			s.hasLoginOther = true
			log.Printf("Error: Miss device info in cookie")
		}

	}
	ss.lock.Lock()
	if _, found := ss.sessCache[s.sid]; found {
		log.Fatalln("ERROR! duplicate session ID", s.sid)
	}
	// if two session use same accout, evit one which has older cookie
	if s.uid != types.ZeroUid && !s.hasLoginOther {
		if oldSess, found := ss.uidSessCache[s.uid.String()]; found && !oldSess.hasLoginOther {
			//inform old session login status invalid
			oldSess.hasLoginOther = oldSess.tokenIssueTime.Before(s.tokenIssueTime) || oldSess.tokenIssueTime.Equal(s.tokenIssueTime)
			s.hasLoginOther = !oldSess.hasLoginOther
			if oldSess.hasLoginOther {
				msg := &ServerComMessage{
					Pres: &MsgServerPres{Topic: "me", What: "expired-login", Src: "usr" + oldSess.uid.String(),
						AcsActor: "", AcsTarget: ""},
					AsUser: "sys",
					RcptTo: "usr" + oldSess.uid.String()}
				oldSess.queueOut(msg)
			}
			log.Printf("old session value = %v", oldSess.hasLoginOther)
		}
	}

	ss.lock.Unlock()
	switch c := conn.(type) {
	case *websocket.Conn:
		s.proto = WEBSOCK
		s.ws = c
	case http.ResponseWriter:
		s.proto = LPOLL
		// no need to store c for long polling, it changes with every request
	case *ClusterNode:
		s.proto = MULTIPLEX
		s.clnode = c
	case pbx.Node_MessageLoopServer:
		s.proto = GRPC
		s.grpcnode = c
	default:
		log.Panicln("session: unknown connection type", conn)
	}

	s.subs = make(map[string]*Subscription)
	s.send = make(chan interface{}, sendQueueLimit+32) // buffered
	s.stop = make(chan interface{}, 1)                 // Buffered by 1 just to make it non-blocking
	s.detach = make(chan string, 64)                   // buffered

	s.bkgTimer = time.NewTimer(time.Hour)
	s.bkgTimer.Stop()

	s.lastTouched = time.Now()

	ss.lock.Lock()

	if s.proto == LPOLL {
		// Only LP sessions need to be sorted by last active
		s.lpTracker = ss.lru.PushFront(&s)
	}

	ss.sessCache[s.sid] = &s
	if s.uid != types.ZeroUid && !s.hasLoginOther {
		ss.uidSessCache[s.uid.String()] = &s
	}

	// Expire stale long polling sessions: ss.lru contains only long polling sessions.
	// If ss.lru is empty this is a noop.
	var expired []*Session
	expire := s.lastTouched.Add(-ss.lifeTime)
	for elem := ss.lru.Back(); elem != nil; elem = ss.lru.Back() {
		sess := elem.Value.(*Session)
		if sess.lastTouched.Before(expire) {
			ss.lru.Remove(elem)
			delete(ss.sessCache, sess.sid)
			delete(ss.uidSessCache, s.uid.String())
			expired = append(expired, sess)
		} else {
			break // don't need to traverse further
		}
	}

	ss.lock.Unlock()

	// Deleting long polling sessions.
	for _, sess := range expired {
		// This locks the session. Thus cleaning up outside of the
		// sessionStore lock. Otherwise deadlock.
		sess.cleanUp(true)
	}

	statsSet("LiveSessions", int64(len(ss.sessCache)))
	statsInc("TotalSessions", 1)

	return &s, len(ss.sessCache)
}

// Get fetches a session from store by session ID.
func (ss *SessionStore) Get(sid string) *Session {
	ss.lock.Lock()
	defer ss.lock.Unlock()

	if sess := ss.sessCache[sid]; sess != nil {
		if sess.proto == LPOLL {
			ss.lru.MoveToFront(sess.lpTracker)
			sess.lastTouched = time.Now()
		}

		return sess
	}

	return nil
}

// Delete removes session from store.
func (ss *SessionStore) Delete(s *Session) {
	ss.lock.Lock()
	defer ss.lock.Unlock()

	delete(ss.sessCache, s.sid)
	if s.proto == LPOLL {
		ss.lru.Remove(s.lpTracker)
	}

	statsSet("LiveSessions", int64(len(ss.sessCache)))
}

// Shutdown terminates sessionStore. No need to clean up.
// Don't send to clustered sessions, their servers are not being shut down.
func (ss *SessionStore) Shutdown() {
	ss.lock.Lock()
	defer ss.lock.Unlock()

	shutdown := NoErrShutdown(types.TimeNow())
	for _, s := range ss.sessCache {
		if !s.isMultiplex() {
			s.stop <- s.serialize(shutdown)
		}
	}

	// TODO: Consider broadcasting shutdown to other cluster nodes.

	log.Println("SessionStore shut down, sessions terminated:", len(ss.sessCache))
}

// EvictUser terminates all sessions of a given user.
func (ss *SessionStore) EvictUser(uid types.Uid, skipSid string) {
	ss.lock.Lock()
	defer ss.lock.Unlock()

	// FIXME: this probably needs to be optimized. This may take very long time if the node hosts 100000 sessions.
	evicted := NoErrEvicted("", "", types.TimeNow())
	evicted.AsUser = uid.UserId()
	for _, s := range ss.sessCache {
		if s.uid == uid && !s.isMultiplex() && s.sid != skipSid {
			s.stop <- s.serialize(evicted)
			delete(ss.sessCache, s.sid)
			delete(ss.uidSessCache, s.uid.String())
			if s.proto == LPOLL {
				ss.lru.Remove(s.lpTracker)
			}
		}
	}

	statsSet("LiveSessions", int64(len(ss.sessCache)))
}

// NodeRestarted removes stale sessions from a restarted cluster node.
//  - nodeName is the name of affected node
//  - fingerprint is the new fingerprint of the node.
func (ss *SessionStore) NodeRestarted(nodeName string, fingerprint int64) {
	ss.lock.Lock()
	defer ss.lock.Unlock()

	for _, s := range ss.sessCache {
		if !s.isMultiplex() || s.clnode.name != nodeName {
			continue
		}
		if s.clnode.fingerprint != fingerprint {
			s.stop <- nil
			delete(ss.sessCache, s.sid)
			delete(ss.uidSessCache, s.uid.String())
		}
	}

	statsSet("LiveSessions", int64(len(ss.sessCache)))
}

// NewSessionStore initializes a session store.
func NewSessionStore(lifetime time.Duration) *SessionStore {
	ss := &SessionStore{
		lru:      list.New(),
		lifeTime: lifetime,

		sessCache:    make(map[string]*Session),
		uidSessCache: make(map[string]*Session),
	}

	statsRegisterInt("LiveSessions")
	statsRegisterInt("TotalSessions")

	return ss
}

func ValidateJWT(tokenStr string) (map[string]interface{}, error) {
	// Don't check s.multi here. Let it panic if called for proxy session.
	if tokenStr == "" {
		return nil, errors.New("miss jwt token.")
	}
	secretKey := []byte("jcBUVtmuhdQ+X9i1T1O/gTJm9fsogxGq")
	// claims := &UserClaims{}
	token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			log.Printf("Unexpected signing method")
			return nil, errors.New("Unexpected signing method")
		}
		return secretKey, nil
	})

	if err != nil {
		log.Printf("validate jwt error => %v", err.Error())
		return nil, err
	}
	var claims = make(map[string]interface{})

	if token != nil && token.Claims != nil {
		for key, value := range token.Claims.(jwt.MapClaims) {
			claims[key] = value
		}
	}
	return claims, err
}
