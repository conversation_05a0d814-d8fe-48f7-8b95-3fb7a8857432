version: "3.7"

services:
  tinode:
    image: fangyu/tinode:latest
    build:
      context: ../
      dockerfile: ./temp-docker/docker/tinode.Dockerfile
    container_name: "fangyu-tinode"
    volumes:
      - "./configs/tinode.conf:/configs/tinode.conf"
      - "../tinode-db/uploads/:/uploads/"
    environment:
      - SET_CONTAINER_TIMEZONE=true
      - CONTAINER_TIMEZONE=Asia/Shanghai
    ports:
      - 6060:6060
      - 16060:16060
    networks:
      - ionnet


networks:
  ionnet:
    external: true
    name: ionnet
    driver: bridge