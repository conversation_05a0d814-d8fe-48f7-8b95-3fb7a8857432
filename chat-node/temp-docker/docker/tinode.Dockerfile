FROM golang:1.14.4-stretch

ENV GO111MODULE=on

WORKDIR $GOPATH/src/github.com/fangyu/tinode

COPY go.mod go.sum ./
RUN cd $GOPATH/src/github.com/fangyu/tinode && go mod download

COPY server $GOPATH/src/github.com/fangyu/tinode/server
COPY pbx $GOPATH/src/github.com/fangyu/tinode/pbx

WORKDIR $GOPATH/src/github.com/fangyu/tinode/server
RUN CGO_ENABLED=0 GOOS=linux go build -tags mysql -a -installsuffix cgo -o /tinode .

FROM alpine:3.12.0

RUN apk --no-cache add ca-certificates

ARG TZ='Asia/Shanghai'
ENV DEFAULT_TZ ${TZ}
RUN apk upgrade --update \
  && apk add -U tzdata \
  && cp /usr/share/zoneinfo/${DEFAULT_TZ} /etc/localtime \
  && apk del tzdata \
  && rm -rf \
  /var/cache/apk/*

COPY --from=0 /tinode /usr/local/bin/tinode
COPY temp-docker/configs/tinode.conf /configs/tinode.conf
COPY webapp /static/webapp
COPY tinode-db/uploads /uploads
COPY server/templ /usr/local/server/templ
EXPOSE 6060 16060
ENTRYPOINT ["/usr/local/bin/tinode"]
CMD ["-config", "/configs/tinode.conf","-static_data","/static/webapp/"]
