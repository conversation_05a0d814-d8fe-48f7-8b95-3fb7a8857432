### Database Moel Design



#### Overview

i. [会议信息表](#会议信息表)

ii. [与会记录表](#与会记录表)

iii. [会议受邀请者状态表](#会议受邀请者状态表)

#### 会议信息表

| Field          | Type    | Null | Key       | Default | Extra |
| -------------- | ------- | ---- | --------- | ------- | ----- |
| `id`           |         |      | `primary` |         |       |
| `conferenceID` | varchar |      |           |         |       |
| `ownerID`      | varchar |      |           |         |       |
| `createdAt`    |         |      |           |         |       |
| `updatedAt`    |         |      |           |         |       |
| `deletedAt`    |         |      |           |         |       |
| `password`     | varchar |      |           |         |       |
| `subject`      | varchar |      |           |         |       |
| `startedAt`    | Int     |      |           |         |       |
| `endAt`        | Int     |      |           |         |       |
| `status`       | int     |      |           |         |       |
| `number`       | varchar |      |           |         |       |
| `setting`      | json    |      |           |         |       |

- `conferenceID`：根据conference id 和password，与会者可以加入会议
- `ownerID`：会议创建者id
- `subject`：会议主题
- `startedAt`：开始会议的时间（有快速会议和预约会议的区别）
- `endAt`：结束会议的时间
- `status`:launching某一时间内，只能有一个，pending和closed可以有多个
  -  pending（预定状态），
  -  launching（会议开启状态），
  -  closed（会议关闭状态）
- `number`: 区分PMI的编号。当为普通会议时为空，PMI时为“rid-timestamp”的格式
- `setting`:
  - 等候室开启：true & false

在ion中，需要进行写操作的有：

`endAt`

`status`





#### 与会记录表：

| Field            | Type | Null | Key       | Default | Extra |
| ---------------- | ---- | ---- | --------- | ------- | ----- |
| `id`             |      |      | `primary` |         |       |
| `createdAt`      |      |      |           |         |       |
| `updatedAt`      |      |      |           |         |       |
| `deletedAt`      |      |      |           |         |       |
| `conferenceID`   |      |      |           |         |       |
| `participatorID` |      |      |           |         |       |
| `joinAt`         |      |      |           |         |       |
| `leaveAt`        |      |      |           |         |       |
| `status`         |      |      |           |         |       |
| `number`         |      |      |           |         |       |

- 可根据`conferenceID`查询此会议都有谁参加，返回`participatorID`

- 可根据`participatorID`查询此用户都有参加了什么会议，返回`conferenceID`，根据`conferenceID`可查询详细的会议信息。

- `joinAt`:uid进入这个会议的时间（可能在会议开始前或者后）
- `leavelAt`:uid离开这个会议的时间（可能在会议结束前）
- `status` : 
  - meeting:开会中；
  - leaving:离会中；
  - closed：会议已经关闭
- `number`: 区分PMI的编号。从table conference中取进行赋值

在ion中需要进行写操作的有：

`joinAt`

`leaveAt`

`status`



**WIP**

#### 会议设置表

| Field          | Type | Null | Key       | Default | Extra |
| -------------- | ---- | ---- | --------- | ------- | ----- |
| `id`           |      |      | `primary` |         |       |
| `createdAt`    |      |      |           |         |       |
| `updatedAt`    |      |      |           |         |       |
| `deletedAt`    |      |      |           |         |       |
| `conferenceid` |      |      |           |         |       |
| `ownerid`      |      |      |           |         |       |
| `pwd`          |      |      |           |         |       |
| `salt`         |      |      |           |         |       |
| `waitingroom`  |      |      |           |         |       |

与user table foreign key关联？

`conferenceid`和`ownerid`唯一index表中记录

无论快速会议，预定会议还是普通会议号还是个人会议号，设置都是写进此表中，快速会议是insert，pmi是insert和update。

在创建新用户的时候，也一起创建关于PMI的设置表；

在设置关于用户PMI设置表的时候，更新PMI设置表；







#### 会议受邀请者状态表(WIP)：

| Field          | Type | Null | Key       | Default | Extra |
| -------------- | ---- | ---- | --------- | ------- | ----- |
| `id`           |      |      | `primary` |         |       |
| `createdAt`    |      |      |           |         |       |
| `updatedAt`    |      |      |           |         |       |
| `deletedAt`    |      |      |           |         |       |
| `conferenceID` |      |      |           |         |       |
| `visitorID`    |      |      |           |         |       |
| `status`       |      |      |           |         |       |

`visitor` : 被邀请人的id

`status`：pending，accepted，aborted，expired

- 1.Pending: 等待回复中

- 2.accepted：接受邀请并加入会议
- 3.aborted：拒绝邀请
- 4.expired：邀请过期

