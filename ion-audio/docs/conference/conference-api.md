### Conference API



#### 会议的基本用法

- 每个用户只能创建一个会议，即一个用户不能在table conference中同时拥有多个启动着的会议。
- 使用个人会议号时，第一次使用需要先创建一次会议（把此次创建写入表里），那么就可以join这个PMI了，即使在会议中离开或者关闭此PMI的会议，也不会真的关闭，体现为，其他非主持人还可以凭借此PMI和密码进入此会议，此次的与会还是隶属于上一次创建的会议。如果需要更新会议，需要重新创建会议，会关闭上一次会议（在表中状态置为关闭），新开一个会议（表中新增一个record）。





#### 个人会议号（PMI）

**PMI的生成：**

在注册账号的时候，随机生成一个10位的数字与用户绑定；

**PMI的使用方式：**

可选择使用个人会议号（PMI）或者不使用：

- 如果不使用PMI作为开启会议的roomid，在`创建会议`中，是在user-system这个节点上，由后端生成一个随机的9位随机数字作为会议id，并存在数据库中，数据库里的9位数字不能重复；
- 如果使用PMI，在`创建会议`中，是在user-system这个节点上，存储会议信息到数据库中，数据库中的10位数字可以重复，并有一个编号number（rid+current timestamp）作为区分相同PMI不同次会议的标记。



#### 会议的创建和加入以及离开

**新会议：**

- 描述：会议id号的获取+**自动**加入会议

- 过程：
  - 前端（jwt）访问user-node，获取roomid，并存储会议信息 ==》前端接收 response roomid ；
  - 前端（jwt）立即带着jwt访问ion-node，无论是否带jwt都会建立websocket连接，是否带jwt是作为区分登陆状态的判断依据；
  - websocket连接建立成功后，join message中带roomid，uid，（password）进行验证房间许可。如果验证成功，可正常和ion进行通信，不用再次验证，除非离开房间或者断开连接

**安排会议：**

- 描述：会议id号的获取+**用户手动点击**加入会议
- 过程：
  - 前端（jwt）访问user-node，获取roomid，并存储会议信息 ==》前端接收 response roomid ；
  - 前端（jwt）在用户点击`加入会议`后带着jwt访问ion-node，无论是否带jwt都会建立websocket连接，是否带jwt是作为区分登陆状态的判断依据；
  - websocket连接建立成功后，join message中带roomid，uid，（password）进行验证房间许可。如果验证成功，可正常和ion进行通信，不用再次验证，除非离开房间或者断开连接

**加入会议：**

- 描述：后端根据提供的roomid，判断此roomid是否存在，是否关闭，是否开启，密码是否相符，来允许用户加入会议
- 过程：
  - 前端（jwt）在用户点击`加入会议`后带着jwt访问ion-node，无论是否带jwt都会建立websocket连接，是否带jwt是作为区分登陆状态的判断依据；
  - websocket连接建立成功后，join message中带roomid，uid，（password）进行验证房间许可。如果验证成功，可正常和ion进行通信，不用再次验证，除非离开房间或者断开连接

**离开会议：**

- 描述：所有人离开会议，才会标志此会议处于关闭，再次点击`加入会议`，此会议号无效，只能点击`新会议`或者`安排会议`，来生成一个新的会议；但是，如果是使用PMI，PMI会保留此房间，如果需要更新新的房间信息，需要新建会议。

**关闭会议（WIP）：**

- 描述：实现主持人关闭会议，所有人都离开会议：
- 关闭会议的具体实现：
  1. 在主持人关闭会议后，发送一个特定的notify给前端，前端自己调用离开会议的接口；
  2. 在主持人关闭会议后，后端自己把这个房间的所有人调用离开会议的函数。再通知前端此会议已经关闭



#### Overview

1. [快速会议](#1.快速会议)
2. [预定会议](#2.预定会议)
3. [加入会议](#3.加入会议)
4. [结束会议 & 离开会议](#4.结束会议 & 离开会议)
5. [接收设备状态信息](#5.接收设备状态信息)
6. [返回参会人列表](#6.返回参会人列表)
7. [等候室](#7.等候室)
8. [历史会议](#8.历史会议)
9. [会议设置](#9.会议设置)
10. [全体静音](#10.全体静音)
11. [会议加锁](#11.会议加锁)
12. [更换主持人](#12.更换主持人)
13. [移除某人](#13.移除某人)
14. [共享屏幕开关](#14.共享屏幕开关)

### api设计：

#### 1.快速会议

**http 访问用户系统**

使用先前默认的信息，快速创建会议，无须在创建的时候填写任何信息。

用户系统首先返回会议ID（roomid），并记录会议基础信息到数据库。

app拿到会议ID，访问ion服务。



```
POST /api/v1/conference/create
```



#### Post Body

| Name        | Required | Type     | Explanation    |
| ----------- | -------- | -------- | -------------- |
| `password`  | `True`   | `String` | 会议密码       |
| `subject`   | `True`   | `String` | 会议主题       |
| `startedAt` | `True`   | `int`    | 会议开始的时间 |
| `endAt`     | `True`   | `int`    | 会议结束时间   |
| `pmi`       | `True`   | `string` | 是否使用PMI    |

- `subject`：快速会议，使用默认的主题：username的快速会议
- `passwrod`：入会密码，如果不需要设置密码，则置为""
- `startedAt`: 快速会议，使用当前时间，前端获取当前时间的时间戳
- `endAt`:快速会议，不用填写，发空；安排会议，填写预定的结束时间
- `pmi`:如果是""，则表示不使用PMI，如果为非""，则表示使用PMI

**用户系统内部产生的信息：**

- conferenceID：随机产生的9位数字；（可使用个人会议号，与用户绑定的10数字）
- 把基本的会议信息写入table conference中。





#### Response

情况一：创建的会议没有处于启动状态的（即已经全部关闭）

Status: 200 OK

```json
{
		"message": "create conference successfully."
    “conferenceid”: ""
}
```

情况二：创建的会议有处于启动状态的

Status: 200 OK

```json
{
		"message": "your created meeting has started."
    “conferenceid”: "返回你已经启动的会议的id"
}
```



#### Error Response

| Status Code | Response | Cause |
| ----------- | -------- | ----- |
|             |          |       |
|             |          |       |
|             |          |       |



#### 2.预定会议

**http 访问用户系统**

用户填写信息进行预定会议。

用户系统首先返回会议ID（roomid），并记录会议基础信息到数据库。

app拿到会议ID，访问ion服务。



```
POST /api/v1/conference/create
```



#### Post Body

| Name        | Required | Type     | Explanation        |
| ----------- | -------- | -------- | ------------------ |
| `password`  | `True`   | `String` | 会议密码           |
| `subject`   | `True`   | `String` | 会议主题           |
| `startedAt` | `True`   | `int`    | 安排会议开始的时间 |
| `endAt`     | `True`   | `int`    | 安排会议结束的时间 |

- `subject`：会议主题

- `passwrod`：入会密码，如果不需要设置密码，则置为""
- `startedAt`: 预定会议，填写预定会议开始的时间
- `endAt`:预定的结束时间

**用户系统内部产生的信息：**

- conferenceID：随机产生的9位数字
- 把基本的会议信息写入table conference中。



#### 3.加入会议

**websocket访问ion**

如果header中有jwt就表明是登陆状态下的 加入会议 ； 如果header中没有带jwt的话，表明是游客状态下的 加入会议。其他的参数一致。



#### Params

```
http://localhost:8443/ws\?peer=6b885d280a8894e9b758a78fb45f6f57474aec2cc303c44a70e819545c64cce7
```

如果登录状态下是需要在header上带`jwt`，如果是游客访问则不用。

`peer`是uid，游客的peer id是前端随机生成。（可以加上前缀`guest-`表明为游客）



websocket建立之后，就和以前一样访问ion 的方法。

request : `join`

join message：

```json
{
  "rid": "%s", 
  "uid": "%s", 
  "info": {
    "name": "%s"
  	},
  "pwd":"%s"
}
```

- rid:会议id号
- uid：用户id
- name：会议中的昵称
- pwd：会议密码



**Join response:**

原版本：成功返回data为空；重构为：成功返回token和topic，目的是为了在会议中使用tinode的群聊系统。

```json
Received: {"response":true,"id":5412080,"ok":true,"data":{"jwt":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MDM0MzQ0NTYsInJpZCI6IjczMjQ3OTg2MDUiLCJvcmlfYXQiOjE2MDM0MzgwMjYsInRvcGljIjoiM2JkNGI5YjQtYTYzOC00MTU2LTg3OGQtZjNkOGRhZDM2ZGQ5In0.ysLkVEMhrzBUn0815m2akow2KSaPyK4d8ebJOVlK3e4","topic":"3bd4b9b4-a638-4156-878d-f3d8dad36dd9"}}
```

错误码：

```
(-200, "this conference invalid")
(-201, "no this conference")
(-202, "this conference has been closed")
(-203, "this format of the conference invalid")
(-206, "waiting for host joining")
(-205, "please input conference id")
(-204, "please input password")
(-204, "wrong password")
(-207, "joining waiting room")

```





#### 4.结束会议 & 离开会议

**websocket访问ion**

request : `leave`

**描述：**

- 无论主持人还是成员离开会议后，都可以根据**会议号**，回到会议；
- 回到会议后主持人还是原来的主持人；
- 如果该会议室已经没有人则自动结束该会议（无法再根据此会议号进入该会议）
- 结束会议，则直接结束（WIP）



#### 5.接收设备状态信息

**websocket访问ion**

Request : `broadcast-device-state`

客户端发送自己的设备状态信息（打开或者关闭摄像头和麦克风）到服务端，进行会议中所有人的设备信息更新。
```json
{
	{
	    "rid":"6632768995",
        "uid":5fda05f08d03395ac8664e50c35309ade290e9e55c2058106ef4c9c9002d1401,
    } 
    info:{
   	    {
   	    	microphone:true, 
   	    	camera:true,
   		}
   	}
 }
```

#### 6.返回参会人列表

**websocket访问ion**

触发条件：当有用户加入或者有用户离开的时候或者用户改变摄像头和麦克风的关闭开启状态的时候，服务端会广播全会议中的全体成员。
体现在代码逻辑上：

- client send message{join} ==> server receive and notify{broadcast-participant} everyone 
- client send message{leave} ==> server receive and notify{broadcast-participant} everyone  
- client send message{broadcast-device-state} ==> server receive and notify{broadcast-participant} everyone 

notify:`broadcast-participant`

```json
{
	"notification":true,
	"method":"broadcast-participant",
	"data":
		{
			"participants":
				{
					"6fcae90e3ff4969c138c1ed4706e8424c20b9c4bddebdfb226dc6694ce15ad74":
						{
							"info":{
							"avatar":"http://*************:5000/templates/avatar/default_avatar.png",
							"name":"logici",
							"role":"participant"
							},
							"rid":"1507111819",
							"uid":"6fcae90e3ff4969c138c1ed4706e8424c20b9c4bddebdfb226dc6694ce15ad74"},
					"b5cf60fe-ab7d-4822-a457-6af8513a0b73":
						{
							"info":{
							"avatar":"http://*************:5000/templates/avatar/default_avatar.png",
							"name":"s1",
							"role":"guest"
							},
							"rid":"1507111819",
							"uid":"b5cf60fe-ab7d-4822-a457-6af8513a0b73"
							}
				},
				"rid":"1507111819",
				"uid":""
		}
}
```



#### 7.等候室

**websocket访问ion**

**等候室场景：**

1. 关闭等候室，主持人未到
   1. 用户可先于主持人进入（打开先于主持人进入功能）；
   2. 用户等待主持人，在主持人进入后，会有通知给用户说，主持人到了，用户进入房间，客户端可发送publish
2. 打开等候室，主持人未到
   1. 用户不可先于主持人进入，即使打开了`先于主持人进入`功能
   2. 用户等待主持人，在主持人进入后，会有通知给用户说，主持人到了，用户进入等候室，进入等候室页面；
      1. 此时，会有等候室列表通知主持人，主持人可同意或者拒绝某用户的入会
      2. 主持人发送request到后台，根据request的accept或者reject来通知此用户，他被同意还是拒绝了；如果是同意，则后台该用户离开等候室，进入正式的开会房间，客户端发送pubish，切换入会页面，；如果是被拒绝，则后台该用户离开等候室，客户端切换到主页面

**设置模块：**

1. 个人会议号设置
2. 安排会议设置
3. 会议中设置



**通信交互模块：**

**1.Client TO ion**

**judgejoining** : 主持人判断会议室中的某人能否入会

```json
{"rid": "%s", "uid": "%s", "waiter": "%s","accept":false}
```

rid: 房间号

uid：本人id

waiter：等候室中的某人

accept：true，同意此人加入会议；false，拒绝此人加入会议



**leave-waitingroom**：等候中的某人离开等待

```
{"rid": "%s", "uid": "%s"}
```

rid: 房间号

uid：本人id



**2.Ion TO client**

**host-coming**

监听host-coming

**~~旧版本:~~**

有两种情况：

1. 如果主持人打开了等候室，参会者客户端需要从等待主持人界面跳到等候室界面
2. 如果主持人关闭了等候室，参会者客户端可以直接加入会议，发送publish指令

```
{"notification":true,"method":"host-coming","data":{"code":200,"onwaitingroom":false}}
```

判断的依据：

"onwaitingroom":false-关闭等候室状态；true-打开等候室状态



**重构版（现版本）：**

有两种情况：

1. 如果主持人打开了等候室，参会者客户端需要从等待主持人界面跳到等候室界面.code=-207
2. 如果主持人关闭了等候室，参会者客户端可以直接加入会议，发送publish指令.code=200

```json
{"notification":true,"method":"host-coming","data":{"code":200,"jwt":"xxxx","topic":"sss"}}
```

code=200,jwt和topic

code=-207，jwt="",topic=""





**allow-joining**

监听allow-joining

**~~旧版本:~~**

```
{"notification":true,"method":"allow-joining","data":{"allow":true}}
```

等候室的用户接收到这个通知，"allow":true表明主持人同意入会，则客户端可发送publish指令；false表明主持人拒接，客户端回到主页面

**重构版（现版本）：**

```json
{"notification":true,"method":"allow-joining","data":{"allow":true,"jwt":"xxxx","topic":"xxx"}}
```

等候室的用户接收到这个通知，"allow":true表明主持人同意入会，则客户端可发送publish指令；false表明主持人拒接，客户端回到主页面

Allow:true时，jwt和topic

Allow:false时，jwt="",topic=""





**waiters-list**

```
{"notification":true,"method":"waiters-list","data":{"uid":"username"}}
```

监听的方法：waiters-list

主持人能拿到等候室中的人的名单



#### 8.历史会议

**http 访问用户系统**

通过index和limit进行刷新历史会议记录。

```
GET /api/v1/conference/history?index=0&limit=10
```

index: 前端提供的序号，作为数据库索引的序号

limit:前端提供的长度限制，作为数据库的限制的长度

**Response**

```json
{
    "message": [
        {
            "conferenceid": "7847581827",
            "createdAt": 1603870039080,
            "duration": 77276,
            "host": "usrA0CHNDQrKUQ",
            "joinAt": 1603870039092,
            "leaveAt": 1603870116368,
            "subject": "usrA0CHNDQrKUQ的快速会议"
        },
        {
            "conferenceid": "944269223",
            "createdAt": 1603869379376,
            "duration": 3672,
            "host": "你好",
            "joinAt": 1603869375733,
            "leaveAt": 1603869379405,
            "subject": "你好的快速会议"
        },
      ...
    ]
}
```



8.1 历史会议删除

8.2 历史会议搜索



#### 9.会议设置

1. 会议前设置(**http 访问用户系统** )

提供个人会议相关的设置：密码，会议前的一些设置，比如等候室。

```
POST
```



2. 会议中设置( **websocket 访问ion**)

Request : `host-setting`

**请求参数**

```json
{
  "request":true,
  "id":6829374,
  "method":"host-setting",
  "data":{
    "rid":"7847581827",
    "uid":"usrA0CHNDQrKUQ",
    "settings":{
      "waitingroom":false,
      "muteall":false,
      "lockroom":false,
      "screen":false
    }
  }
}
```

waitingroom：等候室

muteall：全体静音

lockroom：锁住会议

screen：共享屏幕

**Response**

```

```



#### 10.全体静音

websocket访问ion

功能描述：

1. 允许参会者自我解除静音，muteAllLock
2. 正常情况下，发送这个请求，客户端会自行静音/解除静音，能否自我解除是通过muteAllLock来决定的
3. 如果使用同一个接口来实现静音/解除静音，需要多加一个字段来表明是静音还是解除静音
4. 单个成员的静音/解除静音，如果使用同一个接口来实现，则需在加一个字段，表明需要静音/解除静音的成员
5. 有声音=》静音，主持人可强制使其生效；静音=》有声音，需要询问当事人，可以不解除静音；

主持人权限

client TO ion

请求格式:muteAll

```json
Send data: {"request":true,"id":1887914,"method":"muteAll","data":{"rid":"7324798605","uid":"usrAEaXz-R9i9I","muteAll":true,"muteAllLock":true}}
```

请求响应：

```json
Received: {"response":true,"id":1887914,"ok":true,"data":{}}
```



ion To client

通知除主持人以外的全体成员，通知格式:broadcast-muteall

```json
Received: {"notification":true,"method":"broadcast-muteall","data":{"rid":"7324798605","uid":"usrAEaXz-R9i9I","muteAllLock":true}}
```



#### 11.会议加锁

websocket访问ion

主持人权限

此设置默认为关闭状态

加入会议时的response，错误码为-209

业务逻辑：

1. 检查是否存在此会议
2. 检查是否需要密码和密码是否正确
3. 检查是否锁定会议
4. 检查先于主持人加入和等候室
5. 成功加入会议



#### 12.更换主持人

websocket访问ion

主持人权限

功能逻辑：

选取一个参与者，更换为主持人，自身身份由主持人变为参与者

client TO ion

请求格式:changeHost

```
{"request":true,"id":5509733,"method":"changeHost","data":{"rid":"7324798605","uid":"usrAEaXz-R9i9I","participant":"usrfI4rxe_AXbY"}}
```

更换后，会广播一次参会者列表；如果有等候者的话，会广播等候者列表。



#### 13.移除某人

websocket访问ion

主持人权限

功能逻辑：

选取一个参与者，移出这个房间。



#### 14.共享屏幕开关

websocket访问ion

主持人权限

功能逻辑：

true：允许会议中的所有人共享屏幕

false：仅允许会议主持人共享屏幕

publish：reponse -210 没有权限共享屏幕







重构一：

1.连接：

- 是否携带jwt访问join，用来区分游客和登陆

- 无论是否携带jwt，都会建立websocket连接

- 携带jwt的，会在request.context上带上一个参数，role:participator；没有jwt的，会有role:guest

- 携带jwt的方式：url或者header

2.通信：

- 首先需要，request join，在join的message中，需要有roomid，uid，（或者password，带不带password需要根据此roomid是否设置了password）
- 后端会需要roomid进行查询，无论是查表还是查内存，会得到一个结果：
  - 根据roomid可以得出会议的状态（关闭，启动或者不存在），个人在此会议中的状态（会议中，离会中，会议关闭），来综合判断用户加入的会议是否合法；response给前端的错误信息有：
    - 此会议已经关闭
    - 无此会议
    - 等待主持人开启会议==》在输入PMI而此PMI的主人还未开启会议的时候出现，**腾讯会议出现这个提示，而zoom是可以进入PMI的房间**
    - 现zoom，关闭和不存在统一为无效id
  - 根据roomid可以得出主持人（hostid），和发起join message的用户的uid是否一致，如果一致，则表明是主持人，password部分直接跳过，就到原始join的操作；
  - 非主持人的用户，根据roomid可知此会议是否设置了密码，如果没有密码，则直接到原始join的操作；如果设置了密码，需要填写密码，所以如果此时的前端发送的join message中没有password，会response一个信息：对应的错误码和信息，前端可以根据此信息，弹出密码框或者请用户重新填写正确的密码；
  - join成功后，更新参会时间；leave成功后，更新离会时间
  - 至此，所有用户，无论是主持人还是普通参会者还是游客，通过某些证明，都可以有机会参入此会议
- 前端可以和正常那样request 其他method到ion中；
- 如果前面的某些证明没有通过（password或者会议id关闭或者无效），都会建立起websocket连接，但是进入不了所输入的会议中，一直keepalive
- 因为会有websocket一直连接的可能，所以，在游客状态=》登陆状态，需要确保websocket关闭掉，才能进行登陆；在登陆状态=》登出状态，需要确保websocket关闭，才能进行登出操作



重构二：

3.尽量减少数据表的操作，如何在满足：

- 写操作：数据更新=》加会时间，离会时间，会议状态，个人在某会议的状态
- 读操作：读取会议的状态，读取会议主持人id，读取会议密码

加上redis作为一层缓存，redis-mysql的读取方案为：

- join message拿到数据rid和uid，
- 根据rid生成key，查找redis缓存中有无此数据，
- 如果没有，则查找mysql数据库；
- 如果没有则上报错误，并返回对应错误码发送给前端；
- 如果查找mysql数据库命中，则做相应的处理，并把需要的数据字段作为value和key保存到redis中。
- 其他人加入会议则可以从缓存中拿到数据，比如说：该会议的hostid，pwd，salt。
- 在会议结束后，会更新mysql中的status字段，并删除redis缓存。这里还是涉及mysql redis读写一致性的问题，但现在这个业务场景，碰到的概率比较小，所以暂时没有处理

~~**（WIP）写事务：**在结束会议的时候，可能会出现集中地对同一个会议里的所有参会者，更新离会时间，可能会产生大量的写操作，但是此更新是不会对其他地方的业务产生影响的，所以，打算采取事务方式，把多次写操作归为一次操作。~~



重构三：

现在table participator中，存放的是用户的参会信息：参会时间，离会时间。因为个人会议号（PMI）的可重复性，所以索引的时候，加上了status的字段，来标志正在开启的会议，因为一个会议id只能有一个处于开启中，即：index conferenceid，uid，status才能定位到一条记录。

想重构成：

table conference中，添加number 编号字段，来作为PMI的区分；

table participator中，添加number 编号字段，区分PMI；

使用conference number字段，在redis的缓存中也添加上这个字段，那么在update participator时，可以进行判断，如果是PMI，可以使用number来index；如果是普通的id，可以使用conferenceid来index。

这样使用的效果，就可以达到：

- 每一次join-leave是一次新的记录，现在的实现是，join-leave一次，如果此会议还未结束的话会刷新上一次的join-leave记录
- 获取每个用户的参会记录的时候，更方便，先索引table participator 获取此uid对应的所有记录，然后根据number和conferenceid索引table conference 就能得到每次会议的详细信息。



重构四：

目前的PMI实现是，只要是创建了的PMI会议，就可以允许任何人加入（如果密码正确的话）。

改成，无论是PMI还是普通会议室，都需要有主持人加入，其他人才能进入。

安排会议：安排多次会议，无论是哪一个安排的会议，都得等主持人加入。

1. 无论是PMI还是普通会议号或者预定会议都需要主持人先加入才允许其他人加入
2. 可预约多个会议，这几个预约的会议都是主持人可以随意加入的，但是同时只能加入一个。
3. 预定会议的关闭是，根据时间进行关闭的，就算是会议拥有者点击结束，也可以再次加入。
4. 可以拥有多个会议，但是如果有其他人加入了某个你所拥有的会议，那么不能再加入这个已经预定的会议和创建新会议。
5. 离开会议后，可加入其他非自己拥有的会议
6. owner和host是两个概念，owner是这个会议的创建者，主持人不一定是创建者，主持人可以变化，拥有者是固定不变的

joinGate:

setting 拿个人会议号的设置，包括密码

1. 会议存在（判断launching）

   1. 判断输入的rid是否有效（是否存在或者已经作废，作废是针对普通会议号，输入PMI的话，主要没有状态是launching的pmi，就需要等待主持人的进入）：

      1. ```
         if rid == pmi{
           // 检查存在
         	check user table pmi exist?
         	if not return invalid rid
         	// 检查是否launching
         	check conference table pmi & launching exist?
         	// if not 跳转输入密码界面，如果正确则 return client waiting
         }else if rid == 普通会议号{
         	// 普通会议不launching就是无效的
         	check conference table launching rid & launching exist?
         	// if not return invalid
         }
         ```

         

2. 检查密码（判断password）

   1. 预约会议是普通会议号，可以拿到密码
   2. 快速会议：使用PMI的话，可以根据提前的设置拿到
   3. 快速会议：使用普通会议号，无密码，无会议前的设置

3. 主持人存在（判断主持人是否在room，不一定是owner，但是第一个进去的一定是owner）

   1. client waiting 界面
   2. 只有会议开启的时候出现主持人不存在，只要owner进去之后，默认为主持人，主持人离开需要把主持人交给其他人，或者随机给一个人。
   3. check room has host？

4. 等候室（等待主持人接受）

   1. 等候室流程





PMI场景（只能快速创建或者直接加入）：

- 快速创建：
  - PMI拥有者立即启动一个PMI会议
  - 其他人可直接通过密码加入；因为这个流程，主持人（拥有者）已经在此会议中了
- 其他人直接加入：
  - 此场景，主持人还未在PMI中，需要输入密码并等待

1. PMI中主持人不在，其他人加入，需要先填写密码，再等待PMI所有者进入；
2. PMI中主持人不在，PMI拥有者加入，创建PMI，进入会议；通知等待的人，如果没有等候室就直接进入会议了；如果开启了等候室，就进入等候室，等待下一步的通知；
3. PMI有主持人在，其他人可以输入密码加入，PMI所有者可以直接加入；但是PMI所有者不能新创建，可以预定新会议；如果是新创建的PMI，则是加入现有的这个PMI

普通会议号场景：

1. 快速创建：无密码，无会议前设置（比如等候室）。拥有者直接进入，并且成为会议主持人。等待其他人进入
2. 预定会议：可进行设置。拥有者可以暂时不进入。如果会议中无主持人，其他人进入需要等待会议拥有者进入；会议拥有者可根据“加入会议”或者“开始会议”（在预定会议列表中有选项），直接进入会议，成为主持人，并通知其他等待的成员；如果没有开启等候室，则直接进入会议，如果开启了等候室，则进入等候室等待。



暂时，约定的会议，close掉之后，也不能再加入了。

预定会议的设置，没人进去前，拥有者是可以设置的，而一旦有其他人进入就不能修改设置了，也不能取消预定了。

预定会议过了预定时间的期限，就只能选择加入会议或者取消会议，在会议中的手动结束会议，是不会真正的关闭会议的，如果一直不取消会议，一定时间后就会自动取消。

如果PMI拥有者设置了允许主持人先进去，其他人加入并且密码正确的话，可以使用此人的PMI开会，但是没有主持人的权限。







