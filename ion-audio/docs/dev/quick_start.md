## QuickStart

## 1. Install Deps

`./scripts/installDeps.sh`

It will install all depend modules

## 2. Make Key

`./scripts/makeKey.sh`

It will generate key files to configs

## 3. Run

First Time

`./scripts/allStart.sh`

Next Time

`./scripts/allRestart.sh`

It will start all services we need

*There is also individual module script in scripts folder, you can use them when you debug individual modul*

## 4. Test 

 `https://localhost:8080`

Open it with chrome, let's chat

