name: go-mod-fix
on:
  push:
    branches:
      - renovate/*

jobs:
  go-mod-fix:
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 2
      - name: fix
        uses: at-wat/go-sum-fix-action@v0
        with:
          git_user: <PERSON><PERSON>
          git_email: <EMAIL>
          github_token: ${{ secrets.GITHUB_TOKEN }}
          commit_style: squash
          push: force
