package signal

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/cloudwebrtc/go-protoo/peer"
	conf "github.com/pion/ion/pkg/conf/biz"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
)

type AcceptFunc peer.AcceptFunc
type RejectFunc peer.RejectFunc
type RespondFunc peer.RespondFunc
type BizEntry func(method string, peer *Peer, msg json.RawMessage, claims *Claims, accept RespondFunc, reject RejectFunc)
type BizTinodeEntry func(topic, uid string, method int)

const (
	errInvalidMethod     = "method not found"
	errInvalidData       = "data not found"
	statCycle            = time.Second * 3
	statWaitingRoomCycle = time.Second * 5
	JoinExpired          = time.Hour * 1
	JoinRefresh          = time.Hour * 2
)

var (
	secretKey             = conf.Auth.SecretKey
	bizCall               BizEntry
	tinodeCall            BizTinodeEntry
	rooms                 = make(map[proto.RID]*Room)
	roomLock              sync.RWMutex
	allowClientDisconnect bool
	AllowRedisCache       bool
)

// Init biz signaling
func Init(conf WebSocketServerConfig, allowDisconnected bool, allowCache bool, bizEntry BizEntry, tinode BizTinodeEntry) {
	bizCall = bizEntry
	tinodeCall = tinode
	allowClientDisconnect = allowDisconnected
	AllowRedisCache = allowCache
	go stat()
	go statWaiting()
	go func() {
		panic(NewWebSocketServer(conf, in))
	}()

}

func stat() {
	t := time.NewTicker(statCycle)
	defer t.Stop()
	for range t.C {
		info := "\n----------------signal-----------------\n"
		print := false
		roomLock.Lock()
		if len(rooms) > 0 {
			print = true
		}
		for rid, room := range rooms {
			info += fmt.Sprintf("room: %s\npeers: %d\n", rid, len(room.GetPeers()))
			if len(room.GetPeers()) == 0 {
				delete(rooms, rid)
				room = nil
				waitingroomLook.Lock()
				// DelPartlist(rid)
				if waiting, ok := waitingRooms[rid]; ok {
					fmt.Println("waiting:", waiting)
					waiting = nil
					delete(waitingRooms, rid)
				}
				waitingroomLook.Unlock()
			}
		}
		roomLock.Unlock()
		if print {
			log.Infof(info)
		}
	}
}
