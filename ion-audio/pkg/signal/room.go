package signal

import (
	"github.com/cloudwebrtc/go-protoo/peer"
	"github.com/cloudwebrtc/go-protoo/room"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
)

type Room struct {
	room.Room
	// room setting layer
	roomSetting
	// user info layer
	PeerInfos
	// the topic is used to create group chat in the meeting with tinode
	Topic string
	// the comference owner
	Owner string
}

func (r *Room) AddPeer(peer *Peer) {
	r.Room.AddPeer(&peer.Peer)
	info := NewPeerInfo(peer, r.ID())
	r.PeerInfos.AddPeerInfo(info)
}

func (r *Room) ID() proto.RID {
	return proto.RID(r.Room.ID())
}

func (r *Room) LeaveRoom(rid proto.RID, uid string) {
	r.RemovePeer(uid)
	r.Lock()
	hostLeave := r.PeerInfos.RemovePeerInfo(uid)
	r.Unlock()
	if hostLeave {
		HostLeave(rid)
	}
}

func newRoom(id proto.RID, topic, owner string) *Room {
	r := &Room{
		Room:      *room.NewRoom(string(id)),
		PeerInfos: *NewPeersInfo(),
		Topic:     topic,
		Owner:     owner,
	}
	r.InitRoomSetting(id)
	roomLock.Lock()
	rooms[id] = r
	roomLock.Unlock()
	return r
}

func getRoom(id proto.RID) *Room {
	roomLock.RLock()
	r := rooms[id]
	roomLock.RUnlock()
	log.Debugf("getRoom %v", r)
	return r
}

func delRoom(rid proto.RID) {
	roomLock.Lock()
	r := rooms[rid]
	if r != nil {
		r.Close()
	}
	delete(rooms, rid)
	r = nil
	roomLock.Unlock()
}

func DelRoom(rid proto.RID) {
	delRoom(rid)
	deleteWaitingRoom(rid)
}

// one peer in one room
func GetRoomByPeer(id string) *Room {
	roomLock.RLock()
	defer roomLock.RUnlock()
	for _, room := range rooms {
		if room == nil {
			continue
		}
		if peer := room.GetPeer(id); peer != nil {
			return room
		}
	}
	return nil
}

// one peer in many room
func GetRoomsByPeer(id string) []*Room {
	var r []*Room
	roomLock.RLock()
	defer roomLock.RUnlock()
	for _, room := range rooms {
		//log.Debugf("signal.GetRoomsByPeer rid=%v id=%v", rid, id)
		if room == nil {
			continue
		}
		if peer := room.GetPeer(id); peer != nil {
			r = append(r, room)
		}
	}
	return r
}

func DelPeer(rid proto.RID, id string) {
	log.Infof("DelPeer rid=%s id=%s", rid, id)
	room := getRoom(rid)
	if room != nil {
		room.LeaveRoom(rid, id)
	}
}

func AddPeer(rid proto.RID, peer *Peer, topic, owner string) {
	log.Infof("AddPeer rid=%s peer.ID=%s", rid, peer.ID())
	room := getRoom(rid)
	if room == nil {
		room = newRoom(rid, topic, owner)
	}
	room.AddPeer(peer)
	// room.AddTopic(topic)
}

func (r *Room) AddTopic(topic string) {
	if topic != "" && r != nil {
		r.Lock()
		r.Topic = topic
		r.Unlock()
	}
}

func GetTopic(rid proto.RID) string {
	room := getRoom(rid)
	if room != nil {
		return room.Topic
	}
	return ""
}

func HasPeer(rid proto.RID, peer *Peer) bool {
	log.Debugf("HasPeer rid=%s peer.ID=%s", rid, peer.ID())
	room := getRoom(rid)
	if room == nil {
		return false
	}
	return room.GetPeer(peer.ID()) != nil
}

func HasPeerAndDel(rid proto.RID, id string) {
	log.Debugf("HasPeer rid=%s peer.ID=%s", rid, id)
	room := getRoom(rid)
	if room == nil {
		return
	}
	if peer := room.GetPeer(id); peer != nil {
		DelPeer(rid, id)
		peer.Close()
	}
}

func NotifyAllWithoutPeer(rid proto.RID, peer *Peer, method string, msg interface{}) {
	log.Debugf("signal.NotifyAllWithoutPeer rid=%s peer.ID=%s method=%s msg=%v", rid, peer.ID(), method, msg)
	room := getRoom(rid)
	if room != nil {
		log.Debugf("room %s Notify method=%s msg=%v", rid, method, msg)
		room.Notify(&peer.Peer, method, msg)
	}
}

func NotifyAll(rid proto.RID, method string, msg interface{}) {
	room := getRoom(rid)
	if room != nil {
		room.Map(func(id string, peer *peer.Peer) {
			if peer != nil {
				peer.Notify(method, msg)
			}
		})
	}
}

func NotifyAllWithoutID(rid proto.RID, skipID proto.UID, method string, msg interface{}) {
	room := getRoom(rid)
	log.Infof("notify message room : %v ,msg : %v", rid, msg)
	if room != nil {
		room.Map(func(id string, peer *peer.Peer) {
			log.Infof("peer => %v, peer.ID => %v, skipId => %v, method => $v", peer, peer.ID(), skipID, method)
			if peer != nil && proto.UID(peer.ID()) != skipID {
				peer.Notify(method, msg)
			}
		})
	}
}

func IsOwner(rid proto.RID, uid proto.UID) bool {
	room := getRoom(rid)
	if room != nil && room.Owner == string(uid) {
		return true
	}
	return false
}

func NotifySomeone(rid proto.RID, uid proto.UID, method string, msg interface{}) {
	room := getRoom(rid)
	if room != nil {
		peer := room.GetPeer(string(uid))
		peer.Notify(method, msg)
	}

}
