package signal

import (
	"context"
	"errors"
	"net/http"
	"net/url"
	"strings"

	"github.com/cloudwebrtc/go-protoo/peer"
	"github.com/dgrijalva/jwt-go"
	conf "github.com/pion/ion/pkg/conf/biz"
	"github.com/pion/ion/pkg/entity"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
	"github.com/pion/ion/pkg/util"
)

type contextKey struct {
	name string
}

var claimsCtxKey = &contextKey{"claims"}

const invalidPeerId = "Invalid peer id"

const (
	Guest       = "guest"
	Host        = "host"
	Participant = "participant"
	CoHost      = "co-host"
)

const (
	closeGroup = 1
)

const (
	defaultAvatar = "/templates/avatar/default_avatar.png"
	// domain        = "http://*************:5000"
)

var domain = conf.Domain.Name

// getToken get token from header
func getToken(r *http.Request) string {
	cookie, err := r.<PERSON>("jwt")
	if err != nil {
		return ""
	}
	tokenStr, _ := url.QueryUnescape(cookie.Value)
	log.Infof("url.QueryUnescape cookie: %v", tokenStr)
	return tokenStr

}

// getClaims verify token
func getClaims(r *http.Request) (*Claims, error) {
	log.Debugf("Authenticating token")
	tokenStr := getToken(r)
	if tokenStr == "" {
		return nil, nil
	}

	// Passing nil for keyFunc, since token is expected to be already verified (by a proxy)
	log.Infof("tokenStr : %v ", tokenStr)
	token, err := jwt.ParseWithClaims(tokenStr, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})
	if err != nil {
		log.Infof("ParseWithClaims err :%v", err)
		return nil, err
	}
	if err := token.Claims.Valid(); err != nil {
		log.Infof("Claims.Valid() err :%v", err)
		return nil, errors.New("invalid token")
	}
	return token.Claims.(*Claims), nil
}

// ForContext finds the request claims from the context.
func ForContext(ctx context.Context) *Claims {
	raw, _ := ctx.Value(claimsCtxKey).(*Claims)
	return raw
}

// GetPeerOnRoom return peer on the room
func GetPeerOnRoom(rid proto.RID) map[string]*peer.Peer {
	room := getRoom(rid)
	if room == nil {
		return nil
	}
	peer := room.GetPeers()
	log.Infof("room: %s\npeers: %d\n", rid, len(peer))
	return peer
}

// claimToPeer take claim value to peer
func claimToPeer(peer *Peer, claims *Claims) error {
	peer.AvatarUrl = domain + defaultAvatar
	if claims == nil {
		peer.Role = Guest
		return nil
	}
	uid := peer.ID()
	decodedID, err := entity.DecodeUser(uid)
	if err != nil {
		log.Errorf("decode use failed", err)
		reject(-1, err.Error())
		return err
	}
	if uid != string(claims.Uid) {
		log.Errorf("peerid:%v, token uid: %v", uid, string(claims.Uid))
		reject(-1, invalidPeerId)
		return errors.New("invalid user")
	}

	// get avatar url
	user := entity.QueryUser(&entity.User{Uid: decodedID})
	if user.Uid != 0 {
		avatarUrl := user.GetPublicField("avatarUrl").(string)
		if !strings.HasPrefix(avatarUrl, "http") {
			avatarUrl = domain + avatarUrl
		}
		peer.AvatarUrl = avatarUrl
		return nil
	} else {
		return errors.New("invalid user")
	}

}

// update leaveAt for participator table when leave in meeting
func updateLeaveAt(peer *Peer, rid string) {
	uid := peer.ID()
	decodedID, _ := entity.DecodeUser(uid)
	par := entity.QueryParticipator(&entity.Participator{ParticipatorID: decodedID, ConferenceID: rid, Status: entity.Meeting})
	if par.ConferenceID != "" {
		par.LeaveAt = util.GetCurrentMs()
		par.Status = entity.Leaving
		par.Duration = par.Duration + par.LeaveAt - par.JoinAt
		par.UpdateParticipator(*par)
	}
}

// leave conference
func leaveConference(peer *Peer, rid proto.RID) {
	count := len(GetPeerOnRoom(rid))
	roomid := string(rid)
	updateLeaveAt(peer, roomid)
	if count < 1 {
		// no any one. So close conference
		updateClose(roomid)
		closeAllLeavedPart(roomid)
		NotiftAllWaiters(rid, false)
		tinodeCall(GetTopic(rid), peer.ID(), closeGroup)
		DelRoom(rid)
	}

}

// updateClose close meeting
func updateClose(rid string) {
	con := entity.QueryConference(&entity.Conference{ConferenceID: string(rid), Status: entity.Launching})
	if con.ConferenceID != "" {
		con.Status = entity.Closed
		con.EndAt = util.GetCurrentMs()
		con.UpdateConference(*con)
	}
	// del redis cache => read write sync WIP
	if AllowRedisCache {
		entity.DelConferenceKey(entity.FieldConference + rid)
	}
	return
}

// close all leaved participator table
func closeAllLeavedPart(roomid string) {
	if err := entity.CloseAllLeavedPart(roomid); err != nil {
		log.Errorf("close participator err:%v", err)
	}
}
