package signal

import (
	nprotoo "github.com/cloudwebrtc/nats-protoo"
	"github.com/pion/ion/pkg/entity"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
	"github.com/pion/ion/pkg/util"
)

// about room setting
type roomSetting struct {
	Subject       string
	Number        string
	OnWaitingRoom bool // true:every one join this room and it need host allow
	OnMuteAllLock bool
	OnMuteAll     bool
	OnLockRoom    bool // true:every one do not join this room except the onwer.
	OnScreen      bool // true:only host can share his screen. false:every one can share his screen.
}

// InitRoomSetting init room setting struct
func (setting *roomSetting) InitRoomSetting(id proto.RID) {
	rid := string(id)

	con := entity.QueryConference(&entity.Conference{ConferenceID: rid, Status: entity.Launching})
	if con.ConferenceID != "" {
		setting.OnWaitingRoom = con.OnWaitingRoom
		setting.Subject = con.Subject
		setting.Number = con.Number
		setting.OnMuteAllLock = false
		setting.OnLockRoom = false
		setting.OnScreen = false
		setting.OnMuteAll = false
		setting.OnMuteAllLock = false
	}

	log.Infof("init room setting: %v", setting)
}

// SetRoomSetting set room function
func (setting *roomSetting) SetRoomSetting(msg *proto.SettingMsg) {
	setting.OnWaitingRoom = msg.Settings.OnWaitingRoom
	setting.OnMuteAll = msg.Settings.OnMuteAll
	setting.OnLockRoom = msg.Settings.OnLockRoom
	setting.OnScreen = msg.Settings.OnScreen
	// setting.OnMuteAllLock = msg.Settings.OnMuteAllLock
}

// GetWaitingRoomSetting get state of waiting room
func (setting *roomSetting) GetWaitingRoomSetting() bool {
	log.Infof("GetWaitingRoomSetting: %v", setting)
	return setting.OnWaitingRoom
}

// GetSubject get the subject of conference
func (setting *roomSetting) GetSubject() string {
	log.Infof("GetSubject %v", setting.Subject)
	return setting.Subject
}

func (setting *roomSetting) GetNumber() string {
	log.Infof("GetNumber %v", setting.Number)
	return setting.Number
}

func (setting *roomSetting) GetRoomLock() bool {
	log.Infof("GetRoomLock %v", setting.OnLockRoom)
	return setting.OnLockRoom
}

func (setting *roomSetting) GetScreen() bool {
	log.Infof("GetScreen %v", setting.OnScreen)
	return setting.OnScreen
}

func (setting *roomSetting) SetMuteAll(msg proto.MuteAllMsg) {
	log.Infof("SetMuteAll")
	setting.OnMuteAll = msg.MuteAll
	setting.OnMuteAllLock = msg.MuteAllLock
}

// GetNumber get conference number
func GetNumber(rid proto.RID) string {
	room := getRoom(rid)
	var number string
	if room != nil {
		number = room.GetNumber()
	}
	return number
}

// GetWaitingRoomSetting get waiting room setting
func GetWaitingRoomSetting(rid proto.RID) (bool, bool) {
	room := getRoom(rid)
	if room != nil {
		return true, room.GetWaitingRoomSetting()
	}
	return false, false
}

// SetRoomSetting set room function
func SetRoomSetting(rid proto.RID, msg *proto.SettingMsg) {
	room := getRoom(rid)
	if room != nil {
		room.Lock()
		room.SetRoomSetting(msg)
		room.Unlock()
	}
	log.Infof("SetRoomSetting:%v", room.roomSetting)
}

func GetRoomLock(rid proto.RID) bool {
	room := getRoom(rid)
	var lock bool
	if room != nil {
		room.Lock()
		lock = room.GetRoomLock()
		room.Unlock()
	}
	return lock
}

func GetScreen(rid proto.RID) bool {
	room := getRoom(rid)
	var screen bool
	if room != nil {
		room.Lock()
		screen = room.GetScreen()
		room.Unlock()
	}
	return screen
}

func SetMuteAll(rid proto.RID, msg proto.MuteAllMsg) {
	room := getRoom(rid)
	if room != nil {
		room.Lock()
		room.SetMuteAll(msg)
		room.Unlock()
	}
}

func GetMuteAll(rid proto.RID) (muteall bool, muteallLock bool) {
	room := getRoom(rid)
	if room != nil {
		room.Lock()
		muteall = room.OnMuteAll
		muteallLock = room.OnMuteAllLock
		room.Unlock()
		return
	}
	return
}

func getRoomSetting(rid proto.RID) (proto.RoomSetting, *nprotoo.Error) {
	room := getRoom(rid)
	settingmsg := proto.RoomSetting{}
	if room != nil {
		room.Lock()
		settingmsg = proto.RoomSetting{
			OnWaitingRoom: room.OnWaitingRoom,
			OnMuteAll:     room.OnMuteAll,
			OnLockRoom:    room.OnLockRoom,
			OnScreen:      room.OnScreen,
			// OnMuteAllLock: room.OnMuteAllLock,
		}
		room.Unlock()
		return settingmsg, nil
	}
	return settingmsg, util.NewNpError(-4, "room not found.")
}

// The host UID is the target peer. The notify host setting should comes from biz.client
func PushRoomSettingMsg(hostUID proto.UID, rid proto.RID) *nprotoo.Error {
	// Get the RoomSetting Msg. If success, send msg.
	if msg, err := getRoomSetting(rid); err == nil {
		// Construct the Setting Msg.
		notifySettingMsg := proto.SettingMsg{
			RoomInfo: proto.RoomInfo{RID: rid, UID: hostUID},
			Settings: msg,
		}
		// Notify the new host.
		log.Infof("notifyHostSetting request:", notifySettingMsg)

		// Validate
		if notifySettingMsg.RID == "" {
			return util.NewNpError(-4, "room not found")
		}

		NotifySomeone(notifySettingMsg.RID, notifySettingMsg.UID, proto.NotifyHostSetting, notifySettingMsg)
		return nil
	} else {
		return err
	}
}
