package signal

import (
	"github.com/pion/ion/pkg/proto"
)

type PeerInfos struct {
	peersInfo map[proto.UID]*PeerInfo
	Host      proto.UID
	CoHost    proto.UID
}

type PeerInfo struct {
	proto.RoomInfo
	proto.ClientUserInfo `json:"info"`
}

func NewPeersInfo() *PeerInfos {
	p := &PeerInfos{
		peersInfo: make(map[proto.UID]*PeerInfo),
	}
	return p
}

func NewPeerInfo(peer *Peer, rid proto.RID) *PeerInfo {
	uid := peer.ID()
	peerinfo := &PeerInfo{}
	peerinfo.UID = proto.UID(uid)
	peerinfo.Microphone = true
	peerinfo.Camera = true
	peerinfo.AvatarUrl = peer.AvatarUrl
	peerinfo.Role = peer.Role
	peerinfo.RID = rid
	peerinfo.Name = peer.Username
	peerinfo.Location = peer.Location
	return peerinfo
}

// AddPeerInfo call it when first time joining this room
func (infos *PeerInfos) AddPeerInfo(newInfo *PeerInfo) {
	if newInfo.Role == "host" {
		infos.Host = newInfo.UID
	} else if newInfo.Role == "cohost" {
		infos.CoHost = newInfo.UID
	}
	infos.peersInfo[newInfo.UID] = newInfo
}

// RemovePeerInfo call it when leaving room
func (infos *PeerInfos) RemovePeerInfo(uid string) bool {
	delete(infos.peersInfo, proto.UID(uid))
	if proto.UID(uid) == infos.Host {
		infos.UpdateHost("")
		return true
	} else if proto.UID(uid) == infos.CoHost {
		infos.CoHost = ""
	}
	return false
}

// GetPeerInfo get peer info from room
func (infos *PeerInfos) GetPeerInfo(uid string) *PeerInfo {
	return infos.peersInfo[proto.UID(uid)]
}

// UpdateInfo update peer info from room
func (infos *PeerInfos) UpdateInfo(newInfo *PeerInfo) {
	if _, ok := infos.peersInfo[newInfo.UID]; ok {
		infos.peersInfo[newInfo.UID] = newInfo
	}
}

// UpdateHostFromRoom
func (infos *PeerInfos) UpdateHost(uid proto.UID) {
	if uid != "" {
		if infos.Host == uid {
			return
		} else if infos.CoHost == uid {
			infos.Host = infos.CoHost
			infos.peersInfo[infos.Host].Role = "host"
			infos.CoHost = ""
			return
		} else {
			if peer, ok := infos.peersInfo[uid]; ok {
				peer.Role = "host"
				infos.peersInfo[infos.Host].Role = "participant"
				infos.Host = uid
			}
			return
		}
	} else {
		if infos.CoHost != "" {
			infos.Host = infos.CoHost
			infos.peersInfo[infos.Host].Role = "host"
			infos.CoHost = ""
			return
		} else {
			infos.Host = ""
			for uid, peer := range infos.peersInfo {
				infos.Host = uid
				peer.Role = "host"
				return
			}
		}
	}
}

// GetParticipants get all participants info
func GetParticipants(rid proto.RID) interface{} {
	room := getRoom(rid)
	p := proto.ParticipantList{}
	if room != nil {
		room.Lock()
		p.Participants = room.PeerInfos.peersInfo
		room.Unlock()
		return p
	}
	return p
}

// GetHost get host of the room
func GetHost(rid proto.RID) proto.UID {
	room := getRoom(rid)
	if room == nil {
		return ""
	}
	room.Lock()
	host := room.PeerInfos.Host
	room.Unlock()
	return host
}

// ChangeHost change host
func ChangeHost(rid proto.RID, uid proto.UID) {
	room := getRoom(rid)
	if room != nil {
		room.Lock()
		room.PeerInfos.UpdateHost(uid)
		room.Unlock()
	}
}

// HasHost there is a host on this room
func HasHost(rid proto.RID) bool {
	room := getRoom(rid)
	if room == nil {
		return false
	}
	room.Lock()
	host := room.PeerInfos.Host
	room.Unlock()
	return host != ""
}

// IsHost is this guy a host on this room
func IsHost(rid proto.RID, uid proto.UID) bool {
	room := getRoom(rid)
	if room == nil {
		return false
	}
	room.Lock()
	host := room.PeerInfos.Host
	room.Unlock()
	return host == uid
}

// GetPeerCount get the peer count of this room
func GetPeerCount(rid proto.RID) int {
	room := getRoom(rid)
	if room == nil {
		return 0
	}
	room.Lock()
	p := room.PeerInfos.peersInfo
	room.Unlock()
	return len(p)
}

// GetPeerInfo get some one info on this room
func GetPeerInfo(rid proto.RID, uid proto.UID) *PeerInfo {
	room := getRoom(rid)
	if room == nil {
		return nil
	}
	room.Lock()
	p := room.PeerInfos.peersInfo[uid]
	room.Unlock()
	return p
}

// UpdatePeerInfo update someone info on this room
func UpdatePeerInfo(rid proto.RID, newInfo *PeerInfo) {
	room := getRoom(rid)
	room.Lock()
	room.PeerInfos.UpdateInfo(newInfo)
	room.Unlock()
}
