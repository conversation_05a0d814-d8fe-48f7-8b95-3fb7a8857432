package signal

import (
	"context"
	"net/http"
	"strconv"

	"github.com/cloudwebrtc/go-protoo/logger"
	"github.com/cloudwebrtc/go-protoo/transport"
	"github.com/gorilla/websocket"
	"github.com/pion/ion/pkg/log"
)

type WebSocketServerConfig struct {
	Host          string
	Port          int
	CertFile      string
	KeyFile       string
	WebSocketPath string
	Authorization bool
}

type MsgHandler func(ws *transport.WebSocketTransport, request *http.Request)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func handler(authorization bool, msgHandler MsgHandler) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		// if authorizate?
		if authorization {
			// check jwt
			claims, err := getClaims(r)

			if err != nil {
				log.Errorf("Error authenticating user => %s", err)
				http.Error(w, err.Error(), http.StatusForbidden)
				return
			}
			ctx := context.WithValue(r.Context(), claimsCtxKey, claims)
			r = r.WithContext(ctx)
		}
		responseHeader := http.Header{}
		responseHeader.Add("Sec-WebSocket-Protocol", "protoo")
		socket, err := upgrader.Upgrade(w, r, responseHeader)
		if err != nil {
			log.Errorf("Error upgrading => %s", err)
			http.Error(w, "Error upgrading socket", http.StatusBadRequest)
			return
		}
		wsTransport := transport.NewWebSocketTransport(socket)
		wsTransport.Start()

		msgHandler(wsTransport, r)
	}
}

// NewWebSocketServer for signaling
func NewWebSocketServer(cfg WebSocketServerConfig, msgHandler MsgHandler) error {
	// Websocket handle func
	http.HandleFunc(cfg.WebSocketPath, handler(cfg.Authorization, msgHandler))

	if cfg.CertFile == "" || cfg.KeyFile == "" {
		logger.Infof("non-TLS WebSocketServer listening on: %s:%d", cfg.Host, cfg.Port)
		return http.ListenAndServe(cfg.Host+":"+strconv.Itoa(cfg.Port), nil)
	} else {
		logger.Infof("TLS WebSocketServer listening on: %s:%d", cfg.Host, cfg.Port)
		return http.ListenAndServeTLS(cfg.Host+":"+strconv.Itoa(cfg.Port), cfg.CertFile, cfg.KeyFile, nil)
	}
}
