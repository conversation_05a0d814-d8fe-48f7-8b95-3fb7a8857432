package signal

import (
	"fmt"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/google/uuid"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
)

// join conference jwt auth
type JoinAuth struct {
	jwt.StandardClaims
	Rid       string `json:"rid"`
	OrigIat   int64  `json:"orig_iat"`
	Topic     string `json:"topic"`
	StartedAt int64  `json:"startedat"`
}

func TokenObtain(rid, topic string) *proto.JoinResponse {
	if topic == "" {
		topic = topicObtain()
	}
	join := JoinAuth{
		Rid:       rid,
		Topic:     topic,
		OrigIat:   time.Now().Add(JoinRefresh).Unix(),
		StartedAt: time.Now().Unix(),
	}
	join.ExpiresAt = time.Now().Add(JoinExpired).Unix()
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, join)
	ss, err := token.SignedString([]byte(secretKey))
	rep := &proto.JoinResponse{
		Jwt:   ss,
		Topic: join.Topic,
	}
	fmt.Printf("$$$$$$$$$$$token:%v, err: %v", ss, err)
	return rep
}

func RefreshToken(joinToken string) string {
	token, err := jwt.ParseWithClaims(joinToken, &JoinAuth{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})
	if err != nil {
		log.Infof("ParseWithClaims err :%v", err)
		rt := token.Claims.(*JoinAuth)
		if time.Now().Unix() < rt.OrigIat {
			rt.ExpiresAt = time.Now().Add(JoinExpired).Unix()
			token := jwt.NewWithClaims(jwt.SigningMethodHS256, rt)
			result, _ := token.SignedString(secretKey)
			return result
		}
	}
	return ""
}

func topicObtain() string {
	data := uuid.New().String()
	// topicBase64 := b64.StdEncoding.EncodeToString([]byte(data))
	return "conf" + data
}
