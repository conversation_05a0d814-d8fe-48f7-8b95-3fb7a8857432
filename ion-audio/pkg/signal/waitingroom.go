package signal

import (
	"fmt"
	"sync"
	"time"

	"github.com/cloudwebrtc/go-protoo/peer"
	nprotoo "github.com/cloudwebrtc/nats-protoo"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
)

// waitingRoom save waiter
type waitingRoom struct {
	sync.RWMutex
	Rid     proto.RID
	Waiters map[proto.UID]*Waiter
	Host    *peer.Peer
}

// waiter waiting for joining this room
type Waiter struct {
	One     *Peer
	JoinMsg proto.JoinMsg
}

var (
	waitingRooms    = make(map[proto.RID]*waitingRoom)
	waitingroomLook sync.RWMutex
)

type joinFun func(*Peer, proto.JoinMsg, *proto.JoinResponse) (interface{}, *nprotoo.Error)

// JoinWaitingRoom participants join waiting room
func JoinWaitingRoom(peer *Peer, msg proto.JoinMsg) {
	log.Infof("this is Join<PERSON>ait<PERSON><PERSON><PERSON>")
	rid := msg.RID
	uid := msg.UID
	room := getWaitingRoom(rid)
	one := InitWaiter(msg, peer)
	room.Lock()
	room.Waiters[uid] = one
	room.Unlock()
	log.Infof("waiters:%v", waitingRooms)
	// once waiter join in this waiting romm , notify host
	NotifyHostWaitList(rid)
}

// LeaveWaitingRoom participants leave waiting room
func LeaveWaitingRoom(rid proto.RID, uid proto.UID) {
	log.Infof("this is LeaveWaitingRoom")
	room := getWaitingRoom(rid)
	room.Lock()
	delete(room.Waiters, uid)
	room.Unlock()
}

// InitWaiter init a participant in waiting room
func InitWaiter(msg proto.JoinMsg, peer *Peer) *Waiter {
	log.Infof("this is InitWaiter")
	one := &Waiter{
		One:     peer,
		JoinMsg: msg,
	}
	return one
}

// newWaitintRoom new a waiting room .
func newWaitingRoom(id proto.RID) *waitingRoom {
	log.Infof("this is newWaitingRoom")
	r := &waitingRoom{
		Rid:     id,
		Waiters: make(map[proto.UID]*Waiter),
	}
	waitingroomLook.Lock()
	waitingRooms[id] = r
	waitingroomLook.Unlock()
	return r
}

// getWaitingRoom new a waiting room if the waiting room does not exist.
func getWaitingRoom(id proto.RID) *waitingRoom {
	log.Infof("this is getWaitingRoom")
	waitingroomLook.Lock()
	room := waitingRooms[id]
	log.Infof("waitintroom:", room)
	if room == nil {
		r := &waitingRoom{
			Rid:     id,
			Waiters: make(map[proto.UID]*Waiter),
		}
		waitingRooms[id] = r
	}

	waitingroomLook.Unlock()
	return waitingRooms[id]
}

// notify all watier that host has been in rooms
// 1. if host turns on the waitingroom, it will notify client need to wait for host accepting
// 2. if host turns off the waitingroom, it will notify client can send a publish message, and the room allow the peer to join.
func NotifyAllWaiterHostComing(rid proto.RID, onWait bool, peer *Peer, fn joinFun) {
	log.Infof("this is NotifyAllWaiterHostComing")
	room := getWaitingRoom(rid)
	room.Lock()
	room.Host = &peer.Peer
	room.Unlock()
	var data = make(map[string]interface{})
	if onWait {
		// trun on waiting room
		data["code"] = -207
		data["jwt"] = ""
		data["topic"] = ""
		if room != nil {
			room.Lock()
			for _, peer := range room.Waiters {
				peer.One.Notify(proto.HostComing, data)
			}
			room.Unlock()
		}
		NotifyHostWaitList(rid)
	} else {
		// turn off waiting room
		data["code"] = 200
		rep := TokenObtain(string(rid), GetTopic(rid))
		data["jwt"] = rep.Jwt
		data["topic"] = rep.Topic
		if room != nil {
			room.Lock()
			for _, peer := range room.Waiters {
				_, err := fn(peer.One, peer.JoinMsg, nil)
				if err != nil {
					data["code"] = -209
				}
				peer.One.Notify(proto.HostComing, data)
			}
			delete(waitingRooms, rid)
			room.Unlock()
		}
	}

}

// notify the one who is in waiting room, which he is accepted to join this room by host,
// or rejected to join this room.
func NotifyWaiter(rid proto.RID, partid proto.UID, allow bool) (*Waiter, bool) {
	log.Infof("this is NotifyWaiter")
	room := getWaitingRoom(rid)
	part := room.Waiters[partid]
	if part != nil {
		var data = make(map[string]interface{})
		data["allow"] = allow
		data["jwt"] = ""
		data["topic"] = ""
		if allow {
			rep := TokenObtain(string(rid), GetTopic(rid))
			data["jwt"] = rep.Jwt
			data["topic"] = rep.Topic
		}
		room.Lock()
		part.One.Notify(proto.AllowJoining, data)
		delete(room.Waiters, partid)
		room.Unlock()
		NotifyHostWaitList(rid)
	}
	return part, allow
}

// NotiftAllWaiters when conference close, notify all people in the waiting room exit conference
func NotiftAllWaiters(rid proto.RID, allow bool) {
	log.Infof("this is NotiftAllWaiters")
	room := getWaitingRoom(rid)
	if room != nil {
		room.Lock()
		for _, peer := range room.Waiters {
			var data = make(map[string]interface{})
			data["allow"] = allow
			peer.One.Notify(proto.AllowJoining, data)
		}
		room.Unlock()
	}

}

// notify host all the waiter list
func NotifyHostWaitList(rid proto.RID) {
	log.Infof("this is NotifyHostWaitList")
	log.Infof("waiters:%v", waitingRooms)
	// room := getWaitingRoom(rid)
	waitingroomLook.Lock()
	room := waitingRooms[rid]
	waitingroomLook.Unlock()

	if room != nil && room.Host != nil {
		log.Infof("this is NotifyHostWaitList host ")
		list := getWaiterList(room)
		room.Lock()
		room.Host.Notify(proto.WaitersList, list)
		room.Unlock()

	}

}

// HostLeave
func HostLeave(rid proto.RID) {
	log.Infof("this is HostLeave")
	waitingroomLook.Lock()
	waitingRoom := waitingRooms[rid]
	waitingroomLook.Unlock()
	log.Infof("waitingroom:", waitingRoom)
	if waitingRoom != nil && nil != waitingRoom.Host {
		// candicate := GetHost(rid)
		room := getRoom(rid)
		if room == nil {
			delete(waitingRooms, rid)
			return
		}
		candicate := room.PeerInfos.Host
		peer := room.GetPeer(string(candicate))
		waitingRoom.Lock()
		waitingRoom.Host = peer
		waitingRoom.Unlock()
		NotifyHostWaitList(rid)
	}

}

// getWaiterList get all participants on the waiting room.
func getWaiterList(room *waitingRoom) interface{} {
	log.Infof("this is getWaiterList")
	waiterList := proto.WaiterParticipant{}
	peers := make(map[proto.UID]proto.WaiterInfo)
	for uid, waiter := range room.Waiters {
		info := proto.WaiterInfo{}
		info.Name = waiter.One.Username
		info.AvatarUrl = waiter.One.AvatarUrl
		info.Rid = string(room.Rid)
		info.Location = waiter.One.Location
		peers[uid] = info
	}
	waiterList.Waiters = peers
	return waiterList
}

// GetWaitersCount get count of waiters on the waiting room
func GetWaitersCount(rid proto.RID) int {
	room := getWaitingRoom(rid)
	return len(room.Waiters)
}

// GetWaitingRoomByPeer one peer in one waitint room
func GetWaitingRoomByPeer(id proto.UID) *waitingRoom {
	waitingroomLook.RLock()
	defer waitingroomLook.RUnlock()
	for _, room := range waitingRooms {
		if room == nil {
			continue
		}
		if peer := room.Waiters[id]; peer != nil {
			return room
		}
	}
	return nil
}

// GetWaitingRoomsByPeer one peer in many waiting rooms
func GetWaitingRoomsByPeer(id proto.UID) []*waitingRoom {
	var r []*waitingRoom
	waitingroomLook.RLock()
	defer waitingroomLook.RUnlock()
	for _, room := range waitingRooms {
		//log.Debugf("signal.GetRoomsByPeer rid=%v id=%v", rid, id)
		if room == nil {
			continue
		}
		if peer := room.Waiters[id]; peer != nil {
			r = append(r, room)
		}
	}
	return r
}

// RemovePeerInWaitingRoom remove all the participants on this waiting room
func RemovePeerInWaitingRoom(one *Peer) {
	uid := proto.UID(one.ID())
	waitingroom := GetWaitingRoomByPeer(uid)
	if waitingroom != nil {
		waitingroom.Lock()
		delete(waitingroom.Waiters, uid)
		waitingroom.Unlock()
	}

}

// deleteWaitingRoom delete this waiting room
func deleteWaitingRoom(rid proto.RID) {
	log.Infof("this is deleteWaitingRoom")
	waitingroomLook.Lock()
	room := waitingRooms[rid]
	log.Infof("waitintroom:", room)
	if room != nil {
		delete(waitingRooms, rid)
		room = nil
	}

	waitingroomLook.Unlock()
}

// statWaiting watch waiting rooms status
func statWaiting() {
	t := time.NewTicker(statWaitingRoomCycle)
	defer t.Stop()

	for range t.C {
		info := "\n----------------waiting rooms-----------------\n"
		print := false
		waitingroomLook.Lock()
		if len(waitingRooms) > 0 {
			print = true
		}
		for rid, room := range waitingRooms {
			info += fmt.Sprintf("waitingroom: %s\n waiters: %d\n", rid, len(room.Waiters))
			if len(room.Waiters) == 0 && room.Host == nil {
				delete(waitingRooms, rid)
			}
		}

		waitingroomLook.Unlock()
		if print {
			log.Infof(info)
		}
	}
}
