package entity

import (
	"errors"

	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
)

type PmiSetting struct {
	gorm.Model
	PMI            string `gorm:"type:varchar(255);not null" mapstructure: "pmi"`
	OwnerID        int64  `gorm:"type:bigint;not null" mapstructure: "ownerid"`
	Password       string `gorm:"type:varchar(255);" mapstructure: "password"`
	Salt           string `gorm:"type:varchar(255);not null" mapstructure: "salt"`
	OnEarlyJoining bool   `gorm:"type:tinyint(1);not null" mapstructure:"onjoining"`
	OnWaitingRoom  bool   `gorm:"type:tinyint(1);not null" mapstructure:"onwaiting"`
}

func NewPmiSetting(setting map[string]interface{}) *PmiSetting {
	pmi := &PmiSetting{}
	mapstructure.Decode(setting, pmi)
	return pmi
}

func (pmi *PmiSetting) DeletePmiSetting() {
	tidb.DB.Unscoped().Delete(pmi)
}

func (pmi *PmiSetting) SavePmiSetting() error {
	if pmi.PMI == "" && pmi.OwnerID == 0 {
		return errors.New("miss primary key for creating conference")
	}
	tidb.DB.Create(pmi)
	return nil
}

func (pmi *PmiSetting) UpdatePmiSetting(newSetting PmiSetting) {
	tidb.DB.Model(pmi).Updates(newSetting)
}

func (pmi *PmiSetting) UpdatePmiSettingWithMap(newSetting map[string]interface{}) {
	tidb.DB.Model(pmi).Updates(newSetting)
}

func AddPmiSetting(setting map[string]interface{}) error {
	pmi := NewPmiSetting(setting)
	err := pmi.SavePmiSetting()
	return err
}

func QueryPmiSetting(setting *PmiSetting) *PmiSetting {
	pmi := &PmiSetting{}
	tidb.DB.Where(setting).First(pmi)
	return pmi
}

func DefaultSetting(user *User) error {
	if user.PMI == "" {
		return errors.New("invalid user")
	}
	userPMI := QueryPmiSetting(&PmiSetting{PMI: user.PMI})
	if userPMI.PMI != "" {
		return nil
	}
	pmi := &PmiSetting{}
	pmi.OwnerID = user.Uid
	pmi.PMI = user.PMI
	pmi.Salt = user.Salt
	pmi.Password = ""
	pmi.OnEarlyJoining = false
	pmi.OnWaitingRoom = false
	return pmi.SavePmiSetting()
}
