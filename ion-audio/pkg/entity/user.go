package entity

import (
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"errors"
	"log"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/mitchellh/mapstructure"
	"github.com/pion/ion/pkg/util"
	"gorm.io/datatypes"
)

type User struct {
	gorm.Model
	Uid      int64  `gorm:"type:bigint;unique;not null" mapstructure: "uid"`
	Username string `gorm:"type:varchar(255)" mapstructure: "username"`
	Email    string `gorm:"type:varchar(100);unique;default:null" mapstructure: "email"`
	Phone    string `gorm:"type:varchar(100);unique;default:null" mapstructure: "phone"`
	Password string `gorm:"type:varchar(255)" mapstructure: "password"`
	Salt     string `gorm:"type:varchar(255);not null" mapstructure: "salt"`
	PMI      string `gorm:"type:varchar(255);unique;not null" mapstructure: "pmi"`

	State     int64          `gorm:"not null;default:0" mapstructure: "state"`
	Stateat   time.Time      `gorm:"default:null" mapstructure: "stateAt"`
	Lastseen  time.Time      `gorm:"default:null" mapstructure: "lastSeen"`
	Useragent string         `gorm:"default:''" mapstructure: "useragent"`
	Public    datatypes.JSON `gorm:"type:json" mapstructure: "public"`
	Access    JSONB          `gorm:"type:json" mapstructure: "access"`
	Tags      JSONB          `gorm:"type:json" sql:"type:jsonb" mapstructure: "tags"`
}

type DefaultAccess struct {
	Auth string
	Anon string
}

func NewUser(userInfo map[string]interface{}) *User {
	user := &User{}
	mapstructure.Decode(userInfo, user)
	return user
}

func (user *User) DeleteUser() {
	if one := QueryUser(user); one.Uid != 0 {
		tidb.DB.Unscoped().Delete(user)
	}
}

func (user *User) SaveUser() error {
	if user.Email == "" && user.Password == "" && user.Phone == "" {
		return errors.New("miss primary key for register account")
	}
	err := tidb.DB.Create(user).Error
	if err != nil {
		log.Printf("save user %v", err.Error())
	}
	return err
}

func (user *User) UpdateUser(newUserInfo User) {
	tidb.DB.Model(user).Updates(newUserInfo)
}

func (user *User) UpdateUserWithMap(newUserInfo map[string]interface{}) {
	tidb.DB.Model(user).Updates(newUserInfo)
}

func UserExist(userInfo *User) (bool, error) {
	if userInfo.Email == "" && userInfo.Phone == "" && userInfo.Username == "" {
		return false, errors.New("Miss unique key to retrieve user info.")
	}
	userList := QueryUserList(userInfo)
	return len(userList) == 1, nil
}

func QueryUserList(userinfo *User) []User {
	users := []User{}
	tidb.DB.Where(userinfo).Find(&users)
	return users
}

func QueryUser(userInfo *User) *User {
	user := &User{}
	tidb.DB.Where(userInfo).First(user)
	return user
}

func MarshalBinary(uid uint64) ([]byte, error) {
	dst := make([]byte, 8)
	binary.LittleEndian.PutUint64(dst, uint64(uid))
	return dst, nil
}

func MarshalText(uid uint64) ([]byte, error) {
	if uid == 0 {
		return []byte{}, nil
	}
	src := make([]byte, 8)
	dst := make([]byte, base64.URLEncoding.WithPadding(base64.NoPadding).EncodedLen(8))
	binary.LittleEndian.PutUint64(src, uint64(uid))
	base64.URLEncoding.WithPadding(base64.NoPadding).Encode(dst, src)
	return dst, nil
}

// String converts Uid to base64 string.
func UidString(uid uint64) string {
	buf, _ := MarshalText(uid)
	return string(buf)
}

// decode user userxxxxx ==> 1234567
func DecodeUser(userid string) (int64, error) {
	if strings.HasPrefix(userid, "usr") {
		username := userid[len("usr"):]
		encodedID, err := util.UidUnmarshalText([]byte(username))
		if err != nil {
			return util.ZeroUid, err
		}
		userId := DecodeUid(encodedID)
		return userId, nil
	}
	return util.ZeroUid, errors.New("user name does not have prefix 'usr'.")
}

// encode 123456 ===> usrxxxxx
func EncodeUser(uid int64) (string, error) {
	uidUin64 := EncodeUid(uid)
	uidBase64, err := util.UidMarshalText(uidUin64)
	if err != nil {
		return "", err
	}
	return "usr" + string(uidBase64), nil
}

func (user *User) GetPublicField(field string) interface{} {
	if user.Public == nil {
		return nil
	}
	m := make(map[string]interface{})
	public, _ := user.Public.Value()
	json.Unmarshal(public.([]byte), &m)
	if val, ok := m[field]; ok {
		return val
	}
	return nil
}
