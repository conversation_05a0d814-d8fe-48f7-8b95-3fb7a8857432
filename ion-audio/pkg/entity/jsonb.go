package entity
import (
	"encoding/json"
	"database/sql/driver"
)

//generic json field inserted into mysql
type JSONB struct {
	V interface{}
}

func (j JSONB) Value() (driver.Value, error) {
 	valueString, err := json.Marshal(j.V)
	return string(valueString), err
}
 
func (j *JSONB) Scan(value interface{}) error {
	if err := json.Unmarshal(value.([]byte), &j.V); err != nil {
		return err
	}
	return nil
}