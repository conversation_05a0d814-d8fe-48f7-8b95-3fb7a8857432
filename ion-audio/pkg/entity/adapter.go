package entity

import (
	"net/http"
	"time"

	nprotoo "github.com/cloudwebrtc/nats-protoo"
	"github.com/pion/ion/pkg/db"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
	"github.com/pion/ion/pkg/util"
)

var (
	invalidroomError       = util.NewNpError(-200, "this conference invalid")
	noroomError            = util.NewNpError(-201, "no this conference")
	roomclosedError        = util.NewNpError(-202, "this conference has been closed")
	invalidroomformatError = util.NewNpError(-203, "this format of the conference invalid")
	waitforroomError       = util.NewNpError(-204, "please wait for starting")
	inputError             = util.NewNpError(-205, "please input conference id")
	nopwdError             = util.NewNpError(-206, "please input password")
	wrongpwdErrpr          = util.NewNpError(-206, "wrong password")
)

const FieldConference = "Field/conference/"

const (
	redisLongKeyTTL = 24 * time.Hour
)

var redis *db.Redis

// init redis cache
func Initredis(redisCfg db.Config) {
	redis = db.NewRedis(redisCfg)
}

// set key and value
func SetConferenceCache(k, field string, v interface{}) error {
	err := redis.HSet(k, field, v)
	if err != nil {
		log.Infof("HSet for conference error : %v", err.Error())
	}
	return err
}

//
func SetConferenceAllCache(k string, v map[string]interface{}) {
	for field, value := range v {
		SetConferenceCache(k, field, value)
	}
}

// return one value of the filed on the key
func GetConferenceCache(k, field string) string {
	return redis.HGet(k, field)
}

// return all value of all the field on the key
func GetConferenceAllCache(k string) map[string]string {
	return redis.HGetAll(k)
}

// set key and value
func SetConferenceCacheTTL(k, field string, v interface{}, t time.Duration) error {
	err := redis.HSetTTL(k, field, v, t)
	if err != nil {
		log.Infof("HSet for conference error : %v", err.Error())
	}
	return err
}

//
func SetConferenceAllCacheTTL(k string, v map[string]interface{}, t time.Duration) {
	for field, value := range v {
		SetConferenceCacheTTL(k, field, value, t)
	}
}

// del redis cache from key
func DelConferenceKey(k string) error {
	return redis.Del(k)
}

// del redis cache from key and field
func DelConferenceCache(k, field string) error {
	return redis.HDel(k, field)
}

// mysql-redis adapter
func AdapterJoinGate(msg proto.JoinMsg) *nprotoo.Error {
	// Validate
	var err = util.NewNpError(http.StatusBadRequest, "unknow err")
	var con *Conference
	if msg.RID == "" {
		return invalidroomError
	}
	conferenceid := string(msg.RID)

	// check if this conference exist
	if ok, result := isCache(conferenceid); ok {
		// the meeting must exist
		// who is the host of the meeting?
		log.Infof("read meeting data from redis => %v", result)
		err = isHostForCache(result, msg)
		return err
	} else {
		// redis do not exist message about this meeting
		con, err = isMeetingStartForMysql(conferenceid)
		log.Infof("read meeting data from mysql => %v", con)
		if err != nil {
			return err
		}
		err = isHostForMysql(con, msg)
		return err
	}
}

func isCache(rid string) (bool, map[string]string) {
	result := GetConferenceAllCache(FieldConference + rid)
	log.Infof("#####################result from redis:%v", result)
	if result["rid"] == "" {
		return false, nil
	} else {
		return true, result
	}
}

// isMeetingStart
func isMeetingStartForMysql(rid string) (*Conference, *nprotoo.Error) {
	var con *Conference
	if len(rid) == 9 {
		// ordinary meeting id
		log.Infof("conferenceid : %v", rid)
		con = QueryConference(&Conference{ConferenceID: rid})
		if con.ConferenceID == "" {
			// no this conference
			return nil, invalidroomError
		}
		if con.Status == Closed {
			return nil, invalidroomError
		}
	} else if len(rid) == 10 {
		// PMI
		con = QueryConference(&Conference{ConferenceID: rid, Status: Launching})
		if con.ConferenceID == "" {
			// no this PMI
			return nil, invalidroomError
		}
	} else {
		return nil, invalidroomError
	}
	set := map[string]interface{}{
		"rid":    con.ConferenceID,
		"hostid": con.HostID,
		"pwd":    con.Password,
		"salt":   con.Salt,
		"number": con.Number,
	}
	SetConferenceAllCacheTTL(FieldConference+rid, set, redisLongKeyTTL)
	return con, nil
}

// isHost check if user is host
func isHostForMysql(con *Conference, msg proto.JoinMsg) *nprotoo.Error {
	// this user is host or participator?
	uid, err := DecodeUser(string(msg.UID))
	if err != nil {
		util.NewNpError(-208, err.Error())
	}
	log.Infof("HostID: %v , uid : %v", con.HostID, uid)
	if con.HostID == uid {
		return nil
		// this is host
	} else {
		// this is participator
		perr := paticipatorLoginForMysql(con, msg)
		return perr
	}
}

// isHost check if user is host
func isHostForCache(result map[string]string, msg proto.JoinMsg) *nprotoo.Error {
	// this user is host or participator?
	uid := string(msg.UID)
	log.Infof("HostID: %v , uid : %v", result["hostid"], uid)
	if result["hostid"] == uid {
		return nil
		// this is host
	} else {
		// this is participator
		perr := paticipatorLoginForCache(result, msg)
		return perr
	}
}

// paticipatorLogin paticipatorLogin paricipator login logic
func paticipatorLoginForMysql(con *Conference, msg proto.JoinMsg) *nprotoo.Error {
	if con.Password == "" {
		// no password
		return nil
	} else {
		// conference need password
		pwd := msg.Pwd
		if pwd == "" {
			// input nil value password
			return nopwdError
		}
		// check input-pwd and conference-pwd
		log.Infof("pwd: %v , password: %v", pwd, con.Password)
		if util.CheckPasswordHash(pwd, con.Salt, con.Password) {
			// yes
			return nil
		} else {
			// failed
			log.Errorf("Error authenticating user => %s", "wrong password")
			return wrongpwdErrpr
		}
	}
}

// paticipatorLogin paticipatorLogin paricipator login logic
func paticipatorLoginForCache(result map[string]string, msg proto.JoinMsg) *nprotoo.Error {
	if result["pwd"] == "" {
		// no password
		return nil
	} else {
		// conference need password
		pwd := msg.Pwd
		if pwd == "" {
			// input nil value password
			return nopwdError
		}
		// check input-pwd and conference-pwd
		log.Infof("pwd: %v , password: %v", pwd, result["pwd"])
		if util.CheckPasswordHash(pwd, result["salt"], result["pwd"]) {
			// yes
			return nil
		} else {
			// failed
			log.Errorf("Error authenticating user => %s", "wrong password")
			return wrongpwdErrpr
		}
	}
}
