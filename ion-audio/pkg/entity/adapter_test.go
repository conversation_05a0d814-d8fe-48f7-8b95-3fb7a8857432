package entity

import (
	"fmt"
	"testing"

	"github.com/pion/ion/pkg/db"
	"github.com/pion/ion/pkg/proto"
)

var rkey string
var cfg = db.Config{
	Addrs: []string{":6379"},
	Pwd:   "",
	DB:    0,
}

type valuekey struct {
	Conferenceid string `json:"conferenceid"`
	Uid          string `json:"uid"`
	Pwd          string `json:"pwd"`
}

func TestCache(t *testing.T) {
	t.Run("test redis", func(t *testing.T) {
		rkey = proto.RoomInfo{
			RID: "123456789",
			UID: "12345678sagasd",
		}.BuildKey()
		fmt.Println(rkey)
		Initredis(cfg)
		if redis == (&db.Redis{}) {
			t.Error("nil")
		}
		s := map[string]interface{}{
			"conferenceid": "2341",
			"uid":          "123",
			"pwd":          "12345",
		}
		// SetConferenceCache(rkey, "conferenceid", "12345678")
		// SetConferenceCache(rkey, "uid", "123")
		// SetConferenceCache(rkey, "pwd", "1234as5678")
		// got := redis.HGet(rkey, "conferenceid")
		// set := redis.HGetAll(rkey)
		SetConferenceAllCache(FieldConference+"6632768995", s)
		g := GetConferenceAllCache(FieldConference + "6632768995")
		// got := GetConferenceCache("1234")
		t.Error("set:", g)
	})
}
