package entity

import (
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"

	"github.com/pion/ion/pkg/log"
)

var (
	//nolint:unused
	tidb   *Tidb
	uidgen *UidGenerator
)

// Init func
func Init(dbName, dbUrl, uidKey string) {
	fmt.Println("init database")
	tidb = InitDatabase(dbName, dbUrl)
	if tidb == nil {
		log.Infof("init database fail ")
		return
	}
	decodedKey, err := base64.StdEncoding.DecodeString(uidKey)
	if err != nil {
		panic("Failed to decode key.")
	}
	uidgen, err = NewUidGenerator(1, []byte(decodedKey))
	if err != nil {
		panic("Failed to initialize uid generator.")
	}

	InitTable()
}

func InitDatabase(dbName, dbUrl string) *Tidb {
	tmptidb, err := NewTidb(dbName, dbUrl)

	if err != nil {
		log.Infof("open database err %v", err)
		panic("Failed to open database")
	}
	return tmptidb
}

func Close() {
	if tidb.DB != nil {
		tidb.Close()
	}
}

func InitTable() {
	tidb.InitTable(&User{})
	tidb.InitTable(&Conference{})
	tidb.InitTable(&Participator{})
}

func DecodeUid(uid uint64) int64 {
	if uid == 0 {
		return 0
	}
	return uidgen.DecodeUid(uid)
}

func EncodeUid(id int64) uint64 {
	if id == 0 {
		return 0
	}
	return uidgen.EncodeInt64(id)
}
func EncodeUidStr2Uid(uidStr string) (uint64, error) {
	n, err := strconv.ParseInt(uidStr, 10, 64)
	if err != nil {
		log.Infof("parse user id to int64 error ====> %v", err.Error())
		return 0, err
	}
	uid := EncodeUid(n)
	if uid == 0 {
		return 0, errors.New("invalid user id")
	}
	return uid, nil
}

func DecodeUid2UidStr(uid uint64) (string, error) {
	if uid == 0 {
		return "", errors.New("Invalid uid")
	}
	uidInt64 := DecodeUid(uid)
	uidStr := strconv.FormatInt(uidInt64, 10)
	return uidStr, nil
}
