syntax = "proto3";

package pbx;


// group chating micro service
service ConfChat {
  rpc CreateConfChat(ConferenceRequest) returns (ConferenceReply) {}
  rpc LeaveConfChat(ConferenceRequest) returns (ConferenceReply) {}
  rpc CloseConfChat(ConferenceRequest) returns (ConferenceReply) {}
  rpc UpdatConfHost(ConferenceRequest) returns (ConferenceReply) {}
}

// ConferenceRequest request data type
message ConferenceRequest {
  string topic = 1;
  string userId = 2;
}

// ConferenceReply response data type
message ConferenceReply {
  string message = 1;
  int64    code = 2;
}