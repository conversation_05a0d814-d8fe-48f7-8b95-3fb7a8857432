// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.12.3
// source: ion_grpc_tinode.proto

package pbx

import (
	context "context"
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// ConferenceRequest request data type
type ConferenceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Topic  string `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	UserId string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *ConferenceRequest) Reset() {
	*x = ConferenceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ion_grpc_tinode_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConferenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConferenceRequest) ProtoMessage() {}

func (x *ConferenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ion_grpc_tinode_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConferenceRequest.ProtoReflect.Descriptor instead.
func (*ConferenceRequest) Descriptor() ([]byte, []int) {
	return file_ion_grpc_tinode_proto_rawDescGZIP(), []int{0}
}

func (x *ConferenceRequest) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *ConferenceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// ConferenceReply response data type
type ConferenceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Code    int64  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ConferenceReply) Reset() {
	*x = ConferenceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ion_grpc_tinode_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConferenceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConferenceReply) ProtoMessage() {}

func (x *ConferenceReply) ProtoReflect() protoreflect.Message {
	mi := &file_ion_grpc_tinode_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConferenceReply.ProtoReflect.Descriptor instead.
func (*ConferenceReply) Descriptor() ([]byte, []int) {
	return file_ion_grpc_tinode_proto_rawDescGZIP(), []int{1}
}

func (x *ConferenceReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ConferenceReply) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_ion_grpc_tinode_proto protoreflect.FileDescriptor

var file_ion_grpc_tinode_proto_rawDesc = []byte{
	0x0a, 0x15, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x74, 0x69, 0x6e, 0x6f, 0x64,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x70, 0x62, 0x78, 0x22, 0x41, 0x0a, 0x11,
	0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x3f, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x32, 0x8f, 0x02, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x66, 0x43, 0x68, 0x61, 0x74, 0x12, 0x40, 0x0a,
	0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x43, 0x68, 0x61, 0x74, 0x12,
	0x16, 0x2e, 0x70, 0x62, 0x78, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x78, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12,
	0x3f, 0x0a, 0x0d, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x43, 0x68, 0x61, 0x74,
	0x12, 0x16, 0x2e, 0x70, 0x62, 0x78, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x78, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x3f, 0x0a, 0x0d, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x43, 0x68, 0x61,
	0x74, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x78, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x78, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x00, 0x12, 0x3f, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x48, 0x6f,
	0x73, 0x74, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x78, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x78,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ion_grpc_tinode_proto_rawDescOnce sync.Once
	file_ion_grpc_tinode_proto_rawDescData = file_ion_grpc_tinode_proto_rawDesc
)

func file_ion_grpc_tinode_proto_rawDescGZIP() []byte {
	file_ion_grpc_tinode_proto_rawDescOnce.Do(func() {
		file_ion_grpc_tinode_proto_rawDescData = protoimpl.X.CompressGZIP(file_ion_grpc_tinode_proto_rawDescData)
	})
	return file_ion_grpc_tinode_proto_rawDescData
}

var file_ion_grpc_tinode_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_ion_grpc_tinode_proto_goTypes = []interface{}{
	(*ConferenceRequest)(nil), // 0: pbx.ConferenceRequest
	(*ConferenceReply)(nil),   // 1: pbx.ConferenceReply
}
var file_ion_grpc_tinode_proto_depIdxs = []int32{
	0, // 0: pbx.ConfChat.CreateConfChat:input_type -> pbx.ConferenceRequest
	0, // 1: pbx.ConfChat.LeaveConfChat:input_type -> pbx.ConferenceRequest
	0, // 2: pbx.ConfChat.CloseConfChat:input_type -> pbx.ConferenceRequest
	0, // 3: pbx.ConfChat.UpdatConfHost:input_type -> pbx.ConferenceRequest
	1, // 4: pbx.ConfChat.CreateConfChat:output_type -> pbx.ConferenceReply
	1, // 5: pbx.ConfChat.LeaveConfChat:output_type -> pbx.ConferenceReply
	1, // 6: pbx.ConfChat.CloseConfChat:output_type -> pbx.ConferenceReply
	1, // 7: pbx.ConfChat.UpdatConfHost:output_type -> pbx.ConferenceReply
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_ion_grpc_tinode_proto_init() }
func file_ion_grpc_tinode_proto_init() {
	if File_ion_grpc_tinode_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ion_grpc_tinode_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConferenceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ion_grpc_tinode_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConferenceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ion_grpc_tinode_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ion_grpc_tinode_proto_goTypes,
		DependencyIndexes: file_ion_grpc_tinode_proto_depIdxs,
		MessageInfos:      file_ion_grpc_tinode_proto_msgTypes,
	}.Build()
	File_ion_grpc_tinode_proto = out.File
	file_ion_grpc_tinode_proto_rawDesc = nil
	file_ion_grpc_tinode_proto_goTypes = nil
	file_ion_grpc_tinode_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// ConfChatClient is the client API for ConfChat service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ConfChatClient interface {
	CreateConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error)
	LeaveConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error)
	CloseConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error)
	UpdatConfHost(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error)
}

type confChatClient struct {
	cc grpc.ClientConnInterface
}

func NewConfChatClient(cc grpc.ClientConnInterface) ConfChatClient {
	return &confChatClient{cc}
}

func (c *confChatClient) CreateConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error) {
	out := new(ConferenceReply)
	err := c.cc.Invoke(ctx, "/pbx.ConfChat/CreateConfChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confChatClient) LeaveConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error) {
	out := new(ConferenceReply)
	err := c.cc.Invoke(ctx, "/pbx.ConfChat/LeaveConfChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confChatClient) CloseConfChat(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error) {
	out := new(ConferenceReply)
	err := c.cc.Invoke(ctx, "/pbx.ConfChat/CloseConfChat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *confChatClient) UpdatConfHost(ctx context.Context, in *ConferenceRequest, opts ...grpc.CallOption) (*ConferenceReply, error) {
	out := new(ConferenceReply)
	err := c.cc.Invoke(ctx, "/pbx.ConfChat/UpdatConfHost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfChatServer is the server API for ConfChat service.
type ConfChatServer interface {
	CreateConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error)
	LeaveConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error)
	CloseConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error)
	UpdatConfHost(context.Context, *ConferenceRequest) (*ConferenceReply, error)
}

// UnimplementedConfChatServer can be embedded to have forward compatible implementations.
type UnimplementedConfChatServer struct {
}

func (*UnimplementedConfChatServer) CreateConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateConfChat not implemented")
}
func (*UnimplementedConfChatServer) LeaveConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LeaveConfChat not implemented")
}
func (*UnimplementedConfChatServer) CloseConfChat(context.Context, *ConferenceRequest) (*ConferenceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseConfChat not implemented")
}
func (*UnimplementedConfChatServer) UpdatConfHost(context.Context, *ConferenceRequest) (*ConferenceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatConfHost not implemented")
}

func RegisterConfChatServer(s *grpc.Server, srv ConfChatServer) {
	s.RegisterService(&_ConfChat_serviceDesc, srv)
}

func _ConfChat_CreateConfChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfChatServer).CreateConfChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.ConfChat/CreateConfChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfChatServer).CreateConfChat(ctx, req.(*ConferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfChat_LeaveConfChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfChatServer).LeaveConfChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.ConfChat/LeaveConfChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfChatServer).LeaveConfChat(ctx, req.(*ConferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfChat_CloseConfChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfChatServer).CloseConfChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.ConfChat/CloseConfChat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfChatServer).CloseConfChat(ctx, req.(*ConferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfChat_UpdatConfHost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfChatServer).UpdatConfHost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pbx.ConfChat/UpdatConfHost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfChatServer).UpdatConfHost(ctx, req.(*ConferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ConfChat_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pbx.ConfChat",
	HandlerType: (*ConfChatServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateConfChat",
			Handler:    _ConfChat_CreateConfChat_Handler,
		},
		{
			MethodName: "LeaveConfChat",
			Handler:    _ConfChat_LeaveConfChat_Handler,
		},
		{
			MethodName: "CloseConfChat",
			Handler:    _ConfChat_CloseConfChat_Handler,
		},
		{
			MethodName: "UpdatConfHost",
			Handler:    _ConfChat_UpdatConfHost_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ion_grpc_tinode.proto",
}
