package proto

import (
	"encoding/json"

	"github.com/pion/webrtc/v2"
)

type ClientUserInfo struct {
	Name      string `json:"name"`
	AvatarUrl string `json:"avatarUrl"`
	Role      string `json:"role"`
	Location  string `json:"location"`
	DeviceState
}

type PwdInfo struct {
	Pwd string `json:"pwd,omitempty"`
}

func (m *ClientUserInfo) MarshalBinary() ([]byte, error) {
	return json.Marshal(m)
}

func (m *ClientUserInfo) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, m)
}

type UserClaims struct {
	Uid       UID    `json:"uid,omitempty"`
	Username  string `json:"username,omitempty"`
	AvatarUrl string `json:"avatarUrl,omitempty"`
}

type RoomInfo struct {
	RID RID `json:"rid"`
	UID UID `json:"uid"`
}

type DeviceState struct {
	Microphone bool `json:"microphone"`
	Camera     bool `json:"camera"`
}

type RTCInfo struct {
	Jsep webrtc.SessionDescription `json:"jsep"`
}

type PublishOptions struct {
	Codec       string `json:"codec"`
	Resolution  string `json:"resolution"`
	Bandwidth   int    `json:"bandwidth"`
	Audio       bool   `json:"audio"`
	Video       bool   `json:"video"`
	Screen      bool   `json:"screen"`
	TransportCC bool   `json:"transportCC,omitempty"`
}

type SubscribeOptions struct {
	Bandwidth   int  `json:"bandwidth"`
	TransportCC bool `json:"transportCC"`
}

type TrackMap map[string][]TrackInfo

/// Messages ///

type JoinMsg struct {
	RoomInfo
	Info ClientUserInfo `json:"info"`
	PwdInfo
}

type WaitintMsg struct {
	RoomInfo
	Waiter UID  `json:"waiter"`
	Accept bool `json:"accept"`
}

type MuteAllMsg struct {
	RoomInfo
	MuteAllLock bool `json:"muteAllLock"`
	MuteAll     bool `json:"muteAll"`
}

type SomeoneMsg struct {
	RoomInfo
	One UID `json:"participant"`
}

type LeaveWaitingRoomMsg struct {
	RoomInfo
}

type LeaveMsg struct {
	RoomInfo
	Info ClientUserInfo `json:"info"`
}

type PublishMsg struct {
	RoomInfo
	RTCInfo
	Options PublishOptions `json:"options"`
}

type PublishResponseMsg struct {
	MediaInfo
	RTCInfo
	Tracks TrackMap `json:"tracks"`
}

type UnpublishMsg struct {
	MediaInfo
}

type SFUSubscribeMsg struct {
	SubscribeMsg
	Tracks TrackMap `json:"tracks"`
}

type SubscribeMsg struct {
	MediaInfo
	RTCInfo
	Options SubscribeOptions
}

type SubscribeResponseMsg struct {
	MediaInfo
	RTCInfo
}

type UnsubscribeMsg struct {
	MediaInfo
}

type BroadcastMsg struct {
	RoomInfo
	Info json.RawMessage `json:"info"`
}

// join response
type JoinResponse struct {
	Jwt     string `json:"jwt"`
	Topic   string `json:"topic"`
	Subject string `json:"-"`
	Ownerid string `json:"-"`
}

// room setting request message
type SettingMsg struct {
	RoomInfo
	Settings RoomSetting `json:"settings"`
}

//
type RoomSetting struct {
	OnWaitingRoom bool `json:"waitingroom"`
	OnMuteAll     bool `json:"muteall"`
	OnLockRoom    bool `json:"lockroom"`
	OnScreen      bool `json:"screen"`
	// OnMuteAllLock bool `json:"muteAllLock"`
}

// participant info
type ParticipantMsg struct {
	RoomInfo
	Info ClientUserInfo `json:"info"`
}

// participant list
type ParticipantList struct {
	Participants interface{} `json:"participants"`
}

// waiter list
type WaiterParticipant struct {
	Waiters interface{} `json:"waitingParticipants"`
}

// WaiterInfo
type WaiterInfo struct {
	Name      string `json:"name"`
	AvatarUrl string `json:"avatarUrl"`
	Rid       string `json:"rid"`
	Location  string `json:"location"`
}

type TrickleMsg struct {
	MediaInfo
	Info    json.RawMessage `json:"info"`
	Trickle json.RawMessage `json:"trickle"`
}

type StreamAddMsg struct {
	MediaInfo
	Info   ClientUserInfo `json:"info"`
	Tracks TrackMap       `json:"tracks"`
}

type StreamRemoveMsg struct {
	MediaInfo
}

type MuteSomeoneMsg struct {
	SomeoneMsg
	IsMuted bool `json:"is_muted"`
}

type SFUMuteMsg struct {
	MID     MID  `json:"mid"`
	IsMuted bool `json:"is_muted"`
}

type SFUMuteAllMsg struct {
	// MediaInfo
	IsMuted  bool  `json:"is_muted"`
	MuteList []MID `json:"mute_list"`
}

type HostControlMsg struct {
	SomeoneMsg
	ControlType int  `json:"control_type"`
	ControlBool bool `json:"control_bool"`
}
