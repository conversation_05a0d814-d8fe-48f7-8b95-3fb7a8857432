package avp

import (
	nprotoo "github.com/cloudwebrtc/nats-protoo"
	"github.com/pion/ion/pkg/discovery"
)

var (
	//nolint:unused
	dc = "default"
	//nolint:unused
	nid    = "avp-unkown-node-id"
	protoo *nprotoo.NatsProtoo
	rpcs     map[string]*nprotoo.Requestor
	services map[string]discovery.Node
)

// Init func
func Init(dcID, nodeID, rpcID, eventID, natsURL string) {
	dc = dcID
	nid = nodeID
	protoo = nprotoo.NewNatsProtoo(natsURL)
	services = make(map[string]discovery.Node)
	rpcs = make(map[string]*nprotoo.Requestor)
	handleRequest(rpcID)
}

// Close func
func Close() {
	if protoo != nil {
		protoo.Close()
	}
}
