package biz

import (
	"context"
	"time"

	"github.com/pion/ion/pkg/log"
	pb "github.com/pion/ion/pkg/proto/tinode"
	"google.golang.org/grpc"
)

const (
	closeGroup = 1
)

func rpc2tinode(addr string) {
	conn, err := grpc.Dial(addr, grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		log.Panicf("did not connect: %v", err)
	}

	tinodeClient = pb.NewConfChatClient(conn)
}

// grpc to tinode
func createGroupFromTinode(topic, uid string) {
	if tinodeClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		defer cancel()
		res, err := tinodeClient.CreateConfChat(ctx, &pb.ConferenceRequest{Topic: topic, UserId: uid})
		if err != nil {
			log.Errorf("could not CreateConfChat: %v", err)
			return
		}
		log.Infof("Creating group chating res:%v", res.Message)
	} else {
		log.Infof("tinodeClient is nil")
	}
}

// grpc to tinode
func closeGroupFromTinode(topic, uid string) {
	if tinodeClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		defer cancel()
		res, err := tinodeClient.CloseConfChat(ctx, &pb.ConferenceRequest{Topic: topic, UserId: uid})
		if err != nil {
			log.Errorf("could not CloseConfChat: %v", err)
			return
		}
		log.Infof("Closing group chating res:%v", res.Message)
	} else {
		log.Infof("tinodeClient is nil")
	}
}

func TinodeEntry(topic, uid string, method int) {
	switch method {
	case closeGroup:
		closeGroupFromTinode(topic, uid)
	}
}
