package biz

import (
	"encoding/json"
	"fmt"
	"net/http"

	nprotoo "github.com/cloudwebrtc/nats-protoo"
	conf "github.com/pion/ion/pkg/conf/biz"
	"github.com/pion/ion/pkg/discovery"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
	"github.com/pion/ion/pkg/signal"
	"github.com/pion/ion/pkg/util"
)

// ParseProtoo Unmarshals a protoo payload.
func ParseProtoo(msg json.RawMessage, claims *signal.Claims, method string, msgType interface{}) *nprotoo.Error {
	if err := json.Unmarshal(msg, &msgType); err != nil {
		log.Errorf("Biz.Entry parse error %v", err.Error())
		return util.NewNpError(http.StatusBadRequest, fmt.Sprintf("Error parsing request object %v", err.Error()))
	}

	authMethod(method, claims)
	return nil
}

// authentication : check meeting status, password, who is host
func joinGate(peer *signal.Peer, msg proto.JoinMsg, join func(peer *signal.Peer, msg proto.JoinMsg, joinrep *proto.JoinResponse) (interface{}, *nprotoo.Error)) (interface{}, *nprotoo.Error) {
	log.Infof("check status of join msg:%v", msg)
	var rep *proto.JoinResponse
	var err *nprotoo.Error
	peer.Username = msg.Info.Name
	if conf.Signal.Authorization {
		rep, err = adapter(peer, msg)
		if err != nil {
			return nil, err
		}
	}

	return join(peer, msg, rep)
}

func publishGate(peer *signal.Peer, msg proto.PublishMsg, publish func(peer *signal.Peer, msg proto.PublishMsg) (interface{}, *nprotoo.Error)) (interface{}, *nprotoo.Error) {
	log.Infof("check status of publish msg:%v", msg)
	// check room
	room := signal.GetRoomByPeer(peer.ID())
	if room == nil {
		return nil, util.NewNpError(codeRoomErr, codeStr(codeRoomErr))
	}
	// check screen function
	if msg.Options.Screen {
		if signal.GetScreen(msg.RID) {
			if signal.IsHost(msg.RID, msg.UID) {
				return publish(peer, msg)
			}
			return nil, hostPermisionError
		}
	}

	return publish(peer, msg)
}

// check method permision
func authMethod(method string, claims *signal.Claims) {
	if claims == nil {
		// guest
		log.Infof("guest action: %v", method)
	} else {
		// login
		log.Infof("logining action: %v", method)
	}
}

// Entry is the biz entry
func Entry(method string, peer *signal.Peer, msg json.RawMessage, claims *signal.Claims, accept signal.RespondFunc, reject signal.RejectFunc) {
	var result interface{}
	topErr := util.NewNpError(http.StatusBadRequest, fmt.Sprintf("Unkown method [%s]", method))

	//TODO DRY this up
	switch method {
	case proto.ClientJoin:
		var msgData proto.JoinMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = joinGate(peer, msgData, join)
		}
	case proto.ClientLeave:
		var msgData proto.LeaveMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = leaveConference(peer, msgData)
		}
	case proto.ClientClose:
		var msgData proto.LeaveMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = closeConference(peer, msgData)
		}
	case proto.ClientPublish:
		var msgData proto.PublishMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = publishGate(peer, msgData, publish)
		}
	case proto.ClientUnPublish:
		var msgData proto.UnpublishMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = unpublish(peer, msgData)
		}
	case proto.ClientSubscribe:
		var msgData proto.SubscribeMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = subscribe(peer, msgData)
		}
	case proto.ClientUnSubscribe:
		var msgData proto.UnsubscribeMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = unsubscribe(peer, msgData)
		}
	case proto.ClientBroadcast:
		var msgData proto.BroadcastMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = broadcast(peer, msgData)
		}
	case proto.ClientTrickleICE:
		var msgData proto.TrickleMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = trickle(peer, msgData)
		}

	case proto.ClientDeviceState:
		var msgData proto.JoinMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = device(peer, msgData)
		}

	case proto.ClientWaitigRoomJudge:
		var msgData proto.WaitintMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = acceptjoin(peer, msgData)
		}

	case proto.ClientLeaveWaitingRoom:
		var msgData proto.LeaveWaitingRoomMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = LeaveWaitingRoom(peer, msgData)
		}

	case proto.ClientHostSetting:
		var msgData proto.SettingMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = roomSetting(peer, msgData)
		}
	case proto.ClientMuteAll:
		var msgData proto.MuteAllMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = muteall(peer, msgData)
		}

	case proto.HostControl:
		var msgData proto.HostControlMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = HostControlSwitch(peer, msgData)
		}

	case proto.ClientCanUnmute:
		var msgData proto.MuteSomeoneMsg
		if topErr = ParseProtoo(msg, claims, method, &msgData); topErr == nil {
			result, topErr = CanClientUnmute(peer, msgData)
		}
	}

	if topErr != nil {
		reject(topErr.Code, topErr.Reason)
	} else {
		accept(result)
	}
}

func getRPCForIslb() (*nprotoo.Requestor, bool) {
	for _, item := range services {
		if item.Info["service"] == "islb" {
			id := item.Info["id"]
			rpc, found := rpcs[id]
			if !found {
				rpcID := discovery.GetRPCChannel(item)
				log.Infof("Create rpc [%s] for islb", rpcID)
				rpc = protoo.NewRequestor(rpcID)
				rpcs[id] = rpc
			}
			return rpc, true
		}
	}
	log.Warnf("No islb node was found.")
	return nil, false
}

func handleSFUBroadCast(msg nprotoo.Notification, subj string) {
	go func(msg nprotoo.Notification) {
		var data proto.MediaInfo
		if err := json.Unmarshal(msg.Data, &data); err != nil {
			log.Errorf("handleSFUBroadCast Unmarshall error %v", err)
			return
		}

		log.Infof("handleSFUBroadCast: method=%s, data=%v", msg.Method, data)

		switch msg.Method {
		case proto.SFUTrickleICE:
			signal.NotifyAllWithoutID(data.RID, data.UID, proto.ClientOnStreamAdd, data)
		case proto.SFUStreamRemove:
			islb, found := getRPCForIslb()
			if found {
				islb.AsyncRequest(proto.IslbOnStreamRemove, data)
			}
		}
	}(msg)
}

func handleAVPBroadCast(msg nprotoo.Notification, subj string) {
	go func(msg nprotoo.Notification) {
		var data proto.MediaInfo
		if err := json.Unmarshal(msg.Data, &data); err != nil {
			log.Errorf("handleSFUBroadCast Unmarshall error %v", err)
			return
		}

		log.Infof("handleSFUBroadCast: method=%s, data=%v", msg.Method, data)

		switch msg.Method {
		case proto.SFUTrickleICE:
			signal.NotifyAllWithoutID(data.RID, data.UID, proto.ClientOnStreamAdd, data)
		case proto.SFUStreamRemove:
			islb, found := getRPCForIslb()
			if found {
				islb.AsyncRequest(proto.IslbOnStreamRemove, data)
			}
		}
	}(msg)
}

func getRPCForSFU(mid proto.MID) (string, *nprotoo.Requestor, *nprotoo.Error) {
	islb, found := getRPCForIslb()
	if !found {
		return "", nil, util.NewNpError(500, "Not found any node for islb.")
	}
	result, err := islb.SyncRequest(proto.IslbFindService, util.Map("service", "sfu", "mid", mid))
	if err != nil {
		return "", nil, err
	}

	var answer proto.GetSFURPCParams
	if err := json.Unmarshal(result, &answer); err != nil {
		return "", nil, &nprotoo.Error{Code: 123, Reason: "Unmarshal error getRPCForSFU"}
	}

	log.Infof("SFU result => %v", answer)
	rpcID := answer.RPCID
	rpc, found := rpcs[rpcID]
	if !found {
		rpc = protoo.NewRequestor(rpcID)
		protoo.OnBroadcast(answer.EventID, handleSFUBroadCast)
		rpcs[rpcID] = rpc
	}
	return answer.ID, rpc, nil
}

func getSFUByNid(nid string) *nprotoo.Requestor {
	rpcID := "rpc-" + nid
	eventID := "event-" + nid
	rpc, found := rpcs[rpcID]
	if !found {
		rpc = protoo.NewRequestor(rpcID)
		protoo.OnBroadcast(eventID, handleSFUBroadCast)
		rpcs[rpcID] = rpc
	}
	return rpc
}

func getRPCForAVP() (string, *nprotoo.Requestor, *nprotoo.Error) {
	islb, found := getRPCForIslb()
	if !found {
		return "", nil, util.NewNpError(500, "Not found any node for islb.")
	}
	result, err := islb.SyncRequest(proto.IslbFindService, util.Map("service", "avp"))
	if err != nil {
		return "", nil, err
	}

	var answer proto.GetSFURPCParams
	if err := json.Unmarshal(result, &answer); err != nil {
		return "", nil, &nprotoo.Error{Code: 123, Reason: "Unmarshal error getRPCForAVP"}
	}

	log.Infof("AVP result => %v", result)
	rpcID := answer.RPCID
	rpc, found := rpcs[rpcID]
	if !found {
		rpc = protoo.NewRequestor(rpcID)
		protoo.OnBroadcast(answer.EventID, handleAVPBroadCast)
		rpcs[rpcID] = rpc
	}
	return answer.ID, rpc, nil
}
