package biz

import (
	"fmt"

	nprotoo "github.com/cloudwebrtc/nats-protoo"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
	"github.com/pion/ion/pkg/signal"
)

// broadcast msg from islb
func handleIslbBroadCast(msg nprotoo.Notification, subj string) {
	var isblSignalTransformMap = map[string]string{
		proto.IslbOnStreamAdd:    proto.ClientOnStreamAdd,
		proto.IslbOnStreamRemove: proto.ClientOnStreamRemove,
		proto.IslbClientOnJoin:   proto.ClientOnJoin,
		proto.IslbClientOnLeave:  proto.ClientOnLeave,
		proto.IslbOnBroadcast:    proto.ClientBroadcast,
		proto.IslbClientOnClose:  proto.ClientOnClose,
		proto.IslbOnSubtitle:     proto.ClientSubtitleBroadcast,
		proto.IslbOnParticipant:  proto.ClientOnParticipant,
	}

	go func(msg nprotoo.Notification) {

		var data proto.BroadcastMsg
		if err := msg.Data.Unmarshal(&data); err != nil {
			log.Errorf("Error parsing message %v", err)
			return
		}
		var data2 map[string]interface{}
		if err := msg.Data.Unmarshal(&data2); err != nil {
			log.Errorf("Error parsing message %v", err)
			return
		}
		log.Infof("OnIslbBroadcast: method=%s, data=%v", msg.Method, data)
		log.Infof("OnIslbBroadcast: method=%s, data2=%v", msg.Method, data2)
		if newMethod, ok := isblSignalTransformMap[msg.Method]; ok {
			notifyStrategy(data.RID, data.UID, newMethod, data2)
		}
	}(msg)
}

func notifyStrategy(rid proto.RID, skipID proto.UID, method string, msg interface{}) {
	var notifyMethodMap = map[string]string{
		proto.ClientOnStreamAdd:       "NotifyAllWithoutID",
		proto.ClientOnStreamRemove:    "NotifyAllWithoutID",
		proto.ClientOnJoin:            "NotifyAllWithoutID",
		proto.ClientOnLeave:           "NotifyAllWithoutID",
		proto.ClientBroadcast:         "NotifyAllWithoutID",
		proto.ClientOnClose:           "NotifyAllWithoutID",
		proto.ClientSubtitleBroadcast: "NotifyAll",
		proto.ClientOnParticipant:     "NotifyAll",
	}

	notifyMethod := notifyMethodMap[method]
	switch notifyMethod {
	case "NotifyAllWithoutID":
		signal.NotifyAllWithoutID(rid, skipID, method, msg)
	case "NotifyAll":
		fmt.Println("rid:", rid, "method:", method, "msg:", msg)
		signal.NotifyAll(rid, method, msg)
	}
}
