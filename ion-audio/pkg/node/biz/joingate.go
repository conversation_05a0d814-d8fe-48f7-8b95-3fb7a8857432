package biz

import (
	"fmt"
	"net/http"
	"strconv"

	nprotoo "github.com/cloudwebrtc/nats-protoo"
	conf "github.com/pion/ion/pkg/conf/biz"
	"github.com/pion/ion/pkg/entity"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
	"github.com/pion/ion/pkg/signal"
	"github.com/pion/ion/pkg/util"
)

var (
	invalidroomError       = util.NewNpError(-200, "this conference invalid")
	noroomError            = util.NewNpError(-201, "no this conference")
	roomclosedError        = util.NewNpError(-202, "this conference has been closed")
	invalidroomformatError = util.NewNpError(-203, "this format of the conference invalid")
	waitforhostError       = util.NewNpError(-206, "waiting for host joining")
	inputError             = util.NewNpError(-205, "please input conference id")
	nopwdError             = util.NewNpError(-204, "please input password")
	wrongpwdErrpr          = util.NewNpError(-204, "wrong password")
	JoinWaitingRoomErr     = util.NewNpError(-207, "joining waiting room")
	lockRoomError          = util.NewNpError(-209, "lock room")
	hostPermisionError     = util.NewNpError(-210, "access denied")
)

var (
	LenPMI      = conf.RoomLen.LenPmi
	LenOrdinary = conf.RoomLen.LenOrdinary
)

const (
	owner       = 1
	participant = 2
)

// isMeetingLaunching
func isMeetingLaunching(rid string) (*entity.Conference, *entity.PmiSetting, *nprotoo.Error) {
	var con *entity.Conference
	var pmiSetting *entity.PmiSetting
	if len(rid) == LenOrdinary {
		// ordinary meeting id
		log.Infof("conferenceid : %v", rid)
		con = entity.QueryConference(&entity.Conference{ConferenceID: rid})
		if con.ConferenceID == "" {
			// no this conference
			log.Infof("no this conference")
			return nil, nil, invalidroomError
		}
		if con.Status == entity.Closed {
			log.Infof("this conference has been closed")
			// this conference has been closed
			return nil, nil, invalidroomError
		}
		return con, nil, nil
	} else if len(rid) == LenPMI {
		// PMI
		pmiSetting := entity.QueryPmiSetting(&entity.PmiSetting{PMI: rid})
		if pmiSetting.PMI == "" {
			log.Infof("no this conference PMI")
			return nil, nil, invalidroomError
		} else {
			log.Infof("Launching")
			// Launching
			con = entity.QueryConference(&entity.Conference{ConferenceID: rid, Status: entity.Launching})
			fmt.Println("$$$$$$$$$$$$$con:", con)
			if con.ConferenceID == "" {
				log.Infof("this personal room need to wait for host")
				return nil, pmiSetting, nil
				// this personal room need to wait for host
			} else {
				log.Infof("host has been in this room")
				return con, pmiSetting, nil
				// host has been in this room
			}
		}
	}
	return con, pmiSetting, nil
}

func adapter(peer *signal.Peer, msg proto.JoinMsg) (*proto.JoinResponse, *nprotoo.Error) {
	var err *nprotoo.Error
	var rep *proto.JoinResponse
	if signal.AllowRedisCache {
		// TIPS : have not refactored this part
		err = entity.AdapterJoinGate(msg)
	} else {
		rep, err = checkConference(peer, msg)
	}
	return rep, err
}

// checkConference check if conference exist and start
func checkConference(peer *signal.Peer, msg proto.JoinMsg) (*proto.JoinResponse, *nprotoo.Error) {
	var roomSetting = make(map[string]bool)
	var err = util.NewNpError(http.StatusBadRequest, "unknow err")
	var con *entity.Conference
	var pmiSetting *entity.PmiSetting
	if msg.RID == "" {
		return nil, ridError
	}
	conferenceid := string(msg.RID)
	// input nil value conference id
	if conferenceid == "" {
		return nil, inputError
	}
	// check if this conference exist
	con, pmiSetting, err = isMeetingLaunching(conferenceid)
	if err != nil {
		return nil, err
	}
	// check pwd
	err = isOwner(peer, con, msg, pmiSetting)
	if err != nil {
		return nil, err
	}
	// lock room logic
	if !signal.IsOwner(msg.RID, msg.UID) && signal.GetRoomLock(msg.RID) {
		return nil, lockRoomError
	}
	// get location
	peer.Location = msg.Info.Location
	// this is owner
	if peer.Role == "host" {
		rep := NewConference(peer, pmiSetting, con)
		// roomSetting = GetRoomSetting(msg.RID, pmiSetting, con)
		// signal.NotifyAllWaiterHostComing(msg.RID, roomSetting["onWaitingRoom"], peer, join)
		return rep, nil
	} else if peer.Role == "participant" || peer.Role == "guest" {
		// this is participant
		roomSetting = GetRoomSetting(msg.RID, pmiSetting, con)
		if roomSetting["onEarlyJoin"] == true && roomSetting["onWaitingRoom"] == false {
			log.Infof("open switch about joining this romm before owner and waitingroom also turns off.")
			rep := NewConference(peer, pmiSetting, con)
			return rep, nil
		} else {
			if con != nil && con.Status == entity.Launching {
				// next part
				log.Infof("next part")
			} else {
				log.Infof("waiting for host coming")
				signal.JoinWaitingRoom(peer, msg)
				return nil, waitforhostError
			}
		}

	} else if peer.Role == "co-host" {
		rep := AddTopic(con, msg)
		return rep, nil
	}
	// waiting room logic
	if roomSetting["onWaitingRoom"] == true {
		signal.JoinWaitingRoom(peer, msg)
		return nil, JoinWaitingRoomErr
	}
	rep := AddTopic(con, msg)
	return rep, nil
}

// attach peer role
func attachPeer(peer *signal.Peer, rid proto.RID, role int) {
	if peer.Role == signal.Guest {
		return
	}
	switch role {
	case owner:
		if signal.HasHost(rid) {
			peer.Role = signal.CoHost
		} else {
			peer.Role = signal.Host
		}

	case participant:
		peer.Role = signal.Participant
	}

}

// isOwner check if user is owner
func isOwner(peer *signal.Peer, con *entity.Conference, msg proto.JoinMsg, pmiSetting *entity.PmiSetting) *nprotoo.Error {
	// this user is host or participator?
	uid, _ := entity.DecodeUser(peer.ID())
	rid := msg.RID
	fmt.Println("uid:", uid)
	if len(rid) == LenPMI {
		// waiting for PMI'host. Including:
		// 1. fast meeting with PMI

		if pmiSetting.OwnerID == uid {
			// this is host
			log.Infof("fast meeting with PMI.Host")
			attachPeer(peer, rid, owner)
			return nil

		} else {
			// this is participants
			log.Infof("fast meeting with PMI. participant")
			err := checkPwd(pmiSetting.Salt, pmiSetting.Password, msg.Pwd)
			if err == nil {
				attachPeer(peer, rid, participant)
			}
			return err
		}
	} else if len(rid) == LenOrdinary {
		// can get conference instance.Including:
		// 1. fast meeting with ordinary conference id
		// 2. scheduling ordinaty meeting

		log.Infof("ownerID: %v , uid : %v", con.HostID, uid)
		if con.HostID == uid {
			log.Infof("fast meeting with ordinary conference id. Host")
			attachPeer(peer, rid, owner)
			return nil
			// this is host
		} else {
			log.Infof("fast meeting with ordinary conference id. Participant")
			// this is participator
			perr := checkPwd(con.Salt, con.Password, msg.Pwd)
			if perr == nil {
				attachPeer(peer, rid, participant)
			}
			return perr
		}
	}

	return invalidroomError
}

// checkpwd
func checkPwd(salt, conPwd, pwd string) *nprotoo.Error {
	if conPwd == "" {
		log.Infof("no setting password")
		// no password
		return nil
	} else {
		// conference need password
		log.Infof("conference need password")
		if pwd == "" {
			// input nil value password
			log.Infof("input nil value password")
			return nopwdError
		}
		// check input-pwd and conference-pwd
		log.Infof("pwd: %v , password: %v", pwd, conPwd)
		if util.CheckPasswordHash(pwd, salt, conPwd) {
			// yes
			return nil
		} else {
			// failed
			log.Errorf("Error authenticating user => %s", "wrong password")
			return wrongpwdErrpr
		}
	}
}

// paticipatorLogin paticipatorLogin paricipator login logic
func paticipatorLogin(con *entity.Conference, msg proto.JoinMsg, pmiSetting *entity.PmiSetting) *nprotoo.Error {
	if con.Status == entity.Pending {
		log.Infof("schuduling conference")
		// schuduling conference
		err := checkPwd(con.Salt, con.Password, msg.Pwd)
		return err
	} else if con.Status == entity.Launching {
		log.Infof("create conference")
		// create conference
		if len(con.ConferenceID) == LenPMI {
			// PMI
			log.Infof("PMI pwd")
			err := checkPwd(con.Salt, pmiSetting.Password, msg.Pwd)
			return err
		} else if len(con.ConferenceID) == LenOrdinary {
			log.Infof("ordinary conference no pwd")
			log.Infof("scheduling confenrence need a pwd")
			err := checkPwd(con.Salt, con.Password, msg.Pwd)
			// ordinary conference
			// no pwd
			return err
		}
	}
	return nil
}

// GetRoomSetting get room setting
func GetRoomSetting(rid proto.RID, pmiSetting *entity.PmiSetting, con *entity.Conference) map[string]bool {
	roomSetting := make(map[string]bool)
	if ok, waiting := signal.GetWaitingRoomSetting(rid); ok {
		roomSetting["onWaitingRoom"] = waiting
		if con == nil {
			roomSetting["onEarlyJoin"] = pmiSetting.OnEarlyJoining
		} else {
			roomSetting["onEarlyJoin"] = con.OnEarlyJoining
		}
	} else {
		if con == nil {
			roomSetting["onEarlyJoin"] = pmiSetting.OnEarlyJoining
			roomSetting["onWaitingRoom"] = pmiSetting.OnWaitingRoom

		} else {
			roomSetting["onEarlyJoin"] = con.OnEarlyJoining
			roomSetting["onWaitingRoom"] = con.OnWaitingRoom
		}
	}
	log.Infof("room setting %v", roomSetting)
	return roomSetting
}

// NewConference new a conference
func NewConference(peer *signal.Peer, pmiSetting *entity.PmiSetting, con *entity.Conference) *proto.JoinResponse {
	var rep *proto.JoinResponse
	if con == nil {
		log.Infof("joining PMI and need to create a new confernece with owner-setting")
		username := getHostName(peer, pmiSetting.OwnerID)
		rep = signal.TokenObtain(pmiSetting.PMI, "")
		rep.Ownerid, _ = entity.EncodeUser(pmiSetting.OwnerID)
		rep.Subject = username + "的快速会议"
		createConference(pmiSetting, rep.Topic, username)
		return rep
	}
	if con.Status == entity.Pending {
		// scheduling conference
		log.Infof("scheduling conference update status")
		rep = signal.TokenObtain(con.ConferenceID, "")
		rep.Ownerid, _ = entity.EncodeUser(con.HostID)
		rep.Subject = con.Subject
		con.Status = entity.Launching
		con.Topic = rep.Topic
		con.UpdateConference(*con)
	} else if con.Status == entity.Launching {
		if signal.GetPeerCount(proto.RID(con.ConferenceID)) > 0 {
			// join this confernece before host
			rep = signal.TokenObtain(con.ConferenceID, con.Topic)
		} else {
			// create conference by host
			rep = signal.TokenObtain(con.ConferenceID, "")
			con.Topic = rep.Topic
		}
		log.Infof("creaing conference update topic")
		rep.Ownerid, _ = entity.EncodeUser(con.HostID)
		rep.Subject = con.Subject
		con.UpdateConference(*con)
	}
	return rep
}

func AddTopic(con *entity.Conference, msg proto.JoinMsg) *proto.JoinResponse {
	if con != nil && con.Topic == "" {
		rep := signal.TokenObtain(string(msg.RID), "")
		rep.Ownerid, _ = entity.EncodeUser(con.HostID)
		rep.Subject = con.Subject
		con.Topic = rep.Topic
		con.UpdateConference(*con)
		return rep
	} else if con.Topic != "" {
		rep := signal.TokenObtain(string(msg.RID), con.Topic)
		rep.Ownerid, _ = entity.EncodeUser(con.HostID)
		rep.Subject = con.Subject
		return rep
	}
	return nil
}

func createConference(pmiSetting *entity.PmiSetting, topic, name string) {
	salt := pmiSetting.Salt
	passwordCipher := pmiSetting.Password
	conferenceID := pmiSetting.PMI
	startedAt := util.GetCurrentMs()
	ownerID := pmiSetting.OwnerID
	number := conferenceID + "-" + strconv.FormatInt(startedAt, 10)
	number = util.GenerateHash("sha1", number)
	conference := map[string]interface{}{
		"conferenceid":  conferenceID,
		"salt":          salt,
		"password":      passwordCipher,
		"subject":       name + "的快速会议",
		"startedat":     startedAt,
		"hostid":        ownerID,
		"hostname":      name,
		"status":        entity.Launching,
		"number":        number,
		"topic":         topic,
		"onEarlyJoin":   pmiSetting.OnEarlyJoining,
		"onWaitingRoom": pmiSetting.OnWaitingRoom,
	}
	entity.AddConference(conference)
}

func getHostName(peer *signal.Peer, ownerId int64) string {
	if peer.Role == "host" {
		return peer.Username
	} else {
		user := entity.QueryUser(&entity.User{Uid: ownerId})
		return user.Username
	}
}
