package biz

import (
	"fmt"

	nprotoo "github.com/cloudwebrtc/nats-protoo"
	"github.com/pion/ion/pkg/entity"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/proto"
	"github.com/pion/ion/pkg/signal"
	"github.com/pion/ion/pkg/util"
)

var (
	uidError  = util.NewNpError(codeUIDErr, codeStr(codeUIDErr))
	ridError  = util.NewNpError(codeRoomErr, codeStr(codeRoomErr))
	jsepError = util.NewNpError(codeJsepErr, codeStr(codeJsepErr))
	// sdpError  = util.NewNpError(codeSDPErr, codeStr(codeSDPErr))
	midError = util.NewNpError(codeMIDErr, codeStr(codeMIDErr))
)

// get PMI number from confernece table
func getNumber(rid string) string {
	// allow redis cache
	var number string
	if signal.AllowRedisCache {
		number = entity.GetConferenceCache(entity.FieldConference+rid, "number")
		return number
	}
	// read number from mysql
	con := entity.QueryConference(&entity.Conference{ConferenceID: rid, Status: entity.Launching})
	if con.ConferenceID != "" {
		number = con.Number
	}

	return number
}

// update joinAt for participator table when join in meeting
func updateJoinAt(peer *signal.Peer, msg proto.JoinMsg) {
	rid := string(msg.RID)
	uid, _ := entity.DecodeUser(peer.ID())
	log.Infof("update join at: %v", uid)
	startedAt := util.GetCurrentMs()
	par := entity.QueryPartStatus(uid, rid, entity.Meeting, entity.Leaving)
	// par := entity.QueryParticipator(&entity.Participator{ParticipatorID: uid, ConferenceID: rid, Status: entity.Leaving})
	if par.ConferenceID != "" && par.Status == entity.Leaving {
		par.JoinAt = startedAt
		par.Status = entity.Meeting
		par.UpdateParticipator(*par)
		return
	}

	if par.ConferenceID == "" {
		participator := map[string]interface{}{
			"conferenceid":   rid,
			"participatorid": uid,
			"joinat":         startedAt,
			"status":         entity.Meeting,
			"number":         signal.GetNumber(msg.RID),
		}
		log.Infof("participator:%v", participator)
		entity.AddParticipator(participator)
		return
	}
	if par.Status == entity.Meeting {
		return
	}
}

// update leaveAt for participator table when leave in meeting
func updateLeaveAt(peer *signal.Peer, msg proto.LeaveMsg) {
	rid := string(msg.RID)
	uid, _ := entity.DecodeUser(peer.ID())

	// TO DO : update conference with 'where'
	par := entity.QueryParticipator(&entity.Participator{ParticipatorID: uid, ConferenceID: rid, Status: entity.Meeting})
	if par.ConferenceID != "" {
		par.LeaveAt = util.GetCurrentMs()
		par.Status = entity.Leaving
		par.Duration = par.Duration + par.LeaveAt - par.JoinAt
		par.UpdateParticipator(*par)
	}
}

func updateClose(rid string) {
	con := entity.QueryConference(&entity.Conference{ConferenceID: string(rid), Status: entity.Launching})
	if con.ConferenceID != "" {
		con.Status = entity.Closed
		con.EndAt = util.GetCurrentMs()
		con.UpdateConference(*con)
	}
	// del redis cache => read write sync WIP
	if signal.AllowRedisCache {
		entity.DelConferenceKey(entity.FieldConference + rid)
	}
	return
}

// WIP
// close conference
func closeConference(peer *signal.Peer, msg proto.LeaveMsg) (interface{}, *nprotoo.Error) {
	result, err := leave(peer, msg)
	if err == nil {
		rid := msg.RID
		// uid := peer.ID()
		updateLeaveAt(peer, msg)
		updateClose(string(rid))
		peers := signal.GetPeerOnRoom(rid)
		for _, peer := range peers {
			close(&signal.Peer{Peer: *peer}, msg)
		}
	}
	return result, err
}

// close all leaved participator table
func closeAllLeavedPart(roomid string) {
	if err := entity.CloseAllLeavedPart(roomid); err != nil {
		log.Errorf("close participator err:%v", err)
	}
}

// leave conference
func leaveConference(peer *signal.Peer, msg proto.LeaveMsg) (interface{}, *nprotoo.Error) {
	result, err := leave(peer, msg)
	if err == nil {
		rid := msg.RID
		count := len(signal.GetPeerOnRoom(rid))
		updateLeaveAt(peer, msg)
		if count < 1 {
			// no any one. So close conference
			roomid := string(rid)
			updateClose(roomid)
			closeAllLeavedPart(roomid)
			signal.NotiftAllWaiters(rid, false)
			closeGroupFromTinode(signal.GetTopic(rid), peer.ID())
			signal.DelRoom(rid)
		}
	}

	return result, err
}

// change client device state of audio and video
func device(peer *signal.Peer, msg proto.JoinMsg) (interface{}, *nprotoo.Error) {
	fmt.Println("device:", msg)
	rid := msg.RID
	uid := msg.UID
	// Validate
	if msg.RID == "" {
		return nil, ridError
	}
	room := signal.GetRoomByPeer(peer.ID())
	if room == nil {
		return nil, util.NewNpError(codeRoomErr, codeStr(codeRoomErr))
	}
	// update participants
	one := signal.GetPeerInfo(rid, uid)
	one.Camera = msg.Info.Camera
	one.Microphone = msg.Info.Microphone
	signal.UpdatePeerInfo(rid, one)
	signal.NotifyAll(rid, proto.ClientOnParticipant, signal.GetParticipants(rid))

	// get conference subject
	subject := room.GetSubject()
	var result = make(map[string]string)
	result["subject"] = subject
	return result, nil
}

func acceptjoin(peer *signal.Peer, msg proto.WaitintMsg) (interface{}, *nprotoo.Error) {
	fmt.Println("waiting room request:", msg)
	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	// only host can do this
	if !signal.IsHost(rid, msg.UID) {
		return nil, hostPermisionError
	}

	if waiter, allow := signal.NotifyWaiter(rid, msg.Waiter, msg.Accept); allow && waiter != nil {
		// host allows this guy to join his room
		log.Infof("// host allows this guy to join his room")
		return join(waiter.One, waiter.JoinMsg, nil)
	}
	return emptyMap, nil
}

func LeaveWaitingRoom(peer *signal.Peer, msg proto.LeaveWaitingRoomMsg) (interface{}, *nprotoo.Error) {
	signal.LeaveWaitingRoom(msg.RID, msg.UID)
	signal.NotifyHostWaitList(msg.RID)
	return emptyMap, nil
}

// join room
func join(peer *signal.Peer, msg proto.JoinMsg, joinRep *proto.JoinResponse) (interface{}, *nprotoo.Error) {
	log.Infof("biz.join peer.ID()=%s msg=%v", peer.ID(), msg)
	rid := msg.RID
	tinodeFlag := (signal.GetPeerCount(rid) == 0)
	var topic string
	var ownerId string
	// Validate
	if msg.RID == "" {
		return nil, ridError
	}
	if joinRep != nil {
		topic = joinRep.Topic
		ownerId = joinRep.Ownerid
	}
	log.Infof("topic: %v, ownerid: %v", topic, ownerId)
	//already joined this room
	signal.HasPeerAndDel(rid, peer.ID())
	signal.AddPeer(rid, peer, topic, ownerId)

	updateJoinAt(peer, msg)

	islb, found := getRPCForIslb()
	if !found {
		return nil, util.NewNpError(500, "Not found any node for islb.")
	}
	// Send join => islb
	info := msg.Info
	uid := peer.ID()
	_, err := islb.SyncRequest(proto.IslbClientOnJoin, util.Map("rid", rid, "uid", uid, "info", info))
	if err != nil {
		log.Errorf("IslbClientOnJoin failed %v", err.Error())
	}

	// Send getPubs => islb
	islb.AsyncRequest(proto.IslbGetPubs, msg.RoomInfo).Then(
		func(result nprotoo.RawMessage) {
			var resMsg proto.GetPubResp
			if err := result.Unmarshal(&resMsg); err != nil {
				log.Errorf("Unmarshal pub response %v", err)
				return
			}
			log.Infof("IslbGetPubs: result=%v", result)
			for _, pub := range resMsg.Pubs {
				if pub.MID == "" {
					continue
				}
				notif := proto.StreamAddMsg(pub)
				peer.Notify(proto.ClientOnStreamAdd, notif)
			}
		},
		func(err *nprotoo.Error) {})

	// only the first time this function can be called
	if tinodeFlag {
		createGroupFromTinode(topic, ownerId)
	}
	signal.NotifyAll(rid, proto.ClientOnParticipant, signal.GetParticipants(rid))
	muteall, muteallLock := signal.GetMuteAll(rid)
	mutemsg := proto.MuteAllMsg{
		MuteAll:     muteall,
		MuteAllLock: muteallLock,
	}
	mutemsg.UID = msg.UID
	mutemsg.RID = msg.RID
	// signal.NotifyAll(rid, proto.NotifyMuteAll, mutemsg)
	if signal.IsHost(rid, msg.UID) {
		_, onWaitingRoom := signal.GetWaitingRoomSetting(rid)
		signal.NotifyAllWaiterHostComing(msg.RID, onWaitingRoom, peer, join)
	} else {
		peer.Notify(proto.NotifyMuteAll, mutemsg)
		if err := sfuMuteParticipant(msg.RoomInfo, msg.UID, mutemsg.MuteAllLock, false); err != nil { // <-- The last false means mute that participant
			log.Errorf("%v", err)
		}
	}
	if joinRep != nil {
		return joinRep, nil
	}
	log.Infof("##############joinRep", joinRep)
	return emptyMap, nil
}

func close(peer *signal.Peer, msg proto.LeaveMsg) (interface{}, *nprotoo.Error) {
	log.Infof("biz.close peer.ID()=%s msg=%v", peer.ID(), msg)
	defer util.Recover("biz.close")

	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	uid := peer.ID()

	islb, found := getRPCForIslb()
	if !found {
		return nil, util.NewNpError(500, "Not found any node for islb.")
	}

	islb.AsyncRequest(proto.IslbOnStreamRemove, util.Map("rid", rid, "uid", uid))
	_, err := islb.SyncRequest(proto.IslbClientOnClose, util.Map("rid", rid, "uid", uid))
	if err != nil {
		log.Errorf("IslbOnStreamRemove failed %v", err.Error())
	}
	signal.DelPeer(rid, peer.ID())
	return emptyMap, nil
}

func leave(peer *signal.Peer, msg proto.LeaveMsg) (interface{}, *nprotoo.Error) {
	log.Infof("biz.leave peer.ID()=%s msg=%v", peer.ID(), msg)
	defer util.Recover("biz.leave")

	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	uid := peer.ID()

	islb, found := getRPCForIslb()
	if !found {
		return nil, util.NewNpError(500, "Not found any node for islb.")
	}

	islb.AsyncRequest(proto.IslbOnStreamRemove, util.Map("rid", rid, "uid", uid))
	_, err := islb.SyncRequest(proto.IslbClientOnLeave, util.Map("rid", rid, "uid", uid))
	if err != nil {
		log.Errorf("IslbOnStreamRemove failed %v", err.Error())
	}

	isHost := signal.IsHost(msg.RID, msg.UID)
	signal.DelPeer(rid, peer.ID())
	// If host changes, construct the setting message and send it to the new host.
	if isHost {
		if host := signal.GetHost(rid); host != "" {
			signal.PushRoomSettingMsg(host, rid)
		}
	}
	signal.NotifyAll(rid, proto.ClientOnParticipant, signal.GetParticipants(rid))
	return emptyMap, nil
}

func publish(peer *signal.Peer, msg proto.PublishMsg) (interface{}, *nprotoo.Error) {
	log.Infof("biz.publish peer.ID()=%s", peer.ID())

	nid, sfu, err := getRPCForSFU("")
	if err != nil {
		log.Warnf("Not found any sfu node, reject: %d => %s", err.Code, err.Reason)
		return nil, util.NewNpError(err.Code, err.Reason)
	}

	jsep := msg.Jsep
	options := msg.Options

	rid := msg.RID
	uid := peer.ID()
	resMsg, err := sfu.SyncRequest(proto.ClientPublish, util.Map("uid", uid, "rid", rid, "jsep", jsep, "options", options))
	if err != nil {
		log.Warnf("reject: %d => %s", err.Code, err.Reason)
		return nil, util.NewNpError(err.Code, err.Reason)
	}

	var result map[string]interface{}
	if err := resMsg.Unmarshal(&result); err != nil {
		log.Errorf("Unmarshal pub response %v", err)
		return nil, err
	}

	log.Infof("publish: result => %v", result)
	mid := util.Val(result, "mid")
	tracks := result["tracks"]
	islb, found := getRPCForIslb()
	if !found {
		return nil, util.NewNpError(500, "Not found any node for islb.")
	}
	islb.AsyncRequest(proto.IslbOnStreamAdd, util.Map("rid", rid, "nid", nid, "uid", uid, "mid", mid, "tracks", tracks))
	return result, nil
}

// unpublish from app
func unpublish(peer *signal.Peer, msg proto.UnpublishMsg) (interface{}, *nprotoo.Error) {
	log.Infof("signal.unpublish peer.ID()=%s msg=%v", peer.ID(), msg)

	mid := msg.MID
	rid := msg.RID
	uid := peer.ID()

	_, sfu, err := getRPCForSFU(mid)
	if err != nil {
		log.Warnf("Not found any sfu node, reject: %d => %s", err.Code, err.Reason)
		return nil, err
	}

	_, err = sfu.SyncRequest(proto.ClientUnPublish, util.Map("mid", mid, "uid", uid, "rid", rid))
	if err != nil {
		return nil, err
	}

	islb, found := getRPCForIslb()
	if !found {
		return nil, util.NewNpError(500, "Not found any node for islb.")
	}
	// if this mid is a webrtc pub
	// tell islb stream-remove, `rtc.DelPub(mid)` will be done when islb broadcast stream-remove
	islb.AsyncRequest(proto.IslbOnStreamRemove, util.Map("rid", rid, "uid", uid, "mid", mid))

	// _, avp, err := getRPCForAVP()
	// if err != nil {
	// 	return nil, util.NewNpError(500, "Not found any node for avp.")
	// }
	// avp.AsyncRequest(proto.AVPOnPipelineRemove, util.Map("mid", mid))
	return emptyMap, nil
}

func subscribe(peer *signal.Peer, msg proto.SubscribeMsg) (interface{}, *nprotoo.Error) {
	log.Infof("biz.subscribe peer.ID()=%s ", peer.ID())
	mid := msg.MID

	// Validate
	if mid == "" {
		return nil, midError
	} else if msg.Jsep.SDP == "" {
		return nil, jsepError
	}

	nodeID, sfu, err := getRPCForSFU(mid)
	if err != nil {
		log.Warnf("Not found any sfu node, reject: %d => %s", err.Code, err.Reason)
		return nil, util.NewNpError(err.Code, err.Reason)
	}

	// TODO:
	if nodeID != "node for mid" {
		log.Warnf("Not the same node, need to enable sfu-sfu relay!")
	}

	room := signal.GetRoomByPeer(peer.ID())
	uid := peer.ID()
	rid := room.ID()

	jsep := msg.Jsep

	islb, found := getRPCForIslb()
	if !found {
		return nil, util.NewNpError(500, "Not found any node for islb.")
	}

	result, err := islb.SyncRequest(proto.IslbGetMediaInfo, proto.MediaInfo{RID: rid, MID: mid})
	if err != nil {
		log.Warnf("reject: %d => %s", err.Code, err.Reason)
		return nil, util.NewNpError(err.Code, err.Reason)
	}
	var some map[string]interface{}
	if err := result.Unmarshal(&some); err != nil {
		return nil, err
	}
	// subMsg := proto.SFUSubscribeMsg{
	// 	MediaInfo: proto.MediaInfo{
	// 		UID: uid, RID: rid, MID: mid,
	// 	},
	// }
	result, err = sfu.SyncRequest(proto.ClientSubscribe, util.Map("uid", uid, "rid", rid, "mid", mid, "tracks", some["tracks"], "jsep", jsep))
	if err != nil {
		log.Warnf("reject: %d => %s", err.Code, err.Reason)
		return nil, util.NewNpError(err.Code, err.Reason)
	}

	log.Infof("subscribe: result => %v", result)
	return result, nil
}

func unsubscribe(peer *signal.Peer, msg proto.UnsubscribeMsg) (interface{}, *nprotoo.Error) {
	log.Infof("biz.unsubscribe peer.ID()=%s msg=%v", peer.ID(), msg)
	mid := msg.MID

	// Validate
	if mid == "" {
		return nil, midError
	}

	_, sfu, err := getRPCForSFU(mid)
	if err != nil {
		log.Warnf("Not found any sfu node, reject: %d => %s", err.Code, err.Reason)
		return nil, util.NewNpError(err.Code, err.Reason)
	}

	result, err := sfu.SyncRequest(proto.ClientUnSubscribe, util.Map("mid", mid))
	if err != nil {
		log.Warnf("reject: %d => %s", err.Code, err.Reason)
		return nil, util.NewNpError(err.Code, err.Reason)
	}

	log.Infof("publish: result => %v", result)
	return result, nil
}

func broadcast(peer *signal.Peer, msg proto.BroadcastMsg) (interface{}, *nprotoo.Error) {
	log.Infof("biz.broadcast peer.ID()=%s msg=%v", peer.ID(), msg)

	// Validate
	if msg.RID == "" || msg.UID == "" {
		return nil, ridError
	}

	islb, found := getRPCForIslb()
	if !found {
		return nil, util.NewNpError(500, "Not found any node for islb.")
	}
	rid, uid, info := msg.RID, msg.UID, msg.Info
	islb.AsyncRequest(proto.IslbOnBroadcast, util.Map("rid", rid, "uid", uid, "info", info))
	return emptyMap, nil
}

func trickle(peer *signal.Peer, msg proto.TrickleMsg) (interface{}, *nprotoo.Error) {
	log.Infof("biz.trickle peer.ID()=%s msg=%v", peer.ID(), msg)
	mid := msg.MID

	// Validate
	if msg.RID == "" || msg.UID == "" {
		return nil, ridError
	}

	_, sfu, err := getRPCForSFU(mid)
	if err != nil {
		log.Warnf("Not found any sfu node, reject: %d => %s", err.Code, err.Reason)
		return nil, util.NewNpError(err.Code, err.Reason)
	}

	trickle := msg.Trickle

	sfu.AsyncRequest(proto.ClientTrickleICE, util.Map("mid", mid, "trickle", trickle))
	return emptyMap, nil
}

// room setting
func roomSetting(peer *signal.Peer, msg proto.SettingMsg) (interface{}, *nprotoo.Error) {
	log.Infof("room setting request:", msg)
	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	// only host can do this
	if !signal.IsHost(rid, msg.UID) {
		return nil, hostPermisionError
	}
	signal.SetRoomSetting(rid, &msg)
	return emptyMap, nil
}

// muteall
func muteall(peer *signal.Peer, msg proto.MuteAllMsg) (interface{}, *nprotoo.Error) {
	log.Infof("muteall request:", msg)
	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	// only host can do this
	if !signal.IsHost(rid, msg.UID) {
		return nil, hostPermisionError
	}

	signal.SetMuteAll(rid, msg)
	if msg.MuteAllLock && msg.MuteAll {
		if err := sfuMuteParticipant(msg.RoomInfo, msg.UID, msg.MuteAll, true); err != nil {
			log.Errorf("%v", err)
		}
	} else if !msg.MuteAll {
		if err := sfuMuteParticipant(msg.RoomInfo, msg.UID, msg.MuteAll, true); err != nil {
			log.Errorf("%v", err)
		}
	}

	// notify all people except host
	// signal.NotifyAllWithoutID(rid, msg.UID, proto.NotifyMuteAll, msg)
	if err := signal.PushRoomSettingMsg(msg.UID, msg.RID); err != nil {
		log.Errorf("%v", err)
	}
	signal.NotifyAll(rid, proto.NotifyMuteAll, msg)
	// notify someone
	return emptyMap, nil
}

// change host
func changeHost(peer *signal.Peer, msg proto.HostControlMsg) (interface{}, *nprotoo.Error) {
	log.Infof("changeHost request:", msg)
	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	// only host can do this
	if !signal.IsHost(rid, msg.UID) {
		return nil, hostPermisionError
	}
	signal.ChangeHost(rid, msg.One)
	signal.HostLeave(rid)
	if err := signal.PushRoomSettingMsg(msg.One, msg.RID); err != nil {
		log.Errorf("%v", err)
	}
	signal.NotifyAll(rid, proto.ClientOnParticipant, signal.GetParticipants(rid))
	return emptyMap, nil
}

// remove participant
func removeParticipant(peer *signal.Peer, msg proto.HostControlMsg) (interface{}, *nprotoo.Error) {
	log.Infof("removeParticipant request:", msg)
	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	// only host can do this
	if !signal.IsHost(rid, msg.UID) {
		return nil, hostPermisionError
	}

	signal.NotifySomeone(msg.RID, msg.One, proto.NotifyHostControl, msg)
	return emptyMap, nil
}

// mute audio of certain participant
func muteParticipant(peer *signal.Peer, msg proto.HostControlMsg) (interface{}, *nprotoo.Error) {
	log.Infof("muteParticipant request:", msg)
	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	// only host can do this
	if !signal.IsHost(rid, msg.UID) {
		return nil, hostPermisionError
	}

	if _, lock := signal.GetMuteAll(rid); lock && msg.ControlBool {
		if err := sfuMuteParticipant(msg.RoomInfo, msg.One, msg.ControlBool, false); err != nil {
			log.Errorf("%v", err)
		}
	} else if !msg.ControlBool {
		if err := sfuMuteParticipant(msg.RoomInfo, msg.One, msg.ControlBool, false); err != nil {
			log.Errorf("%v", err)
		}
	}

	signal.NotifySomeone(msg.RID, msg.One, proto.NotifyHostControl, msg)
	return emptyMap, nil
}

// If isMuteAll == false, This function will only mute the users of the uid in sfu.
// If isMuteAll == true, This function will not mute the user(uid) passed in, it will mute all other users in the sfu.
func sfuMuteParticipant(rif proto.RoomInfo, uid proto.UID, isMuted bool, isMuteAll bool) *nprotoo.Error {
	// Get islb
	islb, found := getRPCForIslb()
	if !found {
		return util.NewNpError(500, "Not found any node for islb.")
	}
	// Send mute participant message to the SFU
	islb.AsyncRequest(proto.IslbGetPubs, rif).Then(
		func(result nprotoo.RawMessage) {
			var resMsg proto.GetPubResp
			if err := result.Unmarshal(&resMsg); err != nil {
				log.Errorf("Unmarshal pub response %v", err)
				return
			}
			log.Infof("IslbGetPubs: result=%v", result)
			if isMuteAll {
				// Create target list.
				var targets = make(map[string][]proto.MID)
				for _, pub := range resMsg.Pubs {
					if pub.MID == "" {
						continue
					}
					if uid == pub.UID {
						continue
					}
					if val, ok := targets[pub.NID]; ok {
						targets[pub.NID] = append(val, pub.MID)
					} else {
						targets[pub.NID] = []proto.MID{pub.MID}
					}
				}
				for s, ms := range targets {
					sfu := getSFUByNid(s)
					sfu.AsyncRequest(proto.SFUMuteAll, util.Map("is_muted", isMuted, "mute_list", ms))
				}
			} else {
				for _, pub := range resMsg.Pubs {
					if pub.MID == "" {
						continue
					}
					if uid != pub.UID {
						continue
					}
					_, sfu, err := getRPCForSFU(pub.MID)
					if err != nil {
						log.Warnf("Not found any sfu node when mute participant, reject: %d => %s", err.Code, err.Reason)
						return
					} else {
						log.Infof("Sending SFU mute participant msg! Target:%v", pub.UID)
						sfu.AsyncRequest(proto.SFUMuteParticipant, util.Map("mid", pub.MID, "is_muted", isMuted))
					}
				}
			}
		},
		func(err *nprotoo.Error) {})

	return nil
}

// Msg -> IsMuted here is the target status. For example, the previous status is mute, the client will send IsMute = false to here. AKA sending the current mic status.
// The current logic is: If mute lock locked, no matter what msg client send, this function will tell sfu to mute the client.
// If mute lock unlocked, no matter what msg client send, this function will tell sfu to unmute the client.
// If client IS HOST, this will skip the entire logic to ensure host dont mute it self on sfu.
func CanClientUnmute(peer *signal.Peer, msg proto.MuteSomeoneMsg) (interface{}, *nprotoo.Error) {
	log.Infof("CanClientUnmute request:", msg)
	if signal.IsHost(msg.RID, msg.UID) {
		return true, nil
	}
	_, mutelock := signal.GetMuteAll(msg.RID)
	if mutelock {
		// Return the state ASAP, deal with the sfu later
		go func() {
			if err := sfuMuteParticipant(msg.RoomInfo, msg.UID, true, false); err != nil {
				log.Errorf("%v", err)
			}
		}()
		return msg.IsMuted, nil
	} else {
		// Return the state ASAP, deal with the sfu later
		go func() {
			if err := sfuMuteParticipant(msg.RoomInfo, msg.UID, false, false); err != nil {
				log.Errorf("%v", err)
			}
		}()
		return true, nil
	}
}

func HostControlSwitch(peer *signal.Peer, msg proto.HostControlMsg) (interface{}, *nprotoo.Error) {
	log.Infof("Host Control request:", msg)
	// rid := msg.RID

	// // Validate
	// if msg.RID == "" {
	// 	return nil, ridError
	// }

	// // only host can do this
	// if !signal.IsHost(rid, msg.UID) {
	// 	return nil, hostPermisionError
	// }
	// Define the meaning of each num
	const (
		mutePartiAudio = 0
		mutePartiVideo = 1
		kickParti      = 2
		updateHost     = 3
	)

	switch msg.ControlType {
	case mutePartiAudio:
		return muteParticipant(peer, msg)
	case mutePartiVideo:
		return muteVideoParticipant(peer, msg)
	case kickParti:
		return removeParticipant(peer, msg)
	case updateHost:
		return changeHost(peer, msg)
	default:
		return nil, hostPermisionError
	}

	return nil, nil
}

// mute video of certain participant
func muteVideoParticipant(peer *signal.Peer, msg proto.HostControlMsg) (interface{}, *nprotoo.Error) {
	log.Infof("muteVideoParticipant request:", msg)
	rid := msg.RID

	// Validate
	if msg.RID == "" {
		return nil, ridError
	}

	// only host can do this
	if !signal.IsHost(rid, msg.UID) {
		return nil, hostPermisionError
	}

	signal.NotifySomeone(msg.RID, msg.One, proto.NotifyHostControl, msg)
	return emptyMap, nil
}
