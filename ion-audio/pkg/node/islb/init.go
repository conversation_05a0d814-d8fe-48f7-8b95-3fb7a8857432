package islb

import (
	"sync"
	"time"

	nprotoo "github.com/cloudwebrtc/nats-protoo"
	"github.com/pion/ion/pkg/db"
	"github.com/pion/ion/pkg/discovery"
	"github.com/pion/ion/pkg/proto"
)

const (
	redisLongKeyTTL = 24 * time.Hour
)

var (
	dc = "default"
	//nolint:unused
	nid           = "islb-unkown-node-id"
	protoo        *nprotoo.NatsProtoo
	redis         *db.Redis
	services      map[string]discovery.Node
	servicesMutex *sync.RWMutex
	broadcaster   *nprotoo.Broadcaster
	midToUid      map[proto.MID]proto.RoomInfo
)

// Init func
func Init(dcID, nodeID, rpcID, eventID string, redisCfg db.Config, etcd []string, natsURL string) {
	dc = dcID
	nid = nodeID
	redis = db.NewRedis(redisCfg)
	protoo = nprotoo.NewNatsProtoo(natsURL)
	broadcaster = protoo.NewBroadcaster(eventID)
	services = make(map[string]discovery.Node)
	servicesMutex = new(sync.RWMutex)
	midToUid = make(map[proto.MID]proto.RoomInfo)
	handleRequest(rpcID)
	WatchAllStreams()
}
