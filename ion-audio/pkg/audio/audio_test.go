package audio

import (
	"encoding/json"
	"fmt"
	"testing"

	"golang.org/x/net/websocket"
)

func TestDecode(t *testing.T) {
	t.Run("test decode", func(t *testing.T) {
		var payload = make([]byte, 86)
		var samplerate = 16000
		var channels = 1
		// 20
		var frameSizeMs float32 = 20
		c := &Codec{}
		c.New<PERSON>ode<PERSON>(samplerate, channels, frameSizeMs)
		_, errdec := c.Decode(payload)
		if errdec != nil {
			t.<PERSON>("errdec: %v", errdec)
			// panic("Err!")
		}
	})
}

func testdial() {
	ac, err := NewConn()
	defer ac.Close()
	if err != nil {
		fmt.Println("err: ", err)
		return
	}
	var message string
	websocket.Message.Receive(ac.aCon, &message)
	var m map[string]string
	err = json.Unmarshal([]byte(message), &m)
	println(message)
	if err != nil {
		println(err.Error())
		return
	} else if m["code"] != "0" {
		fmt.Println("handshake fail!" + message)
		return
	}
}
