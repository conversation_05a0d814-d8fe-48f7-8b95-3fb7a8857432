package audio

import (
	"sync"
)

// AudioDataProduce save audio data
type AudioDataProduce struct {
	data      []byte
	FrameSize int
	len       int
}

// NewProduction initialize
func (adp *AudioDataProduce) NewProduction(size int) {
	adp.FrameSize = size
	adp.len = 0
	// adp.data = make([]byte, size * 10)
}

// Close close
func (adp *AudioDataProduce) Close() {
	adp.data = nil
}

//AudioProducingFactory audio produce factory
type AudioProducingFactory struct {
	AudioDataProduce
	dataMutex sync.Mutex
	chSend    chan bool
	chData    chan bool
}

// NewFactory initialize
func (apf *AudioProducingFactory) NewFactory(size int) {
	apf.AudioDataProduce.NewProduction(size)
	apf.chData = make(chan bool)
	apf.chSend = make(chan bool)
}

// GetData get audio rawdata
func (apf *AudioProducingFactory) GetData(buf []byte) {
	// multiproduce mutex
	apf.dataMutex.Lock()
	apf.data = append(apf.data, buf...)
	apf.len += len(buf)
	if apf.len >= apf.FrameSize {
		apf.chData <- true
		<-apf.chSend
	}
	apf.dataMutex.Unlock()
}

// SendData send audio rawdata to XunFei
func (apf *AudioProducingFactory) SendData() []byte {
	// stopping producer and comsumer visiting the same data
	<-apf.chData
	temp := apf.data[:apf.FrameSize]
	apf.data = apf.data[apf.FrameSize:]
	apf.len -= apf.FrameSize
	apf.chSend <- true
	return temp
}

// Close
func (apf *AudioProducingFactory) Close() {
	apf.AudioDataProduce.Close()
}
