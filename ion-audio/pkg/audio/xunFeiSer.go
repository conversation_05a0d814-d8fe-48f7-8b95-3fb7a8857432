package audio

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/pion/ion/pkg/log"
	"golang.org/x/net/websocket"
)

// this is XunFei Server Par
var HOST = "rtasr.xfyun.cn/v1/ws"

var APPID = "5eb920a3"
var APPKEY = "850a179ef7d3355b451744d7cf4e4ec0"

// AudioCon
type AudioCon struct {
	ACon  *websocket.Conn
	Start bool
	Check bool
	Count int
}

// NewConn initialize
func (ac *AudioCon) NewConn() error {
	var Xunerr error
	ts := strconv.FormatInt(time.Now().Unix(), 10)
	mac := hmac.New(sha1.New, []byte(APPKEY))
	strByte := []byte(APPID + ts)
	strMd5Byte := md5.Sum(strByte)
	strMd5 := fmt.Sprintf("%x", strMd5Byte)
	mac.Write([]byte(strMd5))
	signa := url.QueryEscape(base64.StdEncoding.EncodeToString(mac.Sum(nil)))
	requestParam := "appid=" + APPID + "&ts=" + ts + "&signa=" + signa
	ac.ACon, Xunerr = websocket.Dial("ws://"+HOST+"?"+requestParam, websocket.SupportedProtocolVersion, "http://"+HOST)
	ac.Count = 10
	return Xunerr
}

func (ac *AudioCon) Close() {
	ac.ACon.Close()
	log.Infof("XunFei ws cnn closed.")
}

func (ac *AudioCon) Counter() {
	if ac.Count > 0 {
		log.Infof("XunFei ac.Count--.")
		log.Infof("XunFei ac.Count = ", ac.Count)
		ac.Count--
	} else {
		log.Infof("XunFei ac.Check = true.")
		ac.Check = true
	}
}
