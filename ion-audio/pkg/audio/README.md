- xunFeiSer.go : [讯飞实时语音转写服务]([https://www.xfyun.cn/doc/asr/rtasr/API.html#%E6%8E%A5%E5%8F%A3%E8%AF%B4%E6%98%8E](https://www.xfyun.cn/doc/asr/rtasr/API.html#接口说明))，初始化websocket。
- decoder.go errors.go : 使用这个[go wrap的库](https://github.com/hraban/opus/tree/v2)，因为这个库里面的编译参数和docker上使用的Alpine镜像所需要的静态编译有[冲突](https://blog.hashbangbash.com/2014/04/linking-golang-statically/)，[原因](https://yryz.net/post/golang-docker-alpine-start-panic/)，不支持[动态链接](https://en.wikipedia.org/wiki/Dynamic-link_library)。但是，由于所依赖的[libopus，libopusfile](https://opus-codec.org/)使用[静态链接库](https://en.wikipedia.org/wiki/Static_library)进行编译的话，在go build使用参数```go build -ldflags '-extldflags "-static"'```，只能保证到第一步链接，即：libopus.a这个静态链接库使用了libm这个系统的基础数学库，而默认的链接是动态链接，这样的话，在Alpine镜像也是跑不通的。所以，把这个go-wrap库抽出来，重新写他的编译参数，gcc链接静态库的方式有这[几种方式](https://cloud.tencent.com/developer/article/1433457)。所以，这里就单独抽出两个go文件。在dockerfile中，进行编译的指令是```RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /sfu .```，CGO_ENABLED=0是强制静态编译，因为pure go是静态编译的，把cgo关掉后就是静态编译的，但是使用了libopus这个c的依赖库，必须使用cgo，不能把他置0，改成```RUN apt-get update && apt-get -y install libopus-dev libopusfile-dev``` ```RUN GOOS=linux go build -tags netgo -a -installsuffix cgo -o /avp .```，GOOS=linux是交叉编译为linux环境的二进制文件，而且还增加了-tags netgo，[因为](https://stackoverflow.com/questions/2725255/create-statically-linked-binary-that-uses-getaddrinfo/37245653#37245653)go的net底层代码是c写的，而且只能是动态链接，不能静态链接，除非使用pure go，[解决办法](https://www.jianshu.com/p/47aa2ebf5561)。

- Decode.go ： 模块化decode
- audioPro.go : 接收解码后的数据池，一个生产者消费者模型，现在的机制是，生产每超过1280byte，消费者就取出1280byte，送到sender发送器，送到讯飞服务。
- audioElements.go ： 集合了讯飞的websocket服务，解码器，数据池，sender发送器（每隔40ms发送1280byte），receiver接收器（接收返回的文字）