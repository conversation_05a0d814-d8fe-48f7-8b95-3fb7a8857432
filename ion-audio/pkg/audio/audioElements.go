package audio

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	nprotoo "github.com/cloudwebrtc/nats-protoo"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/process"
	"github.com/pion/ion/pkg/process/samples"
	"golang.org/x/net/websocket"
)

// a goroutine
//																					   40ms send
//										  sample           PCM        1280byte data    websocket
// Client(ID:A) -----> RTPTransport(ID:A) ------> Decodec -----> DataPool -----> Sender ------> XunFeiServer
//																									 |
//																									 | word
//																									 |
//																		      					  Receiver
//
//

var (
	// 每次发送的数据大小
	SLICE_SIZE = 1280
	// 结束标识
	END_TAG = "{\"end\": true}"
	// this is decode par
	samplerate = 16000
	channels   = 1
	// 20ms per package size, depend on client
	frameSizeMs float32 = 20
)

// Send status
const (
	STATUS_CONTINUE_FRAME = 0
	STATUS_LAST_FRAME     = 1
)

// Element name
const (
	// TypeAudioService
	TypeAudioService = "AudioService"
)

// client Id
type AudioServiceConfig struct {
	ID string
}

// AudioService
type AudioService struct {
	id              string
	acMutex         sync.Mutex
	ac              *AudioCon
	cod             *Codec
	apf             *AudioProducingFactory
	sendChan        chan bool
	receiveChan     chan bool
	sendStopChan    chan bool
	receiveStopChan chan bool
	rpc             *nprotoo.Requestor
}

// NewAudioService new a audio service
func NewAudioService(config AudioServiceConfig, rpc *nprotoo.Requestor) *AudioService {
	AS := &AudioService{
		id:              config.ID,
		sendChan:        make(chan bool),
		receiveChan:     make(chan bool),
		sendStopChan:    make(chan bool),
		receiveStopChan: make(chan bool),
		cod:             &Codec{},
		apf:             &AudioProducingFactory{},
		ac:              &AudioCon{},
		rpc:             rpc,
	}
	AS.cod.NewCodec(samplerate, channels, frameSizeMs)
	AS.apf.NewFactory(SLICE_SIZE)
	return AS
}

// Type return element type
func (a *AudioService) Type() string {
	return TypeAudioService
}

// Write build ws to XunFei Server, And send pcm data to datapool
func (a *AudioService) Write(sample *samples.Sample) error {
	if a.ac.Check {
		return nil
	}
	if sample.Type == samples.TypeOpus {
		a.acMutex.Lock()
		// ACon
		if a.ac.Start == false {
			a.ac.Counter()
			log.Infof("################Writer ACon")
			Xunerr := a.ac.NewConn()
			if Xunerr != nil {
				fmt.Println("err: ", Xunerr)
				a.acMutex.Unlock()
				return Xunerr
			}
			var message string
			websocket.Message.Receive(a.ac.ACon, &message)
			var mes map[string]string
			err := json.Unmarshal([]byte(message), &mes)
			log.Infof(message)
			if err != nil {
				log.Infof(err.Error())
				a.acMutex.Unlock()
				return err
			} else if mes["code"] != "0" {
				log.Infof("handshake fail! %v", message)
				a.acMutex.Unlock()
				return nil
			}
			a.sendChan <- true
			a.receiveChan <- true
			a.ac.Start = true
		} else {
			a.acMutex.Unlock()
			a.pushOpus(sample)
		}
	}
	return nil
}

// pushOpus finishing from opus sample to pcm data, and send it to data pool
func (a *AudioService) pushOpus(sample *samples.Sample) {
	if a.cod != nil && a.apf != nil {
		outdata, errdec := a.cod.Decode(sample.Payload)
		if errdec != nil {
			panic("Err! codec")
		}
		a.apf.GetData(outdata)
	} else {
		panic("cod or apf is nil")
	}
}

func (a *AudioService) Attach(e process.Element) error {
	return nil
}

func (a *AudioService) Read() <-chan *samples.Sample {
	return nil
}

func (a *AudioService) Close() {
	log.Infof("AudioService close")
	if a.ac.Start == false {
		close(a.sendChan)
		close(a.receiveChan)
		return
	}
	// 上传结束符
	if err := websocket.Message.Send(a.ac.ACon, END_TAG); err != nil {
		log.Infof("send string msg err: %v", err)
	} else {
		log.Infof("send end tag success, %v", len(END_TAG))
	}
	time.Sleep(40 * time.Millisecond)
	// 上传结束符
	if err := websocket.Message.Send(a.ac.ACon, END_TAG); err != nil {
		log.Infof("send string msg err: %v", err)
	} else {
		log.Infof("send end tag success, %v", len(END_TAG))
	}
	if true == <-a.receiveStopChan {
		log.Infof("AudioService close Chan internal")
		if a.ac.ACon != nil {
			a.ac.Close()
		}
	}
}

// SendPcm a goroutine of sender
func (a *AudioService) SendPcm() {
	_, ok := <-a.sendChan
	if ok {
		send(a.ac.ACon, a.apf)
	}

	// a.sendStopChan<-true
}

// ReceiveData a goroutine of receiver
func (a *AudioService) ReceiveData() {
	_, ok := <-a.receiveChan
	if ok {
		receive(a.ac.ACon, a.id, a.rpc)
		a.receiveStopChan <- true
	}

}

func send(conn *websocket.Conn, apf *AudioProducingFactory) {
	// 分片上传音频
	for {
		if conn == nil {
			break
		}

		temp := apf.SendData()
		if err := websocket.Message.Send(conn, temp[:apf.FrameSize]); err != nil {
			log.Infof("send byte msg err: %v", err)
			return
		}
		// println("send data success, sleep 40 ms")
		time.Sleep(40 * time.Millisecond)
	}
}

func receive(conn *websocket.Conn, mid string, rpc *nprotoo.Requestor) {
	for {
		var msg []byte
		var result map[string]string
		if conn == nil {
			break
		}
		if err := websocket.Message.Receive(conn, &msg); err != nil {
			if err.Error() == "EOF" {
				log.Infof("receive date end")
			} else {
				log.Infof("receive msg error: %v", err.Error())
			}
			break
		}

		err := json.Unmarshal(msg, &result)
		if err != nil {
			log.Infof(string(msg))
			log.Infof("response json parse error")
			continue
		}
		log.Infof("result: %v", result)
		if result["code"] == "0" {
			if errp := printWord(result); errp != nil {
				break
			}
			result["mid"] = mid
			rpc.AsyncRequest("subtitle", result)
		} else {
			log.Infof("invalid result: %v", string(msg))
		}
	}
}

func printWord(result map[string]string) error {
	var asrResult AsrResult
	err := json.Unmarshal([]byte(result["data"]), &asrResult)
	if err != nil {
		log.Infof("parse asrResult error: %v", err.Error())
		return err
	}
	var str string
	if asrResult.Cn.St.Type == "0" {
		log.Infof("------------------------------------------------------------------------------------------------------------------------------------")
		// 最终结果
		for _, wse := range asrResult.Cn.St.Rt[0].Ws {
			for _, cwe := range wse.Cw {
				str += cwe.W
			}
		}
		log.Infof(str)
		log.Infof("\r\n------------------------------------------------------------------------------------------------------------------------------------")
	} else {
		for _, wse := range asrResult.Cn.St.Rt[0].Ws {
			for _, cwe := range wse.Cw {
				str += cwe.W
			}
		}
		log.Infof(str)
	}
	return nil
}

type AsrResult struct {
	Cn    Cn      `json:"cn"`
	SegId float64 `json:"seg_id"`
}

type Cn struct {
	St St `json:"st"`
}

type St struct {
	Bg   string      `json:"bg"`
	Ed   string      `json:"ed"`
	Type string      `json:"type"`
	Rt   []RtElement `json:"rt"`
}

type RtElement struct {
	Ws []WsElement `json:"ws"`
}

type WsElement struct {
	Wb float64     `json:"wb"`
	We float64     `json:"we"`
	Cw []CwElement `json:"cw"`
}

type CwElement struct {
	W  string `json:"w"`
	Wp string `json:"wp"`
}
