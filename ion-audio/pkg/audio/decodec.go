package audio

type Codec struct {
	samplerate  int
	channels    int
	frameSizeMs float32
	frameSize   int
	pcm         []int16
	dec         *Decoder
}

// littleEnd
func int16ToByte(data []int16) []byte {
	lenData := len(data)
	var out []byte
	for i := 0; i < lenData; i++ {
		out = append(out, byte(data[i]), byte(data[i]>>8))
	}
	return out
}

// NewCodec initialize
func (cod *Codec) NewCodec(sample_rate, channels int, frameSizeMs float32) error {
	var err error
	cod.samplerate = sample_rate
	cod.channels = channels
	cod.frameSizeMs = frameSizeMs
	cod.frameSize = int(float32(channels) * float32(cod.samplerate) * cod.frameSizeMs / 1000)
	cod.pcm = make([]int16, int(cod.frameSize))
	cod.dec, err = NewDecoder(cod.samplerate, cod.channels)
	return err
}

// Decode decode opus data
func (cod *Codec) Decode(data []byte) ([]byte, error) {
	_, err := cod.dec.Decode(data, cod.pcm)
	outdata := int16ToByte(cod.pcm)
	return outdata, err
}
