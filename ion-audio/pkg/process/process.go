package process

import (
	"sync"
	"time"

	"github.com/pion/ion/pkg/discovery"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/rtc/rtpengine"
	"github.com/pion/ion/pkg/rtc/transport"

	// "github.com/pion/ion/pkg/util"

	nprotoo "github.com/cloudwebrtc/nats-protoo"
)

const (
	statCycle = 3 * time.Second
)

type GetRpcCallback func() (*nprotoo.Requestor, bool)

var (
	pipelines    = make(map[string]*Pipeline)
	pipelineLock sync.RWMutex
	stop         bool
	protoo       *nprotoo.NatsProtoo
	rpcs         map[string]*nprotoo.Requestor
	services     map[string]discovery.Node
)

// InitRTP rtp port
func InitRTP(port int, kcpKey, kcpSalt, natsURL string) error {
	// show stat about all pipelines
	go check()
	protoo = nprotoo.NewNatsProtoo(natsURL)
	services = make(map[string]discovery.Node)
	rpcs = make(map[string]*nprotoo.Requestor)

	var connCh chan *transport.RTPTransport
	var err error
	// accept relay rtptransport
	if kcpKey != "" && kcpSalt != "" {
		connCh, err = rtpengine.ServeWithKCP(port, kcpKey, kcpSalt)
	} else {
		connCh, err = rtpengine.Serve(port)
	}
	if err != nil {
		log.Errorf("process.InitRPC err=%v", err)
		return err
	}
	go func() {
		for {
			if stop {
				log.Infof("It is InitRTP stop!!!!!!!!!!!!!!!!!!!!")
				return
			}
			for rtpTransport := range connCh {
				go func(rtpTransport *transport.RTPTransport) {
					id := <-rtpTransport.IDChan
					if id == "" {
						log.Errorf("invalid id from incoming rtp transport")
						return
					}

					islb, found := GetRPCForIslb()

					if !found {
						log.Errorf("failed to get islb rpc")
						return
					}

					log.Infof("accept new rtp id=%s conn=%s", id, rtpTransport.RemoteAddr().String())
					addPipeline(id, rtpTransport, islb)
				}(rtpTransport)
			}
		}
	}()
	return nil
}

func addPipeline(id string, pub transport.Transport, rpc *nprotoo.Requestor) *Pipeline {
	log.Infof("process.addPipeline id=%s", id)
	pipelineLock.Lock()
	defer pipelineLock.Unlock()
	pipelines[id] = NewPipeline(id, pub, rpc)
	return pipelines[id]
}

// GetPipeline get pipeline from map
func GetPipeline(id string) *Pipeline {
	log.Infof("process.GetPipeline id=%s", id)
	pipelineLock.RLock()
	defer pipelineLock.RUnlock()
	return pipelines[id]
}

// DelPipeline delete pub
func DelPipeline(id string) {
	log.Infof("DelPipeline id=%s", id)
	pipeline := GetPipeline(id)
	if pipeline == nil {
		return
	}
	pipeline.Close()
	pipelineLock.Lock()
	defer pipelineLock.Unlock()
	delete(pipelines, id)
}

// Close close all pipelines
func Close() {
	if stop {
		return
	}
	stop = true
	pipelineLock.Lock()
	defer pipelineLock.Unlock()
	for id, pipeline := range pipelines {
		if pipeline != nil {
			pipeline.Close()
			delete(pipelines, id)
		}
	}
}

// check show all pipelines' stat
func check() {
	t := time.NewTicker(statCycle)
	for range t.C {
		info := "\n----------------process-----------------\n"
		// log.Infof("It is check!!!!!!!!!!!!!!!!len(pipelines)=%v", len(pipelines))
		pipelineLock.Lock()
		if len(pipelines) == 0 {
			pipelineLock.Unlock()
			continue
		}

		for id, pipeline := range pipelines {
			if !pipeline.Alive() {
				log.Infof("It is pipeline'close on check!!!!!!!!!!!!!!!!")
				pipeline.Close()
				delete(pipelines, id)
				log.Infof("Stat delete %v", id)
			}
			info += "pipeline: " + id + "\n"
		}
		pipelineLock.Unlock()
		log.Infof(info)
	}
}

func GetRPCForIslb() (*nprotoo.Requestor, bool) {
	for _, item := range services {
		if item.Info["service"] == "islb" {
			id := item.Info["id"]
			rpc, found := rpcs[id]
			if !found {
				rpcID := discovery.GetRPCChannel(item)
				log.Infof("Create rpc [%s] for islb", rpcID)
				rpc = protoo.NewRequestor(rpcID)
				rpcs[id] = rpc
			}
			return rpc, true
		}
	}
	log.Warnf("No islb node was found.")
	return nil, false
}

func WatchServiceNodes(service string, state discovery.NodeStateType, node discovery.Node) {
	id := node.ID

	if state == discovery.UP {

		if _, found := services[id]; !found {
			services[id] = node
		}

		service := node.Info["service"]
		name := node.Info["name"]
		log.Debugf("Service [%s] %s => %s", service, name, id)

		_, found := rpcs[id]
		if !found {
			rpcID := discovery.GetRPCChannel(node)
			log.Infof("Create islb requestor: rpcID => [%s]", rpcID)
			rpcs[id] = protoo.NewRequestor(rpcID)
		}

	} else if state == discovery.DOWN {
		delete(rpcs, id)
		delete(services, id)
	}

}
