version: "3.7"

services:
  sfu:
    image: pionwebrtc/ion-sfu:v0.4.6
    ports:
      - "5000-5200:5000-5200/udp"
    depends_on:
      - nats
      - etcd
    networks:
      - ionnet

  biz:
    image: pionwebrtc/ion-biz:v0.4.6
    volumes:
      - "./configs/certs:/configs/certs"
    ports:
      - 8443:8443
    networks:
      - ionnet
    depends_on:
      - nats
      - etcd

  islb:
    image: pionwebrtc/ion-islb:v0.4.6
    depends_on:
      - nats
      - etcd
      - redis
    networks:
      - ionnet

  nats:
    image: nats
    ports:
      - 4223:4222
    networks:
      - ionnet

  etcd:
    image: gcr.io/etcd-development/etcd:v3.4.9
    entrypoint: "/usr/local/bin/etcd"
    command: "--listen-client-urls http://0.0.0.0:2379 --advertise-client-urls http://0.0.0.0:2379"
    ports:
      - 2389:2379
    networks:
      - ionnet

  redis:
    image: redis:5.0.9
    ports:
      - 6380:6379
    networks:
      - ionnet

networks:
  ionnet:
    external: true
    name: ionnet
    driver: bridge
