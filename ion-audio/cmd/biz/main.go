package main

import (
	"net/http"
	_ "net/http/pprof"

	conf "github.com/pion/ion/pkg/conf/biz"
	"github.com/pion/ion/pkg/db"
	"github.com/pion/ion/pkg/discovery"
	"github.com/pion/ion/pkg/entity"
	"github.com/pion/ion/pkg/log"
	"github.com/pion/ion/pkg/node/biz"
	"github.com/pion/ion/pkg/signal"
)

func init() {
	allow_redis := conf.Signal.AllowRedisCache
	if allow_redis {
		redisCfg := db.Config{
			Addrs: conf.Redis.Addrs,
			Pwd:   conf.Redis.Pwd,
			DB:    conf.Redis.DB,
		}
		entity.Initredis(redisCfg)
	}

	log.Init(conf.Log.Level)
	entity.Init(conf.Database.Name, conf.Database.Addrs, conf.Database.UidKey)
	signal.Init(signal.WebSocketServerConfig{
		Host:          conf.Signal.Host,
		Port:          conf.Signal.Port,
		CertFile:      conf.Signal.Cert,
		KeyFile:       conf.Signal.Key,
		Authorization: conf.Signal.Authorization,
		WebSocketPath: conf.Signal.WebSocketPath,
	}, conf.Signal.AllowDisconnected, allow_redis, biz.Entry, biz.TinodeEntry)
}

func close() {
	biz.Close()
}

func main() {
	log.Infof("--- Starting Biz Node ---")

	if conf.Global.Pprof != "" {
		go func() {
			log.Infof("Start pprof on %s", conf.Global.Pprof)
			err := http.ListenAndServe(conf.Global.Pprof, nil)
			if err != nil {
				panic(err)
			}
		}()
	}

	serviceNode := discovery.NewServiceNode(conf.Etcd.Addrs, conf.Global.Dc)
	serviceNode.RegisterNode("biz", "node-biz", "biz-channel-id")

	rpcID := serviceNode.GetRPCChannel()
	eventID := serviceNode.GetEventChannel()
	biz.Init(conf.Global.Dc, serviceNode.NodeInfo().ID, rpcID, eventID, conf.Nats.URL, conf.Tinode.Addr)

	serviceWatcher := discovery.NewServiceWatcher(conf.Etcd.Addrs, conf.Global.Dc)
	go serviceWatcher.WatchServiceNode("islb", biz.WatchServiceNodes)

	defer close()
	select {}
}
