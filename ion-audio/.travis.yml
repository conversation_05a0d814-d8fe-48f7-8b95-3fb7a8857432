dist: bionic
language: go

env:
  global:
    - GO111MODULE=on
    - GOLANGCI_LINT_VERSION=1.19.1

cache:
  directories:
    - ${HOME}/.cache/go-build
    - ${GOPATH}/pkg/mod
  npm: true
  yarn: true

_lint_job: &lint_job
  env: CACHE_NAME=lint
  before_install:
    - if [ -f .github/.ci.conf ]; then . .github/.ci.conf; fi
  install: skip
  before_script:
    - |
      curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh \
        | bash -s - -b $GOPATH/bin v${GOLANGCI_LINT_VERSION}
  script:
    - bash .github/lint-filename.sh
    - golangci-lint run ./...

_build_job: &build_job
  env: CACHE_NAME=build
  script:
    - go build ./cmd/biz
    - go build ./cmd/islb
    - go build ./cmd/sfu


_build_avp_docker_job: &build_avp_docker_job
  env: CACHE_NAME=build_avp_docker
  script:
    - echo "TRAVIS_TAG= $TRAVIS_TAG"
    - docker build --tag pionwebrtc/ion-avp:latest -f ./docker/avp.Dockerfile .
  before_deploy:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
    - docker tag pionwebrtc/ion-avp:latest pionwebrtc/ion-avp:"$TRAVIS_TAG"
  deploy:
    provider: script
    script: docker push pionwebrtc/ion-avp:latest && docker push pionwebrtc/ion-avp:"$TRAVIS_TAG"
    on:
      tags: true

_build_biz_docker_job: &build_biz_docker_job
  env: CACHE_NAME=build_biz_docker
  script:
    - docker build --tag pionwebrtc/ion-biz:latest -f ./docker/biz.Dockerfile .
  before_deploy:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
    - docker tag pionwebrtc/ion-biz:latest pionwebrtc/ion-biz:"$TRAVIS_TAG"
  deploy:
    provider: script
    script: docker push pionwebrtc/ion-biz:latest && docker push pionwebrtc/ion-biz:"$TRAVIS_TAG"
    on:
      tags: true

_build_islb_docker_job: &build_islb_docker_job
  env: CACHE_NAME=build_islb_docker
  script:
    - docker build --tag pionwebrtc/ion-islb:latest -f ./docker/islb.Dockerfile .
  before_deploy:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
    - docker tag pionwebrtc/ion-islb:latest pionwebrtc/ion-islb:"$TRAVIS_TAG"
  deploy:
    provider: script
    script: docker push pionwebrtc/ion-islb:latest && docker push pionwebrtc/ion-islb:"$TRAVIS_TAG"
    on:
      tags: true

_build_sfu_docker_job: &build_sfu_docker_job
  env: CACHE_NAME=build_sfu_docker
  script:
    - docker build --tag pionwebrtc/ion-sfu:latest -f ./docker/sfu.Dockerfile .
  before_deploy:
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
    - docker tag pionwebrtc/ion-sfu:latest pionwebrtc/ion-sfu:"$TRAVIS_TAG"
  deploy:
    provider: script
    script: docker push pionwebrtc/ion-sfu:latest && docker push pionwebrtc/ion-sfu:"$TRAVIS_TAG"
    on:
      tags: true

_test_job: &test_job
  env: CACHE_NAME=test
  before_install:
    - if [ -f .github/.ci.conf ]; then . .github/.ci.conf; fi
    - go mod download
  install:
    - go build ./...
  before_script:
    - docker-compose -f docker-compose.test.yml up -d
  script:
    - testpkgs=${TEST_PACKAGES:-$(go list ./... | grep -v cmd | grep -v conf | grep -v node)}
    - coverpkgs=$(echo "${testpkgs}" | paste -s -d ',')
    - |
      go test \
        -coverpkg=${coverpkgs} -coverprofile=cover.out -covermode=atomic \
        ${TEST_EXTRA_ARGS:-} \
        -v -race ${testpkgs}
    - if [ -n "${TEST_HOOK}" ]; then ${TEST_HOOK}; fi
  after_success:
    - travis_retry bash <(curl -s https://codecov.io/bash) -c -F go
_test_i386_job: &test_i386_job
  env: CACHE_NAME=test386
  services: docker
  before_install:
    - if [ -f .github/.ci.conf ]; then . .github/.ci.conf; fi
  before_script:
    - docker-compose -f docker-compose.test.yml up -d
  script:
    - testpkgs=${TEST_PACKAGES:-$(go list ./... | grep -v cmd | grep -v conf)}
    - |
      docker run \
        -u $(id -u):$(id -g) \
        -e "GO111MODULE=on" \
        -e "CGO_ENABLED=0" \
        -v ${PWD}:/go/src/github.com/pion/$(basename ${PWD}) \
        -v ${HOME}/gopath/pkg/mod:/go/pkg/mod \
        -v ${HOME}/.cache/go-build:/.cache/go-build \
        -w /go/src/github.com/pion/$(basename ${PWD}) \
        --network ion_default \
        -it i386/golang:${GO_VERSION}-alpine \
        /usr/local/go/bin/go test \
          ${TEST_EXTRA_ARGS:-} \
          -v ${testpkgs}
jobs:
  include:
    - <<: *lint_job
      name: Lint 1.14
      go: 1.14
      if: branch = master OR tag IS present
    - <<: *build_job
      name: Build 1.13
      go: 1.13
      if: branch = master OR tag IS present
    - <<: *build_job
      name: Build 1.14
      go: 1.14
      if: branch = master OR tag IS present
    - <<: *test_job
      name: Test 1.13
      go: 1.13
      if: branch = master OR tag IS present
    - <<: *test_job
      name: Test 1.14
      go: 1.14
      if: branch = master OR tag IS present
    - <<: *build_avp_docker_job
      name: Build avp docker
      if: branch = master OR tag IS present
    - <<: *build_biz_docker_job
      name: Build biz docker
      if: branch = master OR tag IS present
    - <<: *build_islb_docker_job
      name: Build islb docker
      if: branch = master OR tag IS present
    - <<: *build_sfu_docker_job
      name: Build sfu docker
      if: branch = master OR tag IS present
    # - <<: *test_i386_job
    #   name: Test i386 1.13
    #   env: GO_VERSION=1.13
    #   go: 1.14 # version for host environment used to go list
    # - <<: *test_i386_job
    #   name: Test i386 1.14
    #   env: GO_VERSION=1.14
    #   go: 1.14 # version for host environment used to go list

notifications:
  email: false