version: "3.7"

services:
  sfu:
    image: fangyu/ion-sfu:latest
    build:
      dockerfile: ./docker/sfu.Dockerfile
      context: .
    container_name: "fangyu-ion-sfu"
    volumes:
      - "./configs/docker/sfu.toml:/configs/sfu.toml"
    ports:
      - "5000-5200:5000-5200/udp"
    depends_on:
      - nats
      - etcd
    networks:
      - fangyu_network

  biz:
    image: fangyu/ion-biz:latest
    build:
      dockerfile: ./docker/biz.Dockerfile
      context: .
    container_name: "fangyu-ion-biz"
    volumes:
      - "./configs/docker/biz.toml:/configs/biz.toml"
    ports:
      - 8443:8443
    networks:
      - fangyu_network
    depends_on:
      - nats
      - etcd
    #   - mysql

  islb:
    image: fangyu/ion-islb:latest
    build:
      dockerfile: ./docker/islb.Dockerfile
      context: .
    container_name: "fangyu-ion-islb"
    volumes:
      - "./configs/docker/islb.toml:/configs/islb.toml"
    depends_on:
      - nats
      - etcd
      - redis
    networks:
      - fangyu_network

#   avp:
#     image: fangyu/ion-avp:latest
#     build:
#       dockerfile: ./docker/avp.Dockerfile
#       context: .
#     container_name: "fangyu-ion-avp"
#     volumes:
#       - "./configs/docker/avp.toml:/configs/avp.toml"
#       - "./:/out/"
#     depends_on:
#       - nats
#       - etcd
#     networks:
#       - fangyu_network

  nats:
    image: nats
    container_name: "fangyu-ion-nats"
    ports:
      - 4223:4222
    networks:
      - fangyu_network

  etcd:
    image: gcr.io/etcd-development/etcd:v3.4.9
    container_name: "fangyu-ion-etcd"
    entrypoint: "/usr/local/bin/etcd"
    command: "--listen-client-urls http://0.0.0.0:2379 --advertise-client-urls http://0.0.0.0:2379"
    ports:
      - 2389:2379
    networks:
      - fangyu_network

  redis:
    image: redis:5.0.9
    container_name: "fangyu-ion-redis"
    ports:
      - 6380:6379
    networks:
      - fangyu_network

#   mysql:
#     image: mysql:latest
#     ports:
#       - 33333:3306
#     volumes:
#       - "/home/<USER>/fangyu/mysql/data:/var/lib/mysql"
#     environment:
#       - TZ=Asia/Shanghai
#       - MYSQL_DATABASE=ion
#       - MYSQL_ROOT_PASSWORD=password123
#     networks:
#       - fangyu_network 
#     expose:
#       - '3306'


networks:
  fangyu_network:
    external: true
    name: fangyu_network
    driver: bridge