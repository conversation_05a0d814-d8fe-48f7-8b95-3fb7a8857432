FROM golang:1.14.4-stretch

ENV GO111MODULE=on
# ENV GOPROXY=https://goproxy.io

WORKDIR $GOPATH/src/github.com/fangyu/ion

COPY go.mod go.sum ./
RUN cd $GOPATH/src/github.com/fangyu/ion && go mod download

COPY pkg/ $GOPATH/src/github.com/fangyu/ion/pkg
COPY cmd/ $GOPATH/src/github.com/fangyu/ion/cmd

WORKDIR $GOPATH/src/github.com/fangyu/ion/cmd/sfu
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /sfu .

FROM alpine:3.12.0

RUN apk --no-cache add ca-certificates
ARG TZ='Asia/Shanghai'
ENV DEFAULT_TZ ${TZ}
RUN apk upgrade --update \
  && apk add -U tzdata \
  && cp /usr/share/zoneinfo/${DEFAULT_TZ} /etc/localtime \
  && apk del tzdata \
  && rm -rf \
  /var/cache/apk/*

# RUN apk add iproute2

COPY --from=0 /sfu /usr/local/bin/sfu

COPY configs/docker/sfu.toml /configs/sfu.toml

ENTRYPOINT ["/usr/local/bin/sfu"]
CMD ["-c", "/configs/sfu.toml"]
