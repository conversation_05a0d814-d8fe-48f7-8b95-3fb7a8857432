FROM golang:1.14.4-stretch

ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.io

WORKDIR $GOPATH/src/github.com/fangyu/ion

COPY go.mod go.sum ./
RUN cd $GOPATH/src/github.com/fangyu/ion && go mod download

COPY pkg/ $GOPATH/src/github.com/fangyu/ion/pkg
COPY cmd/ $GOPATH/src/github.com/fangyu/ion/cmd
RUN apt-get update && apt-get -y install libopus-dev libopusfile-dev

WORKDIR $GOPATH/src/github.com/fangyu/ion/cmd/avp
RUN GOOS=linux go build -tags netgo -a -installsuffix cgo -o /avp .

FROM alpine:3.12.0

RUN apk --no-cache add ca-certificates
ARG TZ='Asia/Shanghai'
ENV DEFAULT_TZ ${TZ}
RUN apk upgrade --update \
  && apk add -U tzdata \
  && cp /usr/share/zoneinfo/${DEFAULT_TZ} /etc/localtime \
  && apk del tzdata \
  && rm -rf \
  /var/cache/apk/*

COPY --from=0 /avp /usr/local/bin/avp
COPY configs/docker/avp.toml /configs/avp.toml

ENTRYPOINT ["/usr/local/bin/avp"]
CMD ["-c", "/configs/avp.toml"]
