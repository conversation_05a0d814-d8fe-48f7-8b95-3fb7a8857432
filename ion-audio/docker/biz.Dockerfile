FROM golang:1.14.4-stretch

ENV GO111MODULE=on
# ENV GOPROXY=https://goproxy.io

WORKDIR $GOPATH/src/github.com/fangyu/ion

COPY go.mod go.sum ./
RUN cd $GOPATH/src/github.com/fangyu/ion && go mod download

COPY pkg/ $GOPATH/src/github.com/fangyu/ion/pkg
COPY cmd/ $GOPATH/src/github.com/fangyu/ion/cmd

WORKDIR $GOPATH/src/github.com/fangyu/ion/cmd/biz
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /biz .

FROM alpine:3.12.0

RUN apk --no-cache add ca-certificates
ARG TZ='Asia/Shanghai'
ENV DEFAULT_TZ ${TZ}
RUN apk upgrade --update \
  && apk add -U tzdata \
  && cp /usr/share/zoneinfo/${DEFAULT_TZ} /etc/localtime \
  && apk del tzdata \
  && rm -rf \
  /var/cache/apk/*

COPY --from=0 /biz /usr/local/bin/biz
COPY configs/docker/biz.toml /configs/biz.toml

ENTRYPOINT ["/usr/local/bin/biz"]
CMD ["-c", "/configs/biz.toml"]
